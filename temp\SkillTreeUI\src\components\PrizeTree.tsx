import { useState } from 'react';
import { motion } from 'framer-motion';
import TreeSection from './TreeSection';
import { sections } from '../data/treeData';
import GrandPrize from './GrandPrize';

const PrizeTree = () => {
  const [activeSection, setActiveSection] = useState<string | null>(null);
  
  const handleSectionClick = (sectionId: string) => {
    setActiveSection(prev => prev === sectionId ? null : sectionId);
  };
  
  return (
    <div className="w-full max-w-6xl mx-auto">
      <div className="mb-8 flex justify-center">
        <GrandPrize />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {sections.map((section) => (
          <motion.div
            key={section.id}
            initial={{ opacity: 0.8, y: 20 }}
            animate={{ 
              opacity: 1, 
              y: 0,
              scale: activeSection === section.id ? 1.03 : 1
            }}
            transition={{ duration: 0.3 }}
            className="relative"
          >
            <TreeSection 
              section={section}
              isActive={activeSection === section.id}
              onClick={() => handleSectionClick(section.id)}
            />
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default PrizeTree;