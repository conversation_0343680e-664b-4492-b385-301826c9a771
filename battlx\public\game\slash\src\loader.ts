/**
 * Slash Game Script Loader
 * 
 * This module handles loading all the necessary JavaScript files for the Slash Game
 * before the game is initialized.
 */

// List of scripts to load in order
const scripts = [
  // Core game components
  '/game/slash/js/src/components/inputHandler.js',
  '/game/slash/js/src/components/assetLoader.js',
  '/game/slash/js/src/components/vector2.js',
  '/game/slash/js/src/components/utils.js',
  
  // Enums and constants
  '/game/slash/js/enums/characterType.js',
  '/game/slash/js/enums/enemyType.js',
  '/game/slash/js/enums/weaponType.js',
  '/game/slash/js/enums/bulletType.js',
  '/game/slash/js/enums/pickupType.js',
  '/game/slash/js/enums/treasureType.js',
  '/game/slash/js/enums/fixedTreasures.js',
  '/game/slash/js/enums/destructibleType.js',
  '/game/slash/js/enums/stageType.js',
  
  // Game components
  '/game/slash/js/src/components/containmentRect.js',
  '/game/slash/js/src/components/virtualJoystick.js',
  '/game/slash/js/src/components/player.js',
  '/game/slash/js/src/components/enemy.js',
  '/game/slash/js/src/components/bullet.js',
  '/game/slash/js/src/components/pickup.js',
  '/game/slash/js/src/components/destructible.js',
  '/game/slash/js/src/components/weapon.js',
  '/game/slash/js/src/components/weaponFactory.js',
  '/game/slash/js/src/components/pickupGroup.js',
  '/game/slash/js/src/components/destructibleGroup.js',
  '/game/slash/js/src/components/bgManager.js',
  '/game/slash/js/src/components/stage.js',
  '/game/slash/js/src/components/sceneManager.js',
  
  // UI components
  '/game/slash/js/src/components/mainUI.js',
  '/game/slash/js/src/components/playerUI.js',
  '/game/slash/js/src/components/levelUpUI.js',
  
  // Game core and main game class
  '/game/slash/js/src/components/gameCore.js',
  '/game/slash/js/src/components/game.js',
  
  // Game data
  '/game/slash/js/consts/weapons.js',
  '/game/slash/js/consts/stages.js',
  '/game/slash/js/consts/enemies.js'
];

// Track loading status
let loadedScripts = 0;
let totalScripts = scripts.length;
let loadingPromise: Promise<void> | null = null;

/**
 * Load all scripts required for the Slash Game
 * @returns Promise that resolves when all scripts are loaded
 */
export const loadGameScripts = (): Promise<void> => {
  // Return existing promise if already loading
  if (loadingPromise) {
    return loadingPromise;
  }
  
  // Create new loading promise
  loadingPromise = new Promise((resolve, reject) => {
    // Skip loading if scripts are already loaded
    // Check if Game and GameCore are available
    if ((window as any).Game && (window as any).GameCore) {
      console.log('Slash Game components already loaded');
      console.log('Game:', (window as any).Game);
      console.log('GameCore:', (window as any).GameCore);
      resolve();
      return;
    } else {
      console.log('Game or GameCore not found in window object');
      console.log('Window keys:', Object.keys(window));
    }
    
    console.log('Loading Slash Game scripts...');
    
    // Load scripts in sequence
    const loadNextScript = (index: number) => {
      if (index >= scripts.length) {
        console.log('All Slash Game scripts loaded successfully');
        resolve();
        return;
      }
      
      const script = document.createElement('script');
      script.src = scripts[index];
      script.async = false; // Load in order
      script.type = 'text/javascript'; // Ensure correct type
      
      console.log(`Loading script ${index + 1}/${scripts.length}: ${scripts[index]}`);
      
      script.onload = () => {
        loadedScripts++;
        console.log(`Script loaded successfully: ${scripts[index]}`);
        
        // Check if Game or GameCore was loaded in this script
        if (scripts[index].includes('game.js') && (window as any).Game) {
          console.log('Game class found after loading game.js:', (window as any).Game);
        }
        
        if (scripts[index].includes('gameCore.js') && (window as any).GameCore) {
          console.log('GameCore class found after loading gameCore.js:', (window as any).GameCore);
        }
        
        loadNextScript(index + 1);
      };
      
      script.onerror = (error) => {
        console.error(`Failed to load script: ${scripts[index]}`, error);
        reject(new Error(`Failed to load script: ${scripts[index]}`));
      };
      
      // Append to document.head instead of document.body for better script loading
      document.head.appendChild(script);
    };
    
    // Start loading scripts
    loadNextScript(0);
  });
  
  return loadingPromise;
};

/**
 * Get the current loading progress
 * @returns Object with loading status
 */
export const getLoadingProgress = () => {
  return {
    loaded: loadedScripts,
    total: totalScripts,
    progress: loadedScripts / totalScripts
  };
};
