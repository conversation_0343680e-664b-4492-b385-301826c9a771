# Prize Tree System - Backend Implementation (Part 1)

## Database Schema

### 1. Prize Trees Table

This table will store the main prize tree categories.

```php
// Create migration
php artisan make:migration create_prize_trees_table

// Migration file
public function up()
{
    Schema::create('prize_trees', function (Blueprint $table) {
        $table->id();
        $table->string('name');
        $table->text('description');
        $table->string('icon')->nullable();
        $table->string('theme_color')->nullable(); // For UI theming
        $table->integer('display_order')->default(0);
        $table->boolean('is_active')->default(true);
        $table->boolean('is_seasonal')->default(false); // For seasonal trees
        $table->timestamp('available_until')->nullable(); // For limited-time trees
        $table->timestamps();
    });
}
```

### 2. Prizes Table

This table will store individual prizes within each tree.

```php
// Create migration
php artisan make:migration create_prizes_table

// Migration file
public function up()
{
    Schema::create('prizes', function (Blueprint $table) {
        $table->id();
        $table->foreignId('prize_tree_id')->constrained()->onDelete('cascade');
        $table->string('name');
        $table->text('description');
        $table->string('icon')->nullable();
        $table->integer('tier')->default(1);
        $table->integer('position')->default(0);
        $table->string('category')->nullable(); // For filtering
        $table->integer('cost')->default(1);
        $table->boolean('is_root')->default(false);
        $table->string('reward_type'); // cosmetic, card, balance, booster, special_item, title, emote
        $table->json('reward_data')->nullable(); // Specific details about the reward
        $table->boolean('is_premium')->default(false); // For premium-only prizes
        $table->timestamps();
    });
}
```

### 3. Prize Prerequisites Table

This table will define the prerequisites for each prize.

```php
// Create migration
php artisan make:migration create_prize_prerequisites_table

// Migration file
public function up()
{
    Schema::create('prize_prerequisites', function (Blueprint $table) {
        $table->id();
        $table->foreignId('prize_id')->constrained()->onDelete('cascade');
        $table->foreignId('prerequisite_prize_id')->constrained('prizes')->onDelete('cascade');
        $table->timestamps();
    });
}
```

### 4. User Prizes Table

This table will track which prizes each user has unlocked.

```php
// Create migration
php artisan make:migration create_user_prizes_table

// Migration file
public function up()
{
    Schema::create('user_prizes', function (Blueprint $table) {
        $table->id();
        $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
        $table->foreignId('prize_id')->constrained()->onDelete('cascade');
        $table->timestamp('unlocked_at');
        $table->boolean('is_equipped')->default(false); // For equippable prizes like cosmetics
        $table->timestamps();
        
        // Prevent duplicate entries
        $table->unique(['telegram_user_id', 'prize_id']);
    });
}
```

### 5. User Achievement Points Table

This table will track the achievement points each user has earned and spent.

```php
// Create migration
php artisan make:migration create_user_achievement_points_table

// Migration file
public function up()
{
    Schema::create('user_achievement_points', function (Blueprint $table) {
        $table->id();
        $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
        $table->integer('total_earned')->default(0);
        $table->integer('total_spent')->default(0);
        $table->timestamps();
        
        // Each user should have only one record
        $table->unique(['telegram_user_id']);
    });
}
```

### 6. Achievement Point Transactions Table

This table will track all achievement point transactions for audit purposes.

```php
// Create migration
php artisan make:migration create_achievement_point_transactions_table

// Migration file
public function up()
{
    Schema::create('achievement_point_transactions', function (Blueprint $table) {
        $table->id();
        $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
        $table->integer('amount');
        $table->string('type'); // 'earn', 'spend', 'refund'
        $table->string('source'); // 'level_up', 'mission_complete', 'prize_unlock', etc.
        $table->foreignId('source_id')->nullable(); // ID of the related entity (mission, prize, etc.)
        $table->text('description')->nullable();
        $table->timestamps();
    });
}
```

## Models

### 1. PrizeTree Model

```php
// Create model
php artisan make:model PrizeTree

// Model file
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PrizeTree extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'icon',
        'theme_color',
        'display_order',
        'is_active',
        'is_seasonal',
        'available_until'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_seasonal' => 'boolean',
        'available_until' => 'datetime'
    ];

    /**
     * Get the prizes for this tree.
     */
    public function prizes()
    {
        return $this->hasMany(Prize::class);
    }

    /**
     * Get the root prizes for this tree.
     */
    public function rootPrizes()
    {
        return $this->hasMany(Prize::class)->where('is_root', true);
    }
    
    /**
     * Check if the tree is currently available.
     */
    public function isAvailable()
    {
        if (!$this->is_active) {
            return false;
        }
        
        if ($this->available_until && now()->gt($this->available_until)) {
            return false;
        }
        
        return true;
    }
}
```

### 2. Prize Model

```php
// Create model
php artisan make:model Prize

// Model file
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Prize extends Model
{
    use HasFactory;

    protected $fillable = [
        'prize_tree_id',
        'name',
        'description',
        'icon',
        'tier',
        'position',
        'category',
        'cost',
        'is_root',
        'reward_type',
        'reward_data',
        'is_premium'
    ];

    protected $casts = [
        'is_root' => 'boolean',
        'is_premium' => 'boolean',
        'reward_data' => 'array'
    ];

    /**
     * Get the prize tree that owns this prize.
     */
    public function prizeTree()
    {
        return $this->belongsTo(PrizeTree::class);
    }

    /**
     * Get the prerequisites for this prize.
     */
    public function prerequisites()
    {
        return $this->belongsToMany(
            Prize::class,
            'prize_prerequisites',
            'prize_id',
            'prerequisite_prize_id'
        );
    }

    /**
     * Get the prizes that have this prize as a prerequisite.
     */
    public function unlocks()
    {
        return $this->belongsToMany(
            Prize::class,
            'prize_prerequisites',
            'prerequisite_prize_id',
            'prize_id'
        );
    }

    /**
     * Get the users who have unlocked this prize.
     */
    public function users()
    {
        return $this->belongsToMany(
            TelegramUser::class,
            'user_prizes',
            'prize_id',
            'telegram_user_id'
        )->withTimestamps()->withPivot('unlocked_at', 'is_equipped');
    }
    
    /**
     * Get the reward details based on the reward type.
     */
    public function getRewardDetails()
    {
        switch ($this->reward_type) {
            case 'cosmetic':
                // Return cosmetic details
                return [
                    'type' => $this->reward_data['type'] ?? 'slash_effect',
                    'visual_data' => $this->reward_data['visual_data'] ?? [],
                    'preview_image' => $this->reward_data['preview_image'] ?? null
                ];
                
            case 'card':
                // Return card details
                return [
                    'card_id' => $this->reward_data['card_id'] ?? null,
                    'rarity' => $this->reward_data['rarity'] ?? 'common'
                ];
                
            case 'balance':
                // Return balance amount
                return [
                    'amount' => $this->reward_data['amount'] ?? 0,
                    'currency' => $this->reward_data['currency'] ?? 'coins'
                ];
                
            case 'booster':
                // Return booster details
                return [
                    'type' => $this->reward_data['booster_type'] ?? '',
                    'multiplier' => $this->reward_data['multiplier'] ?? 1,
                    'duration' => $this->reward_data['duration'] ?? 0 // in hours
                ];
                
            case 'special_item':
                // Return special item details
                return [
                    'item_id' => $this->reward_data['item_id'] ?? null,
                    'item_type' => $this->reward_data['item_type'] ?? 'collectible'
                ];
                
            case 'title':
                // Return title details
                return [
                    'title' => $this->reward_data['title'] ?? '',
                    'color' => $this->reward_data['color'] ?? '#FFFFFF'
                ];
                
            case 'emote':
                // Return emote details
                return [
                    'emote' => $this->reward_data['emote'] ?? '',
                    'animation' => $this->reward_data['animation'] ?? null
                ];
                
            default:
                return null;
        }
    }
}
```

### 3. UserPrize Model

```php
// Create model
php artisan make:model UserPrize

// Model file
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserPrize extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id',
        'prize_id',
        'unlocked_at',
        'is_equipped'
    ];

    protected $casts = [
        'unlocked_at' => 'datetime',
        'is_equipped' => 'boolean'
    ];

    /**
     * Get the user that owns this prize.
     */
    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    /**
     * Get the prize that is unlocked.
     */
    public function prize()
    {
        return $this->belongsTo(Prize::class);
    }
    
    /**
     * Equip this prize (for cosmetics, titles, etc.)
     */
    public function equip()
    {
        // First, unequip any other prizes of the same type
        $prize = $this->prize;
        
        if (in_array($prize->reward_type, ['cosmetic', 'title', 'emote'])) {
            // Get the specific subtype for cosmetics
            $subtype = null;
            
            if ($prize->reward_type === 'cosmetic') {
                $rewardDetails = $prize->getRewardDetails();
                $subtype = $rewardDetails['type'] ?? null;
            }
            
            // Unequip other prizes of the same type/subtype
            UserPrize::where('telegram_user_id', $this->telegram_user_id)
                ->whereHas('prize', function ($query) use ($prize, $subtype) {
                    $query->where('reward_type', $prize->reward_type);
                    
                    if ($subtype) {
                        $query->whereJsonContains('reward_data->type', $subtype);
                    }
                })
                ->where('id', '!=', $this->id)
                ->update(['is_equipped' => false]);
        }
        
        // Equip this prize
        $this->is_equipped = true;
        $this->save();
        
        return $this;
    }
    
    /**
     * Unequip this prize
     */
    public function unequip()
    {
        $this->is_equipped = false;
        $this->save();
        
        return $this;
    }
}
```
