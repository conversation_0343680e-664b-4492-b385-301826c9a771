// Stage constants
window.StageType = {
    FOREST: 'FOREST'
};

window.StageEventType = {
    GHOST_SWARM: 'GHOST_SWARM',
    BAT_SWARM: 'BAT_SWARM'
};

// Make STAGES a global variable by attaching it to window
window.STAGES = {
    [window.StageType.FOREST]: [
        {
            stageName: 'Forest - Beginning',
            description: 'The beginning of your journey through the haunted forest.',
            unlocked: true,
            hidden: false,
            tips: 'Move around to avoid enemies and collect gems to level up.',
            hyper: {
                unlocked: false,
                PlayerPxSpeed: 1.5,
                EnemySpeed: 1.6,
                ProjectileSpeed: 1.2,
                GoldMultiplier: 1.5,
                EnemyMinimumMul: 1.25,
                StartingSpawns: 20,
                tips: 'Gold multiplier: x1.5'
            },
            startingSpawns: 4,       // Start with a few enemies
            minute: 0,
            destructibleType: DestructibleType.BOX,
            destructibleFreq: 1000,
            destructibleChance: 10,
            destructibleChanceMax: 500,
            maxDestructibles: 10,
            // Initial values
            minimum: 3,              // At least 3 enemies on screen
            maximum: 5,              // No more than 5 at start
            frequency: 2000,         // 2 seconds between spawns
            // Include basic enemy types for minute 0
            enemies: [EnemyType.GHOST1, EnemyType.BAT, EnemyType.TELEPORTER],
            // Weights favor basic enemies with some TELEPORTER for testing
            enemyWeights: {
                [EnemyType.GHOST1]: 70,
                [EnemyType.BAT]: 20,
                [EnemyType.TELEPORTER]: 10
            }
        },
        {
            stageName: 'Forest - Minute 1',
            minute: 1,
            // Enemy counts will be calculated dynamically based on time and player level
            enemies: [EnemyType.GHOST1, EnemyType.GHOST2, EnemyType.GHOST3, EnemyType.BAT, EnemyType.OGRE, EnemyType.TELEPORTER],
            enemyWeights: typeof EnemySpawnConfig !== 'undefined' ? EnemySpawnConfig.TYPE_WEIGHTS[1] : {},
            bosses: [] // Removed boss from phase 1 to prevent early spawning
        },
        {
            stageName: 'Forest - Minute 2',
            minute: 2,
            enemies: [EnemyType.GHOST1, EnemyType.GHOST2, EnemyType.GHOST3, EnemyType.BAT, EnemyType.OGRE, EnemyType.HORN],
            enemyWeights: typeof EnemySpawnConfig !== 'undefined' ? EnemySpawnConfig.TYPE_WEIGHTS[2] : {},
            events: [
                {
                    eventType: StageEventType.GHOST_SWARM,
                    delay: 5000,     // 5 seconds delay
                    repeat: 0,        // No repeats
                    amount: 1      // 5 ghosts in swarm
                }
            ]
        },
        {
            stageName: 'Forest - Minute 3',
            minute: 3,
            enemies: [EnemyType.GHOST1, EnemyType.GHOST2, EnemyType.GHOST3, EnemyType.BAT, EnemyType.OGRE, EnemyType.HORN],
            enemyWeights: typeof EnemySpawnConfig !== 'undefined' ? EnemySpawnConfig.TYPE_WEIGHTS[3] : {},
            events: [
                {
                    eventType: StageEventType.BAT_SWARM,
                    delay: 5000,
                    repeat: 0,
                    amount: 1
                }
            ],
            bosses: [EnemyType.GHOST_BOSS1]
        },
        {
            stageName: 'Forest - Minute 4',
            minute: 4,
            enemies: [EnemyType.GHOST1, EnemyType.GHOST2, EnemyType.GHOST3, EnemyType.BAT, EnemyType.OGRE, EnemyType.HORN],
            enemyWeights: typeof EnemySpawnConfig !== 'undefined' ? EnemySpawnConfig.TYPE_WEIGHTS[4] : {},
            events: [
                {
                    eventType: StageEventType.GHOST_SWARM,
                    delay: 5000,
                    repeat: 1,
                    amount: 2
                }
            ]
        },
        {
            stageName: 'Forest - Minute 5+',
            minute: 5,
            enemies: [EnemyType.GHOST1, EnemyType.GHOST2, EnemyType.GHOST3, EnemyType.BAT, EnemyType.OGRE, EnemyType.HORN],
            enemyWeights: typeof EnemySpawnConfig !== 'undefined' ? EnemySpawnConfig.TYPE_WEIGHTS[5] : {},
            events: [
                {
                    eventType: StageEventType.BAT_SWARM,
                    delay: 10000,
                    repeat: 1,
                    amount: 2
                },
                {
                    eventType: StageEventType.GHOST_SWARM,
                    delay: 20000,
                    repeat: 1,
                    amount: 3
                }
            ],
            bosses: [EnemyType.GHOST_BOSS1]
        }
    ]
};
