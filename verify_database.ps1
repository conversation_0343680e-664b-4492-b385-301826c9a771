# PowerShell script to verify referral data in database
# This script helps you check if the referral data was saved correctly

Write-Host "=== Database Verification for Referral System ===" -ForegroundColor Green
Write-Host ""

Write-Host "To verify the referral system is working, run these SQL queries in your PostgreSQL database:" -ForegroundColor Yellow
Write-Host ""

Write-Host "1. Check all test users and their referral relationships:" -ForegroundColor Cyan
Write-Host @"
SELECT 
    telegram_id,
    first_name,
    last_name,
    referred_by,
    balance,
    created_at
FROM telegram_users 
WHERE telegram_id IN (12345, 67890, 99999, 11111)
ORDER BY created_at;
"@ -ForegroundColor White

Write-Host ""
Write-Host "2. Check referral relationship (should show <PERSON> referred Bob):" -ForegroundColor Cyan
Write-Host @"
SELECT 
    referrer.telegram_id as referrer_id,
    referrer.first_name as referrer_name,
    referrer.balance as referrer_balance,
    referred.telegram_id as referred_id,
    referred.first_name as referred_name,
    referred.balance as referred_balance,
    referred.referred_by
FROM telegram_users referrer
JOIN telegram_users referred ON referrer.telegram_id = referred.referred_by
WHERE referrer.telegram_id = 12345 AND referred.telegram_id = 67890;
"@ -ForegroundColor White

Write-Host ""
Write-Host "Expected Results:" -ForegroundColor Yellow
Write-Host "- Alice (12345): balance should be 10000 (5000 initial + 5000 referral bonus)" -ForegroundColor Green
Write-Host "- Bob (67890): balance should be 10000 (5000 initial + 5000 referral bonus), referred_by should be 12345" -ForegroundColor Green
Write-Host "- Charlie (99999): balance should be 5000 (no referral bonus), referred_by should be NULL" -ForegroundColor Green
Write-Host "- David (11111): balance should be 5000 (no referral bonus), referred_by should be NULL" -ForegroundColor Green

Write-Host ""
Write-Host "3. Quick balance check:" -ForegroundColor Cyan
Write-Host @"
SELECT telegram_id, first_name, balance, referred_by 
FROM telegram_users 
WHERE telegram_id IN (12345, 67890, 99999, 11111)
ORDER BY telegram_id;
"@ -ForegroundColor White

Write-Host ""
Write-Host "To connect to your PostgreSQL database, use:" -ForegroundColor Yellow
Write-Host "psql -h localhost -U your_username -d your_database_name" -ForegroundColor Cyan
