// Destructible class for handling destructible objects
class Destructible {
    constructor(x, y, destructibleType) {
        this.x = x;
        this.y = y;
        this.hp = 1;
        this.maxHp = 1;
        this.isDead = false;
        this.receivingDamage = false;
        this.isTeleportOnCull = false;
        this.owner = null;
        this.destructibleType = destructibleType;
        this.radius = 20;

        // Load destructible data
        this.loadDestructibleData();
    }

    // Load destructible data from constants
    loadDestructibleData() {
        const data = DESTRUCTIBLES[this.destructibleType];
        if (!data) return;

        this.maxHp = data.maxHp;
        this.hp = this.maxHp;
        this.frameName = data.frameName;
    }

    // Initialize the destructible
    init() {
        // Nothing to do here in the base implementation
    }

    // Reset the destructible when recycled
    onRecycle() {
        this.hp = this.maxHp;
        this.isDead = false;
        this.receivingDamage = false;
    }

    // Teleport on cull
    onTeleportOnCull() {
        // Nothing to do here in the base implementation
    }

    // Despawn the destructible
    despawn() {
        this.isDead = true;

        if (Game.core) {
            // Remove from destructibles array
            const index = Game.core.destructibles.indexOf(this);
            if (index !== -1) {
                Game.core.destructibles.splice(index, 1);
            }
        }
    }

    // Get taken (picked up)
    getTaken() {
        this.despawn();
    }

    // Take damage
    takeDamage(hp = 1) {
        if (this.isDead) return;

        this.hp -= hp;

        if (this.hp <= 0) {
            this.isDead = true;
            this.onDestroyed();
        }

        this.onGetDamaged();
    }

    // Visual feedback when taking damage
    onGetDamaged() {
        this.receivingDamage = true;

        // Reset receiving damage after a short delay
        setTimeout(() => {
            this.receivingDamage = false;

            if (this.hp <= 0) {
                this.despawn();
            }
        }, 120);
    }

    // Handle destruction
    onDestroyed() {
        if (Game.core) {
            // Spawn a random pickup
            Game.core.makePickup(this.x, this.y, Game.core.lootManager.getRandomWeightedItem());
        }

        // Update destruction count
        if (DESTRUCTIBLES[this.destructibleType]) {
            DESTRUCTIBLES[this.destructibleType].destroyedAmount += 1;
        }
    }

    // Update the destructible
    update(deltaTime) {
        // Nothing to do here in the base implementation
    }

    // Draw the destructible
    draw(ctx, camera, sprites) {
        if (this.isDead) return;
        if (!sprites) return;

        // Calculate screen position
        const screenX = this.x - camera.x + camera.width / 2;
        const screenY = this.y - camera.y + camera.height / 2;

        // Get the sprite
        const sprite = sprites[this.frameName];
        if (!sprite) {
            // Draw a fallback rectangle if sprite not found
            ctx.fillStyle = 'rgba(139, 69, 19, 0.8)';
            ctx.fillRect(screenX - 15, screenY - 15, 30, 30);
            return;
        }

        ctx.save();

        // Apply damage effect
        if (this.receivingDamage) {
            ctx.fillStyle = '#ffffff';
            ctx.globalCompositeOperation = 'lighter';
        }

        // Draw the sprite
        ctx.drawImage(
            sprite,
            screenX - sprite.width / 2,
            screenY - sprite.height / 2,
            sprite.width,
            sprite.height
        );

        ctx.restore();

        // Draw debug collision circle
        if (Game.core && Game.core.debug) {
            ctx.beginPath();
            ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(139, 69, 19, 0.5)';
            ctx.stroke();
        }
    }
}

// DestructibleGroup class for managing groups of destructibles
class DestructibleGroup {
    constructor() {
        this.stored = [];
        this.spawned = [];
    }

    // Initialize the group
    init() {
        // Nothing to do here in the base implementation
    }

    // Spawn a destructible at a position
    spawnAt(x, y, type) {
        const item = this.spawn(type);
        item.x = x;
        item.y = y;
        item.onRecycle();
        return item;
    }

    // Spawn a destructible
    spawn(type) {
        let item = this.stored.pop();

        if (!item) {
            item = this.make(type);
            item.init();
        }

        this.spawned.push(item);

        if (Game.core) {
            Game.core.destructibles.push(item);
        }

        return item;
    }

    // Return a destructible to the pool
    return(item) {
        const index = this.spawned.indexOf(item);
        if (index !== -1) {
            this.spawned.splice(index, 1);
        }

        if (Game.core) {
            const gameIndex = Game.core.destructibles.indexOf(item);
            if (gameIndex !== -1) {
                Game.core.destructibles.splice(gameIndex, 1);
            }
        }

        this.stored.push(item);
    }

    // Create a new destructible
    make(type) {
        return new Destructible(0, 0, type);
    }
}

// Attach to window object for global access
window.Destructible = Destructible;
window.DestructibleGroup = DestructibleGroup;
