{"name": "tower_game", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "npm run build && node index.js", "build": "webpack --mode production --module-bind js=babel-loader"}, "repository": {"type": "git", "url": "git+https://github.com/bmqb/tower_game.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/bmqb/tower_game/issues"}, "homepage": "https://github.com/bmqb/tower_game#readme", "devDependencies": {"@babel/core": "^7.0.0-beta.42", "@babel/preset-env": "^7.0.0-beta.42", "babel-loader": "^8.0.0-beta", "express": "^4.16.3", "opn": "^5.3.0", "webpack": "^4.0.1", "webpack-cli": "^3.1.1"}, "dependencies": {"cooljs": "^1.0.2"}}