// import { useState } from "react";
import { Button } from "./ui/button";
import Drawer, { DrawerProps } from "./ui/drawer";
import Price from "./Price";
import { useMutation } from "@tanstack/react-query";
import { $http } from "@/lib/http";
import { toast } from "react-toastify";
import { BattlxIcon } from "@/components/icons/BattlxIcon";
import { Mission, MissionLevel } from "@/types/MissionType";
import { UserType } from "@/types/UserType";
import { useUserStore } from "@/store/user-store";
import { useMemo } from "react";

export default function MissionDrawer({
  mission,
  ...props
}: DrawerProps & {
  mission: Mission | null;
}) {
  // const queryClient = useQueryClient();
  const { balance } = useUserStore();

  const insufficientBalance = useMemo(() => {
    if (!mission?.next_level?.cost) return false;
    return balance < mission?.next_level?.cost;
  }, [balance, mission?.next_level?.cost]);

  const upgradeMution = useMutation({
    mutationFn: () =>
      $http.post<{ message: string; user: UserType; next_level: MissionLevel }>(
        `/clicker/mission-levels/${mission?.next_level?.id}`
      ),
    onSuccess: ({ data }) => {
      toast.success(data.message || "Mission upgraded successfully");
      useUserStore.setState({ ...data.user });
      const pph = mission?.next_level?.production_per_hour || 0;
      mission!.production_per_hour = (
        mission?.production_per_hour ? +mission.production_per_hour + pph : pph
      ).toString();
      mission!.next_level = data.next_level;
      props.onOpenChange?.(false);
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "An error occurred");
    },
  });

  if (!mission || !mission.next_level) return null;
  return (
    <Drawer {...props}>
      <img
        src={mission.image}
        alt={mission.name}
        className="object-contain h-32 mx-auto opacity-80 [filter:sepia(50%)_saturate(50%)_brightness(80%)_contrast(120%)]"
      />
      <h2 className="mt-6 text-2xl font-medium text-center text-[#9B8B6C]">{mission.name}</h2>
      <div className="flex flex-col mx-auto mt-4 w-fit">
        <p className="text-xs text-center text-[#B3B3B3]/80">Production per hour</p>
        <Price
          amount={"+" + mission.next_level.production_per_hour.toLocaleString()}
          className="justify-center mt-2 text-sm text-[#9B8B6C]"
        />
      </div>

      <div className="flex items-center justify-center mx-auto mt-6 space-x-1 text-[#9B8B6C]">
        <BattlxIcon
          icon="coins"
          className="w-8 h-8 text-[#9B8B6C]"
        />
        <span className="font-bold">
          {mission.next_level.cost.toLocaleString()}
        </span>
      </div>
      <Button
        className="w-full mt-6 bg-[#1A1617] border border-[#B3B3B3]/20 text-[#9B8B6C] hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_15px_rgba(74,14,14,0.3)] disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={upgradeMution.isPending || insufficientBalance}
        onClick={() => upgradeMution.mutate()}
      >
        {upgradeMution.isPending && (
          <BattlxIcon icon="loading" className="w-6 h-6 mr-2 animate-spin" />
        )}
        {insufficientBalance ? "Insufficient Balance" : "Go ahead"}
      </Button>
    </Drawer>
  );
}
