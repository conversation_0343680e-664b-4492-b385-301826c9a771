# Performance Optimization Implementation

## Overview
This document covers performance optimization strategies for the Pet System, including database optimization, caching strategies, frontend performance, and mobile optimization.

## Implementation Time: 3-4 days
## Complexity: High
## Dependencies: Redis, database indexing, CDN setup

## Database Optimization

### Database Indexing Strategy
```sql
-- File: api/database/migrations/2024_01_01_000030_add_performance_indexes.php

-- Pet-related indexes
CREATE INDEX idx_pets_user_featured ON pets(telegram_user_id, is_featured);
CREATE INDEX idx_pets_template_active ON pets(pet_template_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_pets_happiness_low ON pets(happiness) WHERE happiness < 30;
CREATE INDEX idx_pets_evolution_ready ON pets(level, evolution_stage);

-- Pet interactions indexes
CREATE INDEX idx_pet_interactions_daily ON pet_interactions(pet_id, interaction_time) 
WHERE interaction_time >= CURRENT_DATE;
CREATE INDEX idx_pet_interactions_type_time ON pet_interactions(interaction_type, interaction_time);
CREATE INDEX idx_pet_interactions_user_recent ON pet_interactions(telegram_user_id, interaction_time DESC);

-- Mystery box indexes
CREATE INDEX idx_mystery_box_openings_user_recent ON mystery_box_openings(telegram_user_id, opened_at DESC);
CREATE INDEX idx_mystery_box_openings_type_stats ON mystery_box_openings(box_type, opened_at);

-- Collection indexes
CREATE INDEX idx_collectibles_user_owned ON collectibles(telegram_user_id, is_owned);
CREATE INDEX idx_collectibles_set_position ON collectibles(collection_set_id, set_position);
CREATE INDEX idx_collectibles_rarity_category ON collectibles(rarity, category) WHERE is_owned = true;

-- Achievement points indexes
CREATE INDEX idx_achievement_points_user_total ON achievement_points(telegram_user_id, points_earned);
CREATE INDEX idx_achievement_points_source_recent ON achievement_points(source_type, created_at DESC);
```

### Query Optimization
```php
<?php
// File: api/app/Services/OptimizedPetService.php

namespace App\Services;

use App\Models\Pet;
use App\Models\TelegramUser;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class OptimizedPetService
{
    /**
     * Get user pets with optimized queries
     */
    public function getUserPetsOptimized(TelegramUser $user): array
    {
        $cacheKey = "user_pets_{$user->id}";
        
        return Cache::remember($cacheKey, 300, function() use ($user) {
            // Single query with all necessary joins
            $pets = Pet::with([
                'template:id,name,category,rarity,image_url,evolution_images,evolution_levels',
                'interactions' => function($query) {
                    $query->whereDate('interaction_time', today())
                          ->select('pet_id', 'interaction_type', 'interaction_time')
                          ->orderBy('interaction_time', 'desc');
                }
            ])
            ->where('telegram_user_id', $user->id)
            ->select([
                'id', 'pet_template_id', 'level', 'experience', 'happiness',
                'evolution_stage', 'nickname', 'is_featured', 'last_interaction',
                'created_at'
            ])
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

            // Calculate derived fields efficiently
            return $pets->map(function($pet) {
                $pet->happiness_percentage = min(100, ($pet->happiness / ($pet->template->max_happiness ?? 100)) * 100);
                $pet->display_name = $pet->nickname ?: $pet->template->name;
                $pet->current_image = $pet->template->evolution_images[$pet->evolution_stage] ?? $pet->template->image_url;
                
                // Calculate interaction availability
                $lastInteractions = $pet->interactions->groupBy('interaction_type');
                $pet->can_feed = $this->canInteract($lastInteractions, 'feed', 60);
                $pet->can_play = $this->canInteract($lastInteractions, 'play', 120);
                $pet->can_pet = $this->canInteract($lastInteractions, 'pet', 30);
                
                $pet->needs_attention = $pet->happiness_percentage < 30 || 
                                       $pet->last_interaction < now()->subHours(24);
                
                return $pet;
            });
        });
    }

    /**
     * Batch update happiness decay for multiple pets
     */
    public function batchUpdateHappinessDecay(): int
    {
        // Use raw SQL for better performance
        $updated = DB::update("
            UPDATE pets 
            SET happiness = GREATEST(0, happiness - (
                EXTRACT(EPOCH FROM (NOW() - last_interaction)) / 3600 * 
                (SELECT happiness_decay_rate FROM pet_templates WHERE id = pets.pet_template_id)
            ))
            WHERE last_interaction < NOW() - INTERVAL '1 hour'
            AND happiness > 0
        ");

        // Clear affected cache entries
        $affectedUsers = Pet::where('last_interaction', '<', now()->subHour())
                           ->distinct()
                           ->pluck('telegram_user_id');
        
        foreach ($affectedUsers as $userId) {
            Cache::forget("user_pets_{$userId}");
        }

        return $updated;
    }

    /**
     * Get collection progress with optimized queries
     */
    public function getCollectionProgressOptimized(TelegramUser $user): array
    {
        $cacheKey = "collection_progress_{$user->id}";
        
        return Cache::remember($cacheKey, 600, function() use ($user) {
            // Single query for all statistics
            $stats = DB::select("
                SELECT 
                    COUNT(CASE WHEN p.id IS NOT NULL THEN 1 END) as owned_pets,
                    COUNT(pt.id) as total_pets,
                    COUNT(CASE WHEN c.is_owned = true THEN 1 END) as owned_collectibles,
                    COUNT(ct.id) as total_collectibles,
                    COUNT(CASE WHEN cs.is_completed = true THEN 1 END) as completed_sets,
                    COUNT(cs.id) as total_sets
                FROM telegram_users u
                CROSS JOIN pet_templates pt
                LEFT JOIN pets p ON p.telegram_user_id = u.id AND p.pet_template_id = pt.id
                CROSS JOIN collectible_templates ct
                LEFT JOIN collectibles c ON c.telegram_user_id = u.id AND c.collectible_id = ct.collectible_id
                CROSS JOIN collection_sets cs
                LEFT JOIN user_collection_progress ucp ON ucp.telegram_user_id = u.id AND ucp.collection_set_id = cs.set_id
                WHERE u.id = ? AND pt.is_active = true AND ct.is_active = true
            ", [$user->id]);

            $stat = $stats[0];
            
            return [
                'pets' => [
                    'owned' => $stat->owned_pets,
                    'total' => $stat->total_pets,
                    'percentage' => $stat->total_pets > 0 ? ($stat->owned_pets / $stat->total_pets) * 100 : 0
                ],
                'collectibles' => [
                    'owned' => $stat->owned_collectibles,
                    'total' => $stat->total_collectibles,
                    'percentage' => $stat->total_collectibles > 0 ? ($stat->owned_collectibles / $stat->total_collectibles) * 100 : 0
                ],
                'sets' => [
                    'completed' => $stat->completed_sets,
                    'total' => $stat->total_sets,
                    'percentage' => $stat->total_sets > 0 ? ($stat->completed_sets / $stat->total_sets) * 100 : 0
                ]
            ];
        });
    }

    private function canInteract($lastInteractions, $type, $cooldownMinutes): bool
    {
        $lastInteraction = $lastInteractions->get($type)?->first();
        if (!$lastInteraction) return true;
        
        return $lastInteraction->interaction_time < now()->subMinutes($cooldownMinutes);
    }
}
```

## Caching Strategy

### Redis Cache Implementation
```php
<?php
// File: api/app/Services/CacheService.php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class CacheService
{
    const CACHE_PREFIXES = [
        'user_pets' => 'pets:user:',
        'pet_templates' => 'templates:pets:',
        'mystery_boxes' => 'boxes:mystery:',
        'collection_progress' => 'progress:collection:',
        'leaderboard' => 'leaderboard:',
        'statistics' => 'stats:'
    ];

    const CACHE_DURATIONS = [
        'user_pets' => 300,        // 5 minutes
        'pet_templates' => 3600,   // 1 hour
        'mystery_boxes' => 1800,   // 30 minutes
        'collection_progress' => 600, // 10 minutes
        'leaderboard' => 900,      // 15 minutes
        'statistics' => 1800       // 30 minutes
    ];

    /**
     * Cache user pets with tags for selective invalidation
     */
    public function cacheUserPets(int $userId, array $pets): void
    {
        $key = self::CACHE_PREFIXES['user_pets'] . $userId;
        
        Cache::tags(['user_pets', "user_{$userId}"])
             ->put($key, $pets, self::CACHE_DURATIONS['user_pets']);
    }

    /**
     * Get cached user pets
     */
    public function getCachedUserPets(int $userId): ?array
    {
        $key = self::CACHE_PREFIXES['user_pets'] . $userId;
        return Cache::get($key);
    }

    /**
     * Invalidate user-specific caches
     */
    public function invalidateUserCaches(int $userId): void
    {
        Cache::tags(["user_{$userId}"])->flush();
    }

    /**
     * Cache pet templates with long duration
     */
    public function cachePetTemplates(array $templates): void
    {
        $key = self::CACHE_PREFIXES['pet_templates'] . 'all';
        
        Cache::tags(['pet_templates'])
             ->put($key, $templates, self::CACHE_DURATIONS['pet_templates']);
    }

    /**
     * Cache mystery box types
     */
    public function cacheMysteryBoxTypes(array $boxTypes): void
    {
        $key = self::CACHE_PREFIXES['mystery_boxes'] . 'types';
        
        Cache::tags(['mystery_boxes'])
             ->put($key, $boxTypes, self::CACHE_DURATIONS['mystery_boxes']);
    }

    /**
     * Cache collection progress
     */
    public function cacheCollectionProgress(int $userId, array $progress): void
    {
        $key = self::CACHE_PREFIXES['collection_progress'] . $userId;
        
        Cache::tags(['collection_progress', "user_{$userId}"])
             ->put($key, $progress, self::CACHE_DURATIONS['collection_progress']);
    }

    /**
     * Cache leaderboard data
     */
    public function cacheLeaderboard(string $type, array $data): void
    {
        $key = self::CACHE_PREFIXES['leaderboard'] . $type;
        
        Cache::tags(['leaderboard'])
             ->put($key, $data, self::CACHE_DURATIONS['leaderboard']);
    }

    /**
     * Batch invalidate related caches
     */
    public function invalidateRelatedCaches(string $action, array $params = []): void
    {
        switch ($action) {
            case 'pet_purchased':
                $this->invalidateUserCaches($params['user_id']);
                Cache::tags(['leaderboard'])->flush();
                break;
                
            case 'pet_interaction':
                $this->invalidateUserCaches($params['user_id']);
                break;
                
            case 'mystery_box_opened':
                $this->invalidateUserCaches($params['user_id']);
                Cache::tags(['collection_progress'])->flush();
                break;
                
            case 'template_updated':
                Cache::tags(['pet_templates'])->flush();
                break;
        }
    }

    /**
     * Warm up critical caches
     */
    public function warmUpCaches(): void
    {
        // Warm up pet templates
        $templates = \App\Models\PetTemplate::active()->get();
        $this->cachePetTemplates($templates->toArray());

        // Warm up mystery box types
        $boxTypes = \App\Models\MysteryBoxType::active()->get();
        $this->cacheMysteryBoxTypes($boxTypes->toArray());

        // Warm up leaderboards
        $this->warmUpLeaderboards();
    }

    private function warmUpLeaderboards(): void
    {
        $leaderboards = [
            'pet_count' => \App\Models\Pet::select('telegram_user_id', DB::raw('COUNT(*) as count'))
                                        ->groupBy('telegram_user_id')
                                        ->orderBy('count', 'desc')
                                        ->limit(100)
                                        ->get(),
            'collection_progress' => \App\Models\TelegramUser::select('id', 'username')
                                                            ->withCount('pets')
                                                            ->orderBy('pets_count', 'desc')
                                                            ->limit(100)
                                                            ->get()
        ];

        foreach ($leaderboards as $type => $data) {
            $this->cacheLeaderboard($type, $data->toArray());
        }
    }
}
```

## Frontend Performance Optimization

### React Component Optimization
```typescript
// File: battlx/src/components/pets/OptimizedPetCollection.tsx

import React, { useMemo, useCallback, memo } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import { motion } from 'framer-motion';
import { usePetStore } from '../../stores/petStore';
import { useVirtualization } from '../../hooks/useVirtualization';

interface OptimizedPetCollectionProps {
  className?: string;
}

// Memoized pet card component
const MemoizedPetCard = memo(({ pet, style, onSelect }: any) => {
  const handleClick = useCallback(() => {
    onSelect(pet.id);
  }, [pet.id, onSelect]);

  return (
    <div style={style}>
      <motion.div
        className="pet-card-optimized"
        onClick={handleClick}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        layout
      >
        <img 
          src={pet.current_image} 
          alt={pet.display_name}
          loading="lazy"
          decoding="async"
        />
        <div className="pet-info">
          <h3>{pet.display_name}</h3>
          <div className="pet-level">Lv. {pet.level}</div>
        </div>
      </motion.div>
    </div>
  );
});

const OptimizedPetCollection: React.FC<OptimizedPetCollectionProps> = ({ 
  className = '' 
}) => {
  const { pets, loading } = usePetStore();
  
  // Memoize filtered and sorted pets
  const processedPets = useMemo(() => {
    return pets
      .filter(pet => pet.is_owned)
      .sort((a, b) => {
        if (a.is_featured !== b.is_featured) {
          return a.is_featured ? -1 : 1;
        }
        return b.level - a.level;
      });
  }, [pets]);

  // Virtualization for large collections
  const {
    containerRef,
    itemCount,
    itemSize,
    containerHeight
  } = useVirtualization({
    items: processedPets,
    itemHeight: 200,
    itemsPerRow: 3,
    containerWidth: window.innerWidth - 32
  });

  const handlePetSelect = useCallback((petId: string) => {
    // Handle pet selection
  }, []);

  // Render item for virtualized grid
  const renderItem = useCallback(({ columnIndex, rowIndex, style }: any) => {
    const index = rowIndex * 3 + columnIndex;
    const pet = processedPets[index];
    
    if (!pet) return null;

    return (
      <MemoizedPetCard
        key={pet.id}
        pet={pet}
        style={style}
        onSelect={handlePetSelect}
      />
    );
  }, [processedPets, handlePetSelect]);

  if (loading) {
    return <div className="loading-skeleton">Loading pets...</div>;
  }

  return (
    <div className={`optimized-pet-collection ${className}`} ref={containerRef}>
      <Grid
        columnCount={3}
        columnWidth={itemSize.width}
        height={containerHeight}
        rowCount={Math.ceil(itemCount / 3)}
        rowHeight={itemSize.height}
        width="100%"
      >
        {renderItem}
      </Grid>
    </div>
  );
};

export default OptimizedPetCollection;
```

### Image Optimization Hook
```typescript
// File: battlx/src/hooks/useImageOptimization.ts

import { useState, useEffect, useCallback } from 'react';

interface ImageOptimizationOptions {
  lazy?: boolean;
  placeholder?: string;
  quality?: number;
  format?: 'webp' | 'avif' | 'auto';
}

export const useImageOptimization = (
  src: string, 
  options: ImageOptimizationOptions = {}
) => {
  const [optimizedSrc, setOptimizedSrc] = useState<string>(options.placeholder || '');
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateOptimizedUrl = useCallback((originalSrc: string) => {
    // Check if browser supports WebP
    const supportsWebP = document.createElement('canvas')
      .toDataURL('image/webp')
      .indexOf('data:image/webp') === 0;

    // Check if browser supports AVIF
    const supportsAVIF = document.createElement('canvas')
      .toDataURL('image/avif')
      .indexOf('data:image/avif') === 0;

    let format = options.format || 'auto';
    if (format === 'auto') {
      format = supportsAVIF ? 'avif' : supportsWebP ? 'webp' : 'jpg';
    }

    // Add optimization parameters
    const url = new URL(originalSrc, window.location.origin);
    url.searchParams.set('format', format);
    url.searchParams.set('quality', (options.quality || 80).toString());
    
    // Add responsive sizing
    const devicePixelRatio = window.devicePixelRatio || 1;
    url.searchParams.set('dpr', devicePixelRatio.toString());

    return url.toString();
  }, [options.format, options.quality]);

  useEffect(() => {
    if (!src) return;

    const img = new Image();
    const optimizedUrl = generateOptimizedUrl(src);

    img.onload = () => {
      setOptimizedSrc(optimizedUrl);
      setIsLoaded(true);
      setError(null);
    };

    img.onerror = () => {
      // Fallback to original image
      const fallbackImg = new Image();
      fallbackImg.onload = () => {
        setOptimizedSrc(src);
        setIsLoaded(true);
        setError(null);
      };
      fallbackImg.onerror = () => {
        setError('Failed to load image');
      };
      fallbackImg.src = src;
    };

    if (options.lazy) {
      // Use Intersection Observer for lazy loading
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              img.src = optimizedUrl;
              observer.disconnect();
            }
          });
        },
        { threshold: 0.1 }
      );

      // Create a dummy element to observe
      const dummyElement = document.createElement('div');
      observer.observe(dummyElement);
      
      return () => observer.disconnect();
    } else {
      img.src = optimizedUrl;
    }
  }, [src, generateOptimizedUrl, options.lazy]);

  return {
    src: optimizedSrc,
    isLoaded,
    error
  };
};
```

## Mobile Performance Optimization

### Touch Optimization
```typescript
// File: battlx/src/hooks/useTouchOptimization.ts

import { useCallback, useRef } from 'react';

interface TouchOptimizationOptions {
  preventScroll?: boolean;
  debounceMs?: number;
  threshold?: number;
}

export const useTouchOptimization = (
  onTouch: (event: TouchEvent) => void,
  options: TouchOptimizationOptions = {}
) => {
  const lastTouchRef = useRef<number>(0);
  const touchStartRef = useRef<{ x: number; y: number } | null>(null);

  const handleTouchStart = useCallback((event: TouchEvent) => {
    const touch = event.touches[0];
    touchStartRef.current = { x: touch.clientX, y: touch.clientY };
    
    if (options.preventScroll) {
      event.preventDefault();
    }
  }, [options.preventScroll]);

  const handleTouchEnd = useCallback((event: TouchEvent) => {
    const now = Date.now();
    const debounceMs = options.debounceMs || 100;
    
    // Debounce rapid touches
    if (now - lastTouchRef.current < debounceMs) {
      return;
    }

    // Check if touch moved too much (might be a scroll)
    if (touchStartRef.current && event.changedTouches[0]) {
      const touch = event.changedTouches[0];
      const deltaX = Math.abs(touch.clientX - touchStartRef.current.x);
      const deltaY = Math.abs(touch.clientY - touchStartRef.current.y);
      const threshold = options.threshold || 10;

      if (deltaX > threshold || deltaY > threshold) {
        return; // Likely a scroll gesture
      }
    }

    lastTouchRef.current = now;
    onTouch(event);
  }, [onTouch, options.debounceMs, options.threshold]);

  return {
    onTouchStart: handleTouchStart,
    onTouchEnd: handleTouchEnd
  };
};
```

### Animation Performance
```typescript
// File: battlx/src/utils/animationOptimization.ts

export const createOptimizedAnimation = (
  element: HTMLElement,
  keyframes: Keyframe[],
  options: KeyframeAnimationOptions = {}
) => {
  // Use transform and opacity for better performance
  const optimizedKeyframes = keyframes.map(frame => ({
    ...frame,
    // Promote to composite layer
    willChange: 'transform, opacity',
    // Use transform instead of changing layout properties
    transform: frame.transform || 'translateZ(0)'
  }));

  const optimizedOptions = {
    ...options,
    // Use hardware acceleration
    composite: 'replace' as CompositeOperation,
    // Optimize for 60fps
    duration: options.duration || 300,
    easing: options.easing || 'cubic-bezier(0.4, 0, 0.2, 1)'
  };

  return element.animate(optimizedKeyframes, optimizedOptions);
};

export const useReducedMotion = () => {
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  
  return {
    shouldReduceMotion: prefersReducedMotion,
    getAnimationDuration: (defaultDuration: number) => 
      prefersReducedMotion ? 0 : defaultDuration,
    getAnimationConfig: (config: any) => 
      prefersReducedMotion ? { ...config, duration: 0 } : config
  };
};
```

## Bundle Optimization

### Webpack Configuration
```javascript
// File: battlx/webpack.config.js (additions)

const path = require('path');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');

module.exports = {
  // ... existing config
  
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        // Separate vendor bundle
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        // Separate pet system bundle
        petSystem: {
          test: /[\\/]src[\\/](components|stores|services)[\\/](pets|collection|mysterybox)/,
          name: 'pet-system',
          chunks: 'all',
          minSize: 20000,
        },
        // Common utilities
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true,
        },
      },
    },
    // Tree shaking
    usedExports: true,
    sideEffects: false,
  },

  // Code splitting for routes
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },

  plugins: [
    // Bundle analyzer for production builds
    process.env.ANALYZE && new BundleAnalyzerPlugin(),
  ].filter(Boolean),
};
```

## Acceptance Criteria
- [ ] Database queries optimized with proper indexing
- [ ] Caching strategy implemented with Redis
- [ ] Frontend components use virtualization for large lists
- [ ] Images optimized with lazy loading and format selection
- [ ] Mobile touch interactions optimized
- [ ] Bundle size reduced through code splitting
- [ ] Performance metrics meet targets (< 3s load time)

## Next Steps
1. Create deployment documentation
2. Set up monitoring and analytics
3. Implement A/B testing framework
4. Create user documentation

## Troubleshooting
- Monitor cache hit rates and adjust TTL values
- Profile database queries under load
- Test performance on low-end mobile devices
- Monitor bundle sizes after updates
- Use performance profiling tools to identify bottlenecks
