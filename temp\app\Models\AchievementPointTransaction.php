<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AchievementPointTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id',
        'amount',
        'type',
        'source',
        'source_id',
        'description'
    ];

    /**
     * Get the user that owns this transaction.
     */
    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }
    
    /**
     * Get the related source entity based on the source type.
     */
    public function sourceEntity()
    {
        if (!$this->source_id) {
            return null;
        }
        
        switch ($this->source) {
            case 'prize_unlock':
                return Prize::find($this->source_id);
                
            case 'mission_complete':
                return Mission::find($this->source_id);
                
            case 'level_up':
                return null; // Level doesn't have a dedicated model
                
            default:
                return null;
        }
    }
}
