<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PetTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $petTemplates = [
            // Shadow Category
            [
                'name' => 'Shadow Wolf',
                'category' => 'shadow',
                'rarity' => 'common',
                'description' => 'A mysterious wolf that emerges from the shadows, loyal to those who show it kindness.',
                'image_url' => '/images/pets/shadow/shadow_wolf.png',
                'animation_url' => '/images/pets/shadow/shadow_wolf_anim.gif',
                'coin_cost' => 1000,
                'gem_cost' => 10,
                'prize_tree_unlock_level' => 5,
                'mystery_box_unlocks' => json_encode(['common_shadow']),
                'collectible_reward_id' => 'shadow_essence',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/shadow/shadow_wolf_evo1.png',
                    '/images/pets/shadow/shadow_wolf_evo2.png',
                    '/images/pets/shadow/shadow_wolf_evo3.png',
                    '/images/pets/shadow/shadow_wolf_evo4.png'
                ]),
                'sort_order' => 1
            ],
            [
                'name' => 'Dark Raven',
                'category' => 'shadow',
                'rarity' => 'rare',
                'description' => 'An intelligent raven with eyes that gleam like obsidian, harboring ancient secrets.',
                'image_url' => '/images/pets/shadow/dark_raven.png',
                'animation_url' => '/images/pets/shadow/dark_raven_anim.gif',
                'coin_cost' => 5000,
                'gem_cost' => 50,
                'prize_tree_unlock_level' => 15,
                'mystery_box_unlocks' => json_encode(['rare_shadow']),
                'collectible_reward_id' => 'raven_feather',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/shadow/dark_raven_evo1.png',
                    '/images/pets/shadow/dark_raven_evo2.png',
                    '/images/pets/shadow/dark_raven_evo3.png',
                    '/images/pets/shadow/dark_raven_evo4.png'
                ]),
                'sort_order' => 2
            ],
            [
                'name' => 'Void Stalker',
                'category' => 'shadow',
                'rarity' => 'epic',
                'description' => 'A creature born from the void itself, capable of bending shadows to its will.',
                'image_url' => '/images/pets/shadow/void_stalker.png',
                'animation_url' => '/images/pets/shadow/void_stalker_anim.gif',
                'coin_cost' => 25000,
                'gem_cost' => 200,
                'prize_tree_unlock_level' => 30,
                'mystery_box_unlocks' => json_encode(['epic_shadow']),
                'collectible_reward_id' => 'void_crystal',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/shadow/void_stalker_evo1.png',
                    '/images/pets/shadow/void_stalker_evo2.png',
                    '/images/pets/shadow/void_stalker_evo3.png',
                    '/images/pets/shadow/void_stalker_evo4.png'
                ]),
                'sort_order' => 3
            ],

            // Undead Category
            [
                'name' => 'Skeleton Minion',
                'category' => 'undead',
                'rarity' => 'common',
                'description' => 'A loyal skeletal servant, animated by dark magic and bound to serve.',
                'image_url' => '/images/pets/undead/skeleton_minion.png',
                'animation_url' => '/images/pets/undead/skeleton_minion_anim.gif',
                'coin_cost' => 1000,
                'gem_cost' => 10,
                'prize_tree_unlock_level' => 5,
                'mystery_box_unlocks' => json_encode(['common_undead']),
                'collectible_reward_id' => 'bone_fragment',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/undead/skeleton_minion_evo1.png',
                    '/images/pets/undead/skeleton_minion_evo2.png',
                    '/images/pets/undead/skeleton_minion_evo3.png',
                    '/images/pets/undead/skeleton_minion_evo4.png'
                ]),
                'sort_order' => 6
            ],
            [
                'name' => 'Zombie Hound',
                'category' => 'undead',
                'rarity' => 'rare',
                'description' => 'A reanimated hound with an insatiable hunger and unwavering loyalty.',
                'image_url' => '/images/pets/undead/zombie_hound.png',
                'animation_url' => '/images/pets/undead/zombie_hound_anim.gif',
                'coin_cost' => 5000,
                'gem_cost' => 50,
                'prize_tree_unlock_level' => 15,
                'mystery_box_unlocks' => json_encode(['rare_undead']),
                'collectible_reward_id' => 'decay_potion',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/undead/zombie_hound_evo1.png',
                    '/images/pets/undead/zombie_hound_evo2.png',
                    '/images/pets/undead/zombie_hound_evo3.png',
                    '/images/pets/undead/zombie_hound_evo4.png'
                ]),
                'sort_order' => 7
            ],
            [
                'name' => 'Death Knight',
                'category' => 'undead',
                'rarity' => 'legendary',
                'description' => 'A fallen paladin risen as an undead champion of darkness.',
                'image_url' => '/images/pets/undead/death_knight.png',
                'animation_url' => '/images/pets/undead/death_knight_anim.gif',
                'coin_cost' => 100000,
                'gem_cost' => 800,
                'prize_tree_unlock_level' => 50,
                'mystery_box_unlocks' => json_encode(['legendary_undead']),
                'collectible_reward_id' => 'deaths_blade',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/undead/death_knight_evo1.png',
                    '/images/pets/undead/death_knight_evo2.png',
                    '/images/pets/undead/death_knight_evo3.png',
                    '/images/pets/undead/death_knight_evo4.png'
                ]),
                'sort_order' => 8
            ]
        ];

        foreach ($petTemplates as $template) {
            DB::table('pet_templates')->updateOrInsert(
                ['name' => $template['name']],
                array_merge($template, [
                    'created_at' => now(),
                    'updated_at' => now()
                ])
            );
        }
    }
}
