// Game class for handling the game loop and initialization
class Game {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.lastTime = 0;
        this.accumulator = 0;
        this.timeStep = 1000 / 60; // 60 FPS
        this.running = false;

        // Static properties
        Game.core = new GameCore(canvas);
        Game.deltaTime = 0;

        // Resize canvas to fit window
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());

        // Handle input
        canvas.addEventListener('mousedown', e => this.handleInput(e));
        canvas.addEventListener('touchstart', e => this.handleInput(e));
    }

    // Resize canvas to fit window
    resizeCanvas() {
        const container = this.canvas.parentElement;
        const containerWidth = container.clientWidth;
        const containerHeight = container.clientHeight;

        // Set canvas size
        this.canvas.width = containerWidth;
        this.canvas.height = containerHeight;

        // Update camera
        if (Game.core) {
            Game.core.camera.width = containerWidth;
            Game.core.camera.height = containerHeight;
        }
    }

    // Handle input
    handleInput(e) {
        // Handle level up UI input
        if (Game.core && Game.core.levelUpUI && Game.core.levelUpUI.visible) {
            const rect = this.canvas.getBoundingClientRect();
            const scaleX = this.canvas.width / rect.width;
            const scaleY = this.canvas.height / rect.height;

            let x, y;

            if (e.type === 'mousedown') {
                x = (e.clientX - rect.left) * scaleX;
                y = (e.clientY - rect.top) * scaleY;
            } else if (e.type === 'touchstart' && e.touches.length > 0) {
                x = (e.touches[0].clientX - rect.left) * scaleX;
                y = (e.touches[0].clientY - rect.top) * scaleY;
            } else {
                return;
            }

            Game.core.levelUpUI.handleInput(x, y, true);
        }
    }

    // Start the game
    async start() {
        if (this.running) return;

        this.running = true;

        // Initialize game
        await Game.core.init();

        // Start game loop
        this.lastTime = performance.now();
        requestAnimationFrame(time => this.gameLoop(time));
    }

    // Stop the game
    stop() {
        this.running = false;
    }

    // Game loop
    gameLoop(currentTime) {
        if (!this.running) return;

        // Calculate delta time
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        Game.deltaTime = deltaTime;

        // Always draw even when paused (to show UI)
        Game.core.draw();

        // Only update if not paused
        if (!Game.core.isPaused && !Game.core.isGameOver) {
            // Update with fixed time step
            this.accumulator += deltaTime;

            while (this.accumulator >= this.timeStep) {
                Game.core.update(this.timeStep);
                this.accumulator -= this.timeStep;
            }
        }

        // Continue loop
        requestAnimationFrame(time => this.gameLoop(time));
    }
}

// Attach to window object for global access
window.Game = Game;
