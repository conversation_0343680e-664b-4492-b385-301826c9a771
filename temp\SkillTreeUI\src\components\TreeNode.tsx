import { motion } from 'framer-motion';
import { <PERSON>, Award, Co<PERSON>, Gem, Lock, Check } from 'lucide-react';
import { Node } from '../types';

interface TreeNodeProps {
  node: Node;
  isExpanded: boolean;
  onClick: () => void;
}

const TreeNode = ({ node, isExpanded, onClick }: TreeNodeProps) => {
  const getIcon = () => {
    switch (node.type) {
      case 'nft':
        return <Gem className="w-5 h-5" />;
      case 'token':
        return <Coins className="w-5 h-5" />;
      case 'reward':
        return <Award className="w-5 h-5" />;
      default:
        return <Shield className="w-5 h-5" />;
    }
  };
  
  return (
    <motion.div
      className="absolute"
      style={{
        left: `${node.position.x}%`,
        top: `${node.position.y}%`,
        zIndex: isExpanded ? 50 : 10
      }}
      initial={{ scale: 0.9, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.3, delay: node.level * 0.1 }}
    >
      <motion.div
        className="relative"
        whileHover={{ scale: 1.1 }}
        onClick={onClick}
      >
        <motion.div 
          className={`h-14 w-14 rounded-full flex items-center justify-center cursor-pointer relative
            ${node.unlocked 
              ? 'bg-gradient-to-br from-brass-300 to-brass-500 border-3 border-brass-100 shadow-inner-glow' 
              : 'bg-abyss-200 border-2 border-brass-900'
            }
          `}
          animate={{
            boxShadow: node.unlocked 
              ? '0 0 15px rgba(155, 139, 108, 0.3)' 
              : '0 0 5px rgba(69, 61, 44, 0.3)'
          }}
        >
          {node.completed ? (
            <Check className="w-6 h-6 text-blood-500" />
          ) : node.unlocked ? (
            <div className="text-stone-100">{getIcon()}</div>
          ) : (
            <Lock className="w-5 h-5 text-brass-900" />
          )}
          
          {node.unlocked && !node.completed && (
            <motion.div
              className="absolute inset-0 rounded-full"
              animate={{
                boxShadow: ['0 0 0 0 rgba(155, 139, 108, 0)', '0 0 0 10px rgba(155, 139, 108, 0)'],
                scale: [1, 1.2, 1]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: 'loop'
              }}
            />
          )}
        </motion.div>
        
        <motion.div
          className="absolute left-1/2 top-full mt-2 bg-abyss-200/95 border border-brass-300/50 p-4 rounded-lg shadow-outer-glow backdrop-blur-sm"
          initial={{ opacity: 0, y: -10, scale: 0.9, translateX: '-50%' }}
          animate={{ 
            opacity: isExpanded ? 1 : 0, 
            y: isExpanded ? 0 : -10,
            scale: isExpanded ? 1 : 0.9,
            translateX: '-50%'
          }}
          transition={{ duration: 0.2 }}
          style={{
            visibility: isExpanded ? 'visible' : 'hidden',
            width: '200px',
            zIndex: 60
          }}
        >
          <h3 className="font-bold text-brass-300 mb-2">{node.title}</h3>
          <p className="text-stone-300 text-sm mb-2">{node.description}</p>
          {node.reward && (
            <div className="text-xs text-stone-400 border-t border-brass-300/20 pt-2 mt-2">
              <span className="text-brass-300 font-bold">Reward: </span>
              {node.reward}
            </div>
          )}
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default TreeNode;