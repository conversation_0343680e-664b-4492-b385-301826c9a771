# Slash Game Integration Guide: 03 - Frontend Component Structure

This document details the required frontend component structure and state management for integrating the Slash game into the React application, focusing on how it interacts with the existing `GameWrapper.tsx` and other components.

## 1. Integration with `GameWrapper.tsx`

The [`GameWrapper.tsx`](battlx/src/components/games/GameWrapper.tsx) component will serve as the primary container for the Slash game, similar to how it hosts the Rabbit and Tower games. The integration will involve:

*   **Game Module Registration:** The Slash game module needs to be registered in the `../../games/registry.ts` file, providing a function that `GameWrapper` can call to get a `GameInstance` for the 'slash' game ID.
*   **`GameInstance` Implementation:** A new file, [`battlx/src/slash_game/src/main.ts`](battlx/src/slash_game/src/main.ts), needs to be implemented. This file will export a function that, when called by `GameWrapper`, creates and returns an object conforming to the `GameInstance` interface. This object will be responsible for:
    *   Initializing the Slash game's core logic (e.g., creating an instance of the `Game` class from `js/src/components/game.js`).
    *   Receiving the `canvasId` and dimensions from `GameWrapper` and passing them to the Slash game's initialization.
    *   Receiving the `onGameOver` callback from `GameWrapper` and providing it to the Slash game's core logic so it can be called when the game ends.
    *   Exposing `load`, `start`, `destroy`, `playBgm`, and `pauseBgm` methods that delegate to the Slash game's internal functions.
    *   Orchestrating the loading of Slash game assets and reporting progress back through the `load` method's progress callback.

## 2. Slash Game Drawer Component (`SlashGameDrawer.tsx`)

The [`battlx/src/components/games/SlashGameDrawer.tsx`](battlx/src/components/games/SlashGameDrawer.tsx) file already exists, suggesting a placeholder for game-specific UI elements that are part of the React application and overlay the game canvas. This component can be used for:

*   Displaying game-specific information (e.g., current coin count during gameplay, wave number).
*   Providing game-specific controls (e.g., a pause button, a button to activate a special ability, if applicable).
*   Presenting the game-over screen UI, potentially displaying the final coin amount collected before `GameWrapper`'s generic game over screen appears.

The structure and content of this component will depend on the specific UI/UX requirements of the Slash game that are not handled by the game's internal rendering or `GameWrapper`'s generic game over screen.

## 3. Internal Slash Game Component Structure

The core logic of the Slash game resides within the `battlx/src/slash_game/js/src/components/` directory. The key components identified are:

*   [`game.js`](battlx/src/slash_game/js/src/components/game.js): Manages the game loop, canvas, and input. Delegates core logic to `GameCore`.
*   [`gameCore.js`](battlx/src/slash_game/js/src/components/gameCore.js): Contains the main game state, manages game objects (player, enemies, pickups, etc.), handles updates and drawing, and includes the logic for coin collection and game over detection.
*   [`player.js`](battlx/src/slash_game/js/src/components/player.js): Represents the player character, handling movement, stats, and interactions. Tracks collected coins (`playerOptions.coins`).
*   [`enemy.js`](battlx/src/slash_game/js/src/components/enemy.js): Represents enemy entities.
*   [`weapon.js`](battlx/src/slash_game/js/src/components/weapon.js): Handles weapon logic and projectile creation.
*   [`pickup.js`](battlx/src/slash_game/js/src/components/pickup.js): Manages collectible items like coins and gems.
*   [`stage.js`](battlx/src/slash_game/js/src/components/stage.js): Manages game progression and enemy spawning based on time or other criteria.
*   [`ui.js`](battlx/src/slash_game/js/src/components/ui.js): Likely contains classes for rendering in-game UI elements directly on the canvas (e.g., health bars, level up options).

The interaction between these components forms the core gameplay loop. The `Game` class drives the loop, calling `update` and `draw` methods on `GameCore`, which in turn manages and updates the various game objects.

## 4. State Management

State management for the Slash game integration involves both the React application and the game module itself:

*   **React State (`GameWrapper.tsx`)**: `GameWrapper` manages states like `isLoading`, `isGameOver`, and `finalScore`. The `onGameOver` callback from the game module will update `isGameOver` and `finalScore` in `GameWrapper`'s state.
*   **React State (User Store)**: The `useUserStore` from `../../store/user-store.ts` is used by `GameWrapper` to update the user's total `game_score` after a game session ends by calling `updateGameScore`.
*   **Game Module State (`GameCore.js`)**: The `GameCore` class manages the internal game state, including the player's coin count (`playerOptions.coins`), the list of active enemies, pickups, weapons, etc. This internal state is independent of the React state but is the source of the final score reported to `GameWrapper`.

## 5. Routing and Navigation

The routing and navigation to the Slash game screen will follow the pattern established for other games. This typically involves:

*   Defining a route in the React application's routing configuration that includes a parameter for the `gameId` (e.g., `/game/:gameId`).
*   Using the `useNavigate` hook from `react-router-dom` to navigate to the Slash game route with the appropriate `gameId` ('slash') when the user selects the Slash game from a game list or menu.
*   The component rendered by this route will likely be `GameWrapper`, which will receive 'slash' as the `gameId` prop.