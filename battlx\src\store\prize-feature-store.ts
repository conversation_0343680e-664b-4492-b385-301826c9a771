import { create } from "zustand";
import { UserPrizesResponse } from "@/types/PrizeTypes";

interface PrizeFeatureState {
  // Feature flags
  tapEnabled: boolean;
  slashEffectsEnabled: boolean;
  comboEnabled: boolean;
  frenzyEnabled: boolean;
  
  // Prize IDs for each feature
  tapPrizeId: number;
  slashEffectsPrizeId: number;
  comboPrizeId: number;
  frenzyPrizeId: number;
  
  // Update methods
  updateFromUserPrizes: (userPrizes: UserPrizesResponse) => void;
  enableFeature: (feature: string, enabled: boolean) => void;
}

export const usePrizeFeatureStore = create<PrizeFeatureState>((set) => ({
  // Default all features to disabled
  tapEnabled: false,
  slashEffectsEnabled: false,
  comboEnabled: false,
  frenzyEnabled: false,
  
  // Prize IDs for each feature (these should match the IDs in the seeder)
  tapPrizeId: 1, // Slasher Initiate
  slashEffectsPrizeId: 2, // Slash Effect Master
  comboPrizeId: 3, // Combo Master
  frenzyPrizeId: 4, // Frenzy Master
  
  // Update feature flags based on user's unlocked prizes
  updateFromUserPrizes: (userPrizes: UserPrizesResponse) => {
    const unlockedPrizes = userPrizes.unlocked_prizes || [];
    
    set((state) => ({
      tapEnabled: unlockedPrizes.includes(state.tapPrizeId),
      slashEffectsEnabled: unlockedPrizes.includes(state.slashEffectsPrizeId),
      comboEnabled: unlockedPrizes.includes(state.comboPrizeId),
      frenzyEnabled: unlockedPrizes.includes(state.frenzyPrizeId)
    }));
  },
  
  // Enable or disable a specific feature (for testing or admin purposes)
  enableFeature: (feature: string, enabled: boolean) => {
    switch (feature) {
      case 'tap':
        set({ tapEnabled: enabled });
        break;
      case 'slashEffects':
        set({ slashEffectsEnabled: enabled });
        break;
      case 'combo':
        set({ comboEnabled: enabled });
        break;
      case 'frenzy':
        set({ frenzyEnabled: enabled });
        break;
      default:
        console.warn(`Unknown feature: ${feature}`);
    }
  }
}));
