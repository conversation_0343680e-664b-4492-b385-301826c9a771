import { cn, compactNumber } from "@/lib/utils";
import { BattlxIcon } from "@/components/icons/BattlxIcon";
import { useUserStore } from "@/store/user-store";

export default function UserGameDetails({
  className,
  ...props
}: React.HtmlHTMLAttributes<HTMLDivElement>) {
  const user = useUserStore();
  return (
    <div
      className={cn("flex items-stretch justify-between gap-2", className)}
      {...props}
    >
      <div className="flex flex-col items-center justify-center flex-1 p-2 select-none bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)]">
        <p className="mb-1 text-xs text-center text-[#B3B3B3]/80">Earn per tap</p>
        <div className="inline-flex items-center space-x-1.5 text-[#9B8B6C] font-medium">
          <BattlxIcon icon="coins" className="w-5 h-5 opacity-80 text-[#9B8B6C]" />{" "}
          <span className="text-sm">+{user?.earn_per_tap}</span>
        </div>
      </div>
      <div className="flex flex-col items-center justify-center flex-1 p-2 select-none bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)]">
        <p className="mb-1 text-xs text-center text-[#B3B3B3]/80">Coins to level up</p>
        {user.level && (
          <div className="inline-flex items-center space-x-1.5 font-medium">
            <BattlxIcon icon="coins" className="w-5 h-5 opacity-80 text-[#9B8B6C]" />
            <span className="text-sm text-[#9B8B6C]">
              {compactNumber(user.level.to_balance)}
            </span>
          </div>
        )}
      </div>
      <div className="flex flex-col items-center justify-center flex-1 p-2 select-none bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)]">
        <p className="mb-1 text-xs text-center text-[#B3B3B3]/80">Profit per hour</p>
        <div className="inline-flex items-center space-x-1.5 text-[#9B8B6C] font-medium">
          <BattlxIcon icon="coins" className="w-5 h-5 opacity-80 text-[#9B8B6C]" />
          <span className="text-sm">
            +{compactNumber(user.production_per_hour)}
          </span>
        </div>
      </div>
    </div>
  );
}
