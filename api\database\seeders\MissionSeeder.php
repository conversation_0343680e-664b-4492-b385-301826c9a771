<?php

namespace Database\Seeders;

use App\Models\Mission;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $missions = [
            ['name' => 'Lily Pad Leap', 'mission_type_id' => 1, 'image' => '/images/missions/1.png', 'required_user_level' => 1, 'required_friends_invitation' => 0],
            ['name' => 'Fly Catch Frenzy', 'mission_type_id' => 1, 'image' => '/images/missions/2.png', 'required_user_level' => 1, 'required_friends_invitation' => 0],
            ['name' => 'Swamp Sprint', 'mission_type_id' => 1, 'image' => '/images/missions/3.png', 'required_user_level' => 1, 'required_friends_invitation' => 0],
            ['name' => 'Croak Quest', 'mission_type_id' => 1, 'image' => '/images/missions/4.png', 'required_user_level' => 1, 'required_friends_invitation' => 0],
            // Grave Yield Missions (Boss-locked, levels 2-5)
            [
                'name' => 'Soul Collector',
                'mission_type_id' => 2,
                'image' => '/images/missions/5.png',
                'required_user_level' => 2,
                'required_friends_invitation' => 0
            ],
            [
                'name' => 'Crypt Keeper',
                'mission_type_id' => 2,
                'image' => '/images/missions/6.png',
                'required_user_level' => 3,
                'required_friends_invitation' => 0
            ],
            [
                'name' => 'Tomb Raider',
                'mission_type_id' => 2,
                'image' => '/images/missions/7.png',
                'required_user_level' => 4,
                'required_friends_invitation' => 0
            ],
            [
                'name' => 'Necropolis Master',
                'mission_type_id' => 2,
                'image' => '/images/missions/8.png',
                'required_user_level' => 5,
                'required_friends_invitation' => 0
            ],
            // Necrotic Flow Missions (Referral-based)
            [
                'name' => 'Graveborn Recruit',
                'mission_type_id' => 3,
                'image' => '/images/missions/10.png',
                'required_user_level' => 1,
                'required_friends_invitation' => 10
            ],
            [
                'name' => 'Shadowbound Footman',
                'mission_type_id' => 3,
                'image' => '/images/missions/11.png',
                'required_user_level' => 1,
                'required_friends_invitation' => 25
            ],
            [
                'name' => 'Cryptstalker Scout',
                'mission_type_id' => 3,
                'image' => '/images/missions/12.png',
                'required_user_level' => 1,
                'required_friends_invitation' => 50
            ],
            [
                'name' => 'Necromancer Familiar',
                'mission_type_id' => 3,
                'image' => '/images/missions/13.png',
                'required_user_level' => 1,
                'required_friends_invitation' => 75
            ],
            [
                'name' => 'Lich Apprentice',
                'mission_type_id' => 3,
                'image' => '/images/missions/14.png',
                'required_user_level' => 1,
                'required_friends_invitation' => 100
            ],
            [
                'name' => 'Archlich Guardian',
                'mission_type_id' => 3,
                'image' => '/images/missions/15.png',
                'required_user_level' => 1,
                'required_friends_invitation' => 200
            ],
            [
                'name' => 'Eternal Sovereign',
                'mission_type_id' => 3,
                'image' => '/images/missions/16.png',
                'required_user_level' => 1,
                'required_friends_invitation' => 300
            ],
            [
                'name' => 'Voidcaller Overlord',
                'mission_type_id' => 3,
                'image' => '/images/missions/17.png',
                'required_user_level' => 1,
                'required_friends_invitation' => 500
            ],
            [
                'name' => 'Phantom Conqueror',
                'mission_type_id' => 3,
                'image' => '/images/missions/18.png',
                'required_user_level' => 1,
                'required_friends_invitation' => 750
            ],
            // Dark Tithes Missions (Skin rewards)
            [
                'name' => 'Crown of Shadows',
                'mission_type_id' => 4,
                'image' => '/images/missions/19.png',
                'required_user_level' => 1,
                'required_mission_id' => 8  // ID of Necropolis Master
            ],
        ];

        // Check if the missions table is empty
        if (Mission::count() == 0) {
            // Remove 'id' from $missions and use create() instead of updateOrCreate()
            foreach ($missions as $mission) {
                \App\Models\Mission::create($mission);
            }
        }

        Mission::all()->each(function ($mission) {
            if ($mission->mission_type_id === 1) {
                // Regular missions (Eternal Harvest) - Upgradeable
                $mission->levels()->createMany([
                    ['level' => 1, 'cost' => 100, 'production_per_hour' => 10],
                    ['level' => 2, 'cost' => 200, 'production_per_hour' => 20],
                    ['level' => 3, 'cost' => 300, 'production_per_hour' => 30],
                    ['level' => 4, 'cost' => 400, 'production_per_hour' => 40],
                    ['level' => 5, 'cost' => 500, 'production_per_hour' => 50],
                ]);
            } else if ($mission->mission_type_id === 2) {
                // Grave Yield missions - One-time rewards
                $baseReward = match($mission->required_user_level) {
                    2 => 2500,   // Second boss
                    3 => 5000,   // Third boss
                    4 => 10000,  // Fourth boss
                    5 => 25000,  // Final boss
                    default => 2500
                };
                $mission->levels()->create([
                    'level' => 1,
                    'cost' => 0, // No cost for one-time rewards
                    'production_per_hour' => $baseReward
                ]);
            } else if ($mission->mission_type_id === 4) {
                // Dark Tithes missions - Skin rewards
                $mission->levels()->create([
                    'level' => 1,
                    'cost' => 0,
                    'production_per_hour' => 0  // No monetary reward for skins
                ]);
            } else {
                // Necrotic Flow missions - Referral rewards
                $baseReward = match($mission->required_friends_invitation) {
                    10 => 10000,      // Graveborn Recruit
                    25 => 50000,      // Shadowbound Footman
                    50 => 100000,     // Cryptstalker Scout
                    75 => 250000,     // Necromancer Familiar
                    100 => 500000,    // Lich Apprentice
                    200 => 1000000,   // Archlich Guardian
                    300 => 2000000,   // Eternal Sovereign
                    500 => 5000000,   // Voidcaller Overlord
                    750 => 10000000,  // Phantom Conqueror
                    default => 10000
                };
                
                $mission->levels()->create([
                    'level' => 1,
                    'cost' => 0, // No cost for one-time rewards
                    'production_per_hour' => $baseReward
                ]);
            }
        });
    }
}
