<?php

use App\Models\Level;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Create the 'telegram_users' table
        Schema::create('telegram_users', function (Blueprint $table) {
            $table->id();  // Creates BIGINT primary key in PostgreSQL
            $table->bigInteger('telegram_id')->unique();
            $table->string('first_name');
            $table->string('last_name')->nullable();
            $table->string('username')->nullable();
            $table->string('ton_wallet')->nullable();  // Collation will be added via raw SQL
            $table->integer('balance')->default(0);
            $table->integer('earn_per_tap')->default(1);
            $table->integer('available_energy')->default(500);
            $table->integer('max_energy')->default(500);
            $table->integer('multi_tap_level')->default(1);
            $table->integer('energy_limit_level')->default(1);
            $table->boolean('booster_pack_2x')->default(false);
            $table->boolean('booster_pack_3x')->default(false);
            $table->boolean('booster_pack_7x')->default(false);
            $table->timestamp('booster_pack_active_until')->nullable();
            $table->integer('login_streak')->default(0);
            $table->integer('daily_booster_uses')->default(0);
            $table->timestamp('last_daily_booster_use')->nullable();
            $table->integer('production_per_hour')->default(0);
            $table->bigInteger('referred_by')->nullable();
            $table->bigInteger('level_id')->default(1);
            $table->rememberToken();
            $table->dateTime('last_tap_date')->nullable();
            $table->dateTime('last_login_date')->nullable();
            $table->timestamps();

            // Add foreign keys
            $table->foreign('referred_by')->references('id')->on('telegram_users')->onDelete('set null');
            $table->foreign('level_id')->references('id')->on('levels')->onDelete('cascade');
        });

        // Add CHECK constraints using raw SQL
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_telegram_id_non_negative CHECK (telegram_id >= 0)');
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_balance_non_negative CHECK (balance >= 0)');
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_earn_per_tap_positive CHECK (earn_per_tap >= 1)');
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_available_energy_non_negative CHECK (available_energy >= 0)');
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_max_energy_non_negative CHECK (max_energy >= 0)');
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_multi_tap_level_positive CHECK (multi_tap_level >= 1)');
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_energy_limit_level_positive CHECK (energy_limit_level >= 1)');
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_login_streak_non_negative CHECK (login_streak >= 0)');
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_daily_booster_uses_non_negative CHECK (daily_booster_uses >= 0)');
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_production_per_hour_non_negative CHECK (production_per_hour >= 0)');
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_referred_by_non_negative CHECK (referred_by >= 0 OR referred_by IS NULL)');
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_level_id_positive CHECK (level_id >= 1)');

        // Add case-sensitive collation for 'ton_wallet'
        DB::statement("ALTER TABLE telegram_users ALTER COLUMN ton_wallet TYPE VARCHAR(255) COLLATE \"C\"");
    }

    public function down()
    {
        // Drop CHECK constraints
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_telegram_id_non_negative');
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_balance_non_negative');
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_earn_per_tap_positive');
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_available_energy_non_negative');
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_max_energy_non_negative');
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_multi_tap_level_positive');
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_energy_limit_level_positive');
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_login_streak_non_negative');
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_daily_booster_uses_non_negative');
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_production_per_hour_non_negative');
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_referred_by_non_negative');
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_level_id_positive');

        // Drop the table
        Schema::dropIfExists('telegram_users');
    }
};