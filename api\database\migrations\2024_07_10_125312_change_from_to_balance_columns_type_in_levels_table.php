<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop existing CHECK constraints if they exist
        DB::statement('ALTER TABLE levels DROP CONSTRAINT IF EXISTS chk_from_balance_non_negative');
        DB::statement('ALTER TABLE levels DROP CONSTRAINT IF EXISTS chk_to_balance_non_negative');

        // Modify the column types to BIGINT using raw SQL
        DB::statement('ALTER TABLE levels ALTER COLUMN from_balance TYPE BIGINT');
        DB::statement('ALTER TABLE levels ALTER COLUMN to_balance TYPE BIGINT');

        // Add CHECK constraints using raw SQL
        DB::statement('ALTER TABLE levels ADD CONSTRAINT chk_from_balance_non_negative CHECK (from_balance >= 0)');
        DB::statement('ALTER TABLE levels ADD CONSTRAINT chk_to_balance_non_negative CHECK (to_balance >= 0)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the CHECK constraints
        DB::statement('ALTER TABLE levels DROP CONSTRAINT IF EXISTS chk_from_balance_non_negative');
        DB::statement('ALTER TABLE levels DROP CONSTRAINT IF EXISTS chk_to_balance_non_negative');

        // Revert the column types to INTEGER using raw SQL
        DB::statement('ALTER TABLE levels ALTER COLUMN from_balance TYPE INTEGER');
        DB::statement('ALTER TABLE levels ALTER COLUMN to_balance TYPE INTEGER');
    }
};