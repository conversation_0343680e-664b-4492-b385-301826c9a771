<?php

namespace App\Services;

use App\Models\Pet;
use App\Models\TelegramUser;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    /**
     * Send pet attention notifications to users
     */
    public function sendPetAttentionNotifications(): array
    {
        $notifications = [];
        $sent = 0;
        $errors = 0;

        // Get pets that need attention (happiness < 30)
        $petsNeedingAttention = Pet::with(['user', 'template'])
            ->where('happiness', '<', 30)
            ->get();

        foreach ($petsNeedingAttention as $pet) {
            try {
                $notification = $this->createPetAttentionNotification($pet);
                
                // Here you would integrate with your notification system
                // For now, we'll just log the notification
                $this->logNotification($notification);
                
                $notifications[] = $notification;
                $sent++;
                
            } catch (\Exception $e) {
                $errors++;
                Log::error('Failed to send pet attention notification', [
                    'pet_id' => $pet->id,
                    'user_id' => $pet->telegram_user_id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'notifications_sent' => $sent,
            'errors' => $errors,
            'notifications' => $notifications
        ];
    }

    /**
     * Send collection completion notifications
     */
    public function sendCollectionCompletionNotifications(): array
    {
        $notifications = [];
        $sent = 0;

        // This would be called when collections are completed
        // Implementation depends on how you want to track and send these notifications
        
        return [
            'notifications_sent' => $sent,
            'notifications' => $notifications
        ];
    }

    /**
     * Send mystery box unlock notifications
     */
    public function sendMysteryBoxUnlockNotifications(TelegramUser $user, array $unlockedBoxes): void
    {
        foreach ($unlockedBoxes as $boxData) {
            $notification = [
                'user_id' => $user->id,
                'type' => 'mystery_box_unlocked',
                'title' => 'New Mystery Box Unlocked! 📦',
                'message' => "You've unlocked the {$boxData['display_name']}! Open it to discover rare collectibles.",
                'data' => [
                    'box_type' => $boxData['box_type'],
                    'display_name' => $boxData['display_name']
                ]
            ];

            $this->logNotification($notification);
        }
    }

    /**
     * Send collectible unlock notifications
     */
    public function sendCollectibleUnlockNotifications(TelegramUser $user, array $collectibleData): void
    {
        $notification = [
            'user_id' => $user->id,
            'type' => 'collectible_unlocked',
            'title' => 'New Collectible Obtained! ✨',
            'message' => "You've obtained {$collectibleData['template']->name}! Check your collection to see your progress.",
            'data' => [
                'collectible_id' => $collectibleData['collectible']->collectible_id,
                'name' => $collectibleData['template']->name,
                'rarity' => $collectibleData['template']->rarity
            ]
        ];

        $this->logNotification($notification);
    }

    /**
     * Send pet evolution notifications
     */
    public function sendPetEvolutionNotifications(TelegramUser $user, Pet $pet): void
    {
        $notification = [
            'user_id' => $user->id,
            'type' => 'pet_evolved',
            'title' => 'Pet Evolution! 🌟',
            'message' => "Your {$pet->display_name} has evolved to stage {$pet->evolution_stage}! They're now stronger and happier.",
            'data' => [
                'pet_id' => $pet->id,
                'pet_name' => $pet->display_name,
                'evolution_stage' => $pet->evolution_stage
            ]
        ];

        $this->logNotification($notification);
    }

    /**
     * Send daily happiness decay warnings
     */
    public function sendHappinessDecayWarnings(): array
    {
        $notifications = [];
        $sent = 0;

        // Get pets with happiness between 30-50 (warning zone)
        $petsInWarningZone = Pet::with(['user', 'template'])
            ->whereBetween('happiness', [30, 50])
            ->get();

        foreach ($petsInWarningZone as $pet) {
            $notification = [
                'user_id' => $pet->telegram_user_id,
                'type' => 'happiness_warning',
                'title' => 'Pet Happiness Warning ⚠️',
                'message' => "Your {$pet->display_name} is getting sad. Interact with them soon to keep them happy!",
                'data' => [
                    'pet_id' => $pet->id,
                    'pet_name' => $pet->display_name,
                    'happiness' => $pet->happiness
                ]
            ];

            $this->logNotification($notification);
            $notifications[] = $notification;
            $sent++;
        }

        return [
            'warnings_sent' => $sent,
            'notifications' => $notifications
        ];
    }

    // Private helper methods

    private function createPetAttentionNotification(Pet $pet): array
    {
        $messages = [
            "Your {$pet->template->name} {$pet->display_name} is feeling lonely! 🐾",
            "{$pet->display_name} needs some love and attention! ❤️",
            "Your pet {$pet->display_name} is waiting for you! 🎮",
            "{$pet->display_name} misses you! Come play together! 🎯",
            "Time to check on {$pet->display_name}! They need your care! 🤗"
        ];

        return [
            'user_id' => $pet->telegram_user_id,
            'type' => 'pet_needs_attention',
            'title' => 'Pet Needs Attention! 🐾',
            'message' => $messages[array_rand($messages)],
            'data' => [
                'pet_id' => $pet->id,
                'pet_name' => $pet->display_name,
                'happiness' => $pet->happiness,
                'last_interaction' => $this->getLastInteractionTime($pet)
            ]
        ];
    }

    private function getLastInteractionTime(Pet $pet): ?string
    {
        $lastInteraction = $pet->interactions()
            ->latest('interaction_time')
            ->first();

        return $lastInteraction ? $lastInteraction->interaction_time->toISOString() : null;
    }

    private function logNotification(array $notification): void
    {
        Log::info('Notification created', [
            'type' => $notification['type'],
            'user_id' => $notification['user_id'],
            'title' => $notification['title'],
            'message' => $notification['message']
        ]);

        // Here you would integrate with your actual notification system
        // Examples:
        // - Send push notification via Firebase
        // - Send Telegram bot message
        // - Store in notifications table
        // - Send email notification
        // - Add to in-app notification queue
    }

    /**
     * Get notification preferences for user
     */
    public function getUserNotificationPreferences(TelegramUser $user): array
    {
        // This would return user's notification preferences
        // For now, return default preferences
        return [
            'pet_attention' => true,
            'pet_evolution' => true,
            'collectible_unlocked' => true,
            'mystery_box_unlocked' => true,
            'collection_completed' => true,
            'happiness_warnings' => true,
            'daily_reminders' => true
        ];
    }

    /**
     * Update notification preferences for user
     */
    public function updateUserNotificationPreferences(TelegramUser $user, array $preferences): bool
    {
        // This would update user's notification preferences in database
        // For now, just log the update
        Log::info('Notification preferences updated', [
            'user_id' => $user->id,
            'preferences' => $preferences
        ]);

        return true;
    }
}
