import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import PrizeTree from './components/PrizeTree';
import BackgroundEffect from './components/BackgroundEffect';

function App() {
  return (
    <div className="min-h-screen bg-abyss-300 text-stone-300 flex flex-col relative overflow-hidden">
      <BackgroundEffect />
      <header className="py-8 px-4 md:px-8 z-10 relative">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-brass-300 tracking-wider mb-2">
            Path to Glory
          </h1>
          <div className="h-1 w-32 mx-auto bg-gradient-to-r from-transparent via-brass-300 to-transparent mb-4" />
          <p className="text-stone-300 mt-2 max-w-2xl mx-auto text-lg">
            Complete challenges across the realms to claim the ultimate prize
          </p>
        </div>
      </header>
      
      <main className="flex-1 flex items-center justify-center p-4 md:p-8 z-10 relative">
        <PrizeTree />
      </main>
      
      <footer className="py-6 text-center text-stone-500 text-sm z-10 relative border-t border-brass-300/20">
        <p>© 2025 Realm of Champions</p>
      </footer>
      
      <ToastContainer
        position="bottom-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="dark"
      />
    </div>
  );
}

export default App;