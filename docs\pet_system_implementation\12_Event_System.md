# Event System Implementation

## Overview
This document covers the implementation of an event-driven system for the Pet System, including events, listeners, and observers for tracking pet actions and triggering automated responses.

## Implementation Time: 2-3 days
## Complexity: Medium-High
## Dependencies: Pet services and models

## Pet Events

### PetPurchased Event
```php
<?php
// File: api/app/Events/PetPurchased.php

namespace App\Events;

use App\Models\Pet;
use App\Models\TelegramUser;
use App\Models\PetTemplate;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PetPurchased
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Pet $pet;
    public TelegramUser $user;
    public PetTemplate $petTemplate;
    public string $purchaseMethod;
    public int $cost;
    public array $unlockedRewards;

    public function __construct(
        Pet $pet,
        TelegramUser $user,
        PetTemplate $petTemplate,
        string $purchaseMethod,
        int $cost,
        array $unlockedRewards = []
    ) {
        $this->pet = $pet;
        $this->user = $user;
        $this->petTemplate = $petTemplate;
        $this->purchaseMethod = $purchaseMethod;
        $this->cost = $cost;
        $this->unlockedRewards = $unlockedRewards;
    }
}
```

### PetInteracted Event
```php
<?php
// File: api/app/Events/PetInteracted.php

namespace App\Events;

use App\Models\Pet;
use App\Models\TelegramUser;
use App\Models\PetInteraction;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PetInteracted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Pet $pet;
    public TelegramUser $user;
    public PetInteraction $interaction;
    public array $rewards;
    public bool $leveledUp;
    public bool $evolved;

    public function __construct(
        Pet $pet,
        TelegramUser $user,
        PetInteraction $interaction,
        array $rewards,
        bool $leveledUp = false,
        bool $evolved = false
    ) {
        $this->pet = $pet;
        $this->user = $user;
        $this->interaction = $interaction;
        $this->rewards = $rewards;
        $this->leveledUp = $leveledUp;
        $this->evolved = $evolved;
    }
}
```

### PetEvolved Event
```php
<?php
// File: api/app/Events/PetEvolved.php

namespace App\Events;

use App\Models\Pet;
use App\Models\TelegramUser;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PetEvolved
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Pet $pet;
    public TelegramUser $user;
    public int $previousStage;
    public int $newStage;
    public string $newImage;

    public function __construct(
        Pet $pet,
        TelegramUser $user,
        int $previousStage,
        int $newStage,
        string $newImage
    ) {
        $this->pet = $pet;
        $this->user = $user;
        $this->previousStage = $previousStage;
        $this->newStage = $newStage;
        $this->newImage = $newImage;
    }
}
```

### PetHappinessChanged Event
```php
<?php
// File: api/app/Events/PetHappinessChanged.php

namespace App\Events;

use App\Models\Pet;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PetHappinessChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Pet $pet;
    public int $previousHappiness;
    public int $newHappiness;
    public int $change;
    public string $reason;

    public function __construct(
        Pet $pet,
        int $previousHappiness,
        int $newHappiness,
        string $reason
    ) {
        $this->pet = $pet;
        $this->previousHappiness = $previousHappiness;
        $this->newHappiness = $newHappiness;
        $this->change = $newHappiness - $previousHappiness;
        $this->reason = $reason;
    }
}
```

## Collection Events

### CollectibleUnlocked Event
```php
<?php
// File: api/app/Events/CollectibleUnlocked.php

namespace App\Events;

use App\Models\Collectible;
use App\Models\CollectibleTemplate;
use App\Models\TelegramUser;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CollectibleUnlocked
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Collectible $collectible;
    public CollectibleTemplate $collectibleTemplate;
    public TelegramUser $user;
    public string $unlockSource;
    public ?string $sourceReference;

    public function __construct(
        Collectible $collectible,
        CollectibleTemplate $collectibleTemplate,
        TelegramUser $user,
        string $unlockSource,
        ?string $sourceReference = null
    ) {
        $this->collectible = $collectible;
        $this->collectibleTemplate = $collectibleTemplate;
        $this->user = $user;
        $this->unlockSource = $unlockSource;
        $this->sourceReference = $sourceReference;
    }
}
```

### CollectionSetCompleted Event
```php
<?php
// File: api/app/Events/CollectionSetCompleted.php

namespace App\Events;

use App\Models\CollectionSet;
use App\Models\TelegramUser;
use App\Models\UserCollectionProgress;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CollectionSetCompleted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public CollectionSet $collectionSet;
    public TelegramUser $user;
    public UserCollectionProgress $progress;
    public array $completionRewards;

    public function __construct(
        CollectionSet $collectionSet,
        TelegramUser $user,
        UserCollectionProgress $progress,
        array $completionRewards
    ) {
        $this->collectionSet = $collectionSet;
        $this->user = $user;
        $this->progress = $progress;
        $this->completionRewards = $completionRewards;
    }
}
```

## Mystery Box Events

### MysteryBoxOpened Event
```php
<?php
// File: api/app/Events/MysteryBoxOpened.php

namespace App\Events;

use App\Models\MysteryBoxOpening;
use App\Models\MysteryBoxType;
use App\Models\TelegramUser;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MysteryBoxOpened
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public MysteryBoxOpening $opening;
    public MysteryBoxType $mysteryBoxType;
    public TelegramUser $user;
    public array $rewards;
    public bool $containedRareItem;

    public function __construct(
        MysteryBoxOpening $opening,
        MysteryBoxType $mysteryBoxType,
        TelegramUser $user,
        array $rewards,
        bool $containedRareItem
    ) {
        $this->opening = $opening;
        $this->mysteryBoxType = $mysteryBoxType;
        $this->user = $user;
        $this->rewards = $rewards;
        $this->containedRareItem = $containedRareItem;
    }
}
```

## Event Listeners

### PetPurchasedListener
```php
<?php
// File: api/app/Listeners/PetPurchasedListener.php

namespace App\Listeners;

use App\Events\PetPurchased;
use App\Services\AchievementPointService;
use App\Services\NotificationService;
use App\Services\AnalyticsService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PetPurchasedListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected AchievementPointService $achievementPointService;
    protected NotificationService $notificationService;
    protected AnalyticsService $analyticsService;

    public function __construct(
        AchievementPointService $achievementPointService,
        NotificationService $notificationService,
        AnalyticsService $analyticsService
    ) {
        $this->achievementPointService = $achievementPointService;
        $this->notificationService = $notificationService;
        $this->analyticsService = $analyticsService;
    }

    public function handle(PetPurchased $event): void
    {
        try {
            // Award achievement points based on pet rarity
            $points = $this->calculateAchievementPoints($event->petTemplate->rarity);
            $this->achievementPointService->awardPoints(
                $event->user->id,
                $points,
                'pet_purchase',
                $event->pet->id,
                "Purchased {$event->petTemplate->name}"
            );

            // Send congratulations notification
            $this->sendPurchaseNotification($event);

            // Track analytics
            $this->trackPurchaseAnalytics($event);

            // Check for purchase milestones
            $this->checkPurchaseMilestones($event);

            Log::info('Pet purchase processed successfully', [
                'user_id' => $event->user->id,
                'pet_id' => $event->pet->id,
                'pet_name' => $event->petTemplate->name
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process pet purchase event', [
                'user_id' => $event->user->id,
                'pet_id' => $event->pet->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function calculateAchievementPoints(string $rarity): int
    {
        return match($rarity) {
            'common' => 5,
            'rare' => 10,
            'epic' => 25,
            'legendary' => 50,
            'mythic' => 100,
            default => 5
        };
    }

    private function sendPurchaseNotification(PetPurchased $event): void
    {
        $message = "🎉 Congratulations! You've acquired {$event->petTemplate->name}!";
        
        if (!empty($event->unlockedRewards)) {
            $message .= "\n\n🎁 Bonus rewards unlocked:";
            foreach ($event->unlockedRewards as $reward) {
                $message .= "\n• " . $reward['name'];
            }
        }

        $this->notificationService->sendPetPurchaseNotification([
            'user_id' => $event->user->id,
            'pet_id' => $event->pet->id,
            'pet_name' => $event->petTemplate->name,
            'message' => $message
        ]);
    }

    private function trackPurchaseAnalytics(PetPurchased $event): void
    {
        $this->analyticsService->track('pet_purchased', [
            'user_id' => $event->user->id,
            'pet_template_id' => $event->petTemplate->id,
            'pet_name' => $event->petTemplate->name,
            'pet_category' => $event->petTemplate->category,
            'pet_rarity' => $event->petTemplate->rarity,
            'purchase_method' => $event->purchaseMethod,
            'cost' => $event->cost,
            'unlocked_rewards_count' => count($event->unlockedRewards)
        ]);
    }

    private function checkPurchaseMilestones(PetPurchased $event): void
    {
        $totalPets = $event->user->pets()->count();
        
        $milestones = [1, 5, 10, 25, 50, 100];
        
        if (in_array($totalPets, $milestones)) {
            $this->achievementPointService->awardPoints(
                $event->user->id,
                $totalPets * 5,
                'pet_collection_milestone',
                $totalPets,
                "Collected {$totalPets} pets"
            );

            $this->notificationService->sendMilestoneNotification([
                'user_id' => $event->user->id,
                'milestone_type' => 'pet_collection',
                'milestone_value' => $totalPets,
                'message' => "🏆 Amazing! You've collected {$totalPets} pets!"
            ]);
        }
    }
}
```

### PetInteractedListener
```php
<?php
// File: api/app/Listeners/PetInteractedListener.php

namespace App\Listeners;

use App\Events\PetInteracted;
use App\Services\AchievementPointService;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PetInteractedListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected AchievementPointService $achievementPointService;
    protected NotificationService $notificationService;

    public function __construct(
        AchievementPointService $achievementPointService,
        NotificationService $notificationService
    ) {
        $this->achievementPointService = $achievementPointService;
        $this->notificationService = $notificationService;
    }

    public function handle(PetInteracted $event): void
    {
        try {
            // Award achievement points for interaction
            $this->achievementPointService->awardPoints(
                $event->user->id,
                1,
                'pet_interaction',
                $event->pet->id,
                "Interacted with {$event->pet->display_name} ({$event->interaction->interaction_type})"
            );

            // Send special notifications for level up or evolution
            if ($event->leveledUp) {
                $this->sendLevelUpNotification($event);
            }

            if ($event->evolved) {
                $this->sendEvolutionNotification($event);
            }

            // Check for interaction milestones
            $this->checkInteractionMilestones($event);

        } catch (\Exception $e) {
            Log::error('Failed to process pet interaction event', [
                'user_id' => $event->user->id,
                'pet_id' => $event->pet->id,
                'interaction_type' => $event->interaction->interaction_type,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function sendLevelUpNotification(PetInteracted $event): void
    {
        $this->notificationService->sendPetLevelUpNotification([
            'user_id' => $event->user->id,
            'pet_id' => $event->pet->id,
            'pet_name' => $event->pet->display_name,
            'new_level' => $event->pet->level,
            'message' => "🎉 {$event->pet->display_name} reached level {$event->pet->level}!"
        ]);
    }

    private function sendEvolutionNotification(PetInteracted $event): void
    {
        $this->notificationService->sendPetEvolutionNotification([
            'user_id' => $event->user->id,
            'pet_id' => $event->pet->id,
            'pet_name' => $event->pet->display_name,
            'evolution_stage' => $event->pet->evolution_stage,
            'message' => "✨ {$event->pet->display_name} has evolved! Amazing!"
        ]);
    }

    private function checkInteractionMilestones(PetInteracted $event): void
    {
        $totalInteractions = $event->user->petInteractions()->count();
        
        $milestones = [10, 50, 100, 500, 1000];
        
        if (in_array($totalInteractions, $milestones)) {
            $this->achievementPointService->awardPoints(
                $event->user->id,
                $totalInteractions / 10,
                'interaction_milestone',
                $totalInteractions,
                "Performed {$totalInteractions} pet interactions"
            );
        }
    }
}
```

### CollectibleUnlockedListener
```php
<?php
// File: api/app/Listeners/CollectibleUnlockedListener.php

namespace App\Listeners;

use App\Events\CollectibleUnlocked;
use App\Services\AchievementPointService;
use App\Services\NotificationService;
use App\Services\CollectibleService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CollectibleUnlockedListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected AchievementPointService $achievementPointService;
    protected NotificationService $notificationService;
    protected CollectibleService $collectibleService;

    public function __construct(
        AchievementPointService $achievementPointService,
        NotificationService $notificationService,
        CollectibleService $collectibleService
    ) {
        $this->achievementPointService = $achievementPointService;
        $this->notificationService = $notificationService;
        $this->collectibleService = $collectibleService;
    }

    public function handle(CollectibleUnlocked $event): void
    {
        try {
            // Award achievement points based on rarity
            $points = $this->calculateAchievementPoints($event->collectibleTemplate->rarity);
            $this->achievementPointService->awardPoints(
                $event->user->id,
                $points,
                'collectible_unlock',
                $event->collectible->id,
                "Unlocked {$event->collectibleTemplate->name}"
            );

            // Send notification for rare collectibles
            if (in_array($event->collectibleTemplate->rarity, ['epic', 'legendary', 'mythic'])) {
                $this->sendRareCollectibleNotification($event);
            }

            // Check if this unlock completed a collection set
            $this->checkSetCompletion($event);

        } catch (\Exception $e) {
            Log::error('Failed to process collectible unlock event', [
                'user_id' => $event->user->id,
                'collectible_id' => $event->collectible->collectible_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function calculateAchievementPoints(string $rarity): int
    {
        return match($rarity) {
            'common' => 2,
            'rare' => 5,
            'epic' => 15,
            'legendary' => 30,
            'mythic' => 75,
            default => 2
        };
    }

    private function sendRareCollectibleNotification(CollectibleUnlocked $event): void
    {
        $rarityEmoji = match($event->collectibleTemplate->rarity) {
            'epic' => '💜',
            'legendary' => '🧡',
            'mythic' => '❤️',
            default => '✨'
        };

        $this->notificationService->sendCollectibleUnlockedNotification([
            'user_id' => $event->user->id,
            'collectible_id' => $event->collectible->collectible_id,
            'collectible_name' => $event->collectibleTemplate->name,
            'rarity' => $event->collectibleTemplate->rarity,
            'message' => "{$rarityEmoji} You've unlocked a {$event->collectibleTemplate->rarity} collectible: {$event->collectibleTemplate->name}!"
        ]);
    }

    private function checkSetCompletion(CollectibleUnlocked $event): void
    {
        $setProgress = $this->collectibleService->getSetProgress(
            $event->user,
            $event->collectibleTemplate->collection_set_id
        );

        if ($setProgress['is_completed'] && !$setProgress['rewards_claimed']) {
            // Collection set just completed!
            $this->notificationService->sendCollectionSetCompletedNotification([
                'user_id' => $event->user->id,
                'set_id' => $event->collectibleTemplate->collection_set_id,
                'set_name' => $event->collectibleTemplate->collectionSet->name,
                'message' => "🏆 Congratulations! You've completed the {$event->collectibleTemplate->collectionSet->name} collection!"
            ]);
        }
    }
}
```

## Event Service Provider

### EventServiceProvider Updates
```php
<?php
// File: api/app/Providers/EventServiceProvider.php

namespace App\Providers;

use App\Events\PetPurchased;
use App\Events\PetInteracted;
use App\Events\PetEvolved;
use App\Events\PetHappinessChanged;
use App\Events\CollectibleUnlocked;
use App\Events\CollectionSetCompleted;
use App\Events\MysteryBoxOpened;
use App\Listeners\PetPurchasedListener;
use App\Listeners\PetInteractedListener;
use App\Listeners\PetEvolvedListener;
use App\Listeners\PetHappinessChangedListener;
use App\Listeners\CollectibleUnlockedListener;
use App\Listeners\CollectionSetCompletedListener;
use App\Listeners\MysteryBoxOpenedListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        // Existing events...
        
        // Pet System Events
        PetPurchased::class => [
            PetPurchasedListener::class,
        ],
        
        PetInteracted::class => [
            PetInteractedListener::class,
        ],
        
        PetEvolved::class => [
            PetEvolvedListener::class,
        ],
        
        PetHappinessChanged::class => [
            PetHappinessChangedListener::class,
        ],
        
        CollectibleUnlocked::class => [
            CollectibleUnlockedListener::class,
        ],
        
        CollectionSetCompleted::class => [
            CollectionSetCompletedListener::class,
        ],
        
        MysteryBoxOpened::class => [
            MysteryBoxOpenedListener::class,
        ],
    ];

    public function boot(): void
    {
        parent::boot();
    }
}
```

## Model Observers

### PetObserver
```php
<?php
// File: api/app/Observers/PetObserver.php

namespace App\Observers;

use App\Models\Pet;
use App\Events\PetEvolved;
use App\Events\PetHappinessChanged;

class PetObserver
{
    public function updated(Pet $pet): void
    {
        // Check if pet evolved
        if ($pet->isDirty('evolution_stage')) {
            $previousStage = $pet->getOriginal('evolution_stage');
            $newStage = $pet->evolution_stage;
            
            if ($newStage > $previousStage) {
                event(new PetEvolved(
                    $pet,
                    $pet->user,
                    $previousStage,
                    $newStage,
                    $pet->current_image
                ));
            }
        }

        // Check if happiness changed
        if ($pet->isDirty('happiness')) {
            $previousHappiness = $pet->getOriginal('happiness');
            $newHappiness = $pet->happiness;
            
            event(new PetHappinessChanged(
                $pet,
                $previousHappiness,
                $newHappiness,
                'manual_update' // This could be more specific based on context
            ));
        }
    }
}
```

## Acceptance Criteria
- [ ] All pet system events defined
- [ ] Event listeners implemented and queued
- [ ] Model observers tracking changes
- [ ] Achievement points awarded correctly
- [ ] Notifications sent for important events
- [ ] Analytics tracking functional
- [ ] Milestone detection working

## Next Steps
1. Create admin interface controllers
2. Implement background task processing
3. Set up comprehensive error handling
4. Create event monitoring and debugging tools

## Troubleshooting
- Monitor queue processing for event listeners
- Check event firing in model updates
- Verify listener dependencies are injected correctly
- Test event handling under high load
- Ensure proper error handling in listeners
