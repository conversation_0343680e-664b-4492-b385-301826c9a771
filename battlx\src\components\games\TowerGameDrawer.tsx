import { useMemo } from 'react';
import { Button } from '../ui/button';
import Drawer from '../ui/drawer';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { BattlxIcon } from '@/components/icons/BattlxIcon';
import { useUserStore } from '@/store/user-store';
import { gameApi } from '@/lib/game-api';
import { UserType } from '@/types/UserType'; // Import UserType
import { ApiResponse } from '@/lib/game-api'; // Import ApiResponse if not already (assuming it's exported)

const UNLOCK_PRICE = 5000;

type TowerGameDrawerProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export default function TowerGameDrawer({ open, onOpenChange }: TowerGameDrawerProps) {
  const { balance, tower_game_unlocked } = useUserStore();

  const insufficientBalance = useMemo(() => {
    return balance < UNLOCK_PRICE;
  }, [balance]);

  // Define the expected response type for clarity
  type UnlockGameResponse = ApiResponse<{ user: UserType }>;

  const unlockMutation = useMutation<UnlockGameResponse, Error>({ // Add types for response and error
    mutationFn: () => gameApi.unlockGame('tower'), // Call generalized function with 'tower'
    onSuccess: (response) => { // 'response' is now correctly typed
      if (response.success && response.user) {
        toast.success('Tower Game unlocked successfully');
        // Update user state with the new user data
        useUserStore.setState((state) => ({
          ...state,
          ...response.user // Spread the updated user object
        }));
        onOpenChange(false); // Close the drawer
      } else {
         // Use optional chaining safely as 'response' is typed
        toast.error(response?.message || 'Failed to unlock game');
      }
    },
    onError: (error: Error) => { // Type the error parameter
       // Handle potential Axios error structure, but default to generic message
       let message = 'An error occurred while unlocking the game';
       if (error && typeof error === 'object' && 'response' in error) {
           const axiosError = error as any; // Type assertion for Axios structure
           message = axiosError.response?.data?.message || message;
       } else if (error instanceof Error) {
           message = error.message;
       }
       toast.error(message);
    },
  });

  // Don't show drawer if game is already unlocked
  if (tower_game_unlocked) return null;

  return (
    <Drawer 
      open={open}
      onOpenChange={onOpenChange}
    >
      <div className="!rounded-none [&_[data-drawer-content]]:!rounded-none">
        <img
          src="/game/thumbnails/tower.png"
          alt="Tower Game"
          className="object-contain h-32 mx-auto opacity-80 rounded-none [filter:sepia(50%)_saturate(50%)_brightness(80%)_contrast(120%)]"
        />
        <h2 className="mt-6 text-2xl font-medium text-center text-[#9B8B6C]">Tower Game</h2>
        <div className="flex flex-col mx-auto mt-4 w-fit">
          <p className="text-xs text-center text-[#B3B3B3]/80">Unlock Price</p>
        </div>

        <div className="flex items-center justify-center mx-auto mt-6 space-x-1 text-[#9B8B6C]">
          <BattlxIcon
            icon="coins"
            className="w-8 h-8 text-[#9B8B6C]"
          />
          <span className="font-bold">
            {UNLOCK_PRICE.toLocaleString()}
          </span>
        </div>

        {insufficientBalance && (
          <div className="flex items-center justify-center mt-4">
            <p className="text-sm text-[#B3B3B3]/80">
              Need {(UNLOCK_PRICE - balance).toLocaleString()} more coins
            </p>
          </div>
        )}

        <Button
          className="w-full mt-6 bg-[#1A1617] border border-[#B3B3B3]/20 text-[#9B8B6C] hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_15px_rgba(74,14,14,0.3)] disabled:opacity-50 disabled:cursor-not-allowed rounded-none"
          disabled={unlockMutation.isPending || insufficientBalance}
          onClick={() => unlockMutation.mutate()}
        >
          {unlockMutation.isPending && (
            <BattlxIcon icon="loading" className="w-6 h-6 mr-2 animate-spin" />
          )}
          {insufficientBalance ? "Insufficient Balance" : "Unlock Game"}
        </Button>
      </div>
    </Drawer>
  );
}
