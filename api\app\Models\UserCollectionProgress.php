<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserCollectionProgress extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id', 'set_id', 'collectibles_owned', 'total_collectibles',
        'completion_percentage', 'is_completed', 'completed_at',
        'rewards_claimed', 'rewards_claimed_at', 'owned_collectible_ids',
        'missing_collectible_ids'
    ];

    protected $casts = [
        'completion_percentage' => 'decimal:2',
        'is_completed' => 'boolean',
        'completed_at' => 'datetime',
        'rewards_claimed' => 'boolean',
        'rewards_claimed_at' => 'datetime',
        'owned_collectible_ids' => 'array',
        'missing_collectible_ids' => 'array',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function collectionSet(): BelongsTo
    {
        return $this->belongsTo(CollectionSet::class, 'set_id', 'set_id');
    }

    // Scopes
    public function scopeCompleted($query)
    {
        return $query->where('is_completed', true);
    }

    public function scopeIncomplete($query)
    {
        return $query->where('is_completed', false);
    }

    public function scopeRewardsClaimed($query)
    {
        return $query->where('rewards_claimed', true);
    }

    public function scopeRewardsUnclaimed($query)
    {
        return $query->where('is_completed', true)
                    ->where('rewards_claimed', false);
    }

    // Accessors
    public function getProgressStatusAttribute(): string
    {
        if ($this->is_completed && $this->rewards_claimed) {
            return 'completed_claimed';
        } elseif ($this->is_completed && !$this->rewards_claimed) {
            return 'completed_unclaimed';
        } elseif ($this->completion_percentage >= 75) {
            return 'nearly_complete';
        } elseif ($this->completion_percentage >= 50) {
            return 'halfway';
        } elseif ($this->completion_percentage >= 25) {
            return 'started';
        } else {
            return 'just_started';
        }
    }

    public function getRemainingCollectiblesAttribute(): int
    {
        return $this->total_collectibles - $this->collectibles_owned;
    }

    public function getDaysToCompleteAttribute(): ?int
    {
        if ($this->is_completed) {
            return $this->completed_at ? $this->created_at->diffInDays($this->completed_at) : null;
        }
        return null;
    }

    // Business Logic Methods
    public function updateProgress(): void
    {
        $collectionSet = $this->collectionSet;
        if (!$collectionSet) {
            return;
        }

        $ownedCollectibles = $this->user->collectibles()
            ->whereHas('template', function($q) use ($collectionSet) {
                $q->where('collection_set_id', $collectionSet->set_id);
            })
            ->pluck('collectible_id')
            ->toArray();

        $allCollectibles = $collectionSet->collectibleTemplates()
            ->pluck('collectible_id')
            ->toArray();

        $missingCollectibles = array_diff($allCollectibles, $ownedCollectibles);

        $this->collectibles_owned = count($ownedCollectibles);
        $this->total_collectibles = count($allCollectibles);
        $this->completion_percentage = $this->total_collectibles > 0 
            ? round(($this->collectibles_owned / $this->total_collectibles) * 100, 2)
            : 0;
        $this->owned_collectible_ids = $ownedCollectibles;
        $this->missing_collectible_ids = array_values($missingCollectibles);

        $wasCompleted = $this->is_completed;
        $this->is_completed = $this->collectibles_owned >= $collectionSet->required_for_completion;

        if ($this->is_completed && !$wasCompleted) {
            $this->completed_at = now();
        }

        $this->save();
    }

    public function claimRewards(): array
    {
        if (!$this->is_completed) {
            throw new \Exception('Collection not completed');
        }

        if ($this->rewards_claimed) {
            throw new \Exception('Rewards already claimed');
        }

        $rewards = $this->collectionSet->awardCompletionRewards($this->user);

        $this->rewards_claimed = true;
        $this->rewards_claimed_at = now();
        $this->save();

        return $rewards;
    }

    public function getDetailedProgress(): array
    {
        $collectionSet = $this->collectionSet;
        $ownedCollectibles = $this->user->collectibles()
            ->whereHas('template', function($q) use ($collectionSet) {
                $q->where('collection_set_id', $collectionSet->set_id);
            })
            ->with('template')
            ->get();

        $allCollectibles = $collectionSet->collectibleTemplates()
            ->orderBy('set_position')
            ->get();

        $progress = [];
        foreach ($allCollectibles as $template) {
            $owned = $ownedCollectibles->firstWhere('collectible_id', $template->collectible_id);
            $progress[] = [
                'collectible_id' => $template->collectible_id,
                'name' => $template->name,
                'rarity' => $template->rarity,
                'type' => $template->type,
                'image_url' => $template->image_url,
                'set_position' => $template->set_position,
                'is_owned' => $owned !== null,
                'obtained_at' => $owned ? $owned->obtained_at : null,
                'unlock_source' => $owned ? $owned->unlock_source : null
            ];
        }

        return [
            'set_info' => [
                'set_id' => $collectionSet->set_id,
                'name' => $collectionSet->name,
                'category' => $collectionSet->category,
                'description' => $collectionSet->description,
                'icon_url' => $collectionSet->icon_url
            ],
            'progress' => [
                'owned' => $this->collectibles_owned,
                'total' => $this->total_collectibles,
                'required' => $collectionSet->required_for_completion,
                'percentage' => $this->completion_percentage,
                'is_completed' => $this->is_completed,
                'rewards_claimed' => $this->rewards_claimed
            ],
            'collectibles' => $progress,
            'rewards' => $collectionSet->completion_rewards
        ];
    }
}
