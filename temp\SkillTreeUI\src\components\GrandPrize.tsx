import { motion } from 'framer-motion';
import { Trophy } from 'lucide-react';
import { useState } from 'react';
import { useSpring, animated } from 'react-spring';

const GrandPrize = () => {
  const [isHovered, setIsHovered] = useState(false);
  
  const glowAnimation = useSpring({
    from: { boxShadow: '0 0 20px rgba(155, 139, 108, 0.3)' },
    to: { boxShadow: '0 0 40px rgba(155, 139, 108, 0.7)' },
    config: { duration: 2000 },
    loop: { reverse: true }
  });
  
  return (
    <animated.div
      style={glowAnimation}
      className="bg-gradient-to-b from-brass-300 to-brass-500 p-1 rounded-full"
    >
      <motion.div 
        className="w-32 h-32 rounded-full bg-gradient-to-br from-brass-200 to-brass-400 flex items-center justify-center cursor-pointer border-4 border-brass-100 relative shadow-inner-glow"
        whileHover={{ scale: 1.05 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
        <Trophy className="w-12 h-12 text-stone-100" />
        
        <div className="absolute inset-0 rounded-full overflow-hidden">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-brass-100 rounded-full"
              style={{ 
                left: `${50 + 45 * Math.cos(i * (Math.PI / 3))}%`, 
                top: `${50 + 45 * Math.sin(i * (Math.PI / 3))}%` 
              }}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.7, 1, 0.7]
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                delay: i * 0.3,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
        
        <motion.div
          className="absolute left-1/2 top-full mt-4 bg-abyss-200/95 border border-brass-300/50 p-4 rounded-lg shadow-outer-glow backdrop-blur-sm z-50 w-64"
          initial={{ opacity: 0, y: -20, translateX: '-50%' }}
          animate={{ 
            opacity: isHovered ? 1 : 0, 
            y: isHovered ? 0 : -20,
            translateX: '-50%'
          }}
          transition={{ duration: 0.3 }}
          style={{ visibility: isHovered ? 'visible' : 'hidden' }}
        >
          <h3 className="font-bold text-brass-300 text-lg mb-2">Grand Prize</h3>
          <p className="text-stone-300 text-sm mb-2">
            Complete all sections to claim your legendary reward!
          </p>
          <ul className="text-xs text-stone-400 list-disc pl-4 space-y-1">
            <li>Unique Legendary NFT</li>
            <li>10,000 Premium Tokens</li>
            <li>Exclusive Game Access</li>
            <li>VIP Tournament Entry</li>
          </ul>
        </motion.div>
      </motion.div>
    </animated.div>
  );
};

export default GrandPrize;