# State Management Implementation

## Overview
This document covers the implementation of state management for the Pet System using Zustand stores, including pet state, collection state, mystery box state, and user state management.

## Implementation Time: 2-3 days
## Complexity: Medium-High
## Dependencies: Zustand, API integration

## Pet Store Implementation

### Pet Store
```typescript
// File: battlx/src/stores/petStore.ts

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Pet, PetTemplate, CollectionProgress } from '../types/pet';
import { petApi } from '../services/api/petApi';

interface PetState {
  // State
  pets: Pet[];
  petTemplates: PetTemplate[];
  featuredPet: Pet | null;
  collectionProgress: CollectionProgress | null;
  petsNeedingAttention: Pet[];
  loading: boolean;
  error: string | null;

  // Actions
  fetchPets: () => Promise<void>;
  fetchPetTemplates: () => Promise<void>;
  fetchFeaturedPet: () => Promise<void>;
  fetchPetsNeedingAttention: () => Promise<void>;
  purchasePet: (templateId: string, purchaseMethod: string, nickname?: string) => Promise<Pet>;
  interactWithPet: (petId: string, interactionType: string) => Promise<any>;
  setFeaturedPet: (petId: string) => Promise<void>;
  updatePetNickname: (petId: string, nickname: string) => Promise<void>;
  togglePetFavorite: (petId: string) => Promise<void>;
  evolvePet: (petId: string) => Promise<void>;
  getInteractionHistory: (petId: string) => Promise<any[]>;
  
  // Utility actions
  clearError: () => void;
  resetStore: () => void;
}

const initialState = {
  pets: [],
  petTemplates: [],
  featuredPet: null,
  collectionProgress: null,
  petsNeedingAttention: [],
  loading: false,
  error: null,
};

export const usePetStore = create<PetState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        fetchPets: async () => {
          set({ loading: true, error: null });
          try {
            const response = await petApi.getPets();
            set({ 
              pets: response.pets,
              collectionProgress: response.collection_progress,
              loading: false 
            });
          } catch (error: any) {
            set({ 
              error: error.message || 'Failed to fetch pets',
              loading: false 
            });
          }
        },

        fetchPetTemplates: async () => {
          set({ loading: true, error: null });
          try {
            const templates = await petApi.getPetTemplates();
            set({ 
              petTemplates: templates,
              loading: false 
            });
          } catch (error: any) {
            set({ 
              error: error.message || 'Failed to fetch pet templates',
              loading: false 
            });
          }
        },

        fetchFeaturedPet: async () => {
          try {
            const featuredPet = await petApi.getFeaturedPet();
            set({ featuredPet });
          } catch (error: any) {
            console.error('Failed to fetch featured pet:', error);
          }
        },

        fetchPetsNeedingAttention: async () => {
          try {
            const pets = await petApi.getPetsNeedingAttention();
            set({ petsNeedingAttention: pets });
          } catch (error: any) {
            console.error('Failed to fetch pets needing attention:', error);
          }
        },

        purchasePet: async (templateId: string, purchaseMethod: string, nickname?: string) => {
          set({ loading: true, error: null });
          try {
            const result = await petApi.purchasePet(templateId, purchaseMethod, nickname);
            
            // Update pets list
            const currentPets = get().pets;
            set({ 
              pets: [...currentPets, result.pet],
              loading: false 
            });

            // If this is the first pet, set as featured
            if (currentPets.length === 0) {
              set({ featuredPet: result.pet });
            }

            return result.pet;
          } catch (error: any) {
            set({ 
              error: error.message || 'Failed to purchase pet',
              loading: false 
            });
            throw error;
          }
        },

        interactWithPet: async (petId: string, interactionType: string) => {
          try {
            const result = await petApi.interactWithPet(petId, interactionType);
            
            // Update pet in the list
            const currentPets = get().pets;
            const updatedPets = currentPets.map(pet => 
              pet.id === petId ? { ...pet, ...result.pet } : pet
            );
            set({ pets: updatedPets });

            // Update featured pet if it's the same pet
            const featuredPet = get().featuredPet;
            if (featuredPet && featuredPet.id === petId) {
              set({ featuredPet: { ...featuredPet, ...result.pet } });
            }

            return result;
          } catch (error: any) {
            set({ error: error.message || 'Failed to interact with pet' });
            throw error;
          }
        },

        setFeaturedPet: async (petId: string) => {
          try {
            await petApi.setFeaturedPet(petId);
            
            // Update pets list
            const currentPets = get().pets;
            const updatedPets = currentPets.map(pet => ({
              ...pet,
              is_featured: pet.id === petId
            }));
            set({ pets: updatedPets });

            // Update featured pet
            const newFeaturedPet = updatedPets.find(pet => pet.id === petId);
            if (newFeaturedPet) {
              set({ featuredPet: newFeaturedPet });
            }
          } catch (error: any) {
            set({ error: error.message || 'Failed to set featured pet' });
            throw error;
          }
        },

        updatePetNickname: async (petId: string, nickname: string) => {
          try {
            const updatedPet = await petApi.updatePetNickname(petId, nickname);
            
            // Update pet in the list
            const currentPets = get().pets;
            const updatedPets = currentPets.map(pet => 
              pet.id === petId ? { ...pet, ...updatedPet } : pet
            );
            set({ pets: updatedPets });

            // Update featured pet if it's the same pet
            const featuredPet = get().featuredPet;
            if (featuredPet && featuredPet.id === petId) {
              set({ featuredPet: { ...featuredPet, ...updatedPet } });
            }
          } catch (error: any) {
            set({ error: error.message || 'Failed to update pet nickname' });
            throw error;
          }
        },

        togglePetFavorite: async (petId: string) => {
          try {
            const updatedPet = await petApi.togglePetFavorite(petId);
            
            // Update pet in the list
            const currentPets = get().pets;
            const updatedPets = currentPets.map(pet => 
              pet.id === petId ? { ...pet, ...updatedPet } : pet
            );
            set({ pets: updatedPets });

            // Update featured pet if it's the same pet
            const featuredPet = get().featuredPet;
            if (featuredPet && featuredPet.id === petId) {
              set({ featuredPet: { ...featuredPet, ...updatedPet } });
            }
          } catch (error: any) {
            set({ error: error.message || 'Failed to toggle pet favorite' });
            throw error;
          }
        },

        evolvePet: async (petId: string) => {
          set({ loading: true, error: null });
          try {
            const result = await petApi.evolvePet(petId);
            
            // Update pet in the list
            const currentPets = get().pets;
            const updatedPets = currentPets.map(pet => 
              pet.id === petId ? { ...pet, ...result.pet } : pet
            );
            set({ pets: updatedPets, loading: false });

            // Update featured pet if it's the same pet
            const featuredPet = get().featuredPet;
            if (featuredPet && featuredPet.id === petId) {
              set({ featuredPet: { ...featuredPet, ...result.pet } });
            }

            return result;
          } catch (error: any) {
            set({ 
              error: error.message || 'Failed to evolve pet',
              loading: false 
            });
            throw error;
          }
        },

        getInteractionHistory: async (petId: string) => {
          try {
            const history = await petApi.getInteractionHistory(petId);
            return history;
          } catch (error: any) {
            set({ error: error.message || 'Failed to fetch interaction history' });
            throw error;
          }
        },

        clearError: () => set({ error: null }),
        
        resetStore: () => set(initialState),
      }),
      {
        name: 'pet-store',
        partialize: (state) => ({
          pets: state.pets,
          featuredPet: state.featuredPet,
          collectionProgress: state.collectionProgress,
        }),
      }
    ),
    { name: 'pet-store' }
  )
);
```

### Mystery Box Store
```typescript
// File: battlx/src/stores/mysteryBoxStore.ts

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { MysteryBoxType, MysteryBoxOpening } from '../types/mysteryBox';
import { mysteryBoxApi } from '../services/api/mysteryBoxApi';

interface MysteryBoxState {
  // State
  availableBoxes: MysteryBoxType[];
  unlockedBoxes: MysteryBoxType[];
  openingHistory: MysteryBoxOpening[];
  loading: boolean;
  error: string | null;

  // Actions
  fetchAvailableBoxes: () => Promise<void>;
  fetchUnlockedBoxes: () => Promise<void>;
  fetchOpeningHistory: () => Promise<void>;
  openMysteryBox: (boxType: string, purchaseMethod: string, quantity?: number) => Promise<any>;
  
  // Utility actions
  clearError: () => void;
  resetStore: () => void;
}

const initialState = {
  availableBoxes: [],
  unlockedBoxes: [],
  openingHistory: [],
  loading: false,
  error: null,
};

export const useMysteryBoxStore = create<MysteryBoxState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        fetchAvailableBoxes: async () => {
          set({ loading: true, error: null });
          try {
            const boxes = await mysteryBoxApi.getAvailableBoxes();
            set({ 
              availableBoxes: boxes,
              loading: false 
            });
          } catch (error: any) {
            set({ 
              error: error.message || 'Failed to fetch available boxes',
              loading: false 
            });
          }
        },

        fetchUnlockedBoxes: async () => {
          try {
            const boxes = await mysteryBoxApi.getUnlockedBoxes();
            set({ unlockedBoxes: boxes });
          } catch (error: any) {
            console.error('Failed to fetch unlocked boxes:', error);
          }
        },

        fetchOpeningHistory: async () => {
          set({ loading: true, error: null });
          try {
            const history = await mysteryBoxApi.getOpeningHistory();
            set({ 
              openingHistory: history,
              loading: false 
            });
          } catch (error: any) {
            set({ 
              error: error.message || 'Failed to fetch opening history',
              loading: false 
            });
          }
        },

        openMysteryBox: async (boxType: string, purchaseMethod: string, quantity = 1) => {
          set({ loading: true, error: null });
          try {
            const result = await mysteryBoxApi.openMysteryBox(boxType, purchaseMethod, quantity);
            
            // Update opening history
            const currentHistory = get().openingHistory;
            set({ 
              openingHistory: [...result.openings, ...currentHistory],
              loading: false 
            });

            return result;
          } catch (error: any) {
            set({ 
              error: error.message || 'Failed to open mystery box',
              loading: false 
            });
            throw error;
          }
        },

        clearError: () => set({ error: null }),
        
        resetStore: () => set(initialState),
      }),
      {
        name: 'mystery-box-store',
        partialize: (state) => ({
          unlockedBoxes: state.unlockedBoxes,
          openingHistory: state.openingHistory.slice(0, 50), // Keep only recent history
        }),
      }
    ),
    { name: 'mystery-box-store' }
  )
);
```

### Collection Store
```typescript
// File: battlx/src/stores/collectionStore.ts

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { Collectible, CollectionSet, CollectionProgress } from '../types/collection';
import { collectionApi } from '../services/api/collectionApi';

interface CollectionState {
  // State
  collectibles: Collectible[];
  collectionSets: CollectionSet[];
  collectionProgress: CollectionProgress | null;
  searchResults: Collectible[];
  loading: boolean;
  error: string | null;

  // Actions
  fetchCollection: () => Promise<void>;
  fetchCollectionSets: () => Promise<void>;
  searchCollectibles: (query: string) => Promise<void>;
  claimSetRewards: (setId: string) => Promise<void>;
  
  // Utility actions
  clearError: () => void;
  clearSearchResults: () => void;
  resetStore: () => void;
}

const initialState = {
  collectibles: [],
  collectionSets: [],
  collectionProgress: null,
  searchResults: [],
  loading: false,
  error: null,
};

export const useCollectionStore = create<CollectionState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        fetchCollection: async () => {
          set({ loading: true, error: null });
          try {
            const response = await collectionApi.getCollection();
            set({ 
              collectibles: response.collectibles,
              collectionProgress: response.progress,
              loading: false 
            });
          } catch (error: any) {
            set({ 
              error: error.message || 'Failed to fetch collection',
              loading: false 
            });
          }
        },

        fetchCollectionSets: async () => {
          set({ loading: true, error: null });
          try {
            const sets = await collectionApi.getCollectionSets();
            set({ 
              collectionSets: sets,
              loading: false 
            });
          } catch (error: any) {
            set({ 
              error: error.message || 'Failed to fetch collection sets',
              loading: false 
            });
          }
        },

        searchCollectibles: async (query: string) => {
          if (query.length < 2) {
            set({ searchResults: [] });
            return;
          }

          try {
            const results = await collectionApi.searchCollectibles(query);
            set({ searchResults: results });
          } catch (error: any) {
            console.error('Search failed:', error);
            set({ searchResults: [] });
          }
        },

        claimSetRewards: async (setId: string) => {
          set({ loading: true, error: null });
          try {
            const result = await collectionApi.claimSetRewards(setId);
            
            // Update collection sets
            const currentSets = get().collectionSets;
            const updatedSets = currentSets.map(set => 
              set.set_id === setId ? { ...set, rewards_claimed: true } : set
            );
            set({ 
              collectionSets: updatedSets,
              loading: false 
            });

            return result;
          } catch (error: any) {
            set({ 
              error: error.message || 'Failed to claim set rewards',
              loading: false 
            });
            throw error;
          }
        },

        clearError: () => set({ error: null }),
        
        clearSearchResults: () => set({ searchResults: [] }),
        
        resetStore: () => set(initialState),
      }),
      {
        name: 'collection-store',
        partialize: (state) => ({
          collectibles: state.collectibles,
          collectionSets: state.collectionSets,
          collectionProgress: state.collectionProgress,
        }),
      }
    ),
    { name: 'collection-store' }
  )
);
```

### User Store Integration
```typescript
// File: battlx/src/stores/userStore.ts (additions for pet system)

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface UserState {
  // Existing user state...
  user: any;
  
  // Pet system additions
  petSystemEnabled: boolean;
  
  // Actions
  updateUserBalance: (balance: number) => void;
  updateUserGems: (gems: number) => void;
  updateUserEnergy: (energy: number) => void;
  updateAchievementPoints: (points: number) => void;
  
  // Pet system actions
  enablePetSystem: () => void;
  disablePetSystem: () => void;
}

export const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set, get) => ({
        // Existing state...
        user: null,
        petSystemEnabled: true,

        updateUserBalance: (balance: number) => {
          const currentUser = get().user;
          if (currentUser) {
            set({ user: { ...currentUser, balance } });
          }
        },

        updateUserGems: (gems: number) => {
          const currentUser = get().user;
          if (currentUser) {
            set({ user: { ...currentUser, gems } });
          }
        },

        updateUserEnergy: (energy: number) => {
          const currentUser = get().user;
          if (currentUser) {
            set({ user: { ...currentUser, available_energy: energy } });
          }
        },

        updateAchievementPoints: (points: number) => {
          const currentUser = get().user;
          if (currentUser) {
            set({ user: { ...currentUser, achievement_points: points } });
          }
        },

        enablePetSystem: () => set({ petSystemEnabled: true }),
        
        disablePetSystem: () => set({ petSystemEnabled: false }),
      }),
      {
        name: 'user-store',
        partialize: (state) => ({
          user: state.user,
          petSystemEnabled: state.petSystemEnabled,
        }),
      }
    ),
    { name: 'user-store' }
  )
);
```

## Store Utilities and Hooks

### Store Synchronization Hook
```typescript
// File: battlx/src/hooks/useStoreSynchronization.ts

import { useEffect } from 'react';
import { usePetStore } from '../stores/petStore';
import { useMysteryBoxStore } from '../stores/mysteryBoxStore';
import { useCollectionStore } from '../stores/collectionStore';
import { useUserStore } from '../stores/userStore';

export const useStoreSynchronization = () => {
  const { user } = useUserStore();
  const { fetchPets, fetchFeaturedPet } = usePetStore();
  const { fetchUnlockedBoxes } = useMysteryBoxStore();
  const { fetchCollection } = useCollectionStore();

  useEffect(() => {
    if (user) {
      // Initialize all stores when user is available
      fetchPets();
      fetchFeaturedPet();
      fetchUnlockedBoxes();
      fetchCollection();
    }
  }, [user, fetchPets, fetchFeaturedPet, fetchUnlockedBoxes, fetchCollection]);

  // Sync user balance updates across stores
  useEffect(() => {
    if (user) {
      // This could be expanded to sync specific data between stores
      // For example, when a pet is purchased, update user balance
    }
  }, [user]);
};
```

### Store Reset Hook
```typescript
// File: battlx/src/hooks/useStoreReset.ts

import { usePetStore } from '../stores/petStore';
import { useMysteryBoxStore } from '../stores/mysteryBoxStore';
import { useCollectionStore } from '../stores/collectionStore';

export const useStoreReset = () => {
  const resetPetStore = usePetStore(state => state.resetStore);
  const resetMysteryBoxStore = useMysteryBoxStore(state => state.resetStore);
  const resetCollectionStore = useCollectionStore(state => state.resetStore);

  const resetAllStores = () => {
    resetPetStore();
    resetMysteryBoxStore();
    resetCollectionStore();
  };

  return { resetAllStores };
};
```

### Error Handling Hook
```typescript
// File: battlx/src/hooks/useErrorHandling.ts

import { useEffect } from 'react';
import { usePetStore } from '../stores/petStore';
import { useMysteryBoxStore } from '../stores/mysteryBoxStore';
import { useCollectionStore } from '../stores/collectionStore';

export const useErrorHandling = () => {
  const petError = usePetStore(state => state.error);
  const mysteryBoxError = useMysteryBoxStore(state => state.error);
  const collectionError = useCollectionStore(state => state.error);
  
  const clearPetError = usePetStore(state => state.clearError);
  const clearMysteryBoxError = useMysteryBoxStore(state => state.clearError);
  const clearCollectionError = useCollectionStore(state => state.clearError);

  const errors = [petError, mysteryBoxError, collectionError].filter(Boolean);
  const hasErrors = errors.length > 0;

  const clearAllErrors = () => {
    clearPetError();
    clearMysteryBoxError();
    clearCollectionError();
  };

  useEffect(() => {
    if (hasErrors) {
      console.error('Store errors detected:', errors);
    }
  }, [hasErrors, errors]);

  return {
    errors,
    hasErrors,
    clearAllErrors,
    petError,
    mysteryBoxError,
    collectionError,
  };
};
```

## Store Provider Setup

### Store Provider Component
```tsx
// File: battlx/src/providers/StoreProvider.tsx

import React, { useEffect } from 'react';
import { useStoreSynchronization } from '../hooks/useStoreSynchronization';
import { useErrorHandling } from '../hooks/useErrorHandling';

interface StoreProviderProps {
  children: React.ReactNode;
}

const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  useStoreSynchronization();
  const { hasErrors, clearAllErrors } = useErrorHandling();

  useEffect(() => {
    // Clear errors on mount
    clearAllErrors();
  }, [clearAllErrors]);

  return <>{children}</>;
};

export default StoreProvider;
```

## Acceptance Criteria
- [ ] All stores properly configured with Zustand
- [ ] State persistence working correctly
- [ ] Store synchronization functional
- [ ] Error handling implemented across stores
- [ ] TypeScript types properly defined
- [ ] Store actions update UI reactively
- [ ] Performance optimized for large datasets

## Next Steps
1. Create comprehensive testing suite
2. Implement performance optimizations
3. Add monitoring and analytics
4. Create deployment documentation

## Troubleshooting
- Monitor store performance with large datasets
- Test persistence across browser sessions
- Verify store synchronization timing
- Check for memory leaks in store subscriptions
- Test error handling scenarios thoroughly
