// Weapon constants
const WEAPONS = {
    [WeaponType.LEG]: [
        {
            level: 1,
            bulletType: WeaponType.LEG,
            name: 'Leg',
            frameName: 'leg_bullet.png',
            description: 'Horizontal attack, pierces through enemies. Upgrades add a projectile in the opposite direction.',
            tips: '',
            isUnlocked: true,
            rarity: 100,
            // Frequency - smaller value means faster
            interval: 1350,
            repeatInterval: 100,
            power: 1,
            area: 1,
            speed: 1,
            amount: 1,
            hitsWalls: false
        },
        {
            amount: 1
        },
        {
            power: 0.5
        },
        {
            power: 0.5,
            area: 0.1
        },
        {
            power: 0.5
        },
        {
            power: 0.5,
            area: 0.1
        },
        {
            power: 0.5
        },
        {
            power: 0.5
        }
    ],
    [WeaponType.BONE]: [
        {
            level: 1,
            bulletType: WeaponType.BONE,
            name: 'Bone',
            frameName: 'bone_bullet.png',
            description: 'Rotates around the player, damaging enemies it touches',
            tips: '',
            isUnlocked: false,
            rarity: 80,
            interval: 3000,
            repeatInterval: 0,
            power: 0.8,
            area: 1,
            speed: 1,
            amount: 1,
            hitsWalls: false
        },
        {
            amount: 1
        },
        {
            power: 0.3
        },
        {
            power: 0.3,
            area: 0.1
        },
        {
            power: 0.3
        },
        {
            power: 0.3,
            area: 0.1
        },
        {
            power: 0.3
        },
        {
            power: 0.3
        }
    ],
    [WeaponType.FIST]: [
        {
            level: 1,
            bulletType: WeaponType.FIST,
            name: 'Fist',
            frameName: 'fist_bullet.png',
            description: 'Circular attack, pierces through enemies. Range increases with upgrades.',
            tips: '',
            isUnlocked: false,
            rarity: 100,
            interval: 2500,
            repeatInterval: 100,
            power: 0.8,
            area: 1,
            speed: 1,
            amount: 1,
            hitsWalls: false
        },
        {
            area: 0.1,
            interval: -200
        },
        {
            interval: -300
        },
        {
            interval: -400
        },
        {
            area: 0.1
        },
        {
            power: 0.5
        },
        {
            area: 0.2
        },
        {
            power: 0.5,
            interval: -400
        }
    ],
    [WeaponType.ROCK]: [
        {
            level: 1,
            bulletType: WeaponType.ROCK,
            name: 'Rock',
            description: 'A heavy rock from childhood, hurts when it hits',
            tips: '',
            frameName: 'rock_bullet.png',
            isUnlocked: false,
            rarity: 100,
            interval: 4000,
            repeatInterval: 200,
            power: 2,
            area: 1,
            speed: 1,
            amount: 1,
            hitsWalls: false
        },
        {
            amount: 1
        },
        {
            power: 2
        },
        {
            power: 1
        },
        {
            amount: 1
        },
        {
            power: 2
        },
        {
            power: 1
        },
        {
            amount: 1
        }
    ],
    [WeaponType.RADIOACTIVE]: [
        {
            level: 1,
            bulletType: WeaponType.RADIOACTIVE,
            name: 'Radiation',
            description: 'Continuously radiates enemies after eating seafood',
            tips: '',
            frameName: 'radiation_bullet.png',
            isUnlocked: false,
            rarity: 70,
            interval: 1000,
            repeatInterval: 0,
            power: 0.5,
            area: 1,
            speed: 1,
            amount: 1,
            hitsWalls: false
        },
        {
            area: 0.4,
            power: 0.2
        },
        {
            interval: -100,
            power: 0.1
        },
        {
            area: 0.2,
            power: 0.2
        },
        {
            interval: -100,
            power: 0.2
        },
        {
            area: 0.2,
            power: 0.1
        },
        {
            interval: -100,
            power: 0.1
        },
        {
            area: 0.2,
            power: 0.2
        }
    ]
};

// Attach to window object for global access
window.WEAPONS = WEAPONS;
