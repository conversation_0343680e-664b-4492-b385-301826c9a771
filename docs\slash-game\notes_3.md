# Prize Tree System - Implementation Notes

## Overview

The Prize Tree system is a comprehensive feature that allows users to unlock various prizes using achievement points earned through gameplay and other activities. The system is designed to encourage engagement and provide a sense of progression and accomplishment.

## Key Components

1. **Achievement Points**: A currency earned through various activities in the app
2. **Prize Trees**: Themed collections of prizes organized in a hierarchical structure
3. **Prizes**: Rewards that can be unlocked using achievement points
4. **User Prizes**: Prizes that a user has unlocked and can equip

## Achievement Point Sources

Achievement points are awarded for various activities:

- **Tapping Milestones**: Points for reaching certain numbers of total taps
- **Daily Tasks**: Points for completing daily login tasks
- **Regular Tasks**: Points for completing bounty tasks
- **Referral Tasks**: Points for completing referral-based tasks
- **Mission Completion**: Points for completing missions
- **Game Milestones**: Points for unlocking games and reaching score milestones

## Prize Types

The system supports various types of prizes:

- **Cosmetic**: Visual enhancements like slash effects, backgrounds, etc.
- **Title**: Special titles displayed next to the user's name
- **Card**: Collectible cards
- **Balance**: In-game currency rewards
- **Booster**: Temporary boosts to gameplay
- **Special Item**: Unique items with special effects
- **Emote**: Animated expressions for social interactions

## Database Structure

The system uses several database tables:

- `prize_trees`: Stores information about different prize trees
- `prizes`: Stores individual prizes and their requirements
- `prize_prerequisites`: Maps prerequisites between prizes
- `user_prizes`: Tracks which prizes users have unlocked
- `user_achievement_points`: Tracks users' achievement point balances
- `achievement_point_transactions`: Records all achievement point transactions

## Implementation Challenges

### 1. Tree Visualization

Implementing the tree visualization was challenging due to:
- Dynamic positioning of nodes based on tier and position
- Drawing connections between nodes
- Supporting zoom and pan interactions
- Ensuring performance on mobile devices

Solution: Used a combination of HTML elements for nodes and a canvas for drawing connections. Implemented custom zoom and pan handlers.

### 2. Achievement Point Balance

Maintaining a consistent achievement point balance required:
- Tracking all sources of points
- Ensuring transactions are atomic
- Preventing negative balances
- Providing transaction history

Solution: Implemented a transaction-based system with database-level constraints and comprehensive logging.

### 3. Prize Prerequisites

Managing prize prerequisites required:
- Enforcing unlock requirements
- Visualizing available vs. locked prizes
- Handling complex prerequisite chains

Solution: Used a many-to-many relationship table and recursive queries to validate prerequisites.

## Future Enhancements

1. **Seasonal Prize Trees**: Time-limited trees with exclusive prizes
2. **Achievement Badges**: Visual indicators of achievement milestones
3. **Prize Sharing**: Ability to showcase prizes to friends
4. **Prize Crafting**: Combining multiple prizes to create new ones
5. **Prize Effects**: More interactive effects for equipped prizes

## Performance Considerations

- The tree visualization uses canvas for efficient rendering
- Achievement point transactions are batched where possible
- Prize data is cached on the client side
- Tree data is loaded on demand when a tree is selected

## Security Considerations

- All achievement point transactions are validated server-side
- Prize unlocks verify prerequisites and point balances
- Admin-only endpoints are protected by middleware
- Transaction history provides audit trail

## User Experience Notes

- The tree visualization includes visual cues for available vs. locked prizes
- Tooltips provide information about prerequisites and costs
- Animations provide feedback for unlocking and equipping prizes
- Filter tabs in the gallery make it easy to find specific prize types

## Integration with Existing Systems

- Achievement points are displayed in the user profile
- Equipped prizes affect gameplay visuals
- Prize unlocks are announced with toast notifications
- Achievement point transactions are recorded in the user's activity feed

## Testing Strategy

1. **Unit Tests**: Test individual components like the achievement point service
2. **Integration Tests**: Test the interaction between components
3. **UI Tests**: Test the tree visualization and user interactions
4. **Load Tests**: Test performance with many prizes and transactions
5. **Security Tests**: Test authentication and authorization

## Deployment Considerations

- Database migrations should be run in the correct order
- Seeder data should be provided for initial prize trees
- Frontend assets should be properly bundled and minified
- Cache should be cleared after deployment
