import { BattlxIcon } from "./icons/BattlxIcon";

export default function LoadingPage() {
    return (
        <div className="flex flex-col justify-center items-center min-h-screen bg-[url('/images/bg.png')] bg-cover">
            <div className="flex flex-col items-center relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]">
                <BattlxIcon
                    icon="loading"
                    className="w-16 h-16 text-6xl opacity-80 text-[#9B8B6C] animate-spin"
                />
                <p className="mt-4 text-[#B3B3B3] font-gothic">Loading...</p>
            </div>
        </div>
    );
}
