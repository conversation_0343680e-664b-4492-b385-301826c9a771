<?php

use App\Models\MissionLevel;
use App\Models\TelegramUser;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the table
        Schema::create('telegram_user_missions', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(TelegramUser::class)->constrained()->onDelete('cascade');
            $table->foreignIdFor(MissionLevel::class)->constrained()->onDelete('cascade');
            $table->smallInteger('level')->unsigned(); // SMALLINT with unsigned constraint

            $table->unique(['telegram_user_id', 'mission_level_id']);
            $table->timestamps();
        });

        // Add CHECK constraint using raw SQL
        DB::statement('ALTER TABLE telegram_user_missions ADD CONSTRAINT chk_level_non_negative CHECK (level >= 0)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the CHECK constraint
        DB::statement('ALTER TABLE telegram_user_missions DROP CONSTRAINT IF EXISTS chk_level_non_negative');

        // Drop the table
        Schema::dropIfExists('telegram_user_missions');
    }
};