# Phase 4: Social & Economic Features Implementation

## Overview
Implement advanced social features and a player-driven economy to create deep engagement through spectating, trading, crafting, and social competition.

## Backend Implementation

### 1. Database Schema

#### Social & Economic System Tables
```php
// 2024_XX_XX_create_social_economic_system.php

Schema::create('items', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->text('description');
    $table->enum('type', ['weapon', 'armor', 'consumable', 'material', 'cosmetic']);
    $table->enum('rarity', ['common', 'uncommon', 'rare', 'epic', 'legendary', 'mythic']);
    $table->json('stats'); // Attack, defense, special abilities
    $table->json('requirements'); // Level, guild membership, etc.
    $table->boolean('tradeable')->default(true);
    $table->boolean('craftable')->default(false);
    $table->string('icon_url')->nullable();
    $table->integer('base_value')->default(0);
    $table->timestamps();
});

Schema::create('user_items', function (Blueprint $table) {
    $table->id();
    $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
    $table->foreignId('item_id')->constrained()->onDelete('cascade');
    $table->integer('quantity')->default(1);
    $table->json('item_data')->nullable(); // Enchantments, durability, etc.
    $table->timestamp('acquired_at');
    $table->timestamps();
});

Schema::create('market_listings', function (Blueprint $table) {
    $table->id();
    $table->foreignId('seller_id')->constrained('telegram_users')->onDelete('cascade');
    $table->foreignId('item_id')->constrained()->onDelete('cascade');
    $table->integer('quantity');
    $table->integer('price_per_unit');
    $table->integer('total_price');
    $table->text('description')->nullable();
    $table->enum('status', ['active', 'sold', 'cancelled', 'expired']);
    $table->timestamp('expires_at');
    $table->timestamps();
});

Schema::create('market_transactions', function (Blueprint $table) {
    $table->id();
    $table->foreignId('listing_id')->constrained('market_listings')->onDelete('cascade');
    $table->foreignId('buyer_id')->constrained('telegram_users')->onDelete('cascade');
    $table->foreignId('seller_id')->constrained('telegram_users')->onDelete('cascade');
    $table->foreignId('item_id')->constrained()->onDelete('cascade');
    $table->integer('quantity');
    $table->integer('price_per_unit');
    $table->integer('total_price');
    $table->timestamp('completed_at');
    $table->timestamps();
});

Schema::create('crafting_recipes', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->foreignId('result_item_id')->constrained('items')->onDelete('cascade');
    $table->integer('result_quantity')->default(1);
    $table->json('required_materials'); // item_id => quantity pairs
    $table->integer('required_level')->default(1);
    $table->integer('crafting_time_minutes')->default(60);
    $table->integer('success_rate')->default(100); // Percentage
    $table->boolean('is_active')->default(true);
    $table->timestamps();
});

Schema::create('crafting_queue', function (Blueprint $table) {
    $table->id();
    $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
    $table->foreignId('recipe_id')->constrained('crafting_recipes')->onDelete('cascade');
    $table->integer('quantity');
    $table->enum('status', ['in_progress', 'completed', 'failed', 'cancelled']);
    $table->timestamp('started_at');
    $table->timestamp('completed_at')->nullable();
    $table->timestamps();
});

Schema::create('friend_requests', function (Blueprint $table) {
    $table->id();
    $table->foreignId('sender_id')->constrained('telegram_users')->onDelete('cascade');
    $table->foreignId('receiver_id')->constrained('telegram_users')->onDelete('cascade');
    $table->enum('status', ['pending', 'accepted', 'declined']);
    $table->timestamp('sent_at');
    $table->timestamp('responded_at')->nullable();
    $table->timestamps();
    
    $table->unique(['sender_id', 'receiver_id']);
});

Schema::create('friendships', function (Blueprint $table) {
    $table->id();
    $table->foreignId('user_1_id')->constrained('telegram_users')->onDelete('cascade');
    $table->foreignId('user_2_id')->constrained('telegram_users')->onDelete('cascade');
    $table->timestamp('established_at');
    $table->timestamps();
    
    $table->unique(['user_1_id', 'user_2_id']);
});

Schema::create('spectator_sessions', function (Blueprint $table) {
    $table->id();
    $table->foreignId('battle_id')->constrained()->onDelete('cascade');
    $table->foreignId('spectator_id')->constrained('telegram_users')->onDelete('cascade');
    $table->timestamp('joined_at');
    $table->timestamp('left_at')->nullable();
    $table->timestamps();
});

Schema::create('social_challenges', function (Blueprint $table) {
    $table->id();
    $table->foreignId('challenger_id')->constrained('telegram_users')->onDelete('cascade');
    $table->foreignId('challenged_id')->constrained('telegram_users')->onDelete('cascade');
    $table->enum('challenge_type', ['battle', 'score_race', 'resource_collection']);
    $table->json('challenge_parameters'); // Specific rules and conditions
    $table->json('stakes'); // What's wagered
    $table->enum('status', ['pending', 'accepted', 'declined', 'in_progress', 'completed']);
    $table->foreignId('winner_id')->nullable()->constrained('telegram_users');
    $table->timestamp('expires_at');
    $table->timestamp('completed_at')->nullable();
    $table->timestamps();
});

Schema::create('mentorship_relationships', function (Blueprint $table) {
    $table->id();
    $table->foreignId('mentor_id')->constrained('telegram_users')->onDelete('cascade');
    $table->foreignId('mentee_id')->constrained('telegram_users')->onDelete('cascade');
    $table->enum('status', ['active', 'completed', 'terminated']);
    $table->integer('sessions_completed')->default(0);
    $table->json('progress_milestones')->nullable();
    $table->timestamp('started_at');
    $table->timestamp('ended_at')->nullable();
    $table->timestamps();
});
```

### 2. Models

#### Item Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Item extends Model
{
    protected $fillable = [
        'name', 'description', 'type', 'rarity', 'stats',
        'requirements', 'tradeable', 'craftable', 'icon_url', 'base_value'
    ];

    protected $casts = [
        'stats' => 'array',
        'requirements' => 'array',
        'tradeable' => 'boolean',
        'craftable' => 'boolean'
    ];

    public function userItems()
    {
        return $this->hasMany(UserItem::class);
    }

    public function marketListings()
    {
        return $this->hasMany(MarketListing::class);
    }

    public function craftingRecipes()
    {
        return $this->hasMany(CraftingRecipe::class, 'result_item_id');
    }

    public function getRarityColorAttribute(): string
    {
        return match ($this->rarity) {
            'common' => '#9CA3AF',
            'uncommon' => '#10B981',
            'rare' => '#3B82F6',
            'epic' => '#8B5CF6',
            'legendary' => '#F59E0B',
            'mythic' => '#EF4444',
            default => '#9CA3AF'
        };
    }

    public function getMarketValueAttribute(): int
    {
        // Calculate current market value based on recent transactions
        $recentTransactions = MarketTransaction::where('item_id', $this->id)
            ->where('completed_at', '>=', now()->subDays(7))
            ->avg('price_per_unit');

        return $recentTransactions ?: $this->base_value;
    }

    public function canBeUsedBy(TelegramUser $user): bool
    {
        foreach ($this->requirements as $requirement => $value) {
            switch ($requirement) {
                case 'min_level':
                    if ($user->level->level < $value) return false;
                    break;
                case 'guild_member':
                    if ($value && !$user->guild) return false;
                    break;
            }
        }
        return true;
    }
}
```

#### MarketListing Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MarketListing extends Model
{
    protected $fillable = [
        'seller_id', 'item_id', 'quantity', 'price_per_unit',
        'total_price', 'description', 'status', 'expires_at'
    ];

    protected $casts = [
        'expires_at' => 'datetime'
    ];

    public function seller()
    {
        return $this->belongsTo(TelegramUser::class, 'seller_id');
    }

    public function item()
    {
        return $this->belongsTo(Item::class);
    }

    public function transaction()
    {
        return $this->hasOne(MarketTransaction::class, 'listing_id');
    }

    public function isActive(): bool
    {
        return $this->status === 'active' && now()->isBefore($this->expires_at);
    }

    public function canBePurchasedBy(TelegramUser $user): bool
    {
        return $this->isActive() && 
               $this->seller_id !== $user->id && 
               $user->balance >= $this->total_price;
    }

    public function purchase(TelegramUser $buyer)
    {
        if (!$this->canBePurchasedBy($buyer)) {
            throw new \Exception('Cannot purchase this item');
        }

        \DB::transaction(function () use ($buyer) {
            // Transfer money
            $buyer->decrement('balance', $this->total_price);
            $this->seller->increment('balance', $this->total_price);

            // Transfer item
            UserItem::create([
                'telegram_user_id' => $buyer->id,
                'item_id' => $this->item_id,
                'quantity' => $this->quantity,
                'acquired_at' => now()
            ]);

            // Remove from seller's inventory
            $sellerItem = UserItem::where('telegram_user_id', $this->seller_id)
                ->where('item_id', $this->item_id)
                ->first();
            
            if ($sellerItem->quantity <= $this->quantity) {
                $sellerItem->delete();
            } else {
                $sellerItem->decrement('quantity', $this->quantity);
            }

            // Record transaction
            MarketTransaction::create([
                'listing_id' => $this->id,
                'buyer_id' => $buyer->id,
                'seller_id' => $this->seller_id,
                'item_id' => $this->item_id,
                'quantity' => $this->quantity,
                'price_per_unit' => $this->price_per_unit,
                'total_price' => $this->total_price,
                'completed_at' => now()
            ]);

            // Update listing status
            $this->update(['status' => 'sold']);
        });
    }
}
```

### 3. Controllers

#### MarketController
```php
<?php

namespace App\Http\Controllers;

use App\Models\MarketListing;
use App\Models\Item;
use App\Models\UserItem;
use App\Services\MarketService;
use Illuminate\Http\Request;

class MarketController extends Controller
{
    protected $marketService;

    public function __construct(MarketService $marketService)
    {
        $this->marketService = $marketService;
    }

    public function index(Request $request)
    {
        $query = MarketListing::with(['item', 'seller'])
            ->where('status', 'active')
            ->where('expires_at', '>', now());

        // Filters
        if ($request->has('item_type')) {
            $query->whereHas('item', function ($q) use ($request) {
                $q->where('type', $request->item_type);
            });
        }

        if ($request->has('rarity')) {
            $query->whereHas('item', function ($q) use ($request) {
                $q->where('rarity', $request->rarity);
            });
        }

        if ($request->has('max_price')) {
            $query->where('price_per_unit', '<=', $request->max_price);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $listings = $query->paginate(20);

        return response()->json($listings);
    }

    public function createListing(Request $request)
    {
        $request->validate([
            'item_id' => 'required|exists:items,id',
            'quantity' => 'required|integer|min:1',
            'price_per_unit' => 'required|integer|min:1',
            'description' => 'nullable|string|max:500'
        ]);

        $user = $request->user();
        
        $userItem = UserItem::where('telegram_user_id', $user->id)
            ->where('item_id', $request->item_id)
            ->first();

        if (!$userItem || $userItem->quantity < $request->quantity) {
            return response()->json(['error' => 'Insufficient items'], 400);
        }

        $item = Item::find($request->item_id);
        if (!$item->tradeable) {
            return response()->json(['error' => 'Item not tradeable'], 400);
        }

        $listing = $this->marketService->createListing(
            $user,
            $request->item_id,
            $request->quantity,
            $request->price_per_unit,
            $request->description
        );

        return response()->json([
            'message' => 'Listing created successfully',
            'listing' => $listing
        ]);
    }

    public function purchaseItem(Request $request, MarketListing $listing)
    {
        $user = $request->user();

        try {
            $listing->purchase($user);
            
            return response()->json([
                'message' => 'Item purchased successfully',
                'transaction' => $listing->transaction
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    public function getUserListings(Request $request)
    {
        $user = $request->user();
        
        $listings = MarketListing::with('item')
            ->where('seller_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json($listings);
    }

    public function cancelListing(Request $request, MarketListing $listing)
    {
        $user = $request->user();

        if ($listing->seller_id !== $user->id) {
            return response()->json(['error' => 'Not authorized'], 403);
        }

        if ($listing->status !== 'active') {
            return response()->json(['error' => 'Listing not active'], 400);
        }

        $this->marketService->cancelListing($listing);

        return response()->json(['message' => 'Listing cancelled successfully']);
    }

    public function getMarketStats(Request $request)
    {
        $stats = $this->marketService->getMarketStatistics();
        return response()->json($stats);
    }
}
```

#### SocialController
```php
<?php

namespace App\Http\Controllers;

use App\Models\FriendRequest;
use App\Models\Friendship;
use App\Models\SocialChallenge;
use App\Models\Battle;
use App\Services\SocialService;
use Illuminate\Http\Request;

class SocialController extends Controller
{
    protected $socialService;

    public function __construct(SocialService $socialService)
    {
        $this->socialService = $socialService;
    }

    public function sendFriendRequest(Request $request)
    {
        $request->validate([
            'receiver_id' => 'required|exists:telegram_users,id'
        ]);

        $user = $request->user();
        
        if ($user->id === $request->receiver_id) {
            return response()->json(['error' => 'Cannot send friend request to yourself'], 400);
        }

        $result = $this->socialService->sendFriendRequest($user, $request->receiver_id);
        
        return response()->json($result);
    }

    public function respondToFriendRequest(Request $request, FriendRequest $friendRequest)
    {
        $request->validate([
            'response' => 'required|in:accept,decline'
        ]);

        $user = $request->user();

        if ($friendRequest->receiver_id !== $user->id) {
            return response()->json(['error' => 'Not authorized'], 403);
        }

        $result = $this->socialService->respondToFriendRequest(
            $friendRequest, 
            $request->response === 'accept'
        );

        return response()->json($result);
    }

    public function getFriends(Request $request)
    {
        $user = $request->user();
        $friends = $this->socialService->getUserFriends($user);
        
        return response()->json($friends);
    }

    public function challengeFriend(Request $request)
    {
        $request->validate([
            'friend_id' => 'required|exists:telegram_users,id',
            'challenge_type' => 'required|in:battle,score_race,resource_collection',
            'stakes' => 'required|array'
        ]);

        $user = $request->user();
        
        $challenge = $this->socialService->createSocialChallenge(
            $user,
            $request->friend_id,
            $request->challenge_type,
            $request->stakes
        );

        return response()->json([
            'message' => 'Challenge sent successfully',
            'challenge' => $challenge
        ]);
    }

    public function spectateGame(Request $request, Battle $battle)
    {
        $user = $request->user();

        if (!$battle->isActive()) {
            return response()->json(['error' => 'Battle not active'], 400);
        }

        $spectatorSession = $this->socialService->joinSpectatorSession($user, $battle);

        return response()->json([
            'message' => 'Joined as spectator',
            'session' => $spectatorSession,
            'battle_data' => $battle->load(['participants.user', 'arena'])
        ]);
    }

    public function leaveSpectating(Request $request, Battle $battle)
    {
        $user = $request->user();
        
        $this->socialService->leaveSpectatorSession($user, $battle);

        return response()->json(['message' => 'Left spectator session']);
    }

    public function getLeaderboards(Request $request)
    {
        $type = $request->get('type', 'friends'); // friends, global, guild
        $category = $request->get('category', 'elo'); // elo, wins, damage
        
        $leaderboard = $this->socialService->getLeaderboard($request->user(), $type, $category);
        
        return response()->json($leaderboard);
    }
}
```

## Frontend Implementation

### 1. Market Interface

```typescript
// src/components/market/MarketPlace.tsx
import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { $http } from '@/lib/http';

interface MarketListing {
  id: number;
  item: {
    id: number;
    name: string;
    type: string;
    rarity: string;
    icon_url: string;
  };
  quantity: number;
  price_per_unit: number;
  total_price: number;
  seller: {
    id: number;
    first_name: string;
  };
}

export const MarketPlace: React.FC = () => {
  const [filters, setFilters] = useState({
    item_type: '',
    rarity: '',
    max_price: '',
    sort_by: 'created_at'
  });

  const { data: listings, refetch } = useQuery({
    queryKey: ['market-listings', filters],
    queryFn: () => $http.get('/api/market', { params: filters }).then(res => res.data)
  });

  const purchaseMutation = useMutation({
    mutationFn: (listingId: number) => $http.post(`/api/market/${listingId}/purchase`),
    onSuccess: () => {
      refetch();
      alert('Item purchased successfully!');
    }
  });

  const renderListingCard = (listing: MarketListing) => (
    <div key={listing.id} className="listing-card">
      <div className="item-info">
        <img src={listing.item.icon_url} alt={listing.item.name} />
        <div>
          <h3 className={`item-name rarity-${listing.item.rarity}`}>
            {listing.item.name}
          </h3>
          <p>Type: {listing.item.type}</p>
          <p>Quantity: {listing.quantity}</p>
        </div>
      </div>
      
      <div className="pricing">
        <div className="price-per-unit">
          {listing.price_per_unit} coins each
        </div>
        <div className="total-price">
          Total: {listing.total_price} coins
        </div>
      </div>
      
      <div className="seller-info">
        Sold by: {listing.seller.first_name}
      </div>
      
      <button
        onClick={() => purchaseMutation.mutate(listing.id)}
        disabled={purchaseMutation.isPending}
        className="purchase-btn"
      >
        Buy Now
      </button>
    </div>
  );

  return (
    <div className="marketplace">
      <div className="market-header">
        <h1>Marketplace</h1>
        <button className="sell-items-btn">Sell Items</button>
      </div>

      <div className="market-filters">
        <select
          value={filters.item_type}
          onChange={(e) => setFilters(f => ({ ...f, item_type: e.target.value }))}
        >
          <option value="">All Types</option>
          <option value="weapon">Weapons</option>
          <option value="armor">Armor</option>
          <option value="consumable">Consumables</option>
          <option value="material">Materials</option>
        </select>

        <select
          value={filters.rarity}
          onChange={(e) => setFilters(f => ({ ...f, rarity: e.target.value }))}
        >
          <option value="">All Rarities</option>
          <option value="common">Common</option>
          <option value="uncommon">Uncommon</option>
          <option value="rare">Rare</option>
          <option value="epic">Epic</option>
          <option value="legendary">Legendary</option>
        </select>

        <input
          type="number"
          placeholder="Max price"
          value={filters.max_price}
          onChange={(e) => setFilters(f => ({ ...f, max_price: e.target.value }))}
        />

        <select
          value={filters.sort_by}
          onChange={(e) => setFilters(f => ({ ...f, sort_by: e.target.value }))}
        >
          <option value="created_at">Newest</option>
          <option value="price_per_unit">Price: Low to High</option>
          <option value="price_per_unit desc">Price: High to Low</option>
        </select>
      </div>

      <div className="listings-grid">
        {listings?.data?.map(renderListingCard)}
      </div>
    </div>
  );
};
```

### 2. Social Features Interface

```typescript
// src/components/social/SocialHub.tsx
import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { $http } from '@/lib/http';

export const SocialHub: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'friends' | 'spectate' | 'challenges'>('friends');

  const { data: friends } = useQuery({
    queryKey: ['friends'],
    queryFn: () => $http.get('/api/social/friends').then(res => res.data)
  });

  const { data: activeBattles } = useQuery({
    queryKey: ['spectatable-battles'],
    queryFn: () => $http.get('/api/battles/spectatable').then(res => res.data)
  });

  const challengeFriendMutation = useMutation({
    mutationFn: (data: any) => $http.post('/api/social/challenge', data),
    onSuccess: () => alert('Challenge sent!')
  });

  const spectateGameMutation = useMutation({
    mutationFn: (battleId: number) => $http.post(`/api/battles/${battleId}/spectate`),
    onSuccess: (data) => {
      // Navigate to spectator view
      window.location.href = `/spectate/${data.data.battle_id}`;
    }
  });

  const renderFriendsTab = () => (
    <div className="friends-tab">
      <h2>Friends</h2>
      {friends?.map((friend: any) => (
        <div key={friend.id} className="friend-card">
          <div className="friend-info">
            <span>{friend.first_name}</span>
            <span className={`status ${friend.is_online ? 'online' : 'offline'}`}>
              {friend.is_online ? 'Online' : 'Offline'}
            </span>
          </div>
          <div className="friend-actions">
            <button onClick={() => challengeFriendMutation.mutate({
              friend_id: friend.id,
              challenge_type: 'battle',
              stakes: { coins: 100 }
            })}>
              Challenge
            </button>
            <button>Message</button>
          </div>
        </div>
      ))}
    </div>
  );

  const renderSpectateTab = () => (
    <div className="spectate-tab">
      <h2>Live Battles</h2>
      {activeBattles?.map((battle: any) => (
        <div key={battle.id} className="battle-card">
          <div className="battle-info">
            <h3>{battle.type} Battle</h3>
            <p>Arena: {battle.arena.name}</p>
            <div className="participants">
              {battle.participants.map((p: any) => (
                <span key={p.id}>{p.user.first_name}</span>
              ))}
            </div>
          </div>
          <button onClick={() => spectateGameMutation.mutate(battle.id)}>
            Spectate
          </button>
        </div>
      ))}
    </div>
  );

  return (
    <div className="social-hub">
      <div className="social-tabs">
        <button 
          className={activeTab === 'friends' ? 'active' : ''}
          onClick={() => setActiveTab('friends')}
        >
          Friends
        </button>
        <button 
          className={activeTab === 'spectate' ? 'active' : ''}
          onClick={() => setActiveTab('spectate')}
        >
          Spectate
        </button>
        <button 
          className={activeTab === 'challenges' ? 'active' : ''}
          onClick={() => setActiveTab('challenges')}
        >
          Challenges
        </button>
      </div>

      <div className="tab-content">
        {activeTab === 'friends' && renderFriendsTab()}
        {activeTab === 'spectate' && renderSpectateTab()}
      </div>
    </div>
  );
};
```

## Next Steps

1. **Real-time Spectating**: Implement WebSocket-based live battle viewing
2. **Advanced Trading**: Add auction system and trade negotiations
3. **Crafting System**: Complete item creation and enhancement mechanics
4. **Social Guilds**: Expand guild features with internal economies
5. **Achievement System**: Add comprehensive achievement tracking and rewards

This social and economic system creates a thriving player-driven ecosystem that encourages long-term engagement and community building.
