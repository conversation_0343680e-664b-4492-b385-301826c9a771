<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CollectibleTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collectibles = [
            // Shadow Collection
            [
                'collectible_id' => 'shadow_essence',
                'name' => 'Shadow Essence',
                'type' => 'essence',
                'category' => 'shadow',
                'rarity' => 'common',
                'description' => 'A swirling mass of dark energy, the basic component of shadow magic.',
                'image_url' => '/images/collectibles/shadow/shadow_essence.png',
                'collection_set_id' => 'shadow_collection',
                'set_position' => 1,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'shadow_wolf'
            ],
            [
                'collectible_id' => 'raven_feather',
                'name' => 'Raven Feather',
                'type' => 'artifact',
                'category' => 'shadow',
                'rarity' => 'rare',
                'description' => 'A mystical feather that shimmers with otherworldly darkness.',
                'image_url' => '/images/collectibles/shadow/raven_feather.png',
                'collection_set_id' => 'shadow_collection',
                'set_position' => 2,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'dark_raven'
            ],
            [
                'collectible_id' => 'void_crystal',
                'name' => 'Void Crystal',
                'type' => 'relic',
                'category' => 'shadow',
                'rarity' => 'epic',
                'description' => 'A crystal formed in the depths of the void, pulsing with dark energy.',
                'image_url' => '/images/collectibles/shadow/void_crystal.png',
                'collection_set_id' => 'shadow_collection',
                'set_position' => 3,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'void_stalker'
            ],
            [
                'collectible_id' => 'shadow_crown',
                'name' => 'Shadow Crown',
                'type' => 'trophy',
                'category' => 'shadow',
                'rarity' => 'legendary',
                'description' => 'The crown of the Shadow Lord, symbol of dominion over darkness.',
                'image_url' => '/images/collectibles/shadow/shadow_crown.png',
                'collection_set_id' => 'shadow_collection',
                'set_position' => 4,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'shadow_lord'
            ],

            // Undead Collection
            [
                'collectible_id' => 'bone_fragment',
                'name' => 'Bone Fragment',
                'type' => 'essence',
                'category' => 'undead',
                'rarity' => 'common',
                'description' => 'A piece of ancient bone, still radiating with necromantic energy.',
                'image_url' => '/images/collectibles/undead/bone_fragment.png',
                'collection_set_id' => 'undead_collection',
                'set_position' => 1,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'skeleton_minion'
            ],
            [
                'collectible_id' => 'decay_potion',
                'name' => 'Decay Potion',
                'type' => 'artifact',
                'category' => 'undead',
                'rarity' => 'rare',
                'description' => 'A bubbling concoction that accelerates the process of decay.',
                'image_url' => '/images/collectibles/undead/decay_potion.png',
                'collection_set_id' => 'undead_collection',
                'set_position' => 2,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'zombie_hound'
            ],
            [
                'collectible_id' => 'deaths_blade',
                'name' => 'Deaths Blade',
                'type' => 'relic',
                'category' => 'undead',
                'rarity' => 'legendary',
                'description' => 'The legendary sword of the Death Knight, forged in the fires of the underworld.',
                'image_url' => '/images/collectibles/undead/deaths_blade.png',
                'collection_set_id' => 'undead_collection',
                'set_position' => 3,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'death_knight'
            ]
        ];

        foreach ($collectibles as $collectible) {
            DB::table('collectible_templates')->updateOrInsert(
                ['collectible_id' => $collectible['collectible_id']],
                array_merge($collectible, [
                    'created_at' => now(),
                    'updated_at' => now()
                ])
            );
        }
    }
}
