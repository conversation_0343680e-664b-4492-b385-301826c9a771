# Non-Docker Deployment Guide

## Overview
This document provides deployment instructions for the Pet System without using Docker, suitable for traditional server setups and existing infrastructure.

## Server Requirements

### Minimum Server Specifications
- **CPU**: 2 cores (4 cores recommended)
- **RAM**: 4GB (8GB recommended)
- **Storage**: 50GB SSD
- **OS**: Ubuntu 20.04+ or CentOS 8+

### Software Requirements
- **PHP**: 8.1 or higher
- **Node.js**: 18.x or higher
- **PostgreSQL**: 13 or higher
- **Redis**: 6.x or higher
- **Nginx**: 1.18 or higher
- **Composer**: 2.x
- **npm/yarn**: Latest stable

## Backend Deployment (Laravel API)

### 1. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP 8.1 and extensions
sudo apt install php8.1 php8.1-fpm php8.1-cli php8.1-common php8.1-mysql \
php8.1-zip php8.1-gd php8.1-mbstring php8.1-curl php8.1-xml php8.1-bcmath \
php8.1-pgsql php8.1-redis php8.1-intl -y

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib -y

# Install Redis
sudo apt install redis-server -y

# Install Nginx
sudo apt install nginx -y

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### 2. Database Setup
```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE battlx_production;
CREATE USER battlx_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE battlx_production TO battlx_user;
\q
```

### 3. Laravel Application Deployment
```bash
# Navigate to web directory
cd /var/www

# Clone your repository (or upload files)
sudo git clone https://github.com/your-repo/battlx-api.git
sudo chown -R www-data:www-data battlx-api
cd battlx-api

# Install dependencies
composer install --optimize-autoloader --no-dev

# Set up environment
cp .env.example .env
nano .env
```

### 4. Environment Configuration
```bash
# File: /var/www/battlx-api/.env

APP_NAME="BattlX Pet System"
APP_ENV=production
APP_KEY=base64:your-generated-app-key
APP_DEBUG=false
APP_URL=https://run.gamebot.com

DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=battlx_production
DB_USERNAME=battlx_user
DB_PASSWORD=your_secure_password

CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Pet System Configuration
PET_SYSTEM_ENABLED=true
PET_HAPPINESS_DECAY_ENABLED=true
CACHE_TTL_PETS=300
```

### 5. Application Setup
```bash
# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate --force

# Seed database
php artisan db:seed --class=PetSystemProductionSeeder --force

# Cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set permissions
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache
```

### 6. Queue Worker Setup
```bash
# Create systemd service for queue worker
sudo nano /etc/systemd/system/battlx-queue.service
```

```ini
[Unit]
Description=BattlX Queue Worker
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/battlx-api
ExecStart=/usr/bin/php artisan queue:work --sleep=3 --tries=3 --max-time=3600
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start queue worker
sudo systemctl enable battlx-queue
sudo systemctl start battlx-queue
```

### 7. Scheduler Setup
```bash
# Add to crontab
sudo crontab -e

# Add this line:
* * * * * cd /var/www/battlx-api && php artisan schedule:run >> /dev/null 2>&1
```

## Frontend Deployment (React)

### 1. Node.js Setup
```bash
# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

### 2. Build React Application
```bash
# Navigate to frontend directory
cd /var/www/battlx-frontend

# Install dependencies
npm install

# Create production environment file
nano .env.production
```

```bash
# File: .env.production
REACT_APP_API_URL=https://run.gamebot.com/api
REACT_APP_CDN_URL=https://run.gamebot.com/assets
REACT_APP_ENVIRONMENT=production
```

```bash
# Build for production
npm run build

# Copy build files to web directory
sudo cp -r build/* /var/www/html/
sudo chown -R www-data:www-data /var/www/html/
```

## Nginx Configuration

### 1. Main Nginx Configuration
```nginx
# File: /etc/nginx/sites-available/battlx

server {
    listen 80;
    server_name run.gamebot.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name run.gamebot.com;

    # SSL Configuration (use your existing SSL setup)
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Frontend (React)
    location / {
        root /var/www/html;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API (Laravel)
    location /api {
        alias /var/www/battlx-api/public;
        try_files $uri $uri/ @api;

        location ~ \.php$ {
            include fastcgi_params;
            fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
            fastcgi_param SCRIPT_FILENAME $request_filename;
        }
    }

    location @api {
        rewrite /api/(.*)$ /api/index.php?/$1 last;
    }

    # Handle Laravel API
    location ~ ^/api/index\.php(/|$) {
        root /var/www/battlx-api/public;
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }
}
```

### 2. Enable Site
```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/battlx /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

## Process Management (Alternative to Docker)

### 1. Using Supervisor for Process Management
```bash
# Install Supervisor
sudo apt install supervisor -y

# Create configuration for queue worker
sudo nano /etc/supervisor/conf.d/battlx-queue.conf
```

```ini
[program:battlx-queue]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/battlx-api/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/battlx-api/storage/logs/queue.log
stopwaitsecs=3600
```

```bash
# Update supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start battlx-queue:*
```

## Monitoring Without Docker

### 1. System Monitoring
```bash
# Install monitoring tools
sudo apt install htop iotop nethogs -y

# Create monitoring script
sudo nano /usr/local/bin/battlx-monitor.sh
```

```bash
#!/bin/bash
# BattlX System Monitor

echo "=== BattlX System Status ==="
echo "Date: $(date)"
echo ""

# Check services
echo "=== Service Status ==="
systemctl is-active nginx
systemctl is-active php8.1-fpm
systemctl is-active postgresql
systemctl is-active redis-server
systemctl is-active supervisor

echo ""
echo "=== Queue Status ==="
supervisorctl status battlx-queue:*

echo ""
echo "=== Database Connections ==="
sudo -u postgres psql -d battlx_production -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';"

echo ""
echo "=== Disk Usage ==="
df -h /var/www

echo ""
echo "=== Memory Usage ==="
free -h
```

```bash
# Make executable
sudo chmod +x /usr/local/bin/battlx-monitor.sh

# Add to crontab for regular monitoring
echo "*/5 * * * * /usr/local/bin/battlx-monitor.sh >> /var/log/battlx-monitor.log 2>&1" | sudo crontab -
```

## Backup Strategy

### 1. Database Backup
```bash
# Create backup script
sudo nano /usr/local/bin/battlx-backup.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/battlx"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
sudo -u postgres pg_dump battlx_production > $BACKUP_DIR/database_$DATE.sql

# Application files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/battlx-api --exclude=node_modules --exclude=vendor

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
```

```bash
# Make executable and schedule
sudo chmod +x /usr/local/bin/battlx-backup.sh
echo "0 2 * * * /usr/local/bin/battlx-backup.sh >> /var/log/battlx-backup.log 2>&1" | sudo crontab -
```

## Deployment Script

### 1. Automated Deployment
```bash
# Create deployment script
sudo nano /usr/local/bin/deploy-battlx.sh
```

```bash
#!/bin/bash
set -e

echo "Starting BattlX deployment..."

# Backup before deployment
/usr/local/bin/battlx-backup.sh

# Update API
cd /var/www/battlx-api
git pull origin main
composer install --optimize-autoloader --no-dev
php artisan migrate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Update Frontend
cd /var/www/battlx-frontend
git pull origin main
npm install
npm run build
sudo cp -r build/* /var/www/html/

# Restart services
sudo systemctl restart php8.1-fpm
sudo systemctl restart nginx
sudo supervisorctl restart battlx-queue:*

echo "Deployment completed successfully!"
```

## Advantages of Non-Docker Approach

1. **Simpler Setup**: No container orchestration complexity
2. **Direct Access**: Easy debugging and log access
3. **Resource Efficiency**: No container overhead
4. **Familiar Environment**: Standard server administration
5. **Existing Infrastructure**: Works with current setup

## Disadvantages

1. **Environment Differences**: Dev/prod environment inconsistencies
2. **Dependency Management**: Manual dependency installation
3. **Scaling Complexity**: Harder to scale horizontally
4. **Service Isolation**: Services share the same environment

This approach is perfectly suitable for your existing infrastructure and provides a robust deployment strategy without Docker complexity.
