<?php

namespace App\Http\Controllers;

use App\Models\PrizeTree;
use App\Models\Prize;
use App\Models\UserPrize;
use App\Models\UserAchievementPoint;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PrizeTreeController extends Controller
{
    /**
     * Get all prize trees
     */
    public function index()
    {
        $trees = PrizeTree::where('is_active', true)
            ->orderBy('display_order')
            ->get();

        return response()->json($trees);
    }

    /**
     * Get a specific prize tree with its prizes
     */
    public function show($id)
    {
        $tree = PrizeTree::findOrFail($id);

        $prizes = Prize::where('prize_tree_id', $id)
            ->with('prerequisites')
            ->get()
            ->map(function ($prize) {
                // Extract prerequisite IDs into a simple array
                $prerequisiteIds = $prize->prerequisites->pluck('id')->toArray();
                $prize->setRelation('prerequisites', collect());
                $prize->prerequisites = $prerequisiteIds;
                return $prize;
            });

        // Create connections array from prerequisites
        $connections = [];

        // Get the original prizes with full prerequisite objects for connections
        $prizesWithPrereqs = Prize::where('prize_tree_id', $id)
            ->with('prerequisites')
            ->get();

        foreach ($prizesWithPrereqs as $prize) {
            foreach ($prize->prerequisites as $prerequisite) {
                $connections[] = [
                    'startNodeId' => $prerequisite->id,
                    'endNodeId' => $prize->id
                ];
            }
        }

        // Format response to match frontend expectations
        return response()->json([
            'id' => $tree->id,
            'name' => $tree->name,
            'description' => $tree->description,
            'icon' => $tree->icon,
            'theme_color' => $tree->theme_color,
            'nodes' => $prizes,
            'connections' => $connections
        ]);
    }

    /**
     * Get user's prizes and achievement points
     */
    public function getUserPrizes()
    {
        $user = Auth::user();

        // Get user's unlocked prizes
        $unlockedPrizes = UserPrize::where('telegram_user_id', $user->id)
            ->pluck('prize_id')
            ->toArray();

        // Get user's equipped prizes
        $equippedPrizes = UserPrize::where('telegram_user_id', $user->id)
            ->where('is_equipped', true)
            ->with('prize')
            ->get()
            ->groupBy(function ($item) {
                return $item->prize->reward_type;
            });

        // Get user's achievement points
        $achievementPoints = UserAchievementPoint::where('telegram_user_id', $user->id)
            ->first();

        if (!$achievementPoints) {
            $achievementPoints = UserAchievementPoint::create([
                'telegram_user_id' => $user->id,
                'total_earned' => 0,
                'total_spent' => 0
            ]);
        }

        // Format equipped prizes to match frontend expectations
        $formattedEquippedPrizes = [];
        foreach ($equippedPrizes as $type => $prizes) {
            foreach ($prizes as $prize) {
                $formattedEquippedPrizes[] = [
                    'prize_id' => $prize->prize_id,
                    'reward_type' => $type,
                    'reward_details' => $prize->prize->getRewardDetails()
                ];
            }
        }

        // Get user prizes with details
        $userPrizes = UserPrize::where('telegram_user_id', $user->id)
            ->with('prize')
            ->get()
            ->map(function ($userPrize) {
                return [
                    'prize_id' => $userPrize->prize_id,
                    'unlocked_at' => $userPrize->unlocked_at,
                    'is_equipped' => $userPrize->is_equipped,
                    'prize' => $userPrize->prize
                ];
            });

        return response()->json([
            'success' => true,
            'unlocked_prizes' => $unlockedPrizes,
            'equipped_prizes' => $formattedEquippedPrizes,
            'user_prizes' => $userPrizes,
            'achievement_points' => $achievementPoints->total_earned - $achievementPoints->total_spent
        ]);
    }

    /**
     * Unlock a prize
     */
    public function unlockPrize(Request $request)
    {
        $request->validate([
            'prize_id' => 'required|exists:prizes,id'
        ]);

        $user = Auth::user();
        $prizeId = $request->prize_id;

        // Check if the prize is already unlocked
        $alreadyUnlocked = UserPrize::where('telegram_user_id', $user->id)
            ->where('prize_id', $prizeId)
            ->exists();

        if ($alreadyUnlocked) {
            return response()->json([
                'success' => false,
                'message' => 'Prize already unlocked'
            ], 400);
        }

        // Get the prize
        $prize = Prize::with('prerequisites')->findOrFail($prizeId);

        // Check if the user has unlocked all prerequisites
        $unlockedPrizes = UserPrize::where('telegram_user_id', $user->id)
            ->pluck('prize_id')
            ->toArray();

        foreach ($prize->prerequisites as $prerequisite) {
            if (!in_array($prerequisite->id, $unlockedPrizes)) {
                return response()->json([
                    'success' => false,
                    'message' => 'You need to unlock all prerequisites first'
                ], 400);
            }
        }

        // Check if the user has enough achievement points
        $achievementPoints = UserAchievementPoint::where('telegram_user_id', $user->id)
            ->first();

        if (!$achievementPoints) {
            $achievementPoints = UserAchievementPoint::create([
                'telegram_user_id' => $user->id,
                'total_earned' => 0,
                'total_spent' => 0
            ]);
        }

        $availablePoints = $achievementPoints->total_earned - $achievementPoints->total_spent;

        if ($availablePoints < $prize->cost) {
            return response()->json([
                'success' => false,
                'message' => 'Not enough achievement points'
            ], 400);
        }

        // Unlock the prize
        DB::transaction(function () use ($user, $prize, $achievementPoints) {
            // Create user prize record
            UserPrize::create([
                'telegram_user_id' => $user->id,
                'prize_id' => $prize->id,
                'unlocked_at' => now(),
                'is_equipped' => false
            ]);

            // Update achievement points
            $achievementPoints->total_spent += $prize->cost;
            $achievementPoints->save();

            // Record transaction
            DB::table('achievement_point_transactions')->insert([
                'telegram_user_id' => $user->id,
                'amount' => -$prize->cost,
                'type' => 'spend',
                'source' => 'prize_unlock',
                'source_id' => $prize->id,
                'description' => 'Unlocked prize: ' . $prize->name,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Apply reward if applicable
            if ($prize->reward_type === 'balance' && isset($prize->reward_data['amount'])) {
                $user->balance += $prize->reward_data['amount'];
                $user->save();
            }
        });

        // Get updated achievement points
        $updatedPoints = $achievementPoints->total_earned - $achievementPoints->total_spent;

        return response()->json([
            'success' => true,
            'message' => 'Prize unlocked successfully',
            'remaining_points' => $updatedPoints,
            'prize' => [
                'id' => $prize->id,
                'name' => $prize->name,
                'reward_type' => $prize->reward_type,
                'reward_details' => $prize->getRewardDetails()
            ]
        ]);
    }

    /**
     * Equip a prize
     */
    public function equipPrize(Request $request)
    {
        $request->validate([
            'prize_id' => 'required|exists:prizes,id'
        ]);

        $user = Auth::user();
        $prizeId = $request->prize_id;

        // Check if the prize is unlocked
        $userPrize = UserPrize::where('telegram_user_id', $user->id)
            ->where('prize_id', $prizeId)
            ->first();

        if (!$userPrize) {
            return response()->json([
                'success' => false,
                'message' => 'Prize not unlocked yet'
            ], 400);
        }

        // Get the prize
        $prize = Prize::findOrFail($prizeId);

        // Unequip any other prizes of the same type
        UserPrize::where('telegram_user_id', $user->id)
            ->whereHas('prize', function ($query) use ($prize) {
                $query->where('reward_type', $prize->reward_type);
            })
            ->update(['is_equipped' => false]);

        // Equip this prize
        $userPrize->is_equipped = true;
        $userPrize->save();

        return response()->json([
            'success' => true,
            'message' => 'Prize equipped successfully'
        ]);
    }

    /**
     * Unequip a prize
     */
    public function unequipPrize(Request $request)
    {
        $request->validate([
            'prize_id' => 'required|exists:prizes,id'
        ]);

        $user = Auth::user();
        $prizeId = $request->prize_id;

        // Check if the prize is unlocked and equipped
        $userPrize = UserPrize::where('telegram_user_id', $user->id)
            ->where('prize_id', $prizeId)
            ->first();

        if (!$userPrize) {
            return response()->json([
                'success' => false,
                'message' => 'Prize not unlocked yet'
            ], 400);
        }

        if (!$userPrize->is_equipped) {
            return response()->json([
                'success' => false,
                'message' => 'Prize not equipped'
            ], 400);
        }

        // Unequip the prize
        $userPrize->is_equipped = false;
        $userPrize->save();

        return response()->json([
            'success' => true,
            'message' => 'Prize unequipped successfully'
        ]);
    }

    /**
     * Get achievement point transactions
     */
    public function getTransactions()
    {
        $user = Auth::user();

        $transactions = DB::table('achievement_point_transactions')
            ->where('telegram_user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json($transactions);
    }
}
