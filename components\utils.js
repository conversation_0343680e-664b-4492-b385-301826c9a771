// Utility functions

// Format time in MM:SS format
function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
}

// Random number between min and max
function randomRange(min, max) {
    return Math.random() * (max - min) + min;
}

// Random integer between min and max (inclusive)
function randomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Calculate distance between two points
function distance(x1, y1, x2, y2) {
    return Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
}

// Check if two circles overlap
function circleOverlap(x1, y1, r1, x2, y2, r2) {
    return distance(x1, y1, x2, y2) < r1 + r2;
}

// Linear interpolation
function lerp(a, b, t) {
    return a + (b - a) * t;
}

// Clamp a value between min and max
function clamp(value, min, max) {
    return Math.max(min, Math.min(max, value));
}

// Load an image and return a promise
function loadImage(src) {
    return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = src;
    });
}

// Load multiple images and return a promise
async function loadImages(sources) {
    const promises = Object.entries(sources).map(([name, src]) => 
        loadImage(src).then(img => [name, img])
    );
    const results = await Promise.all(promises);
    return Object.fromEntries(results);
}

// Create a sprite sheet from an image
function createSpriteSheet(image, frameWidth, frameHeight) {
    const cols = Math.floor(image.width / frameWidth);
    const rows = Math.floor(image.height / frameHeight);
    const frames = [];
    
    for (let y = 0; y < rows; y++) {
        for (let x = 0; x < cols; x++) {
            frames.push({
                x: x * frameWidth,
                y: y * frameHeight,
                width: frameWidth,
                height: frameHeight
            });
        }
    }
    
    return {
        image,
        frames,
        frameWidth,
        frameHeight
    };
}

// Get a random position outside the screen
function getRandomPositionOutsideScreen(canvas, margin = 50) {
    const side = Math.floor(Math.random() * 4); // 0: top, 1: right, 2: bottom, 3: left
    let x, y;
    
    switch (side) {
        case 0: // top
            x = randomRange(0, canvas.width);
            y = -margin;
            break;
        case 1: // right
            x = canvas.width + margin;
            y = randomRange(0, canvas.height);
            break;
        case 2: // bottom
            x = randomRange(0, canvas.width);
            y = canvas.height + margin;
            break;
        case 3: // left
            x = -margin;
            y = randomRange(0, canvas.height);
            break;
    }
    
    return { x, y };
}

// Get a random position around a center point
function getRandomPositionAround(centerX, centerY, radius) {
    const angle = Math.random() * Math.PI * 2;
    const distance = Math.random() * radius;
    const x = centerX + Math.cos(angle) * distance;
    const y = centerY + Math.sin(angle) * distance;
    return { x, y };
}

// Simple event system
class EventEmitter {
    constructor() {
        this.events = {};
    }
    
    on(event, listener) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(listener);
        return () => this.off(event, listener);
    }
    
    off(event, listener) {
        if (!this.events[event]) return;
        this.events[event] = this.events[event].filter(l => l !== listener);
    }
    
    emit(event, ...args) {
        if (!this.events[event]) return;
        this.events[event].forEach(listener => listener(...args));
    }
}

// Asset loader
class AssetLoader {
    constructor() {
        this.images = {};
        this.audio = {};
        this.loaded = false;
        this.progress = 0;
        this.total = 0;
        this.completed = 0;
    }
    
    loadImage(name, src) {
        this.total++;
        return loadImage(src).then(img => {
            this.images[name] = img;
            this.completed++;
            this.progress = this.completed / this.total;
            return img;
        });
    }
    
    loadAudio(name, src) {
        this.total++;
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.oncanplaythrough = () => {
                this.audio[name] = audio;
                this.completed++;
                this.progress = this.completed / this.total;
                resolve(audio);
            };
            audio.onerror = reject;
            audio.src = src;
            audio.load();
        });
    }
    
    async loadAll(assets) {
        const promises = [];
        
        if (assets.images) {
            Object.entries(assets.images).forEach(([name, src]) => {
                promises.push(this.loadImage(name, src));
            });
        }
        
        if (assets.audio) {
            Object.entries(assets.audio).forEach(([name, src]) => {
                promises.push(this.loadAudio(name, src));
            });
        }
        
        await Promise.all(promises);
        this.loaded = true;
        return { images: this.images, audio: this.audio };
    }
}

// Timer class for handling delayed and repeated actions
class Timer {
    constructor() {
        this.timers = [];
    }
    
    add(delay, callback, repeat = false) {
        const timer = {
            delay,
            callback,
            repeat,
            elapsed: 0,
            id: Date.now() + Math.random()
        };
        this.timers.push(timer);
        return timer.id;
    }
    
    remove(id) {
        this.timers = this.timers.filter(timer => timer.id !== id);
    }
    
    update(deltaTime) {
        this.timers.forEach((timer, index) => {
            timer.elapsed += deltaTime;
            if (timer.elapsed >= timer.delay) {
                timer.callback();
                if (timer.repeat) {
                    timer.elapsed = 0;
                } else {
                    this.timers.splice(index, 1);
                }
            }
        });
    }
    
    clear() {
        this.timers = [];
    }
}

// Input handler
class InputHandler {
    constructor(canvas) {
        this.canvas = canvas;
        this.keys = {};
        this.mouse = {
            x: 0,
            y: 0,
            pressed: false
        };
        this.touch = {
            x: 0,
            y: 0,
            pressed: false
        };
        
        // Keyboard events
        window.addEventListener('keydown', e => {
            this.keys[e.key] = true;
        });
        
        window.addEventListener('keyup', e => {
            this.keys[e.key] = false;
        });
        
        // Mouse events
        canvas.addEventListener('mousedown', e => {
            this.mouse.pressed = true;
            this.updateMousePosition(e);
        });
        
        canvas.addEventListener('mouseup', e => {
            this.mouse.pressed = false;
        });
        
        canvas.addEventListener('mousemove', e => {
            this.updateMousePosition(e);
        });
        
        // Touch events
        canvas.addEventListener('touchstart', e => {
            this.touch.pressed = true;
            this.updateTouchPosition(e);
            e.preventDefault();
        }, { passive: false });
        
        canvas.addEventListener('touchend', e => {
            this.touch.pressed = false;
            e.preventDefault();
        }, { passive: false });
        
        canvas.addEventListener('touchmove', e => {
            this.updateTouchPosition(e);
            e.preventDefault();
        }, { passive: false });
    }
    
    updateMousePosition(e) {
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;
        
        this.mouse.x = (e.clientX - rect.left) * scaleX;
        this.mouse.y = (e.clientY - rect.top) * scaleY;
    }
    
    updateTouchPosition(e) {
        if (e.touches.length > 0) {
            const rect = this.canvas.getBoundingClientRect();
            const scaleX = this.canvas.width / rect.width;
            const scaleY = this.canvas.height / rect.height;
            
            this.touch.x = (e.touches[0].clientX - rect.left) * scaleX;
            this.touch.y = (e.touches[0].clientY - rect.top) * scaleY;
        }
    }
    
    isKeyPressed(key) {
        return this.keys[key] === true;
    }
    
    isMousePressed() {
        return this.mouse.pressed;
    }
    
    isTouchPressed() {
        return this.touch.pressed;
    }
    
    getPointerPosition() {
        return this.isTouchPressed() 
            ? { x: this.touch.x, y: this.touch.y } 
            : { x: this.mouse.x, y: this.mouse.y };
    }
    
    isPointerPressed() {
        return this.isMousePressed() || this.isTouchPressed();
    }
}

// Virtual joystick for mobile controls
class VirtualJoystick {
    constructor(canvas) {
        this.canvas = canvas;
        this.input = new InputHandler(canvas);
        this.position = { x: 0, y: 0 };
        this.center = { x: 0, y: 0 };
        this.radius = 50;
        this.innerRadius = 20;
        this.active = false;
        this.visible = false;
        this.force = 0;
        this.angle = 0;
        this.vector = { x: 0, y: 0 };
        
        this.canvas.addEventListener('touchstart', e => {
            if (!this.active) {
                this.activate(this.input.touch.x, this.input.touch.y);
            }
        });
        
        this.canvas.addEventListener('mousedown', e => {
            if (!this.active) {
                this.activate(this.input.mouse.x, this.input.mouse.y);
            }
        });
    }
    
    activate(x, y) {
        this.active = true;
        this.visible = true;
        this.center.x = x;
        this.center.y = y;
        this.position.x = x;
        this.position.y = y;
    }
    
    deactivate() {
        this.active = false;
        this.visible = false;
        this.force = 0;
        this.angle = 0;
        this.vector.x = 0;
        this.vector.y = 0;
    }
    
    update() {
        if (!this.active) return;
        
        if (!this.input.isPointerPressed()) {
            this.deactivate();
            return;
        }
        
        const pointer = this.input.getPointerPosition();
        const dx = pointer.x - this.center.x;
        const dy = pointer.y - this.center.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance > this.radius) {
            // Clamp position to the outer circle
            this.position.x = this.center.x + (dx / distance) * this.radius;
            this.position.y = this.center.y + (dy / distance) * this.radius;
        } else {
            this.position.x = pointer.x;
            this.position.y = pointer.y;
        }
        
        // Calculate force (0-1) and angle
        this.force = Math.min(1, distance / this.radius);
        this.angle = Math.atan2(dy, dx);
        
        // Calculate normalized vector
        this.vector.x = Math.cos(this.angle) * this.force;
        this.vector.y = Math.sin(this.angle) * this.force;
    }
    
    draw(ctx) {
        if (!this.visible) return;
        
        // Draw outer circle
        ctx.beginPath();
        ctx.arc(this.center.x, this.center.y, this.radius, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
        ctx.fill();
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
        ctx.stroke();
        
        // Draw inner circle (joystick)
        ctx.beginPath();
        ctx.arc(this.position.x, this.position.y, this.innerRadius, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.fill();
        ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.stroke();
    }
}
