// Player class for handling the player character
class Player {
    constructor(x, y, character) {
        this.x = x;
        this.y = y;
        this.radius = 18;
        this.lastFaceDirection = new Vector2(1, 0);
        this.hp = 100;
        this.isDead = false;
        this.xp = 0;
        this.level = 1;
        this.maxHp = 100;
        this.power = 1;
        this.area = 1;
        this.speed = 1;
        this.cooldown = 1;
        this.amount = 0;
        this.moveSpeed = 1;
        this.growth = 1;
        this.duration = 1;
        this.luck = 1;
        this.shields = 0;
        this.armor = 0;
        this.startingWeapon = WeaponType.LEG;
        this.posHistory = [];
        this.maxHistory = 3;
        this.historyIndex = 0;
        this.hasWalkingAnimation = false;
        this.invulTime = 0;
        this._invul = false;
        this.receivingDamage = false;
        this.characterType = character;
        this.evilValue = 0;
        this.velocity = new Vector2(0, 0);
        this.worldBoxCollider = {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };

        // God mode for testing (default: off)
        this.godMode = false;

        // Initialize position history
        for (let i = 0; i <= this.maxHistory; i++) {
            this.posHistory.push(new Vector2(this.x, this.y));
        }

        // Load character data
        this.loadCharacterData();
    }

    // Load character data from constants
    loadCharacterData() {
        const characterData = CHARACTERS[this.characterType][0];
        if (characterData) {
            this.startingWeapon = characterData.startingWeapon;
            this.maxHp = characterData.maxHp;
            this.hp = this.maxHp;
            this.power = characterData.power;
            this.area = characterData.area;
            this.speed = characterData.speed;
            this.cooldown = characterData.cooldown;
            this.amount = characterData.amount;
            this.moveSpeed = characterData.moveSpeed;
            this.growth = characterData.growth;
            this.duration = characterData.duration;
            this.spriteName = characterData.spriteName;
        }
    }

    // Get/set invulnerability
    get isInvul() {
        return this._invul;
    }

    set isInvul(value) {
        this._invul = value;
    }

    // Take damage
    takeDamage(amount) {
        // Skip damage if player is dead, invulnerable, or in god mode
        if (this.isDead || this.isInvul || this.godMode) return 0;

        // Apply armor reduction
        const damageReduction = this.armor / (100 + this.armor);
        const actualDamage = amount * (1 - damageReduction);

        this.hp -= actualDamage;

        // Set brief invulnerability after taking damage (500ms)
        this.setInvulForMilliSeconds(500);

        if (this.hp <= 0) {
            this.hp = 0;
            this.die();
        } else {
            this.onGetDamaged();
        }

        return actualDamage;
    }

    // Visual feedback when taking damage
    onGetDamaged() {
        this.receivingDamage = true;

        // Reset receiving damage after a short delay
        setTimeout(() => {
            this.receivingDamage = false;
        }, 250); // Increased from 120ms to 250ms for more visible effect

        // Show damage effect in game core if available
        if (Game.core) {
            // Flash the screen red briefly
            const canvas = Game.core.canvas;
            const ctx = Game.core.ctx;

            // Draw red overlay
            ctx.save();
            ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.restore();

            // Clear the overlay after a short delay
            setTimeout(() => {
                // The next frame will clear this automatically
            }, 100);
        }
    }

    // Die
    die() {
        this.isDead = true;
        this.velocity.x = 0;
        this.velocity.y = 0;

        // Trigger game over
        if (Game.core) {
            Game.core.gameOver();
        }
    }

    // Revive
    revive() {
        this.isDead = false;
        this.recoverHp(this.maxHp);
        this.setInvulForMilliSeconds(2000);

        // Update UI
        if (Game.core && Game.core.playerUI) {
            Game.core.playerUI.update();
        }
    }

    // Set invulnerability for a duration
    setInvulForMilliSeconds(time) {
        this.isInvul = true;
        this.invulTime += time;
    }

    // Recover health
    recoverHp(amount) {
        this.hp = Math.min(this.hp + amount, this.maxHp);

        // Update UI
        if (Game.core && Game.core.playerUI) {
            Game.core.playerUI.update();
        }
    }

    // Add experience
    addXp(amount) {
        this.xp += amount;

        // Check for level up
        if (Game.core) {
            Game.core.checkForLevelUp();
        }
    }

    // Level up
    levelUp() {
        this.level++;

        // Unlock weapons based on level
        this.unlockWeaponsByLevel();

        // Update UI
        if (Game.core && Game.core.playerUI) {
            Game.core.playerUI.updatePlayerLevel();
        }
    }

    // Unlock weapons based on player level
    unlockWeaponsByLevel() {
        // Unlock weapons based on level
        switch (this.level) {
            case 2:
                this.unlockWeapon(WeaponType.BONE);
                break;
            case 3:
                this.unlockWeapon(WeaponType.FIST);
                break;
            case 4:
                this.unlockWeapon(WeaponType.ROCK);
                break;
            case 5:
                this.unlockWeapon(WeaponType.RADIOACTIVE);
                break;
        }
    }

    // Unlock a specific weapon
    unlockWeapon(weaponType) {
        if (!WEAPONS[weaponType] || !WEAPONS[weaponType][0]) return;

        // Set the weapon as unlocked
        WEAPONS[weaponType][0].isUnlocked = true;

        // Add to player options unlocked weapons list
        if (Game.core && Game.core.playerOptions) {
            if (!Game.core.playerOptions.unlockedWeapons.includes(weaponType)) {
                Game.core.playerOptions.unlockedWeapons.push(weaponType);
                Game.core.playerOptions.save();
            }
        }

        

        // Show notification
        if (Game.core) {
            Game.core.showDamageAt(this.x, this.y - 40, `Unlocked: ${WEAPONS[weaponType][0].name}!`);
        }
    }

    // Move the player
    move(dx, dy, deltaTime) {
        if (this.isDead) return;

        // Calculate movement speed
        const baseSpeed = Game.core ? Game.core.playerPxSpeed : 82.5;
        const speed = baseSpeed * this.moveSpeed * deltaTime / 1000;

        // Normalize direction if needed
        const length = Math.sqrt(dx * dx + dy * dy);
        if (length > 0) {
            dx /= length;
            dy /= length;

            // Update last face direction
            this.lastFaceDirection.x = dx;
            this.lastFaceDirection.y = dy;
        }

        // Update velocity
        this.velocity.x = dx * speed;
        this.velocity.y = dy * speed;

        // Update position
        this.x += this.velocity.x;
        this.y += this.velocity.y;

        // Update position history
        this.historyIndex = this.historyIndex < this.maxHistory ? this.historyIndex + 1 : 0;
        this.posHistory[this.historyIndex].x = this.x;
        this.posHistory[this.historyIndex].y = this.y;

        // Update world box collider
        this.worldBoxCollider.x = this.x - Game.core.canvas.width / 2;
        this.worldBoxCollider.y = this.y - Game.core.canvas.height / 2;
        this.worldBoxCollider.width = Game.core.canvas.width;
        this.worldBoxCollider.height = Game.core.canvas.height;
    }

    // Update the player
    update(deltaTime) {
        if (this.isDead) return;

        // Update invulnerability time
        if (this.invulTime > 0) {
            this.invulTime -= deltaTime;
            if (this.invulTime <= 0) {
                this.invulTime = 0;
                this.isInvul = false;
            }
        }
    }

    // Draw the player
    draw(ctx, camera, sprites) {
        if (!sprites) return;

        // Calculate screen position
        const screenX = this.x - camera.x + camera.width / 2;
        const screenY = this.y - camera.y + camera.height / 2;

        // Get the sprite
        const sprite = sprites[this.spriteName];
        if (!sprite) return;

        ctx.save();

        // Apply flipping based on direction
        if (this.velocity.x < 0) {
            ctx.scale(-1, 1);
            ctx.translate(-screenX * 2, 0);
        }

        // Apply invulnerability effect
        if (this.isInvul) {
            ctx.globalAlpha = 0.5 + Math.sin(Date.now() / 100) * 0.3;
        }

        // Apply damage effect
        if (this.receivingDamage) {
            ctx.fillStyle = '#ffffff';
            ctx.globalCompositeOperation = 'lighter';
        }

        // Apply god mode effect (golden glow)
        if (this.godMode) {
            ctx.shadowColor = 'gold';
            ctx.shadowBlur = 10;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
        }

        // Draw the sprite
        const scale = 1; // Adjust scale as needed
        ctx.drawImage(
            sprite,
            screenX - sprite.width / 2 * scale,
            screenY - sprite.height / 2 * scale,
            sprite.width * scale,
            sprite.height * scale
        );

        ctx.restore();

        // Draw health bar
        this.drawHealthBar(ctx, screenX, screenY);

        // Draw god mode indicator
        if (this.godMode) {
            ctx.font = 'bold 12px Arial';
            ctx.fillStyle = 'gold';
            ctx.textAlign = 'center';
            ctx.fillText('GOD MODE', screenX, screenY - 40);
        }

        // Draw debug collision circle
        if (Game.core && Game.core.debug) {
            ctx.beginPath();
            ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(0, 255, 0, 0.5)';
            ctx.stroke();
        }
    }

    // Draw the health bar
    drawHealthBar(ctx, x, y) {
        const barWidth = 50;
        const barHeight = 4;
        const barX = x - barWidth / 2;
        const barY = y + 30;

        // Background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(barX, barY, barWidth, barHeight);

        // Health
        const healthPercent = this.hp / this.maxHp;
        ctx.fillStyle = healthPercent > 0.5 ? 'rgba(0, 255, 0, 0.8)' :
                        healthPercent > 0.25 ? 'rgba(255, 255, 0, 0.8)' :
                        'rgba(255, 0, 0, 0.8)';
        ctx.fillRect(barX, barY, barWidth * healthPercent, barHeight);
    }
}
