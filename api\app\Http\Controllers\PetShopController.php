<?php

namespace App\Http\Controllers;

use App\Models\PetTemplate;
use App\Models\TelegramUser;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PetShopController extends Controller
{
    /**
     * Get available pets for purchase
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $category = $request->query('category');
        $rarity = $request->query('rarity');
        
        $query = PetTemplate::active()->availableForPurchase();
        
        if ($category) {
            $query->byCategory($category);
        }
        
        if ($rarity) {
            $query->byRarity($rarity);
        }
        
        $petTemplates = $query->orderBy('sort_order')
                             ->orderBy('rarity')
                             ->orderBy('name')
                             ->get();
        
        $availablePets = $petTemplates->map(function($template) use ($user) {
            $isOwned = $user->pets()->where('pet_template_id', $template->id)->exists();
            $canPurchase = !$isOwned && $template->canBePurchasedBy($user);
            
            return [
                'id' => $template->id,
                'name' => $template->name,
                'category' => $template->category,
                'rarity' => $template->rarity,
                'rarity_color' => $template->rarity_color,
                'description' => $template->description,
                'image_url' => $template->image_url,
                'animation_url' => $template->animation_url,
                'coin_cost' => $template->coin_cost,
                'gem_cost' => $template->gem_cost,
                'prize_tree_unlock_level' => $template->prize_tree_unlock_level,
                'is_premium_only' => $template->is_premium_only,
                'is_owned' => $isOwned,
                'can_purchase' => $canPurchase,
                'mystery_box_unlocks' => $template->mystery_box_unlocks,
                'collectible_reward_id' => $template->collectible_reward_id,
                'unlock_reason' => $this->getUnlockReason($template, $user)
            ];
        });
        
        return response()->json([
            'success' => true,
            'pets' => $availablePets,
            'categories' => ['shadow', 'undead', 'demon', 'spirit', 'beast'],
            'rarities' => ['common', 'rare', 'epic', 'legendary', 'mythic'],
            'user_balance' => [
                'balance' => $user->balance,
                'gems' => $user->gems ?? 0
            ]
        ]);
    }

    /**
     * Get featured pets (rotating selection)
     */
    public function getFeaturedPets(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Get 3 random pets that user doesn't own
        $ownedPetTemplateIds = $user->pets()->pluck('pet_template_id');
        
        $featuredPets = PetTemplate::active()
                                  ->availableForPurchase()
                                  ->whereNotIn('id', $ownedPetTemplateIds)
                                  ->inRandomOrder()
                                  ->limit(3)
                                  ->get();
        
        return response()->json([
            'success' => true,
            'featured_pets' => $featuredPets->map(function($template) use ($user) {
                return [
                    'id' => $template->id,
                    'name' => $template->name,
                    'category' => $template->category,
                    'rarity' => $template->rarity,
                    'rarity_color' => $template->rarity_color,
                    'image_url' => $template->image_url,
                    'coin_cost' => $template->coin_cost,
                    'gem_cost' => $template->gem_cost,
                    'can_purchase' => $template->canBePurchasedBy($user)
                ];
            })
        ]);
    }

    /**
     * Get pet details by ID
     */
    public function show(Request $request, PetTemplate $petTemplate): JsonResponse
    {
        $user = $request->user();
        $isOwned = $user->pets()->where('pet_template_id', $petTemplate->id)->exists();
        
        return response()->json([
            'success' => true,
            'pet' => [
                'id' => $petTemplate->id,
                'name' => $petTemplate->name,
                'category' => $petTemplate->category,
                'rarity' => $petTemplate->rarity,
                'rarity_color' => $petTemplate->rarity_color,
                'description' => $petTemplate->description,
                'image_url' => $petTemplate->image_url,
                'animation_url' => $petTemplate->animation_url,
                'coin_cost' => $petTemplate->coin_cost,
                'gem_cost' => $petTemplate->gem_cost,
                'prize_tree_unlock_level' => $petTemplate->prize_tree_unlock_level,
                'is_premium_only' => $petTemplate->is_premium_only,
                'base_happiness' => $petTemplate->base_happiness,
                'max_happiness' => $petTemplate->max_happiness,
                'happiness_decay_rate' => $petTemplate->happiness_decay_rate,
                'max_level' => $petTemplate->max_level,
                'evolution_levels' => $petTemplate->evolution_levels,
                'evolution_images' => $petTemplate->evolution_images,
                'mystery_box_unlocks' => $petTemplate->mystery_box_unlocks,
                'collectible_reward_id' => $petTemplate->collectible_reward_id,
                'is_owned' => $isOwned,
                'can_purchase' => !$isOwned && $petTemplate->canBePurchasedBy($user),
                'unlock_reason' => $this->getUnlockReason($petTemplate, $user)
            ]
        ]);
    }

    /**
     * Get pets by category
     */
    public function getByCategory(Request $request, string $category): JsonResponse
    {
        $user = $request->user();
        
        $petTemplates = PetTemplate::active()
                                  ->byCategory($category)
                                  ->orderBy('rarity')
                                  ->orderBy('sort_order')
                                  ->get();
        
        $pets = $petTemplates->map(function($template) use ($user) {
            $isOwned = $user->pets()->where('pet_template_id', $template->id)->exists();
            
            return [
                'id' => $template->id,
                'name' => $template->name,
                'rarity' => $template->rarity,
                'rarity_color' => $template->rarity_color,
                'image_url' => $template->image_url,
                'coin_cost' => $template->coin_cost,
                'gem_cost' => $template->gem_cost,
                'is_owned' => $isOwned,
                'can_purchase' => !$isOwned && $template->canBePurchasedBy($user)
            ];
        });
        
        return response()->json([
            'success' => true,
            'category' => $category,
            'pets' => $pets
        ]);
    }

    /**
     * Get unlock reason for a pet template
     */
    private function getUnlockReason(PetTemplate $template, TelegramUser $user): ?string
    {
        if ($template->canBePurchasedBy($user)) {
            return null;
        }
        
        if ($template->prize_tree_unlock_level) {
            $userProgress = $user->prizeTreeProgress();
            $currentLevel = $userProgress ? $userProgress->current_level : 0;
            
            if ($currentLevel < $template->prize_tree_unlock_level) {
                return "Requires Prize Tree level {$template->prize_tree_unlock_level} (current: {$currentLevel})";
            }
        }
        
        return 'Requirements not met';
    }
}
