# Database Seeders Implementation

## Overview
This document covers the creation of database seeders to populate the pet system with initial data including pet templates, collectibles, and mystery box configurations.

## Implementation Time: 2-3 days
## Complexity: Medium
## Dependencies: Database schema completed

## Seeder Files

### Pet Templates Seeder
```php
<?php
// File: api/database/seeders/PetTemplateSeeder.php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PetTemplateSeeder extends Seeder
{
    public function run(): void
    {
        $petTemplates = [
            // Shadow Category
            [
                'name' => 'Shadow Wolf',
                'category' => 'shadow',
                'rarity' => 'common',
                'description' => 'A mysterious wolf that emerges from the shadows, loyal to those who show it kindness.',
                'image_url' => '/images/pets/shadow/shadow_wolf.png',
                'animation_url' => '/images/pets/shadow/shadow_wolf_anim.gif',
                'coin_cost' => 1000,
                'gem_cost' => 10,
                'prize_tree_unlock_level' => 5,
                'mystery_box_unlocks' => json_encode(['common_shadow']),
                'collectible_reward_id' => 'shadow_essence',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/shadow/shadow_wolf_evo1.png',
                    '/images/pets/shadow/shadow_wolf_evo2.png',
                    '/images/pets/shadow/shadow_wolf_evo3.png',
                    '/images/pets/shadow/shadow_wolf_evo4.png'
                ])
            ],
            [
                'name' => 'Dark Raven',
                'category' => 'shadow',
                'rarity' => 'rare',
                'description' => 'An intelligent raven with eyes that gleam like obsidian, harboring ancient secrets.',
                'image_url' => '/images/pets/shadow/dark_raven.png',
                'animation_url' => '/images/pets/shadow/dark_raven_anim.gif',
                'coin_cost' => 5000,
                'gem_cost' => 50,
                'prize_tree_unlock_level' => 15,
                'mystery_box_unlocks' => json_encode(['rare_shadow']),
                'collectible_reward_id' => 'raven_feather',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/shadow/dark_raven_evo1.png',
                    '/images/pets/shadow/dark_raven_evo2.png',
                    '/images/pets/shadow/dark_raven_evo3.png',
                    '/images/pets/shadow/dark_raven_evo4.png'
                ])
            ],
            [
                'name' => 'Void Stalker',
                'category' => 'shadow',
                'rarity' => 'epic',
                'description' => 'A creature born from the void itself, capable of bending shadows to its will.',
                'image_url' => '/images/pets/shadow/void_stalker.png',
                'animation_url' => '/images/pets/shadow/void_stalker_anim.gif',
                'coin_cost' => 25000,
                'gem_cost' => 200,
                'prize_tree_unlock_level' => 30,
                'mystery_box_unlocks' => json_encode(['epic_shadow']),
                'collectible_reward_id' => 'void_crystal',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/shadow/void_stalker_evo1.png',
                    '/images/pets/shadow/void_stalker_evo2.png',
                    '/images/pets/shadow/void_stalker_evo3.png',
                    '/images/pets/shadow/void_stalker_evo4.png'
                ])
            ],
            [
                'name' => 'Shadow Lord',
                'category' => 'shadow',
                'rarity' => 'legendary',
                'description' => 'The master of all shadows, commanding respect from lesser dark creatures.',
                'image_url' => '/images/pets/shadow/shadow_lord.png',
                'animation_url' => '/images/pets/shadow/shadow_lord_anim.gif',
                'coin_cost' => 100000,
                'gem_cost' => 800,
                'prize_tree_unlock_level' => 50,
                'mystery_box_unlocks' => json_encode(['legendary_shadow']),
                'collectible_reward_id' => 'shadow_crown',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/shadow/shadow_lord_evo1.png',
                    '/images/pets/shadow/shadow_lord_evo2.png',
                    '/images/pets/shadow/shadow_lord_evo3.png',
                    '/images/pets/shadow/shadow_lord_evo4.png'
                ])
            ],
            [
                'name' => 'Nightmare King',
                'category' => 'shadow',
                'rarity' => 'mythic',
                'description' => 'The ultimate shadow entity, ruler of nightmares and master of the dark realm.',
                'image_url' => '/images/pets/shadow/nightmare_king.png',
                'animation_url' => '/images/pets/shadow/nightmare_king_anim.gif',
                'coin_cost' => 500000,
                'gem_cost' => 3000,
                'prize_tree_unlock_level' => 75,
                'is_premium_only' => true,
                'mystery_box_unlocks' => json_encode(['mythic_shadow']),
                'collectible_reward_id' => 'nightmare_scroll',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/shadow/nightmare_king_evo1.png',
                    '/images/pets/shadow/nightmare_king_evo2.png',
                    '/images/pets/shadow/nightmare_king_evo3.png',
                    '/images/pets/shadow/nightmare_king_evo4.png'
                ])
            ],

            // Undead Category
            [
                'name' => 'Skeleton Minion',
                'category' => 'undead',
                'rarity' => 'common',
                'description' => 'A loyal skeletal servant, animated by dark magic and bound to serve.',
                'image_url' => '/images/pets/undead/skeleton_minion.png',
                'animation_url' => '/images/pets/undead/skeleton_minion_anim.gif',
                'coin_cost' => 1000,
                'gem_cost' => 10,
                'prize_tree_unlock_level' => 5,
                'mystery_box_unlocks' => json_encode(['common_undead']),
                'collectible_reward_id' => 'bone_fragment',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/undead/skeleton_minion_evo1.png',
                    '/images/pets/undead/skeleton_minion_evo2.png',
                    '/images/pets/undead/skeleton_minion_evo3.png',
                    '/images/pets/undead/skeleton_minion_evo4.png'
                ])
            ],
            [
                'name' => 'Zombie Hound',
                'category' => 'undead',
                'rarity' => 'rare',
                'description' => 'A reanimated hound with an insatiable hunger and unwavering loyalty.',
                'image_url' => '/images/pets/undead/zombie_hound.png',
                'animation_url' => '/images/pets/undead/zombie_hound_anim.gif',
                'coin_cost' => 5000,
                'gem_cost' => 50,
                'prize_tree_unlock_level' => 15,
                'mystery_box_unlocks' => json_encode(['rare_undead']),
                'collectible_reward_id' => 'decay_potion',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/undead/zombie_hound_evo1.png',
                    '/images/pets/undead/zombie_hound_evo2.png',
                    '/images/pets/undead/zombie_hound_evo3.png',
                    '/images/pets/undead/zombie_hound_evo4.png'
                ])
            ],
            [
                'name' => 'Lich Apprentice',
                'category' => 'undead',
                'rarity' => 'epic',
                'description' => 'A young lich learning the dark arts of necromancy and undeath.',
                'image_url' => '/images/pets/undead/lich_apprentice.png',
                'animation_url' => '/images/pets/undead/lich_apprentice_anim.gif',
                'coin_cost' => 25000,
                'gem_cost' => 200,
                'prize_tree_unlock_level' => 30,
                'mystery_box_unlocks' => json_encode(['epic_undead']),
                'collectible_reward_id' => 'necromancy_tome',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/undead/lich_apprentice_evo1.png',
                    '/images/pets/undead/lich_apprentice_evo2.png',
                    '/images/pets/undead/lich_apprentice_evo3.png',
                    '/images/pets/undead/lich_apprentice_evo4.png'
                ])
            ],
            [
                'name' => 'Death Knight',
                'category' => 'undead',
                'rarity' => 'legendary',
                'description' => 'A fallen paladin risen as an undead champion of darkness.',
                'image_url' => '/images/pets/undead/death_knight.png',
                'animation_url' => '/images/pets/undead/death_knight_anim.gif',
                'coin_cost' => 100000,
                'gem_cost' => 800,
                'prize_tree_unlock_level' => 50,
                'mystery_box_unlocks' => json_encode(['legendary_undead']),
                'collectible_reward_id' => 'deaths_blade',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/undead/death_knight_evo1.png',
                    '/images/pets/undead/death_knight_evo2.png',
                    '/images/pets/undead/death_knight_evo3.png',
                    '/images/pets/undead/death_knight_evo4.png'
                ])
            ],
            [
                'name' => 'Bone Dragon',
                'category' => 'undead',
                'rarity' => 'mythic',
                'description' => 'An ancient dragon reborn as an undead monstrosity of immense power.',
                'image_url' => '/images/pets/undead/bone_dragon.png',
                'animation_url' => '/images/pets/undead/bone_dragon_anim.gif',
                'coin_cost' => 500000,
                'gem_cost' => 3000,
                'prize_tree_unlock_level' => 75,
                'is_premium_only' => true,
                'mystery_box_unlocks' => json_encode(['mythic_undead']),
                'collectible_reward_id' => 'dragon_soul_scroll',
                'evolution_levels' => json_encode([10, 25, 50, 100]),
                'evolution_images' => json_encode([
                    '/images/pets/undead/bone_dragon_evo1.png',
                    '/images/pets/undead/bone_dragon_evo2.png',
                    '/images/pets/undead/bone_dragon_evo3.png',
                    '/images/pets/undead/bone_dragon_evo4.png'
                ])
            ]
        ];

        foreach ($petTemplates as $template) {
            DB::table('pet_templates')->insert(array_merge($template, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }
    }
}
```

### Collectible Templates Seeder
```php
<?php
// File: api/database/seeders/CollectibleTemplateSeeder.php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CollectibleTemplateSeeder extends Seeder
{
    public function run(): void
    {
        $collectibles = [
            // Shadow Collection
            [
                'collectible_id' => 'shadow_essence',
                'name' => 'Shadow Essence',
                'type' => 'essence',
                'category' => 'shadow',
                'rarity' => 'common',
                'description' => 'A swirling mass of dark energy, the basic component of shadow magic.',
                'image_url' => '/images/collectibles/shadow/shadow_essence.png',
                'collection_set_id' => 'shadow_collection',
                'set_position' => 1,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'shadow_wolf'
            ],
            [
                'collectible_id' => 'raven_feather',
                'name' => 'Raven Feather',
                'type' => 'artifact',
                'category' => 'shadow',
                'rarity' => 'rare',
                'description' => 'A mystical feather that shimmers with otherworldly darkness.',
                'image_url' => '/images/collectibles/shadow/raven_feather.png',
                'collection_set_id' => 'shadow_collection',
                'set_position' => 2,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'dark_raven'
            ],
            [
                'collectible_id' => 'void_crystal',
                'name' => 'Void Crystal',
                'type' => 'relic',
                'category' => 'shadow',
                'rarity' => 'epic',
                'description' => 'A crystal formed in the depths of the void, pulsing with dark energy.',
                'image_url' => '/images/collectibles/shadow/void_crystal.png',
                'collection_set_id' => 'shadow_collection',
                'set_position' => 3,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'void_stalker'
            ],
            [
                'collectible_id' => 'shadow_crown',
                'name' => 'Shadow Crown',
                'type' => 'trophy',
                'category' => 'shadow',
                'rarity' => 'legendary',
                'description' => 'The crown of the Shadow Lord, symbol of dominion over darkness.',
                'image_url' => '/images/collectibles/shadow/shadow_crown.png',
                'collection_set_id' => 'shadow_collection',
                'set_position' => 4,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'shadow_lord'
            ],
            [
                'collectible_id' => 'nightmare_scroll',
                'name' => 'Nightmare Scroll',
                'type' => 'scroll',
                'category' => 'shadow',
                'rarity' => 'mythic',
                'description' => 'Ancient scroll containing the forbidden knowledge of nightmare creation.',
                'image_url' => '/images/collectibles/shadow/nightmare_scroll.png',
                'collection_set_id' => 'shadow_collection',
                'set_position' => 5,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'nightmare_king'
            ],

            // Undead Collection
            [
                'collectible_id' => 'bone_fragment',
                'name' => 'Bone Fragment',
                'type' => 'essence',
                'category' => 'undead',
                'rarity' => 'common',
                'description' => 'A piece of ancient bone, still radiating with necromantic energy.',
                'image_url' => '/images/collectibles/undead/bone_fragment.png',
                'collection_set_id' => 'undead_collection',
                'set_position' => 1,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'skeleton_minion'
            ],
            [
                'collectible_id' => 'decay_potion',
                'name' => 'Decay Potion',
                'type' => 'artifact',
                'category' => 'undead',
                'rarity' => 'rare',
                'description' => 'A bubbling concoction that accelerates the process of decay.',
                'image_url' => '/images/collectibles/undead/decay_potion.png',
                'collection_set_id' => 'undead_collection',
                'set_position' => 2,
                'unlock_source' => 'pet_purchase',
                'unlock_requirement' => 'zombie_hound'
            ]
        ];

        foreach ($collectibles as $collectible) {
            DB::table('collectible_templates')->insert(array_merge($collectible, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }
    }
}
```

### Running Seeders

#### Step 1: Create Seeder Files
```bash
cd api
php artisan make:seeder PetTemplateSeeder
php artisan make:seeder CollectibleTemplateSeeder
php artisan make:seeder MysteryBoxTypeSeeder
php artisan make:seeder CollectionSetSeeder
```

#### Step 2: Update DatabaseSeeder
```php
<?php
// File: api/database/seeders/DatabaseSeeder.php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        $this->call([
            // Existing seeders
            LevelSeeder::class,
            DailyTaskSeeder::class,
            TaskTypeSeeder::class,
            TaskSeeder::class,
            MissionTypeSeeder::class,
            MissionSeeder::class,
            ReferralTaskSeeder::class,
            PrizeTreeSeeder::class,
            
            // New pet system seeders
            PetTemplateSeeder::class,
            CollectibleTemplateSeeder::class,
            MysteryBoxTypeSeeder::class,
            CollectionSetSeeder::class,
        ]);
    }
}
```

#### Step 3: Run Seeders
```bash
php artisan db:seed --class=PetTemplateSeeder
php artisan db:seed --class=CollectibleTemplateSeeder
```

## Acceptance Criteria
- [ ] All pet templates seeded (25 pets across 5 categories)
- [ ] All collectible templates seeded (125+ collectibles)
- [ ] Mystery box types configured
- [ ] Collection sets defined
- [ ] Seeder can be run multiple times safely

## Next Steps
1. Create Eloquent models
2. Implement API endpoints
3. Create frontend components
4. Test data integrity

## Troubleshooting
- Use DB::table()->updateOrInsert() for idempotent seeding
- Ensure image URLs are accessible
- Verify JSON data is properly formatted
