<?php

namespace App\Http\Controllers;

use App\Models\DailyTask;
use App\Models\Level;
use App\Models\MissionType;
use App\Models\TelegramUser;
use App\Services\AchievementPointService;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClickerController extends Controller
{
    protected $achievementPointService;
    
    public function __construct(AchievementPointService $achievementPointService)
    {
        $this->achievementPointService = $achievementPointService;
    }
    
    public function sync(Request $request)
    {
        $user = $request->user();

        // Check if booster pack has expired
        $this->checkBoosterPackExpiration($user);

        // Calculate passive earnings for the user and increase balance
        $passiveEarnings = $user->calcPassiveEarning();

        // Update login streak
        $user->updateLoginStreak();

        // Check and reset daily booster if it's a new day
        $user->checkAndResetDailyBooster();

        // Restore energy
        $restoredEnergy = $user->restoreEnergy();

        // Load the level relationship
        $user->load('level');

        $canUseDailyBooster = $user->canUseDailyBooster();

        $totalDailyRewards = DailyTask::sum('reward_coins');

        $levels = Level::all();

        $missionTypes = MissionType::all();

        // Get user's achievement points
        $achievementPoints = \App\Models\UserAchievementPoint::firstOrCreate(
            ['telegram_user_id' => $user->id],
            ['total_earned' => 0, 'total_spent' => 0]
        );

        try {
            return response()->json([
                'user' => array_merge($user->toArray(), [
                    'level' => $user->level,
                    'achievement_points' => $achievementPoints->available_points
                ]),
                'passive_earnings' => $passiveEarnings,
                'restored_energy' => $restoredEnergy,
                'can_use_daily_booster' => $canUseDailyBooster,
                'next_daily_booster_at' => $canUseDailyBooster ? null : $user->last_daily_booster_use?->addHour(),
                'daily_booster_uses' => $user->daily_booster_uses,
                'total_daily_rewards' => $totalDailyRewards,
                'levels' => $levels,
                'mission_types' => $missionTypes,
                'next_multi_tap_cost' => $this->getBoosterCost($user, 'multi_tap'),
                'next_energy_limit_cost' => $this->getBoosterCost($user, 'energy_limit'),
            ]);
        } catch (\Exception $e) {
            \Log::error("Error in ClickerController::sync: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return response()->json(['error' => 'Sync failed'], 500);
        }
    }

    public function tap(Request $request)
    {
        $validated = $request->validate([
            'count' => 'required|integer|min:1',
        ]);

        $user = $request->user();

        $earned = $user->tap($validated['count']);
        
        // Check for tap milestones and award achievement points
        $this->checkTapMilestones($user, $validated['count']);

        return response()->json([
            'success' => true,
            'earned' => $earned,
            'balance' => $user->balance,
            'available_energy' => $user->available_energy,
        ]);
    }
    
    /**
     * Check for tap milestones and award achievement points
     */
    private function checkTapMilestones($user, $tapCount)
    {
        // Get or create tap stats
        $tapStats = \App\Models\TapStat::firstOrCreate(
            ['telegram_user_id' => $user->id],
            ['total_taps' => 0]
        );
        
        // Update total taps
        $oldTotal = $tapStats->total_taps;
        $tapStats->total_taps += $tapCount;
        $tapStats->save();
        
        // Define milestones
        $milestones = [100, 500, 1000, 5000, 10000, 50000, 100000];
        
        // Check if any milestones were crossed
        foreach ($milestones as $milestone) {
            if ($oldTotal < $milestone && $tapStats->total_taps >= $milestone) {
                // Award achievement points
                $points = 1;
                if ($milestone >= 1000) $points = 2;
                if ($milestone >= 10000) $points = 3;
                if ($milestone >= 100000) $points = 5;
                
                $this->achievementPointService->awardPoints(
                    $user->id,
                    $points,
                    'tap_milestone',
                    $milestone,
                    "Reached {$milestone} total taps"
                );
            }
        }
    }

    public function buyBoosterPack(Request $request)
    {
        $request->validate([
            'booster_pack' => 'required|in:booster_pack_2x,booster_pack_3x,booster_pack_7x',
        ]);

        $user = $request->user();
        $boosterPack = $request->input('booster_pack');

        try {
            DB::transaction(function () use ($user, $boosterPack) {
                $currentTime = new DateTime();
                $boosterActiveUntil = new DateTime($user->booster_pack_active_until);

                if ($boosterActiveUntil > $currentTime) {
                    if ($this->isValidUpgrade($user, $boosterPack)) {
                        $this->deactivateCurrentBooster($user);
                    } else {
                        throw new \InvalidArgumentException("Cannot downgrade or repurchase the same booster pack while one is active.");
                    }
                }

                $this->activateBoosterPack($user, $boosterPack);
                $user->save();
            });

            return response()->json([
                'success' => true,
                'message' => 'Booster pack purchased successfully',
                'booster_pack_active_until' => $user->booster_pack_active_until,
                'balance' => $user->balance
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    private function isValidUpgrade($user, $newBoosterPack)
    {
        $currentBooster = null;
        if ($user->booster_pack_2x) $currentBooster = 'booster_pack_2x';
        if ($user->booster_pack_3x) $currentBooster = 'booster_pack_3x';
        if ($user->booster_pack_7x) $currentBooster = 'booster_pack_7x';

        if ($currentBooster === $newBoosterPack) {
            return false; // Cannot repurchase the same booster
        }

        $boosterValues = [
            'booster_pack_2x' => 2,
            'booster_pack_3x' => 3,
            'booster_pack_7x' => 7
        ];

        return $boosterValues[$newBoosterPack] > $boosterValues[$currentBooster];
    }

    private function deactivateCurrentBooster($user)
    {
        $user->booster_pack_2x = 0;
        $user->booster_pack_3x = 0;
        $user->booster_pack_7x = 0;
    }

    private function activateBoosterPack($user, $boosterPack)
    {
        $this->deactivateCurrentBooster($user);

        switch ($boosterPack) {
            case 'booster_pack_2x':
                $user->booster_pack_2x = 1;
                $duration = 24; // 24 hours
                break;
            case 'booster_pack_3x':
                $user->booster_pack_3x = 1;
                $duration = 24; // 24 hours
                break;
            case 'booster_pack_7x':
                $user->booster_pack_7x = 1;
                $duration = 24; // 24 hours
                break;
            default:
                throw new \InvalidArgumentException("Invalid booster pack: {$boosterPack}");
        }

        $user->booster_pack_active_until = (new DateTime())->modify("+{$duration} hours");
    }

    private function checkBoosterPackExpiration($user)
    {
        if ($user->booster_pack_active_until) {
            $expirationDate = new DateTime($user->booster_pack_active_until);
            $currentDate = new DateTime();

            if ($currentDate > $expirationDate) {
                // Booster pack has expired, deactivate it
                $user->booster_pack_2x = 0;
                $user->booster_pack_3x = 0;
                $user->booster_pack_7x = 0;
                $user->booster_pack_active_until = null;
                $user->save();
            }
        }
    }

    public function buyBooster(Request $request)
    {
        $request->validate([
            'booster_type' => 'required|in:multi_tap,energy_limit',
        ]);

        $user = $request->user();
        $boosterType = $request->input('booster_type');
        $cost = $this->getBoosterCost($user, $boosterType);

        if ($user->balance < $cost) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient balance',
            ], 400);
        }

        try {
            DB::transaction(function () use ($user, $boosterType, $cost) {
                //$user->balance -= $cost;

                switch ($boosterType) {
                    case 'multi_tap':
                        $user->multi_tap_level++;
                        $user->earn_per_tap++;
                        break;
                    case 'energy_limit':
                        $user->energy_limit_level++;
                        $user->max_energy += 500;
                        break;
                    default:
                        throw new \InvalidArgumentException("Invalid booster type: {$boosterType}");
                }

                $user->save();
            });

            return response()->json([
                'success' => true,
                'message' => 'Booster purchased successfully',
                'balance' => $user->balance,
                'earn_per_tap' => $user->earn_per_tap,
                'max_energy' => $user->max_energy,
                'multi_tap_level' => $user->multi_tap_level,
                'energy_limit_level' => $user->energy_limit_level,
                'next_multi_tap_cost' => $this->getBoosterCost($user, 'multi_tap'),
                'next_energy_limit_cost' => $this->getBoosterCost($user, 'energy_limit'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while purchasing the booster.',
            ], 500);
        }
    }

    private function getBoosterCost($user, $boosterType)
    {
        $boostLevel = $boosterType === 'multi_tap' ? $user->multi_tap_level : $user->energy_limit_level;
        return 1 + ($boostLevel - 1);  // Cost of TON increases by one each level
    }

    public function listDailyTasks(Request $request)
    {
        $user = $request->user();

        $tasks = DailyTask::orderBy('required_login_streak')
            ->get()
            ->map(function ($task) use ($user) {
                $completed = $user->dailyTasks()
                    ->where('daily_task_id', $task->id)
                    ->exists();

                return [
                    'id' => $task->id,
                    'required_login_streak' => $task->required_login_streak,
                    'reward_coins' => $task->reward_coins,
                    'completed' => $completed,
                    'available' => $user->login_streak >= $task->required_login_streak,
                ];
            });

        return response()->json([
            'tasks' => $tasks,
            'login_streak' => $user->login_streak,
        ]);
    }

    public function useDailyBooster(Request $request)
    {
        $user = $request->user();

        if ($user->useDailyBooster()) {
            return response()->json([
                'success' => true,
                'message' => 'Daily booster used successfully',
                'current_energy' => $user->max_energy,
                'daily_booster_uses' => $user->daily_booster_uses,
                'next_available_at' => $user->last_daily_booster_use->addHour(),
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Cannot use daily booster at this time',
                'daily_booster_uses' => $user->daily_booster_uses,
                'next_available_at' => $user->last_daily_booster_use ? $user->last_daily_booster_use->addHour() : null,
            ], 400);
        }
    }

    public function claimDailyTaskReward(Request $request)
    {
        $user = $request->user();

        $task = DailyTask::where('required_login_streak', '<=', $user->login_streak)
            ->whereDoesntHave('telegramUsers', function ($query) use ($user) {
                $query->where('telegram_user_id', $user->id);
            })
            ->first();

        if ($task) {
            DB::transaction(function () use ($task, $user) {
                $user->increment('balance', $task->reward_coins);
                $user->dailyTasks()->attach($task->id, [
                    'completed' => true,
                    'updated_at' => now()
                ]);
                
                // Award achievement points for completing daily tasks
                $this->achievementPointService->awardPoints(
                    $user->id,
                    1, // Award 1 point for each daily task
                    'daily_task_complete',
                    $task->id,
                    "Completed daily task: Day {$task->required_login_streak}"
                );
            });

            return response()->json([
                'success' => true,
                'message' => 'Daily task reward claimed successfully',
                'balance' => $user->balance,
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'No available daily task rewards to claim',
        ], 400);
    }

    public function listLeaderboard(Request $request)
    {
        $user = $request->user();

        $leaderboard = TelegramUser::orderBy('balance', 'desc')
            ->select('id', 'telegram_id', 'first_name', 'last_name', 'username', 'balance')
            ->limit(100)
            ->get();

        $userRank = TelegramUser::where('balance', '>', $user->balance)->count() + 1;

        return response()->json([
            'leaderboard' => $leaderboard,
            'user_rank' => $userRank,
        ]);
    }
}
