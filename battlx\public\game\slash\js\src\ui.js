// UI classes for handling game UI elements

// MainUI class for handling the main game UI
class MainUI {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.weaponIcons = [];
        this.powerUpIcons = [];
        this.survivedSeconds = 0;
        this.kills = 0;
        this.coins = 0;
    }

    // Set the survived time
    setSurvivedSeconds(seconds) {
        this.survivedSeconds = seconds;
    }

    // Set the kill count
    setKills(kills) {
        this.kills = kills;
    }

    // Set the coin count
    setCoins(coins) {
        this.coins = coins;
    }

    // Update the kill count display
    updateKills() {
        if (Game.core && Game.core.playerOptions) {
            // Ensure runEnemies is a number
            if (typeof Game.core.playerOptions.runEnemies !== 'number') {
                Game.core.playerOptions.runEnemies = 0;
            }
            this.kills = Game.core.playerOptions.runEnemies;

        }
    }

    // Update the coin count display
    updateCoins() {
        if (Game.core && Game.core.playerOptions) {
            this.coins = Game.core.playerOptions.coins;
        }
    }

    // Add a weapon icon
    addWeaponIcon(weaponType) {
        const weapon = WEAPONS[weaponType] ? WEAPONS[weaponType][0] : null;
        if (!weapon) return;

        this.weaponIcons.push({
            type: weaponType,
            level: 1,
            name: weapon.name,
            frameName: weapon.frameName
        });
    }

    // Update a weapon icon
    updateWeaponIcon(weaponType, level) {
        const icon = this.weaponIcons.find(icon => icon.type === weaponType);
        if (icon) {
            icon.level = level;
        }
    }

    // Add a power-up icon
    addPowerUpIcon(powerUpType) {
        this.powerUpIcons.push({
            type: powerUpType,
            level: 1,
            name: this.getPowerUpName(powerUpType),
            frameName: this.getPowerUpFrameName(powerUpType)
        });
    }

    // Update a power-up icon
    updatePowerUpIcon(powerUpType, level) {
        const icon = this.powerUpIcons.find(icon => icon.type === powerUpType);
        if (icon) {
            icon.level = level;
        } else {
            this.addPowerUpIcon(powerUpType);
        }
    }

    // Get power-up name
    getPowerUpName(powerUpType) {
        switch (powerUpType) {
            case WeaponType.AMOUNT:
                return 'Amount';
            case WeaponType.AREA:
                return 'Area';
            case WeaponType.COOLDOWN:
                return 'Cooldown';
            case WeaponType.SPEED:
                return 'Speed';
            case WeaponType.DURATION:
                return 'Duration';
            case WeaponType.ARMOR:
                return 'Armor';
            case WeaponType.MAXHEALTH:
                return 'Max Health';
            case WeaponType.GROWTH:
                return 'Growth';
            case WeaponType.MOVESPEED:
                return 'Move Speed';
            case WeaponType.LUCK:
                return 'Luck';
            default:
                return 'Unknown';
        }
    }

    // Get power-up frame name
    getPowerUpFrameName(powerUpType) {
        switch (powerUpType) {
            case WeaponType.AMOUNT:
                return 'amount_powerup.png';
            case WeaponType.AREA:
                return 'area_powerup.png';
            case WeaponType.COOLDOWN:
                return 'cooldown_powerup.png';
            case WeaponType.SPEED:
                return 'speed_powerup.png';
            case WeaponType.DURATION:
                return 'duration_powerup.png';
            case WeaponType.ARMOR:
                return 'armor_powerup.png';
            case WeaponType.MAXHEALTH:
                return 'maxhealth_powerup.png';
            case WeaponType.GROWTH:
                return 'growth_powerup.png';
            case WeaponType.MOVESPEED:
                return 'movespeed_powerup.png';
            case WeaponType.LUCK:
                return 'luck_powerup.png';
            default:
                return '';
        }
    }

    // Draw the UI
    draw(sprites) {
        // Draw survived time
        this.ctx.font = 'bold 20px Arial';
        this.ctx.fillStyle = 'black';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
            `Survived ${formatTime(this.survivedSeconds)}`,
            this.canvas.width / 2,
            30
        );

        // Draw kills
        this.ctx.font = 'bold 12px Arial';
        this.ctx.textAlign = 'right';
        this.ctx.fillText(
            `Defeated ${this.kills}`,
            this.canvas.width * 0.85 - 4,
            30
        );

        // Draw coins
        this.ctx.fillText(
            `Coins ${this.coins}`,
            this.canvas.width - 4,
            30
        );

        // Draw weapon icons
        this.drawWeaponIcons(sprites);

        // Draw power-up icons
        this.drawPowerUpIcons(sprites);
    }

    // Helper method to draw rounded rectangles
    roundRect(x, y, width, height, radius, fill, stroke) {
        if (typeof radius === 'undefined') {
            radius = 5;
        }

        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();

        if (fill) {
            this.ctx.fill();
        }

        if (stroke) {
            this.ctx.stroke();
        }
    }

    // Draw weapon icons
    drawWeaponIcons(sprites) {
        const iconSize = 24;
        const padding = 4;
        const startX = 2;
        const startY = 25;
        const cornerRadius = 8;

        if (this.weaponIcons.length === 0) return;

        // Draw background with rounded corners
        this.ctx.fillStyle = 'rgba(50, 50, 70, 0.85)';
        this.roundRect(
            startX,
            startY,
            (iconSize + padding) * this.weaponIcons.length - padding,
            iconSize,
            cornerRadius,
            true
        );

        // Add a subtle border
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.5)';
        this.ctx.lineWidth = 1;
        this.roundRect(
            startX,
            startY,
            (iconSize + padding) * this.weaponIcons.length - padding,
            iconSize,
            cornerRadius,
            false,
            true
        );

        // Draw icons
        this.weaponIcons.forEach((icon, index) => {
            const x = startX + (iconSize + padding) * index;
            const y = startY;

            // Draw icon background with rounded corners
            this.ctx.fillStyle = 'rgba(30, 30, 50, 0.7)';
            this.roundRect(x + 2, y + 2, iconSize - 4, iconSize - 4, 4, true);

            // Draw icon
            if (sprites && sprites[icon.frameName]) {
                this.ctx.drawImage(
                    sprites[icon.frameName],
                    x + 2,
                    y + 2,
                    iconSize - 4,
                    iconSize - 4
                );
            } else {
                // Fallback if sprite not found
                this.ctx.fillStyle = 'rgba(255, 215, 0, 0.8)';
                this.roundRect(x + 2, y + 2, iconSize - 4, iconSize - 4, 4, true);
            }

            // Draw level badge with rounded corners
            this.ctx.fillStyle = 'rgba(255, 215, 0, 0.9)';
            this.roundRect(x + iconSize - 12, y + iconSize - 12, 12, 12, 4, true);

            this.ctx.font = 'bold 10px Arial';
            this.ctx.fillStyle = 'rgba(40, 40, 60, 1)';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(
                icon.level.toString(),
                x + iconSize - 6,
                y + iconSize - 2
            );
        });
    }

    // Draw power-up icons
    drawPowerUpIcons(sprites) {
        const iconSize = 24;
        const padding = 4;
        const startX = 2;
        const startY = 25 + iconSize + padding;
        const cornerRadius = 8;

        if (this.powerUpIcons.length === 0) return;

        // Draw background with rounded corners
        this.ctx.fillStyle = 'rgba(50, 50, 70, 0.85)';
        this.roundRect(
            startX,
            startY,
            (iconSize + padding) * this.powerUpIcons.length - padding,
            iconSize,
            cornerRadius,
            true
        );

        // Add a subtle border
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.5)';
        this.ctx.lineWidth = 1;
        this.roundRect(
            startX,
            startY,
            (iconSize + padding) * this.powerUpIcons.length - padding,
            iconSize,
            cornerRadius,
            false,
            true
        );

        // Draw icons
        this.powerUpIcons.forEach((icon, index) => {
            const x = startX + (iconSize + padding) * index;
            const y = startY;

            // Draw icon background with rounded corners
            this.ctx.fillStyle = 'rgba(30, 30, 50, 0.7)';
            this.roundRect(x + 2, y + 2, iconSize - 4, iconSize - 4, 4, true);

            // Draw icon
            if (sprites && sprites[icon.frameName]) {
                this.ctx.drawImage(
                    sprites[icon.frameName],
                    x + 2,
                    y + 2,
                    iconSize - 4,
                    iconSize - 4
                );
            } else {
                // Fallback if sprite not found
                this.ctx.fillStyle = 'rgba(0, 255, 255, 0.8)';
                this.roundRect(x + 2, y + 2, iconSize - 4, iconSize - 4, 4, true);
            }

            // Draw level badge with rounded corners
            this.ctx.fillStyle = 'rgba(255, 215, 0, 0.9)';
            this.roundRect(x + iconSize - 12, y + iconSize - 12, 12, 12, 4, true);

            this.ctx.font = 'bold 10px Arial';
            this.ctx.fillStyle = 'rgba(40, 40, 60, 1)';
            this.ctx.textAlign = 'center';
            this.ctx.fillText(
                icon.level.toString(),
                x + iconSize - 6,
                y + iconSize - 2
            );
        });
    }
}

// PlayerUI class for handling player-specific UI
class PlayerUI {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.xpBarWidth = canvas.width - 4;
        this.xpBarHeight = 10;
        this.xpBarX = 2;
        this.xpBarY = 2;
        this.level = 1;
        this.xp = 0;
        this.maxXp = 100;
    }

    // Initialize the UI
    init() {
        // Nothing to do here in the base implementation
    }

    // Update the UI
    update() {
        if (Game.core && Game.core.player) {
            this.xp = Game.core.player.xp;
            this.level = Game.core.player.level;
            this.maxXp = this.calculateMaxXp(this.level);
        }
    }

    // Update player level
    updatePlayerLevel() {
        if (Game.core && Game.core.player) {
            this.level = Game.core.player.level;
            this.maxXp = this.calculateMaxXp(this.level);
        }
    }

    // Calculate maximum XP for a level
    calculateMaxXp(level) {
        // Starting at 5 points for level 1, doubling each level (5→10→20→40, etc.)
        const baseXp = 5;
        return baseXp * Math.pow(2, level - 1);
    }

    // Draw the UI
    draw() {
        // Draw XP bar
        this.drawXpBar();
    }

    // Helper method to draw rounded rectangles
    roundRect(x, y, width, height, radius, fill, stroke) {
        if (typeof radius === 'undefined') {
            radius = 5;
        }

        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();

        if (fill) {
            this.ctx.fill();
        }

        if (stroke) {
            this.ctx.stroke();
        }
    }

    // Draw the XP bar
    drawXpBar() {
        const cornerRadius = 5;

        // Draw background with rounded corners
        this.ctx.fillStyle = 'rgba(50, 50, 70, 0.85)';
        this.roundRect(
            this.xpBarX,
            this.xpBarY,
            this.xpBarWidth,
            this.xpBarHeight,
            cornerRadius,
            true
        );

        // Add a subtle border
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.5)';
        this.ctx.lineWidth = 1;
        this.roundRect(
            this.xpBarX,
            this.xpBarY,
            this.xpBarWidth,
            this.xpBarHeight,
            cornerRadius,
            false,
            true
        );

        // Draw progress with rounded corners (only on the right side)
        const progress = this.xp / this.maxXp;
        if (progress > 0) {
            const progressWidth = this.xpBarWidth * progress;

            // Use a gradient for the progress bar
            const gradient = this.ctx.createLinearGradient(
                this.xpBarX,
                this.xpBarY,
                this.xpBarX + progressWidth,
                this.xpBarY
            );
            gradient.addColorStop(0, 'rgba(255, 215, 0, 0.7)');
            gradient.addColorStop(1, 'rgba(255, 165, 0, 0.9)');

            this.ctx.fillStyle = gradient;

            // Use a special approach for the progress bar to have rounded corners only when needed
            if (progress >= 0.98) {
                // If almost full, use rounded corners on both sides
                this.roundRect(
                    this.xpBarX,
                    this.xpBarY,
                    progressWidth,
                    this.xpBarHeight,
                    cornerRadius,
                    true
                );
            } else {
                // Otherwise, create a custom shape with rounded corners only on the left
                this.ctx.beginPath();
                this.ctx.moveTo(this.xpBarX + cornerRadius, this.xpBarY);
                this.ctx.lineTo(this.xpBarX + progressWidth, this.xpBarY);
                this.ctx.lineTo(this.xpBarX + progressWidth, this.xpBarY + this.xpBarHeight);
                this.ctx.lineTo(this.xpBarX + cornerRadius, this.xpBarY + this.xpBarHeight);
                this.ctx.quadraticCurveTo(this.xpBarX, this.xpBarY + this.xpBarHeight, this.xpBarX, this.xpBarY + this.xpBarHeight - cornerRadius);
                this.ctx.lineTo(this.xpBarX, this.xpBarY + cornerRadius);
                this.ctx.quadraticCurveTo(this.xpBarX, this.xpBarY, this.xpBarX + cornerRadius, this.xpBarY);
                this.ctx.closePath();
                this.ctx.fill();
            }
        }

        // Draw level badge with rounded corners
        this.ctx.fillStyle = 'rgba(255, 215, 0, 0.9)';
        this.roundRect(
            this.xpBarX + 5,
            this.xpBarY + 1,
            36,
            this.xpBarHeight - 2,
            cornerRadius - 1,
            true
        );

        // Draw level text
        this.ctx.font = 'bold 10px Arial';
        this.ctx.fillStyle = 'rgba(40, 40, 60, 1)';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
            `Level ${this.level}`,
            this.xpBarX + 23,
            this.xpBarY + this.xpBarHeight - 2
        );

        // Draw XP percentage on the right
        const percentage = Math.floor(progress * 100);
        this.ctx.fillStyle = 'white';
        this.ctx.textAlign = 'right';
        this.ctx.fillText(
            `${percentage}%`,
            this.xpBarX + this.xpBarWidth - 5,
            this.xpBarY + this.xpBarHeight - 2
        );
    }
}

// LevelUpUI class for handling level-up UI
class LevelUpUI {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.visible = false;
        this.options = [];
        this.selectedIndex = -1;
        this.onSelect = null;
    }

    // Show the level-up UI with options
    show(options, callback) {
        try {
            // Ensure options is an array
            if (!Array.isArray(options)) {
                options = [];
            }

            // Ensure we have at least one option
            if (options.length === 0) {
                // Add a default growth power-up option
                if (Game.core && Game.core.sceneManager) {
                    options.push({
                        type: 'power_up',
                        powerUpType: WeaponType.GROWTH,
                        name: Game.core.sceneManager.getPowerUpName(WeaponType.GROWTH),
                        description: Game.core.sceneManager.getPowerUpDescription(WeaponType.GROWTH),
                        frameName: Game.core.sceneManager.getPowerUpFrameName(WeaponType.GROWTH)
                    });
                }
            }

            this.visible = true;
            this.options = options;
            this.selectedIndex = -1;
            this.onSelect = callback || function(option) {
                // Default behavior: resume game
                if (Game.core && Game.core.sceneManager) {
                    Game.core.sceneManager.resumeFromLevelUp();
                }
            };
        } catch (error) {
            // Ensure game continues even if UI fails
            if (Game.core) {
                Game.core.isPaused = false;
            }
        }
    }

    // Hide the level-up UI
    hide() {
        try {
            this.visible = false;
            this.options = [];
            this.selectedIndex = -1;
        } catch (error) {
            // Error handling without logging
        }
    }

    // Handle mouse/touch input
    handleInput(x, y, isClick) {
        if (!this.visible) return;

        // Calculate dimensions for the square UI panel
        const panelSize = Math.min(this.canvas.width * 0.9, this.canvas.height * 0.9);
        const panelX = (this.canvas.width - panelSize) / 2;
        const panelY = (this.canvas.height - panelSize) / 2;

        // Calculate option dimensions
        const optionHeight = 80;
        const optionWidth = panelSize * 0.9;
        const startX = panelX + (panelSize - optionWidth) / 2;
        const titleHeight = 60;
        const startY = panelY + titleHeight + 20;

        // Check if mouse/touch is over an option
        for (let i = 0; i < this.options.length; i++) {
            const optionX = startX;
            const optionY = startY + (optionHeight + 10) * i;

            if (x >= optionX && x <= optionX + optionWidth &&
                y >= optionY && y <= optionY + optionHeight) {

                this.selectedIndex = i;

                if (isClick && this.onSelect) {
                    // Play a selection sound if available
                    if (Game.core && Game.core.playSound) {
                        Game.core.playSound('select');
                    }

                    this.onSelect(this.options[i]);
                    this.hide();
                }

                return;
            }
        }

        this.selectedIndex = -1;
    }

    // Draw the level-up UI
    draw(sprites) {
        if (!this.visible) return;

        // Draw background overlay
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Calculate dimensions for a square UI panel
        const panelSize = Math.min(this.canvas.width * 0.9, this.canvas.height * 0.9);
        const panelX = (this.canvas.width - panelSize) / 2;
        const panelY = (this.canvas.height - panelSize) / 2;

        // Draw panel background with rounded corners
        this.ctx.fillStyle = 'rgba(50, 50, 70, 0.95)';
        this.roundRect(panelX, panelY, panelSize, panelSize, 20, true);

        // Add a border
        this.ctx.strokeStyle = 'rgba(255, 215, 0, 0.8)';
        this.ctx.lineWidth = 3;
        this.roundRect(panelX, panelY, panelSize, panelSize, 20, false, true);

        // Calculate option dimensions
        const optionHeight = 80;
        const optionWidth = panelSize * 0.9;
        const startX = panelX + (panelSize - optionWidth) / 2;
        const titleHeight = 60;
        const startY = panelY + titleHeight + 20;

        // Draw title
        this.ctx.font = 'bold 28px Arial';
        this.ctx.fillStyle = 'rgba(255, 215, 0, 1)';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
            'LEVEL UP!',
            this.canvas.width / 2,
            panelY + 40
        );

        this.ctx.font = 'bold 20px Arial';
        this.ctx.fillStyle = 'white';
        this.ctx.fillText(
            'Choose Your Upgrade',
            this.canvas.width / 2,
            panelY + 70
        );

        // Draw options
        for (let i = 0; i < this.options.length; i++) {
            const option = this.options[i];
            const optionX = startX;
            const optionY = startY + (optionHeight + 10) * i;

            // Draw option background with rounded corners
            this.ctx.fillStyle = this.selectedIndex === i ? 'rgba(255, 255, 255, 0.9)' : 'rgba(255, 255, 255, 0.7)';
            this.roundRect(optionX, optionY, optionWidth, optionHeight, 10, true);

            // Add a highlight border for selected option
            if (this.selectedIndex === i) {
                this.ctx.strokeStyle = 'rgba(255, 215, 0, 1)';
                this.ctx.lineWidth = 2;
                this.roundRect(optionX, optionY, optionWidth, optionHeight, 10, false, true);
            }

            // Draw option icon with rounded corners
            if (sprites && sprites[option.frameName]) {
                // Draw icon background
                this.ctx.fillStyle = 'rgba(40, 40, 60, 0.7)';
                this.roundRect(optionX + 10, optionY + 10, 60, 60, 8, true);

                // Draw the icon
                this.ctx.drawImage(
                    sprites[option.frameName],
                    optionX + 10,
                    optionY + 10,
                    60,
                    60
                );
            } else {
                // Fallback if sprite not found
                this.ctx.fillStyle = 'rgba(255, 215, 0, 0.8)';
                this.roundRect(optionX + 10, optionY + 10, 60, 60, 8, true);
            }

            // Draw option text
            this.ctx.font = 'bold 18px Arial';
            this.ctx.fillStyle = 'rgba(40, 40, 60, 1)';
            this.ctx.textAlign = 'left';
            this.ctx.fillText(
                option.name,
                optionX + 80,
                optionY + 30
            );

            this.ctx.font = '14px Arial';
            this.ctx.fillText(
                option.description,
                optionX + 80,
                optionY + 55
            );

            if (option.level) {
                // Draw level badge with rounded corners
                this.ctx.fillStyle = 'rgba(255, 215, 0, 0.9)';
                this.roundRect(optionX + optionWidth - 60, optionY + 10, 50, 24, 12, true);

                this.ctx.font = 'bold 14px Arial';
                this.ctx.fillStyle = 'rgba(40, 40, 60, 1)';
                this.ctx.textAlign = 'center';
                this.ctx.fillText(
                    `Level ${option.level}`,
                    optionX + optionWidth - 35,
                    optionY + 27
                );
            }
        }
    }

    // Helper method to draw rounded rectangles
    roundRect(x, y, width, height, radius, fill, stroke) {
        if (typeof radius === 'undefined') {
            radius = 5;
        }

        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();

        if (fill) {
            this.ctx.fill();
        }

        if (stroke) {
            this.ctx.stroke();
        }
    }
}

// Attach to window object for global access
window.MainUI = MainUI;
window.PlayerUI = PlayerUI;
window.LevelUpUI = LevelUpUI;
