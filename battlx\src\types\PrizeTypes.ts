// Prize Tree Types
export interface PrizeTree {
  id: number;
  name: string;
  description: string | null;
  icon: string | null;
  theme_color: string | null;
  display_order: number;
  is_active: boolean;
  is_seasonal: boolean;
  available_until: string | null;
}

// Prize Types
export interface Prize {
  id: number;
  prize_tree_id: number;
  name: string;
  description: string | null;
  icon: string | null;
  tier: number;
  position: number;
  category: string | null;
  cost: number;
  is_root: boolean;
  reward_type: RewardType;
  reward_data: any;
  prerequisites: number[];
  reward_details?: RewardDetails;
}

// User Prize Types
export interface UserPrize {
  prize_id: number;
  unlocked_at: string;
  is_equipped: boolean;
  prize: Prize;
}

// Prize Tree Response
export interface PrizeTreeResponse {
  id: number;
  name: string;
  description: string | null;
  icon: string | null;
  theme_color: string | null;
  nodes: Prize[];
  connections: Connection[];
}

// Connection Type
export interface Connection {
  startNodeId: number;
  endNodeId: number;
}

// User Prizes Response
export interface UserPrizesResponse {
  success: boolean;
  achievement_points: number;
  unlocked_prizes: number[];
  equipped_prizes: EquippedPrize[];
  user_prizes: UserPrize[];
}

// Equipped Prize
export interface EquippedPrize {
  prize_id: number;
  reward_type: RewardType;
  reward_details: RewardDetails;
}

// Reward Type
export type RewardType = 
  | 'cosmetic' 
  | 'title' 
  | 'card' 
  | 'balance' 
  | 'booster' 
  | 'special_item' 
  | 'emote';

// Reward Details
export interface RewardDetails {
  // Cosmetic reward details
  type?: string;
  visual_data?: {
    color?: string;
    trail_length?: number;
    width?: number;
    primary_color?: string;
    secondary_color?: string;
    image?: string;
    overlay_opacity?: number;
  };
  preview_image?: string;
  
  // Title reward details
  title?: string;
  color?: string;
  
  // Card reward details
  card_id?: number;
  rarity?: string;
  
  // Balance reward details
  amount?: number;
  currency?: string;
  
  // Booster reward details
  multiplier?: number;
  duration?: number;
  
  // Special item reward details
  item_id?: number;
  item_type?: string;
  
  // Emote reward details
  emote?: string;
  animation?: string | null;
}

// Achievement Point Transaction
export interface AchievementPointTransaction {
  id: number;
  telegram_user_id: number;
  amount: number;
  type: 'earn' | 'spend' | 'refund';
  source: string;
  source_id: number | null;
  description: string | null;
  created_at: string;
  updated_at: string;
}

// Unlock Prize Response
export interface UnlockPrizeResponse {
  success: boolean;
  message: string;
  prize?: {
    id: number;
    name: string;
    reward_type: RewardType;
    reward_details: RewardDetails;
  };
  remaining_points?: number;
}

// Equip Prize Response
export interface EquipPrizeResponse {
  success: boolean;
  message: string;
  prize_id: number;
  reward_type: RewardType;
  reward_details: RewardDetails;
}

// Unequip Prize Response
export interface UnequipPrizeResponse {
  success: boolean;
  message: string;
  prize_id: number;
}
