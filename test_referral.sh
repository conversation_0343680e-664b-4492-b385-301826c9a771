#!/bin/bash

# Test script for referral system
# This script tests the referral functionality by creating test users

API_URL="http://localhost:8000/api"

echo "=== Testing Referral System ==="
echo ""

# Test 1: Create a referrer user (User A)
echo "1. Creating referrer user (ID: 12345)..."
REFERRER_RESPONSE=$(curl -s -X POST "$API_URL/auth/telegram-user" \
  -H "Content-Type: application/json" \
  -d '{
    "telegram_id": 12345,
    "first_name": "<PERSON>",
    "last_name": "Referrer",
    "username": "alice_referrer"
  }')

echo "Referrer Response: $REFERRER_RESPONSE"
echo ""

# Test 2: Create a referred user (User B) with referral
echo "2. Creating referred user (ID: 67890) with referral from 12345..."
REFERRED_RESPONSE=$(curl -s -X POST "$API_URL/auth/telegram-user" \
  -H "Content-Type: application/json" \
  -d '{
    "telegram_id": 67890,
    "first_name": "<PERSON>",
    "last_name": "Referred",
    "username": "bob_referred",
    "referred_by": "12345"
  }')

echo "Referred Response: $REFERRED_RESPONSE"
echo ""

# Test 3: Try to create the same user again (should not give referral bonus)
echo "3. Trying to create the same user again (should not give referral bonus)..."
DUPLICATE_RESPONSE=$(curl -s -X POST "$API_URL/auth/telegram-user" \
  -H "Content-Type: application/json" \
  -d '{
    "telegram_id": 67890,
    "first_name": "Bob",
    "last_name": "Referred",
    "username": "bob_referred",
    "referred_by": "12345"
  }')

echo "Duplicate Response: $DUPLICATE_RESPONSE"
echo ""

# Test 4: Test self-referral (should not work)
echo "4. Testing self-referral (ID: 99999 referring themselves)..."
SELF_REFERRAL_RESPONSE=$(curl -s -X POST "$API_URL/auth/telegram-user" \
  -H "Content-Type: application/json" \
  -d '{
    "telegram_id": 99999,
    "first_name": "Charlie",
    "last_name": "SelfRef",
    "username": "charlie_self",
    "referred_by": "99999"
  }')

echo "Self-referral Response: $SELF_REFERRAL_RESPONSE"
echo ""

# Test 5: Test referral with non-existent referrer
echo "5. Testing referral with non-existent referrer (ID: 999999)..."
INVALID_REFERRER_RESPONSE=$(curl -s -X POST "$API_URL/auth/telegram-user" \
  -H "Content-Type: application/json" \
  -d '{
    "telegram_id": 11111,
    "first_name": "David",
    "last_name": "InvalidRef",
    "username": "david_invalid",
    "referred_by": "999999"
  }')

echo "Invalid referrer Response: $INVALID_REFERRER_RESPONSE"
echo ""

echo "=== Test completed ==="
echo ""
echo "Check the Laravel logs for detailed referral processing information:"
echo "tail -f api/storage/logs/laravel.log"
