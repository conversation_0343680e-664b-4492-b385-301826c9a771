@font-face {
  font-family: 'Olnova-HeavyCond';
  src: url('/fonts/Olnova-HeavyCond.woff2') format('woff2'),
       url('/fonts/Olnova-HeavyCond.woff') format('woff');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'battlx';
  src: url('/fonts/battlx.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply touch-manipulation;
    box-sizing: border-box;
  }

  html {
    /* Prevent horizontal scrolling on all devices */
    overflow-x: hidden;
    /* Use consistent viewport units */
    height: 100%;
    width: 100%;
  }

  body {
    @apply relative antialiased bg-black overflow-x-hidden text-white;
    font-family: "Roboto", sans-serif;
    /* Ensure consistent sizing across devices */
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
    width: 100%;
    margin: 0;
    padding: 0;
    /* Prevent text size adjustment on mobile devices */
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  .font-gothic {
    font-family: "Olnova-HeavyCond", sans-serif;
  }

  .battlx-icon {
    font-family: "battlx";
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Global responsive container for consistent display */
  .responsive-container {
    width: 100%;
    max-width: min(100vw, 414px);
    margin: 0 auto;
    padding: 0 1rem;
  }
}

.text-shadow {
  text-shadow: 0px 4px 100px #000, 0px 4px 41.8px #000;
}

.text-gradient {
  background: #fff;
  background-clip: text;
  color: transparent;
}

.modal-body {
  border-radius: 30px 30px 0px 0px;
  background: linear-gradient(
      180deg,
      rgba(234, 0, 0, 0.09) 0%,
      rgba(234, 0, 0, 0) 17%
    ),
    rgba(0, 0, 0, 0.8);
  box-shadow: 0px 0px 82px 0px purple,
    0px 1px 0px 0px purple inset,
    0px 2px 2px 0px purple inset;
  backdrop-filter: blur(6px);
}

.popup-body {
  border-radius: 30px;
  background: linear-gradient(
      180deg,
      rgba(234, 0, 0, 0.09) 0%,
      rgba(234, 0, 0, 0) 17%
    ),
    rgba(0, 0, 0, 0.8);
  box-shadow: 0px 0px 82px 0px rgba(255, 171, 93, 0.3);
  backdrop-filter: blur(6px);
  border: transparent;
  border-top: 2px solid #ffab5d;
}

.Toastify__toast-container {
  transform: translateY(99rem);
}

.Toastify__toast-container {
  transform: translateX(99rem);
}

.Toastify__toast {
  border-radius: 30px;
  background: linear-gradient(
      180deg,
      rgba(234, 0, 0, 0.09) 0%,
      rgba(234, 0, 0, 0) 17%
    ),
    rgba(0, 0, 0, 0.8) !important;
  box-shadow: 0px 0px 82px 0px rgba(255, 171, 93, 0.3);
  backdrop-filter: blur(6px);
  border: transparent;
  border-top: 2px solid #ffab5d;
  color: #9B8B6C !important;
  font-family: "battlx", sans-serif;
}

.Toastify__toast-body {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
}

.Toastify__close-button {
  color: #9B8B6C !important;
  opacity: 0.7;
}

.Toastify__close-button:hover {
  opacity: 1;
}


.user-tap-animate {
  z-index: 50;
}

.user-tap-animate > div {
  color: #fff;
  font-size: 32px;
  font-weight: 700;
  left: 0;
  position: absolute;
  top: 0;
  pointer-events: none;
  text-align: center;
  user-select: none;
  z-index: 60;
  animation: user-tap-animate 1s ease 0s 1;
}

@keyframes user-tap-animate {
  0% {
    opacity: 1;
    transform: translate(-50%, -100%);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -300%);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slice Trail Canvas */
.slice-trail-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 40;
  /* Improve rendering quality */
  image-rendering: optimizeQuality;
}

/* Splash Effect Animation */
.slice-splash {
  position: absolute;
  width: 60px;
  height: 60px;
  background-image: radial-gradient(circle, rgba(207, 181, 59, 0.9) 0%, rgba(74, 21, 75, 0.8) 40%, rgba(45, 12, 45, 0) 80%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 55;
  animation: splash-animation 0.7s ease-out forwards;
  box-shadow: 0 0 25px rgba(207, 181, 59, 0.8);
}

@keyframes splash-animation {
  0% {
    transform: translate(-50%, -50%) scale(0.2);
    opacity: 1;
    box-shadow: 0 0 5px rgba(207, 181, 59, 0.8);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 30px rgba(207, 181, 59, 0.6);
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
    box-shadow: 0 0 0 rgba(207, 181, 59, 0);
  }
}

/* Particle Effect Animation */
.slice-particle {
  position: absolute;
  width: 10px; /* Slightly larger particles */
  height: 10px;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 55;
  animation: particle-animation 0.7s ease-out forwards; /* Longer animation duration */
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.7); /* Add glow effect */
}

@keyframes particle-animation {
  0% {
    transform: translate(-50%, -50%) scale(1) rotate(0deg);
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(0) rotate(180deg);
    opacity: 0;
    margin-left: 30px; /* Increased spread */
    margin-top: 30px;
  }
}

/* Combo System Styles */

/* Combo Indicator */
.combo-indicator {
  font-family: 'Olnova-HeavyCond', sans-serif;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* Combo Activator */
.combo-activator {
  font-family: 'Olnova-HeavyCond', sans-serif;
  text-transform: uppercase;
  letter-spacing: 2px;
  user-select: none;
}

/* Combo Particles */
.combo-particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  z-index: 95;
}

/* Combo Encouragement Text */
.combo-encouragement {
  font-family: 'Olnova-HeavyCond', sans-serif;
  text-transform: uppercase;
  letter-spacing: 2px;
  white-space: nowrap;
  animation: encouragement-fade 1s ease-out forwards;
}

@keyframes encouragement-fade {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  80% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

/* Frenzy System Styles */

/* Frenzy Button Shake Animation */
.shake-animation {
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
  0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
  10%, 30%, 50%, 70%, 90% { transform: translate(-52%, -50%) rotate(-2deg); }
  20%, 40%, 60%, 80% { transform: translate(-48%, -50%) rotate(2deg); }
}

/* Frenzy Mode Pulse Animation */
@keyframes frenzy-pulse {
  0% {
    background-color: rgba(255, 215, 0, 0.05);
    box-shadow: inset 0 0 50px rgba(255, 215, 0, 0.2);
  }
  100% {
    background-color: rgba(255, 215, 0, 0.1);
    box-shadow: inset 0 0 100px rgba(255, 215, 0, 0.4);
  }
}

/* Performance-optimized Frenzy Pulse Animation */
@keyframes frenzy-pulse-optimized {
  0% {
    opacity: 0.03;
    transform: scale(1);
  }
  100% {
    opacity: 0.05;
    transform: scale(1.01);
  }
}

/* Frenzy Meter Reset Animation */
@keyframes frenzy-meter-reset {
  0% {
    background-color: #FF4500;
    opacity: 1;
  }
  50% {
    background-color: #FF0000;
    opacity: 0.8;
  }
  100% {
    background-color: #FF4500;
    opacity: 1;
  }
}
