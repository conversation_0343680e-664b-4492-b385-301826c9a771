# Monitoring and Analytics Implementation - Part 1: Core Monitoring

## Overview
This document covers the implementation of comprehensive monitoring and analytics for the Pet System, including performance monitoring, user behavior tracking, and system health monitoring.

## Implementation Time: 3-4 days
## Complexity: High
## Dependencies: Analytics services, monitoring tools, logging infrastructure

## System Health Monitoring

### Health Check Endpoints
```php
<?php
// File: api/app/Http/Controllers/HealthController.php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cache;
use App\Services\PetService;
use App\Services\MysteryBoxService;

class HealthController extends Controller
{
    /**
     * Comprehensive health check
     */
    public function health(): JsonResponse
    {
        $checks = [
            'database' => $this->checkDatabase(),
            'redis' => $this->checkRedis(),
            'cache' => $this->checkCache(),
            'queue' => $this->checkQueue(),
            'storage' => $this->checkStorage(),
            'pet_system' => $this->checkPetSystem(),
            'mystery_boxes' => $this->checkMysteryBoxes()
        ];

        $overall = collect($checks)->every(fn($check) => $check['status'] === 'healthy');

        return response()->json([
            'status' => $overall ? 'healthy' : 'unhealthy',
            'timestamp' => now()->toISOString(),
            'checks' => $checks,
            'version' => config('app.version', '1.0.0')
        ], $overall ? 200 : 503);
    }

    /**
     * Quick health check for load balancers
     */
    public function ping(): JsonResponse
    {
        return response()->json(['status' => 'ok', 'timestamp' => now()->toISOString()]);
    }

    /**
     * Detailed system metrics
     */
    public function metrics(): JsonResponse
    {
        return response()->json([
            'system' => [
                'memory_usage' => memory_get_usage(true),
                'memory_peak' => memory_get_peak_usage(true),
                'cpu_load' => sys_getloadavg(),
                'disk_usage' => $this->getDiskUsage(),
                'uptime' => $this->getUptime()
            ],
            'database' => [
                'connections' => $this->getDatabaseConnections(),
                'slow_queries' => $this->getSlowQueries(),
                'table_sizes' => $this->getTableSizes()
            ],
            'cache' => [
                'hit_rate' => $this->getCacheHitRate(),
                'memory_usage' => $this->getCacheMemoryUsage(),
                'keys_count' => $this->getCacheKeysCount()
            ],
            'application' => [
                'active_users' => $this->getActiveUsers(),
                'pets_count' => $this->getPetsCount(),
                'daily_interactions' => $this->getDailyInteractions(),
                'mystery_boxes_opened' => $this->getMysteryBoxesOpened()
            ]
        ]);
    }

    private function checkDatabase(): array
    {
        try {
            $start = microtime(true);
            DB::select('SELECT 1');
            $responseTime = (microtime(true) - $start) * 1000;

            return [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'connection_count' => $this->getDatabaseConnections()
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    private function checkRedis(): array
    {
        try {
            $start = microtime(true);
            Redis::ping();
            $responseTime = (microtime(true) - $start) * 1000;

            return [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'memory_usage' => Redis::info('memory')['used_memory_human'] ?? 'unknown'
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    private function checkCache(): array
    {
        try {
            $testKey = 'health_check_' . time();
            $testValue = 'test_value';
            
            Cache::put($testKey, $testValue, 60);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);

            if ($retrieved === $testValue) {
                return [
                    'status' => 'healthy',
                    'hit_rate' => $this->getCacheHitRate()
                ];
            } else {
                return [
                    'status' => 'unhealthy',
                    'error' => 'Cache read/write test failed'
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    private function checkQueue(): array
    {
        try {
            $queueSize = Redis::llen('queues:default');
            $failedJobs = DB::table('failed_jobs')->count();

            $status = 'healthy';
            if ($queueSize > 1000) {
                $status = 'warning';
            }
            if ($failedJobs > 100) {
                $status = 'unhealthy';
            }

            return [
                'status' => $status,
                'pending_jobs' => $queueSize,
                'failed_jobs' => $failedJobs
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    private function checkStorage(): array
    {
        try {
            $diskUsage = $this->getDiskUsage();
            $status = $diskUsage['percentage'] > 90 ? 'unhealthy' : 
                     ($diskUsage['percentage'] > 80 ? 'warning' : 'healthy');

            return [
                'status' => $status,
                'disk_usage' => $diskUsage
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    private function checkPetSystem(): array
    {
        try {
            $petService = app(PetService::class);
            
            // Check if pet system is responsive
            $start = microtime(true);
            $recentInteractions = DB::table('pet_interactions')
                                   ->where('interaction_time', '>', now()->subMinutes(5))
                                   ->count();
            $responseTime = (microtime(true) - $start) * 1000;

            return [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'recent_interactions' => $recentInteractions,
                'total_pets' => DB::table('pets')->count()
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    private function checkMysteryBoxes(): array
    {
        try {
            $mysteryBoxService = app(MysteryBoxService::class);
            
            $start = microtime(true);
            $recentOpenings = DB::table('mystery_box_openings')
                               ->where('opened_at', '>', now()->subMinutes(5))
                               ->count();
            $responseTime = (microtime(true) - $start) * 1000;

            return [
                'status' => 'healthy',
                'response_time_ms' => round($responseTime, 2),
                'recent_openings' => $recentOpenings,
                'active_box_types' => DB::table('mystery_box_types')
                                       ->where('is_active', true)
                                       ->count()
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getDiskUsage(): array
    {
        $bytes = disk_total_space('/');
        $free = disk_free_space('/');
        $used = $bytes - $free;
        $percentage = round(($used / $bytes) * 100, 2);

        return [
            'total' => $this->formatBytes($bytes),
            'used' => $this->formatBytes($used),
            'free' => $this->formatBytes($free),
            'percentage' => $percentage
        ];
    }

    private function getUptime(): string
    {
        $uptime = file_get_contents('/proc/uptime');
        $seconds = (int) explode(' ', $uptime)[0];
        
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        
        return "{$days}d {$hours}h {$minutes}m";
    }

    private function getDatabaseConnections(): int
    {
        try {
            $result = DB::select("SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'");
            return $result[0]->count ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getSlowQueries(): int
    {
        try {
            // This would require query logging to be enabled
            return 0; // Placeholder
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getTableSizes(): array
    {
        try {
            $sizes = DB::select("
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
                LIMIT 10
            ");

            return collect($sizes)->mapWithKeys(function($size) {
                return [$size->tablename => $size->size];
            })->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    private function getCacheHitRate(): float
    {
        try {
            $info = Redis::info('stats');
            $hits = $info['keyspace_hits'] ?? 0;
            $misses = $info['keyspace_misses'] ?? 0;
            $total = $hits + $misses;
            
            return $total > 0 ? round(($hits / $total) * 100, 2) : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getCacheMemoryUsage(): string
    {
        try {
            $info = Redis::info('memory');
            return $info['used_memory_human'] ?? 'unknown';
        } catch (\Exception $e) {
            return 'unknown';
        }
    }

    private function getCacheKeysCount(): int
    {
        try {
            $info = Redis::info('keyspace');
            preg_match('/keys=(\d+)/', $info['db0'] ?? '', $matches);
            return (int) ($matches[1] ?? 0);
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getActiveUsers(): int
    {
        return DB::table('telegram_users')
                ->where('last_activity', '>', now()->subMinutes(30))
                ->count();
    }

    private function getPetsCount(): array
    {
        return [
            'total' => DB::table('pets')->count(),
            'active_today' => DB::table('pet_interactions')
                               ->whereDate('interaction_time', today())
                               ->distinct('pet_id')
                               ->count()
        ];
    }

    private function getDailyInteractions(): int
    {
        return DB::table('pet_interactions')
                ->whereDate('interaction_time', today())
                ->count();
    }

    private function getMysteryBoxesOpened(): array
    {
        return [
            'today' => DB::table('mystery_box_openings')
                        ->whereDate('opened_at', today())
                        ->count(),
            'this_hour' => DB::table('mystery_box_openings')
                            ->where('opened_at', '>', now()->subHour())
                            ->count()
        ];
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
```

## Performance Monitoring

### Performance Metrics Collection
```php
<?php
// File: api/app/Services/PerformanceMonitoringService.php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class PerformanceMonitoringService
{
    private array $metrics = [];
    private float $startTime;
    private int $startMemory;

    public function __construct()
    {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage();
    }

    /**
     * Start tracking a metric
     */
    public function startMetric(string $name): void
    {
        $this->metrics[$name] = [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage()
        ];
    }

    /**
     * End tracking a metric
     */
    public function endMetric(string $name): array
    {
        if (!isset($this->metrics[$name])) {
            return [];
        }

        $metric = $this->metrics[$name];
        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $result = [
            'name' => $name,
            'duration_ms' => ($endTime - $metric['start_time']) * 1000,
            'memory_usage_bytes' => $endMemory - $metric['start_memory'],
            'timestamp' => now()->toISOString()
        ];

        unset($this->metrics[$name]);

        // Log slow operations
        if ($result['duration_ms'] > 1000) {
            Log::warning('Slow operation detected', $result);
        }

        return $result;
    }

    /**
     * Track database query performance
     */
    public function trackDatabaseQuery(string $query, float $duration): void
    {
        $metric = [
            'type' => 'database_query',
            'query' => $this->sanitizeQuery($query),
            'duration_ms' => $duration * 1000,
            'timestamp' => now()->toISOString()
        ];

        // Store in cache for recent queries analysis
        $cacheKey = 'performance:db_queries:' . date('Y-m-d-H');
        $queries = Cache::get($cacheKey, []);
        $queries[] = $metric;
        
        // Keep only last 1000 queries per hour
        if (count($queries) > 1000) {
            $queries = array_slice($queries, -1000);
        }
        
        Cache::put($cacheKey, $queries, 3600);

        // Log slow queries
        if ($duration > 1) {
            Log::warning('Slow database query', $metric);
        }
    }

    /**
     * Track API endpoint performance
     */
    public function trackApiEndpoint(string $method, string $endpoint, float $duration, int $statusCode): void
    {
        $metric = [
            'type' => 'api_endpoint',
            'method' => $method,
            'endpoint' => $endpoint,
            'duration_ms' => $duration * 1000,
            'status_code' => $statusCode,
            'timestamp' => now()->toISOString()
        ];

        // Store in cache for analysis
        $cacheKey = 'performance:api_endpoints:' . date('Y-m-d-H');
        $endpoints = Cache::get($cacheKey, []);
        $endpoints[] = $metric;
        
        if (count($endpoints) > 1000) {
            $endpoints = array_slice($endpoints, -1000);
        }
        
        Cache::put($cacheKey, $endpoints, 3600);

        // Log slow endpoints
        if ($duration > 2) {
            Log::warning('Slow API endpoint', $metric);
        }
    }

    /**
     * Get performance summary
     */
    public function getPerformanceSummary(): array
    {
        $currentHour = date('Y-m-d-H');
        
        return [
            'database' => $this->getDatabasePerformance($currentHour),
            'api_endpoints' => $this->getApiPerformance($currentHour),
            'cache' => $this->getCachePerformance(),
            'memory' => $this->getMemoryUsage(),
            'system' => $this->getSystemMetrics()
        ];
    }

    private function getDatabasePerformance(string $hour): array
    {
        $queries = Cache::get("performance:db_queries:{$hour}", []);
        
        if (empty($queries)) {
            return ['total_queries' => 0, 'avg_duration_ms' => 0, 'slow_queries' => 0];
        }

        $totalQueries = count($queries);
        $totalDuration = array_sum(array_column($queries, 'duration_ms'));
        $slowQueries = count(array_filter($queries, fn($q) => $q['duration_ms'] > 1000));

        return [
            'total_queries' => $totalQueries,
            'avg_duration_ms' => round($totalDuration / $totalQueries, 2),
            'slow_queries' => $slowQueries,
            'slow_query_percentage' => round(($slowQueries / $totalQueries) * 100, 2)
        ];
    }

    private function getApiPerformance(string $hour): array
    {
        $endpoints = Cache::get("performance:api_endpoints:{$hour}", []);
        
        if (empty($endpoints)) {
            return ['total_requests' => 0, 'avg_duration_ms' => 0, 'error_rate' => 0];
        }

        $totalRequests = count($endpoints);
        $totalDuration = array_sum(array_column($endpoints, 'duration_ms'));
        $errorRequests = count(array_filter($endpoints, fn($e) => $e['status_code'] >= 400));

        return [
            'total_requests' => $totalRequests,
            'avg_duration_ms' => round($totalDuration / $totalRequests, 2),
            'error_rate' => round(($errorRequests / $totalRequests) * 100, 2),
            'slowest_endpoints' => $this->getSlowestEndpoints($endpoints)
        ];
    }

    private function getCachePerformance(): array
    {
        try {
            $info = \Illuminate\Support\Facades\Redis::info('stats');
            $hits = $info['keyspace_hits'] ?? 0;
            $misses = $info['keyspace_misses'] ?? 0;
            $total = $hits + $misses;
            
            return [
                'hit_rate' => $total > 0 ? round(($hits / $total) * 100, 2) : 0,
                'total_operations' => $total,
                'hits' => $hits,
                'misses' => $misses
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    private function getMemoryUsage(): array
    {
        return [
            'current_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'peak_usage_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
            'limit_mb' => ini_get('memory_limit')
        ];
    }

    private function getSystemMetrics(): array
    {
        return [
            'load_average' => sys_getloadavg(),
            'cpu_count' => (int) shell_exec('nproc'),
            'uptime' => $this->getSystemUptime()
        ];
    }

    private function getSlowestEndpoints(array $endpoints): array
    {
        usort($endpoints, fn($a, $b) => $b['duration_ms'] <=> $a['duration_ms']);
        
        return array_slice(array_map(function($endpoint) {
            return [
                'method' => $endpoint['method'],
                'endpoint' => $endpoint['endpoint'],
                'duration_ms' => $endpoint['duration_ms']
            ];
        }, $endpoints), 0, 10);
    }

    private function sanitizeQuery(string $query): string
    {
        // Remove sensitive data from queries
        $query = preg_replace('/\b\d{10,}\b/', '[ID]', $query);
        $query = preg_replace('/\'[^\']*\'/', '[STRING]', $query);
        return substr($query, 0, 200) . (strlen($query) > 200 ? '...' : '');
    }

    private function getSystemUptime(): string
    {
        try {
            $uptime = file_get_contents('/proc/uptime');
            $seconds = (int) explode(' ', $uptime)[0];
            
            $days = floor($seconds / 86400);
            $hours = floor(($seconds % 86400) / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            
            return "{$days}d {$hours}h {$minutes}m";
        } catch (\Exception $e) {
            return 'unknown';
        }
    }
}
```

## Acceptance Criteria
- [ ] Health check endpoints operational
- [ ] Performance metrics collection working
- [ ] Database monitoring functional
- [ ] Cache performance tracking active
- [ ] API endpoint monitoring implemented
- [ ] System resource monitoring operational
- [ ] Alerting thresholds configured

## Next Steps
1. Continue with Part 2: User Analytics and Behavior Tracking
2. Implement alerting and notification systems
3. Create monitoring dashboards
4. Set up automated reporting

## Troubleshooting
- Verify monitoring endpoints are accessible
- Check performance data collection accuracy
- Test alerting thresholds and notifications
- Monitor system resource usage
- Validate metric storage and retention
