<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pet_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->enum('category', ['shadow', 'undead', 'demon', 'spirit', 'beast']);
            $table->enum('rarity', ['common', 'rare', 'epic', 'legendary', 'mythic']);
            $table->text('description');
            $table->string('image_url')->nullable();
            $table->string('animation_url')->nullable();
            
            // Purchase configuration
            $table->integer('coin_cost')->default(0);
            $table->integer('gem_cost')->default(0);
            $table->integer('prize_tree_unlock_level')->nullable();
            $table->boolean('is_premium_only')->default(false);
            
            // Interaction configuration
            $table->integer('base_happiness')->default(50);
            $table->integer('max_happiness')->default(100);
            $table->integer('happiness_decay_rate')->default(5); // per day
            
            // Mystery box unlocks
            $table->json('mystery_box_unlocks'); // Array of box types this pet unlocks
            $table->string('collectible_reward_id'); // Collectible given when pet is obtained
            
            // Evolution configuration
            $table->integer('max_level')->default(100);
            $table->json('evolution_levels'); // [10, 25, 50, 100]
            $table->json('evolution_images'); // URLs for each evolution stage
            
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index(['category', 'rarity']);
            $table->index('is_active');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pet_templates');
    }
};
