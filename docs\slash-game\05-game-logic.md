# Slash Game Integration Guide: 05 - Game Logic Implementation

This document outlines the core game mechanics and state management within the Slash game module, focusing on how the existing JavaScript classes handle gameplay, scoring (coin collection), and the game over condition.

## 1. Core Game Mechanics

The Slash game is described as being similar to "Vampire Survivors". Based on the components in `battlx/src/slash_game/js/src/components/`, the core mechanics are likely implemented through the interaction of the following:

*   [`player.js`](battlx/src/slash_game/js/src/components/player.js): Handles player movement (likely based on input from `js/src/components/game.js`), health, and potentially other stats.
*   [`enemy.js`](battlx/src/slash_game/js/src/components/enemy.js): Defines enemy behavior, movement (likely towards the player), health, and attack patterns.
*   [`weapon.js`](battlx/src/slash_game/js/src/components/weapon.js): Manages player weapons, including attack patterns, projectile creation (`enemyProjectile.js`), and damage.
*   [`pickup.js`](battlx/src/slash_game/js/src/components/pickup.js): Handles collectible items like coins and gems, their behavior (e.g., moving towards the player when a vacuum is active), and what happens when the player collects them (`getPickup` in `GameCore`).
*   [`stage.js`](battlx/src/slash_game/js/src/components/stage.js): Likely controls the spawning of enemies and potentially other events based on the elapsed game time (`survivedSeconds` in `GameCore`).
*   [`gameCore.js`](battlx/src/slash_game/js/src/components/gameCore.js): Acts as the central coordinator, managing lists of active game objects (enemies, bullets, pickups, etc.), handling collisions, and updating the state of all game elements within the game loop.

The game loop in [`game.js`](battlx/src/slash_game/js/src/components/game.js) drives the simulation, calling the `update` and `draw` methods of `GameCore` at a fixed time step.

## 2. State Management within the Game Module

The primary game state is managed within the `GameCore` class ([`js/src/components/gameCore.js`](battlx/src/slash_game/js/src/components/gameCore.js)). Key state variables include:

*   `isPaused`: Controls whether the game simulation is updated.
*   `isGameOver`: Indicates if the game session has ended.
*   Lists of active game objects (`enemies`, `bullets`, `pickups`, etc.).
*   Player state (`player` object, including health, level, XP, and coins).
*   Game progression state (`survivedSeconds`, `currentTreasureLevel`).

The `update` method in `GameCore` is responsible for updating the state of all active game objects, handling interactions (like collisions), and checking for game events (like level up or game over).

## 3. Scoring and Progression System

The scoring system in the Slash game is based on the final coin amount collected during a game session.

*   **Coin Collection:** The `getPickup` method in `GameCore` is called when the player collects a pickup. When a `PickupType.COIN` is collected, the player's coin count (`this.playerOptions.coins`) is incremented.
*   **Experience and Leveling:** Collecting `PickupType.GEM` adds experience points (`player.addXp`). When the player's XP reaches the required amount (`checkForLevelUp` in `GameCore`), their level increases, potentially unlocking new weapons or upgrades.
*   **Final Score:** The final score for a game session is the total number of coins collected, stored in `this.playerOptions.coins` at the end of the game.

## 4. Game Over Condition

The game over condition is detected and handled within the `GameCore` class. The specific trigger for game over is likely the player's health reaching zero.

*   The `gameOver()` method in `GameCore` is called when the game over condition is met (e.g., player health <= 0).
*   This method should set the `isGameOver` state to `true` to stop the game loop's update cycle.
*   **Crucially, within the `gameOver()` method, the final collected coin amount (`this.playerOptions.coins`) must be retrieved and passed as the score argument when calling the `onGameOver` callback provided during the game's initialization in `main.ts`.** This is how the final score is communicated back to the `GameWrapper` for submission to the backend.

By implementing the `onGameOver` callback call within the `GameCore.gameOver()` method, the Slash game will correctly report the final coin amount as the score to the frontend integration layer.