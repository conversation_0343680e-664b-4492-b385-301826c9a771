<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ReferralTaskSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tasks = [
            [
                'title' => 'Invite 3 friends',
                'number_of_referrals' => 3,
                'reward' => 1000,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'Invite 5 friends',
                'number_of_referrals' => 5,
                'reward' => 2500,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'Invite 10 friends',
                'number_of_referrals' => 10,
                'reward' => 7500,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'Invite 25 friends',
                'number_of_referrals' => 25,
                'reward' => 25000,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'Invite 50 friends',
                'number_of_referrals' => 50,
                'reward' => 75000,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'Invite 100 friends',
                'number_of_referrals' => 100,
                'reward' => 200000,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('referral_tasks')->insert($tasks);
    }
}
