/**
 * Game Over State
 * Displays the game over screen
 */
class GameOverState extends GameState {
    /**
     * @param {Engine} engine - Game engine
     */
    constructor(engine) {
        super(engine);
        this.input = new InputHandler(engine);
        this.gameOverMusic = null;
        this.score = 0;
        this.restartTimer = 0;
    }

    /**
     * Called when entering the state
     */
    enter() {
        console.log('[RabbitGame] Entering game over state');

        // Get score from play state
        this.score = this.engine.getVariable('score', 0);
        console.log(`[RabbitGame] Final score: ${this.score}`);

        // Play game over music
        this.gameOverMusic = this.engine.playSound('gameOver', { volume: 0.3 });

        // Store reference to game over music for cleanup
        this.engine.setVariable('gameOverInstance', this.gameOverMusic);

        // Set restart timer
        this.restartTimer = 1; // Wait 1 second before allowing restart

        // Call the onGameOver callback if available (for backend integration)
        try {
            const onGameOver = this.engine.getVariable('onGameOver');
            if (onGameOver && typeof onGameOver === 'function') {
                console.log(`[RabbitGame] Calling onGameOver with score: ${this.score}`);
                onGameOver(this.score);
            } else {
                console.warn('[RabbitGame] onGameOver callback not found or not a function');
            }
        } catch (error) {
            console.error('[RabbitGame] Error calling onGameOver:', error);
        }
    }

    /**
     * Called when exiting the state
     */
    exit() {
        // Stop game over music
        if (this.gameOverMusic) {
            this.engine.stopSound(this.gameOverMusic);
        }
    }

    /**
     * Update the state
     * @param {number} deltaTime - Time since last update
     */
    update(deltaTime) {
        // Update input
        this.input.update();

        // Update restart timer
        if (this.restartTimer > 0) {
            this.restartTimer -= deltaTime;
        }

        // Check for restart input
        if (this.restartTimer <= 0 && this.input.isStartPressed()) {
            this.engine.setState(CONSTANTS.STATES.PLAYING);
        }
    }

    /**
     * Render the state
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     */
    render(ctx) {
        // Draw background
        const bgImage = this.engine.getImage('gameOverBackground');
        ctx.drawImage(bgImage, 0, 0, this.engine.width, this.engine.height);

        // Draw game over text
        Utils.drawText(ctx, 'GAME OVER', this.engine.width / 2, 200, {
            fillStyle: '#FF0000',
            strokeStyle: '#000000',
            font: 'bold 40px Arial',
            lineWidth: 5
        });

        // Draw score
        Utils.drawText(ctx, `SCORE: ${this.score}`, this.engine.width / 2, 300, {
            fillStyle: '#FFFFFF',
            strokeStyle: '#000000',
            font: 'bold 30px Arial',
            lineWidth: 4
        });

        // Draw restart text with pulsing effect if timer is done
        if (this.restartTimer <= 0) {
            const pulseAmount = Math.sin(performance.now() / 500) * 0.2 + 0.8;
            ctx.globalAlpha = pulseAmount;
            Utils.drawText(ctx, 'PRESS SPACE OR TAP TO RESTART', this.engine.width / 2, 400, {
                fillStyle: '#FFFFFF',
                strokeStyle: '#000000',
                font: 'bold 20px Arial',
                lineWidth: 3
            });
            ctx.globalAlpha = 1;
        }
    }
}

// Explicitly add GameOverState to the window object
window.GameOverState = GameOverState;
