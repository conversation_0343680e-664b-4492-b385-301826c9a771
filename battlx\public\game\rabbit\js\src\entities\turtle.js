/**
 * Turtle Entity
 * Enemy that moves from right to left
 */
class Turtle {
    /**
     * @param {Engine} engine - Game engine
     * @param {number} x - X position
     * @param {number} y - Y position
     * @param {number} speed - Movement speed
     */
    constructor(engine, x, y, speed) {
        this.engine = engine;
        this.x = x;
        this.y = y;
        this.speed = speed;
        this.width = 74 * 2; // Sprite width * scale (74 = 888/12)
        this.height = 63 * 2; // Sprite height * scale
        this.scale = 2;

        // Animation
        this.sprite = engine.createSprite('turtle');

        // Only play animation if sprite is available
        if (this.sprite) {
            this.sprite.play('run');
        } else {
            console.error('Failed to create turtle sprite');
            // Try to create a fallback sprite
            const img = engine.getImage('turtle-img');
            if (img) {
                console.log('Using fallback turtle sprite');
                this.fallbackImg = img;
            }
        }

        // State
        this.active = true;
    }

    /**
     * Update the turtle
     * @param {number} deltaTime - Time since last update
     */
    update(deltaTime) {
        // Move from right to left
        this.x -= this.speed * deltaTime;

        // Update sprite animation
        if (this.sprite) {
            this.sprite.update(deltaTime);
        }

        // Deactivate if off screen
        if (this.x < -this.width) {
            this.active = false;
        }
    }

    /**
     * Get the turtle's collision box
     * @returns {Object} - Collision box
     */
    getCollisionBox() {
        return {
            x: this.x - this.width / 2 + 10,
            y: this.y - this.height / 2 + 10,
            width: this.width - 20,
            height: this.height - 20
        };
    }

    /**
     * Check if the turtle is active
     * @returns {boolean} - True if active
     */
    isActive() {
        return this.active;
    }

    /**
     * Deactivate the turtle
     */
    deactivate() {
        this.active = false;
    }

    /**
     * Render the turtle
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     */
    render(ctx) {
        // Draw the turtle sprite
        if (this.sprite) {
            this.sprite.draw(ctx, this.x, this.y, this.scale);
        } else if (this.fallbackImg) {
            // Draw fallback image
            const frameWidth = 74; // 888 / 12
            const frameHeight = 63;
            ctx.drawImage(
                this.fallbackImg,
                0, 0, // Source x, y
                frameWidth, frameHeight, // Source width, height
                this.x - this.width/2, this.y - this.height/2, // Destination x, y
                this.width, this.height // Destination width, height
            );
        } else {
            // Fallback rendering if sprite is not available
            ctx.fillStyle = 'green';
            ctx.fillRect(this.x - this.width/2, this.y - this.height/2, this.width, this.height);
        }

        // Draw collision box in debug mode
        if (this.engine.debug) {
            const box = this.getCollisionBox();
            ctx.strokeStyle = 'red';
            ctx.lineWidth = 2;
            ctx.strokeRect(box.x, box.y, box.width, box.height);
        }
    }
}
