<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @property int $id
 * @property string $title
 * @property int $number_of_referrals
 * @property int $reward
 * @property bool $is_completed
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class ReferralTask extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'number_of_referrals',
        'reward'
    ];

    protected $casts = [
        'number_of_referrals' => 'integer',
        'reward' => 'integer',
        'is_completed' => 'boolean'
    ];

    protected $appends = ['is_completed'];

    /**
     * Get the completion status of the task for the current user.
     */
    public function getIsCompletedAttribute(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        return $this->telegramUsers()
            ->where('telegram_user_id', auth()->id())
            ->where('is_completed', true)
            ->exists();
    }

    /**
     * The telegram users that have completed this task.
     */
    public function telegramUsers(): BelongsToMany
    {
        return $this->belongsToMany(TelegramUser::class, 'telegram_user_referral_task')
            ->withPivot('is_completed')
            ->withTimestamps();
    }
}
