{"name": "clicker-game", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@tanstack/react-query": "^5.47.0", "@tonconnect/sdk": "^3.0.6", "@tonconnect/ui-react": "^2.0.11", "@uidotdev/usehooks": "^2.4.1", "axios": "^1.7.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dayjs": "^1.11.11", "framer-motion": "^12.7.4", "lucide-react": "^0.395.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.23.1", "react-spring": "^9.7.5", "react-toastify": "^10.0.5", "swiper": "^11.1.4", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.1", "zustand": "^4.5.2"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.47.0", "@types/node": "^20.14.6", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/telegram-web-app": "^7.2.1", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "telegram-webapps-types": "^1.0.5", "typescript": "^5.2.2", "vite": "^5.2.0"}}