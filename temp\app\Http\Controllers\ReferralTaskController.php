<?php

namespace App\Http\Controllers;

use App\Models\ReferralTask;
use App\Services\AchievementPointService;
use Illuminate\Http\Request;

class ReferralTaskController extends Controller
{
    protected $achievementPointService;
    
    public function __construct(AchievementPointService $achievementPointService)
    {
        $this->achievementPointService = $achievementPointService;
    }
    
    public function index()
    {
        $tasks = ReferralTask::all();
        return response()->json($tasks);
    }

    public function complete(ReferralTask $referralTask)
    {
        $user = auth()->user();
        
        // Check if task is already completed
        if ($user->referralTasks()
            ->where('referral_task_id', $referralTask->id)
            ->where('is_completed', true)
            ->exists()
        ) {
            return response()->json(['message' => 'Task already completed'], 400);
        }

        // Check if user has enough referrals
        $referralCount = $user->referrals()->count();
        if ($referralCount >= $referralTask->number_of_referrals) {
            // Add reward to user balance
            $user->increment('balance', $referralTask->reward);
            
            // Mark task as completed
            $user->referralTasks()->attach($referralTask->id, [
                'is_completed' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            // Award achievement points for completing a referral task
            $this->achievementPointService->awardPoints(
                $user->id,
                2, // Award 2 points for each referral task (higher value due to difficulty)
                'referral_task_complete',
                $referralTask->id,
                "Completed referral task: {$referralTask->number_of_referrals} referrals"
            );

            return response()->json([
                'message' => 'Task completed',
                'reward' => $referralTask->reward,
                'achievement_points_awarded' => 2
            ]);
        }

        return response()->json([
            'message' => 'Not enough referrals',
            'required' => $referralTask->number_of_referrals,
            'current' => $referralCount
        ], 400);
    }
}
