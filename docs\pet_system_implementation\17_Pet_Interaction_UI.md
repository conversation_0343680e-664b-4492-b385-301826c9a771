# Pet Interaction UI Implementation

## Overview
This document covers the implementation of detailed pet interaction UI components, including advanced interaction panels, pet care interfaces, and interaction history displays.

## Implementation Time: 3-4 days
## Complexity: High
## Dependencies: Pet components, animation libraries

## Advanced Pet Interaction Components

### PetCareCenter Component
```tsx
// File: battlx/src/components/pets/PetCareCenter.tsx

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePetStore } from '../../stores/petStore';
import { useUserStore } from '../../stores/userStore';
import { Pet } from '../../types/pet';
import InteractionPanel from './InteractionPanel';
import PetMoodIndicator from './PetMoodIndicator';
import InteractionHistory from './InteractionHistory';
import PetCareSchedule from './PetCareSchedule';

interface PetCareCenterProps {
  pet: Pet;
  className?: string;
}

const PetCareCenter: React.FC<PetCareCenterProps> = ({ pet, className = '' }) => {
  const { interactWithPet, getInteractionHistory } = usePetStore();
  const { user } = useUserStore();
  
  const [activePanel, setActivePanel] = useState<'interact' | 'history' | 'schedule'>('interact');
  const [interactionHistory, setInteractionHistory] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (activePanel === 'history') {
      loadInteractionHistory();
    }
  }, [activePanel, pet.id]);

  const loadInteractionHistory = async () => {
    setIsLoading(true);
    try {
      const history = await getInteractionHistory(pet.id);
      setInteractionHistory(history);
    } catch (error) {
      console.error('Failed to load interaction history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInteraction = async (interactionType: string) => {
    try {
      await interactWithPet(pet.id, interactionType);
      
      // Refresh history if it's currently displayed
      if (activePanel === 'history') {
        loadInteractionHistory();
      }
    } catch (error) {
      console.error('Interaction failed:', error);
    }
  };

  return (
    <div className={`pet-care-center ${className}`}>
      {/* Pet Display Header */}
      <div className="care-center-header">
        <div className="pet-display-large">
          <motion.img
            src={pet.current_image}
            alt={pet.display_name}
            className="pet-image-large"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          />
          
          <PetMoodIndicator 
            pet={pet}
            size="large"
            showDetails={true}
          />
        </div>

        <div className="pet-info-detailed">
          <h2 className="pet-name">{pet.display_name}</h2>
          <div className="pet-meta">
            <span className={`rarity rarity-${pet.rarity}`}>
              {pet.rarity.toUpperCase()}
            </span>
            <span className="category">{pet.category}</span>
            <span className="level">Level {pet.level}</span>
          </div>

          {/* Vital Stats */}
          <div className="vital-stats">
            <div className="stat-item">
              <span className="stat-label">Happiness</span>
              <div className="stat-bar">
                <motion.div
                  className="stat-fill happiness"
                  initial={{ width: 0 }}
                  animate={{ width: `${pet.happiness_percentage}%` }}
                  transition={{ duration: 0.8 }}
                  style={{
                    backgroundColor: pet.happiness_percentage > 70 ? '#4ade80' :
                                   pet.happiness_percentage > 30 ? '#fbbf24' : '#ef4444'
                  }}
                />
                <span className="stat-value">{pet.happiness_percentage}%</span>
              </div>
            </div>

            <div className="stat-item">
              <span className="stat-label">Experience</span>
              <div className="stat-bar">
                <motion.div
                  className="stat-fill experience"
                  initial={{ width: 0 }}
                  animate={{ 
                    width: `${(pet.experience / (pet.experience + pet.experience_to_next_level)) * 100}%` 
                  }}
                  transition={{ duration: 0.8, delay: 0.2 }}
                />
                <span className="stat-value">
                  {pet.experience}/{pet.experience + pet.experience_to_next_level}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Panel Navigation */}
      <div className="panel-navigation">
        <button
          className={`panel-tab ${activePanel === 'interact' ? 'active' : ''}`}
          onClick={() => setActivePanel('interact')}
        >
          <span className="tab-icon">🎮</span>
          Interact
        </button>
        
        <button
          className={`panel-tab ${activePanel === 'history' ? 'active' : ''}`}
          onClick={() => setActivePanel('history')}
        >
          <span className="tab-icon">📊</span>
          History
        </button>
        
        <button
          className={`panel-tab ${activePanel === 'schedule' ? 'active' : ''}`}
          onClick={() => setActivePanel('schedule')}
        >
          <span className="tab-icon">📅</span>
          Schedule
        </button>
      </div>

      {/* Panel Content */}
      <div className="panel-content">
        <AnimatePresence mode="wait">
          {activePanel === 'interact' && (
            <motion.div
              key="interact"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <InteractionPanel
                pet={pet}
                onInteract={handleInteraction}
                userEnergy={user?.available_energy || 0}
              />
            </motion.div>
          )}

          {activePanel === 'history' && (
            <motion.div
              key="history"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <InteractionHistory
                pet={pet}
                interactions={interactionHistory}
                isLoading={isLoading}
                onRefresh={loadInteractionHistory}
              />
            </motion.div>
          )}

          {activePanel === 'schedule' && (
            <motion.div
              key="schedule"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <PetCareSchedule pet={pet} />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default PetCareCenter;
```

### InteractionPanel Component
```tsx
// File: battlx/src/components/pets/InteractionPanel.tsx

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Pet } from '../../types/pet';
import InteractionButton from './InteractionButton';
import InteractionPreview from './InteractionPreview';

interface InteractionPanelProps {
  pet: Pet;
  onInteract: (type: string) => Promise<void>;
  userEnergy: number;
}

const InteractionPanel: React.FC<InteractionPanelProps> = ({
  pet,
  onInteract,
  userEnergy
}) => {
  const [selectedInteraction, setSelectedInteraction] = useState<string | null>(null);
  const [isInteracting, setIsInteracting] = useState(false);

  const interactions = [
    {
      type: 'feed',
      icon: '🍖',
      title: 'Feed',
      description: 'Give your pet a delicious meal to boost happiness',
      energyCost: 5,
      cooldown: 60, // minutes
      rewards: {
        happiness: 20,
        experience: 10,
        coins: 50
      },
      available: pet.can_feed
    },
    {
      type: 'play',
      icon: '🎾',
      title: 'Play',
      description: 'Play games with your pet for better rewards',
      energyCost: 10,
      cooldown: 120, // minutes
      rewards: {
        happiness: 30,
        experience: 15,
        coins: 100,
        materials: 'Possible'
      },
      available: pet.can_play
    },
    {
      type: 'pet',
      icon: '❤️',
      title: 'Pet',
      description: 'Show affection to your companion',
      energyCost: 2,
      cooldown: 30, // minutes
      rewards: {
        happiness: 10,
        experience: 5,
        coins: 25
      },
      available: pet.can_pet
    }
  ];

  const handleInteractionClick = async (interactionType: string) => {
    const interaction = interactions.find(i => i.type === interactionType);
    if (!interaction || !interaction.available || userEnergy < interaction.energyCost) {
      return;
    }

    setIsInteracting(true);
    try {
      await onInteract(interactionType);
    } finally {
      setIsInteracting(false);
      setSelectedInteraction(null);
    }
  };

  return (
    <div className="interaction-panel">
      {/* Energy Display */}
      <div className="energy-display">
        <div className="energy-info">
          <span className="energy-icon">⚡</span>
          <span className="energy-text">Energy: {userEnergy}</span>
        </div>
        
        <div className="energy-bar">
          <motion.div
            className="energy-fill"
            initial={{ width: 0 }}
            animate={{ width: `${Math.min((userEnergy / 100) * 100, 100)}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      {/* Interaction Buttons */}
      <div className="interaction-grid">
        {interactions.map((interaction, index) => (
          <motion.div
            key={interaction.type}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <InteractionButton
              type={interaction.type}
              icon={interaction.icon}
              title={interaction.title}
              description={interaction.description}
              energyCost={interaction.energyCost}
              available={interaction.available}
              userEnergy={userEnergy}
              isSelected={selectedInteraction === interaction.type}
              isInteracting={isInteracting}
              onClick={() => setSelectedInteraction(interaction.type)}
              onInteract={() => handleInteractionClick(interaction.type)}
            />
          </motion.div>
        ))}
      </div>

      {/* Interaction Preview */}
      <AnimatePresence>
        {selectedInteraction && (
          <InteractionPreview
            interaction={interactions.find(i => i.type === selectedInteraction)!}
            pet={pet}
            onConfirm={() => handleInteractionClick(selectedInteraction)}
            onCancel={() => setSelectedInteraction(null)}
          />
        )}
      </AnimatePresence>

      {/* Daily Interaction Summary */}
      <div className="daily-summary">
        <h4>Today's Interactions</h4>
        <div className="interaction-counts">
          <div className="count-item">
            <span className="count-icon">🍖</span>
            <span className="count-text">Fed: {pet.daily_feeds || 0}/5</span>
          </div>
          <div className="count-item">
            <span className="count-icon">🎾</span>
            <span className="count-text">Played: {pet.daily_plays || 0}/3</span>
          </div>
          <div className="count-item">
            <span className="count-icon">❤️</span>
            <span className="count-text">Petted: {pet.daily_pets || 0}/10</span>
          </div>
        </div>
        
        <div className="total-interactions">
          Total interactions today: {pet.interactions_today || 0}
        </div>
      </div>

      {/* Tips and Hints */}
      <div className="interaction-tips">
        <h4>💡 Tips</h4>
        <ul>
          <li>Happy pets (80%+ happiness) give double coin rewards!</li>
          <li>Playing has a chance to find rare materials</li>
          <li>Regular interactions help your pet level up faster</li>
          <li>Each interaction type has different cooldown periods</li>
        </ul>
      </div>
    </div>
  );
};

export default InteractionPanel;
```

### PetMoodIndicator Component
```tsx
// File: battlx/src/components/pets/PetMoodIndicator.tsx

import React from 'react';
import { motion } from 'framer-motion';
import { Pet } from '../../types/pet';

interface PetMoodIndicatorProps {
  pet: Pet;
  size?: 'small' | 'medium' | 'large';
  showDetails?: boolean;
}

const PetMoodIndicator: React.FC<PetMoodIndicatorProps> = ({
  pet,
  size = 'medium',
  showDetails = false
}) => {
  const getMoodInfo = () => {
    const happiness = pet.happiness_percentage;
    
    if (happiness >= 90) {
      return {
        mood: 'Ecstatic',
        icon: '🤩',
        color: '#10b981',
        description: 'Your pet is absolutely thrilled!',
        effects: ['Double coin rewards', 'Faster experience gain']
      };
    } else if (happiness >= 70) {
      return {
        mood: 'Happy',
        icon: '😊',
        color: '#4ade80',
        description: 'Your pet is very content and happy.',
        effects: ['Double coin rewards', 'Normal experience gain']
      };
    } else if (happiness >= 50) {
      return {
        mood: 'Content',
        icon: '😐',
        color: '#fbbf24',
        description: 'Your pet is doing okay.',
        effects: ['Normal rewards']
      };
    } else if (happiness >= 30) {
      return {
        mood: 'Sad',
        icon: '😢',
        color: '#f59e0b',
        description: 'Your pet needs some attention.',
        effects: ['Reduced rewards', 'Slower experience gain']
      };
    } else {
      return {
        mood: 'Depressed',
        icon: '😭',
        color: '#ef4444',
        description: 'Your pet is very unhappy and needs immediate care!',
        effects: ['Minimal rewards', 'Very slow experience gain']
      };
    }
  };

  const moodInfo = getMoodInfo();
  const sizeClasses = {
    small: 'mood-indicator-small',
    medium: 'mood-indicator-medium',
    large: 'mood-indicator-large'
  };

  return (
    <div className={`pet-mood-indicator ${sizeClasses[size]}`}>
      <motion.div
        className="mood-display"
        animate={pet.happiness_percentage < 30 ? {
          scale: [1, 1.1, 1],
          rotate: [0, 2, -2, 0]
        } : {}}
        transition={pet.happiness_percentage < 30 ? {
          duration: 2,
          repeat: Infinity,
          repeatType: 'loop'
        } : {}}
      >
        <div 
          className="mood-icon"
          style={{ backgroundColor: moodInfo.color }}
        >
          {moodInfo.icon}
        </div>
        
        <div className="mood-info">
          <div className="mood-label" style={{ color: moodInfo.color }}>
            {moodInfo.mood}
          </div>
          
          {size !== 'small' && (
            <div className="happiness-value">
              {pet.happiness_percentage.toFixed(1)}%
            </div>
          )}
        </div>
      </motion.div>

      {showDetails && (
        <motion.div
          className="mood-details"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
        >
          <p className="mood-description">{moodInfo.description}</p>
          
          <div className="mood-effects">
            <h5>Current Effects:</h5>
            <ul>
              {moodInfo.effects.map((effect, index) => (
                <li key={index}>{effect}</li>
              ))}
            </ul>
          </div>

          {/* Happiness History Graph */}
          <div className="happiness-trend">
            <h5>Happiness Trend (Last 7 days)</h5>
            <div className="trend-graph">
              {/* This would show a simple line graph of happiness over time */}
              <div className="trend-placeholder">
                📈 Graph would go here
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default PetMoodIndicator;
```

### InteractionPreview Component
```tsx
// File: battlx/src/components/pets/InteractionPreview.tsx

import React from 'react';
import { motion } from 'framer-motion';
import { Pet } from '../../types/pet';

interface InteractionPreviewProps {
  interaction: {
    type: string;
    icon: string;
    title: string;
    description: string;
    energyCost: number;
    rewards: any;
  };
  pet: Pet;
  onConfirm: () => void;
  onCancel: () => void;
}

const InteractionPreview: React.FC<InteractionPreviewProps> = ({
  interaction,
  pet,
  onConfirm,
  onCancel
}) => {
  const calculateRewards = () => {
    const baseRewards = interaction.rewards;
    const happinessMultiplier = pet.happiness_percentage >= 80 ? 2 : 1;
    
    return {
      ...baseRewards,
      coins: baseRewards.coins ? baseRewards.coins * happinessMultiplier : 0,
      multiplier: happinessMultiplier
    };
  };

  const rewards = calculateRewards();

  return (
    <motion.div
      className="interaction-preview"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.2 }}
    >
      <div className="preview-header">
        <div className="interaction-icon">{interaction.icon}</div>
        <h3>{interaction.title}</h3>
      </div>

      <div className="preview-content">
        <p className="interaction-description">{interaction.description}</p>

        <div className="cost-display">
          <span className="cost-label">Energy Cost:</span>
          <span className="cost-value">⚡ {interaction.energyCost}</span>
        </div>

        <div className="rewards-preview">
          <h4>Expected Rewards:</h4>
          <div className="reward-list">
            {rewards.happiness && (
              <div className="reward-item">
                <span className="reward-icon">😊</span>
                <span className="reward-text">+{rewards.happiness} Happiness</span>
              </div>
            )}
            
            {rewards.experience && (
              <div className="reward-item">
                <span className="reward-icon">📈</span>
                <span className="reward-text">+{rewards.experience} Experience</span>
              </div>
            )}
            
            {rewards.coins && (
              <div className="reward-item">
                <span className="reward-icon">🪙</span>
                <span className="reward-text">+{rewards.coins} Coins</span>
                {rewards.multiplier > 1 && (
                  <span className="multiplier-badge">{rewards.multiplier}x</span>
                )}
              </div>
            )}
            
            {rewards.materials && (
              <div className="reward-item">
                <span className="reward-icon">🔧</span>
                <span className="reward-text">{rewards.materials} Materials</span>
              </div>
            )}
          </div>

          {rewards.multiplier > 1 && (
            <div className="bonus-notice">
              <span className="bonus-icon">✨</span>
              <span>Happy pet bonus active! Double coin rewards!</span>
            </div>
          )}
        </div>

        <div className="preview-actions">
          <button 
            className="confirm-btn"
            onClick={onConfirm}
          >
            Confirm Interaction
          </button>
          
          <button 
            className="cancel-btn"
            onClick={onCancel}
          >
            Cancel
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default InteractionPreview;
```

### InteractionHistory Component
```tsx
// File: battlx/src/components/pets/InteractionHistory.tsx

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Pet } from '../../types/pet';

interface InteractionHistoryProps {
  pet: Pet;
  interactions: any[];
  isLoading: boolean;
  onRefresh: () => void;
}

const InteractionHistory: React.FC<InteractionHistoryProps> = ({
  pet,
  interactions,
  isLoading,
  onRefresh
}) => {
  const [filter, setFilter] = useState<'all' | 'feed' | 'play' | 'pet'>('all');
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month'>('week');

  const filteredInteractions = interactions.filter(interaction => {
    if (filter !== 'all' && interaction.interaction_type !== filter) {
      return false;
    }

    const interactionDate = new Date(interaction.interaction_time);
    const now = new Date();
    
    switch (timeRange) {
      case 'today':
        return interactionDate.toDateString() === now.toDateString();
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return interactionDate >= weekAgo;
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        return interactionDate >= monthAgo;
      default:
        return true;
    }
  });

  const getInteractionIcon = (type: string) => {
    switch (type) {
      case 'feed': return '🍖';
      case 'play': return '🎾';
      case 'pet': return '❤️';
      default: return '🎮';
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    }
  };

  return (
    <div className="interaction-history">
      {/* Controls */}
      <div className="history-controls">
        <div className="filter-group">
          <label>Filter by type:</label>
          <select 
            value={filter} 
            onChange={(e) => setFilter(e.target.value as any)}
          >
            <option value="all">All Interactions</option>
            <option value="feed">Feed</option>
            <option value="play">Play</option>
            <option value="pet">Pet</option>
          </select>
        </div>

        <div className="filter-group">
          <label>Time range:</label>
          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(e.target.value as any)}
          >
            <option value="today">Today</option>
            <option value="week">This Week</option>
            <option value="month">This Month</option>
          </select>
        </div>

        <button 
          className="refresh-btn"
          onClick={onRefresh}
          disabled={isLoading}
        >
          {isLoading ? '⟳' : '🔄'} Refresh
        </button>
      </div>

      {/* Statistics Summary */}
      <div className="history-stats">
        <div className="stat-card">
          <div className="stat-value">{filteredInteractions.length}</div>
          <div className="stat-label">Total Interactions</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-value">
            {filteredInteractions.reduce((sum, i) => sum + (i.coins_rewarded || 0), 0)}
          </div>
          <div className="stat-label">Coins Earned</div>
        </div>
        
        <div className="stat-card">
          <div className="stat-value">
            {filteredInteractions.reduce((sum, i) => sum + (i.experience_gained || 0), 0)}
          </div>
          <div className="stat-label">Experience Gained</div>
        </div>
      </div>

      {/* Interaction List */}
      <div className="interaction-list">
        {isLoading ? (
          <div className="loading-placeholder">
            <div className="loading-spinner" />
            <span>Loading interaction history...</span>
          </div>
        ) : filteredInteractions.length > 0 ? (
          filteredInteractions.map((interaction, index) => (
            <motion.div
              key={interaction.id}
              className="interaction-item"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
            >
              <div className="interaction-icon">
                {getInteractionIcon(interaction.interaction_type)}
              </div>
              
              <div className="interaction-details">
                <div className="interaction-type">
                  {interaction.interaction_type.charAt(0).toUpperCase() + 
                   interaction.interaction_type.slice(1)}
                </div>
                
                <div className="interaction-time">
                  {formatTime(interaction.interaction_time)}
                </div>
              </div>

              <div className="interaction-rewards">
                {interaction.coins_rewarded > 0 && (
                  <span className="reward-item">
                    🪙 +{interaction.coins_rewarded}
                  </span>
                )}
                
                {interaction.experience_gained > 0 && (
                  <span className="reward-item">
                    📈 +{interaction.experience_gained}
                  </span>
                )}
                
                {interaction.happiness_gained > 0 && (
                  <span className="reward-item">
                    😊 +{interaction.happiness_gained}
                  </span>
                )}

                {interaction.bonus_applied && (
                  <span className="bonus-indicator">✨ Bonus</span>
                )}
              </div>
            </motion.div>
          ))
        ) : (
          <div className="empty-history">
            <div className="empty-icon">📊</div>
            <h3>No interactions found</h3>
            <p>Try adjusting your filters or interact with your pet!</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default InteractionHistory;
```

## Acceptance Criteria
- [ ] Pet care center displays comprehensive pet information
- [ ] Interaction panel shows all available interactions
- [ ] Pet mood indicator accurately reflects happiness state
- [ ] Interaction preview shows expected rewards
- [ ] Interaction history displays with filtering options
- [ ] All animations smooth and responsive
- [ ] Mobile-optimized touch interactions

## Next Steps
1. Create mystery box UI components
2. Implement collectible page components
3. Add state management integration
4. Create comprehensive testing suite

## Troubleshooting
- Test interaction cooldowns and energy requirements
- Verify reward calculations match backend
- Check mood indicator updates in real-time
- Test history filtering and pagination
- Ensure responsive design on all devices
