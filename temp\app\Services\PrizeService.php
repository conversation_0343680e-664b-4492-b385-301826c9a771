<?php

namespace App\Services;

use App\Models\Prize;
use App\Models\UserPrize;
use App\Models\TelegramUser;
use Illuminate\Support\Facades\DB;

class PrizeService
{
    protected $achievementPointService;
    
    public function __construct(AchievementPointService $achievementPointService)
    {
        $this->achievementPointService = $achievementPointService;
    }
    
    /**
     * Unlock a prize for a user.
     *
     * @param int $userId
     * @param int $prizeId
     * @return array
     */
    public function unlockPrize($userId, $prizeId)
    {
        // Check if the user already has this prize
        $existingPrize = UserPrize::where('telegram_user_id', $userId)
            ->where('prize_id', $prizeId)
            ->first();
            
        if ($existingPrize) {
            return [
                'success' => false,
                'message' => 'Prize already unlocked'
            ];
        }
        
        // Get the prize and its prerequisites
        $prize = Prize::with('prerequisites')->findOrFail($prizeId);
        
        // Check if the user has all prerequisites
        $userPrizeIds = UserPrize::where('telegram_user_id', $userId)
            ->pluck('prize_id')
            ->toArray();
            
        $prerequisiteIds = $prize->prerequisites->pluck('id')->toArray();
        
        $missingPrerequisites = array_diff($prerequisiteIds, $userPrizeIds);
        
        if (!empty($missingPrerequisites)) {
            return [
                'success' => false,
                'message' => 'Missing prerequisites',
                'missing_prerequisites' => $missingPrerequisites
            ];
        }
        
        // Check if the user has enough achievement points
        $user = TelegramUser::find($userId);
        
        if (!$user) {
            return [
                'success' => false,
                'message' => 'User not found'
            ];
        }
        
        if ($user->available_achievement_points < $prize->cost) {
            return [
                'success' => false,
                'message' => 'Not enough achievement points',
                'required' => $prize->cost,
                'available' => $user->available_achievement_points
            ];
        }
        
        // Unlock the prize and update achievement points in a transaction
        DB::beginTransaction();
        
        try {
            // Create user prize
            $userPrize = UserPrize::create([
                'telegram_user_id' => $userId,
                'prize_id' => $prizeId,
                'unlocked_at' => now()
            ]);
            
            // Deduct achievement points
            $deductResult = $this->achievementPointService->deductPoints(
                $userId,
                $prize->cost,
                'prize_unlock',
                $prizeId,
                "Unlocked prize: {$prize->name}"
            );
            
            if (!$deductResult['success']) {
                throw new \Exception($deductResult['message']);
            }
            
            // Apply prize effects based on reward type
            $this->applyPrizeEffects($userId, $prize);
            
            DB::commit();
            
            return [
                'success' => true,
                'message' => 'Prize unlocked successfully',
                'prize' => [
                    'id' => $prize->id,
                    'name' => $prize->name,
                    'reward_type' => $prize->reward_type,
                    'reward_details' => $prize->getRewardDetails()
                ],
                'remaining_points' => $deductResult['remaining_points']
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            return [
                'success' => false,
                'message' => 'Failed to unlock prize: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Apply the effects of a prize to the user.
     *
     * @param int $userId
     * @param Prize $prize
     * @return void
     */
    private function applyPrizeEffects($userId, $prize)
    {
        $user = TelegramUser::find($userId);
        
        if (!$user) {
            return;
        }
        
        switch ($prize->reward_type) {
            case 'balance':
                // Award balance
                $rewardDetails = $prize->getRewardDetails();
                $amount = $rewardDetails['amount'] ?? 0;
                
                if ($amount > 0) {
                    $user->balance += $amount;
                    $user->save();
                }
                break;
                
            case 'booster':
                // Apply booster
                $rewardDetails = $prize->getRewardDetails();
                $boosterType = $rewardDetails['type'] ?? '';
                
                if ($boosterType === 'energy_booster') {
                    // Refill energy
                    $user->available_energy = $user->max_energy;
                    $user->save();
                }
                break;
                
            // Handle other reward types as needed
        }
    }
    
    /**
     * Get all available prize trees.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAvailablePrizeTrees()
    {
        return \App\Models\PrizeTree::where('is_active', true)
            ->orderBy('display_order')
            ->get();
    }
    
    /**
     * Get a prize tree with its prizes.
     *
     * @param int $treeId
     * @return array
     */
    public function getPrizeTree($treeId)
    {
        $prizeTree = \App\Models\PrizeTree::with(['prizes' => function ($query) {
            $query->orderBy('tier')->orderBy('position');
        }])->findOrFail($treeId);
        
        // Format the prizes to include prerequisites
        $prizes = $prizeTree->prizes->map(function ($prize) {
            $prerequisites = $prize->prerequisites->pluck('id')->toArray();
            return array_merge($prize->toArray(), [
                'prerequisites' => $prerequisites,
                'reward_details' => $prize->getRewardDetails()
            ]);
        });
        
        // Create connections between prizes based on prerequisites
        $connections = [];
        foreach ($prizes as $prize) {
            foreach ($prize['prerequisites'] as $prerequisiteId) {
                $connections[] = [
                    'startNodeId' => $prerequisiteId,
                    'endNodeId' => $prize['id']
                ];
            }
        }
        
        return [
            'id' => $prizeTree->id,
            'name' => $prizeTree->name,
            'description' => $prizeTree->description,
            'icon' => $prizeTree->icon,
            'theme_color' => $prizeTree->theme_color,
            'is_seasonal' => $prizeTree->is_seasonal,
            'available_until' => $prizeTree->available_until,
            'nodes' => $prizes,
            'connections' => $connections
        ];
    }
    
    /**
     * Get a user's prizes.
     *
     * @param int $userId
     * @return array
     */
    public function getUserPrizes($userId)
    {
        $user = TelegramUser::find($userId);
        
        if (!$user) {
            return [
                'success' => false,
                'message' => 'User not found'
            ];
        }
        
        // Get user's unlocked prizes
        $userPrizes = UserPrize::where('telegram_user_id', $userId)
            ->with('prize')
            ->get();
            
        // Get user's achievement points
        $achievementPoints = \App\Models\UserAchievementPoint::firstOrCreate(
            ['telegram_user_id' => $userId],
            ['total_earned' => 0, 'total_spent' => 0]
        );
        
        // Get equipped prizes
        $equippedPrizes = $userPrizes->where('is_equipped', true)
            ->map(function ($userPrize) {
                return [
                    'prize_id' => $userPrize->prize_id,
                    'reward_type' => $userPrize->prize->reward_type,
                    'reward_details' => $userPrize->prize->getRewardDetails()
                ];
            });
        
        return [
            'success' => true,
            'achievement_points' => $achievementPoints->available_points,
            'unlocked_prizes' => $userPrizes->pluck('prize_id')->toArray(),
            'equipped_prizes' => $equippedPrizes,
            'user_prizes' => $userPrizes->map(function ($userPrize) {
                return [
                    'prize_id' => $userPrize->prize_id,
                    'unlocked_at' => $userPrize->unlocked_at->toIso8601String(),
                    'is_equipped' => $userPrize->is_equipped,
                    'prize' => [
                        'name' => $userPrize->prize->name,
                        'description' => $userPrize->prize->description,
                        'icon' => $userPrize->prize->icon,
                        'reward_type' => $userPrize->prize->reward_type,
                        'reward_details' => $userPrize->prize->getRewardDetails()
                    ]
                ];
            })
        ];
    }
    
    /**
     * Equip a prize for a user.
     *
     * @param int $userId
     * @param int $prizeId
     * @return array
     */
    public function equipPrize($userId, $prizeId)
    {
        // Check if the user has this prize
        $userPrize = UserPrize::where('telegram_user_id', $userId)
            ->where('prize_id', $prizeId)
            ->first();
            
        if (!$userPrize) {
            return [
                'success' => false,
                'message' => 'You have not unlocked this prize'
            ];
        }
        
        // Equip the prize
        $userPrize->equip();
        
        return [
            'success' => true,
            'message' => 'Prize equipped successfully',
            'prize_id' => $prizeId,
            'reward_type' => $userPrize->prize->reward_type,
            'reward_details' => $userPrize->prize->getRewardDetails()
        ];
    }
    
    /**
     * Unequip a prize for a user.
     *
     * @param int $userId
     * @param int $prizeId
     * @return array
     */
    public function unequipPrize($userId, $prizeId)
    {
        // Check if the user has this prize
        $userPrize = UserPrize::where('telegram_user_id', $userId)
            ->where('prize_id', $prizeId)
            ->first();
            
        if (!$userPrize) {
            return [
                'success' => false,
                'message' => 'You have not unlocked this prize'
            ];
        }
        
        // Unequip the prize
        $userPrize->unequip();
        
        return [
            'success' => true,
            'message' => 'Prize unequipped successfully',
            'prize_id' => $prizeId
        ];
    }
}
