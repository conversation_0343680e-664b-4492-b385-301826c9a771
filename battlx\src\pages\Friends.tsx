import CopyIcon from "@/components/icons/CopyIcon";
import { But<PERSON> } from "@/components/ui/button";
import { $http } from "@/lib/http";
import { compactNumber, sanitizeText } from "@/lib/utils";
import { uesStore } from "@/store";
import { useUserStore } from "@/store/user-store";
import { PaginationResponse } from "@/types/Response";
import { UserType } from "@/types/UserType";
import { useQuery } from "@tanstack/react-query";
import { useCopyToClipboard } from "@uidotdev/usehooks";
import { BattlxIcon } from "@/components/icons/BattlxIcon";
import { useMemo, useState } from "react";
import { toast } from "react-toastify";
import { ReferralTaskType } from "@/types/TaskType";

// Encode share message at build time
const shareMessage = encodeURI("Join the dark realm of Battlx with me!");

export default function Friends() {
  const [, copy] = useCopyToClipboard();
  const { telegram_id } = useUserStore();
  const { referral, levels } = uesStore();
  const [showMoreBonuses, setShowMoreBonuses] = useState(false);

  // Create referral link using memo to prevent unnecessary recalculation
  const referralLink = useMemo(() => {
    const baseUrl = import.meta.env.VITE_BOT_URL || '';
    // Validate telegram_id is present and is a valid format
    if (!telegram_id || !/^\d+$/.test(telegram_id.toString())) {
      return '';
    }
    return `${baseUrl}/?startapp=ref${telegram_id}`;
  }, [telegram_id]);

  const { data: referredUsersData, isLoading: isReferredUsersLoading, error: referredUsersError } = useQuery({
    queryKey: ["referredUsers"],
    queryFn: () => $http.$get<PaginationResponse<UserType>>("/referred-users"),
    retry: 2,
    staleTime: 30000,
  });

  const { data: referralTasksData, error: referralTasksError } = useQuery({
    queryKey: ["referral-tasks"],
    queryFn: () => $http.$get<ReferralTaskType[]>("/clicker/referral-tasks"),
    retry: 2,
    staleTime: 30000,
  });

  // Handle API errors
  if (referredUsersError || referralTasksError) {
    toast.error("Failed to load data. Please try again.");
  }

  const currentFriendsCount = referredUsersData?.meta?.total || 0;

  const nextTask = useMemo(() => {
    if (!referralTasksData) return null;

    try {
      const incompleteTasks = referralTasksData
        .filter(task => !task.is_completed)
        .sort((a, b) => a.number_of_referrals - b.number_of_referrals);

      return incompleteTasks.find(task => task.number_of_referrals > currentFriendsCount);
    } catch (error) {
      console.error('Error processing task data:', error);
      return null;
    }
  }, [referralTasksData, currentFriendsCount]);

  const progressPercent = useMemo(() => {
    if (!nextTask || !referralTasksData) return 0;

    try {
      const previousTask = referralTasksData
        .filter(task => !task.is_completed && task.number_of_referrals <= currentFriendsCount)
        .sort((a, b) => a.number_of_referrals - b.number_of_referrals)
        .pop();

      const startCount = previousTask ? previousTask.number_of_referrals : 0;
      const progress = ((currentFriendsCount - startCount) / (nextTask.number_of_referrals - startCount)) * 100;
      return Math.min(100, Math.max(0, progress));
    } catch (error) {
      console.error('Error calculating progress:', error);
      return 0;
    }
  }, [nextTask, currentFriendsCount, referralTasksData]);

  // Safe copy function
  const handleCopy = () => {
    if (!referralLink) {
      toast.error("Invalid referral link");
      return;
    }
    copy(referralLink);
    toast.success("Dark pact link copied");
  };

  // Safe share function
  const handleShare = () => {
    if (!referralLink) {
      toast.error("Invalid referral link");
      return;
    }
    Telegram.WebApp.openTelegramLink(
      `https://t.me/share/url?text=${shareMessage}&url=${referralLink}`
    );
  };

  return (
    <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1">
      <div className="flex flex-col flex-1 w-full px-6 py-8 pb-24 mt-2 modal-body">
        <h1 className="mt-4 text-2xl font-bold font-gothic text-center uppercase text-[#9B8B6C]">
          Friends
        </h1>
        <p className="mt-2 text-sm text-[#B3B3B3]/80 text-center">
          Summon allies to receive dark rewards
        </p>

        <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-3">
          <button className="flex items-center w-full gap-3 px-4 py-3.5 bg-[#120D0E] rounded-xl border border-[#B3B3B3]/20 relative overflow-hidden transform transition-all duration-500 hover:scale-[1.02] active:scale-[0.98] before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.07)_0px,rgba(155,139,108,0.07)_1px,transparent_1px,transparent_10px)] after:absolute after:inset-0 after:opacity-0 after:bg-gradient-to-r after:from-[#4A0E0E]/40 after:to-transparent after:transition-opacity hover:after:opacity-100 shadow-[0_4px_20px_rgba(74,14,14,0.4)]">
            <div className="relative z-10 flex items-center gap-3 w-full">
              <div className="text-sm font-medium text-left min-w-0">
                <p className="text-[#B3B3B3] text-base mb-0.5 truncate">Invite a friend</p>
                <div className="flex items-center space-x-1.5 flex-wrap">
                  <BattlxIcon
                    icon="coins"
                    className="w-5 h-5 opacity-90 text-[#9B8B6C]"
                  />
                  <span className="font-bold text-[#9B8B6C] whitespace-nowrap">
                    +{referral.base.welcome.toLocaleString()}
                  </span>
                  <span className="text-sm text-[#B3B3B3]/70 whitespace-nowrap">for both</span>
                </div>
              </div>
            </div>
          </button>

          <button className="flex items-center w-full gap-3 px-4 py-3.5 bg-[#120D0E] rounded-xl border border-[#B3B3B3]/20 relative overflow-hidden transform transition-all duration-500 hover:scale-[1.02] active:scale-[0.98] before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.07)_0px,rgba(155,139,108,0.07)_1px,transparent_1px,transparent_10px)] after:absolute after:inset-0 after:opacity-0 after:bg-gradient-to-r after:from-[#4A0E0E]/40 after:to-transparent after:transition-opacity hover:after:opacity-100 shadow-[0_4px_20px_rgba(74,14,14,0.4)]">
            <div className="relative z-10 flex items-center gap-3 w-full">
              <div className="text-sm font-medium min-w-0">
                <p className="text-[#B3B3B3] text-base mb-0.5 truncate">Invite Premium friend</p>
                <div className="flex items-center space-x-1.5 flex-wrap">
                  <BattlxIcon
                    icon="coins"
                    className="w-5 h-5 opacity-90 text-[#9B8B6C]"
                  />
                  <span className="font-bold text-[#9B8B6C] whitespace-nowrap">
                    +{referral.premium.welcome.toLocaleString()}
                  </span>
                  <span className="text-sm text-[#B3B3B3]/70 whitespace-nowrap">for both</span>
                </div>
              </div>
            </div>
          </button>
        </div>

        <div className="relative flex-1 mt-2">
          <div className="w-full py-4 mt-4 overflow-y-auto scrollbar-thin scrollbar-thumb-[#4A0E0E] scrollbar-track-[#1A1617]">
            {!showMoreBonuses ? (
              <div className="text-center">
                <button
                  className="px-6 py-2.5 text-sm font-medium transition-all duration-300 rounded-lg border border-[#B3B3B3]/20 bg-[#120D0E] text-[#9B8B6C] shadow-[0_4px_15px_rgba(74,14,14,0.4)] hover:bg-[#4A0E0E]/30 transform hover:scale-105 active:scale-95"
                  onClick={() => setShowMoreBonuses((value) => !value)}
                >
                  Reveal Dark Rewards
                </button>
              </div>
            ) : (
              <>
                <p
                  className="mt-8 text-base font-bold uppercase tracking-wider text-[#9B8B6C] cursor-pointer hover:text-[#B3B3B3] transition-colors"
                  onClick={() => setShowMoreBonuses((value) => !value)}
                >
                  Ascension Rewards
                </p>
                <div className="relative flex-1 mt-4 min-h-60">
                  <div className="w-full overflow-y-auto scrollbar-thin scrollbar-thumb-[#4A0E0E] scrollbar-track-[#1A1617]">
                    <table className="w-full">
                      <thead className="text-sm text-[#9B8B6C]/50">
                        <tr className="border-b border-[#D9D9D9]/10">
                          <th className="px-3 py-2.5 text-left">Level</th>
                          <th className="px-3 py-2.5 text-right">Reward</th>
                          <th className="px-3 py-2.5 text-right">Premium</th>
                        </tr>
                      </thead>
                      <tbody>
                        {levels
                          .filter((item) => referral.base.levelUp[item.level])
                          .map((item, key) => (
                            <tr
                              key={key}
                              className="border-b border-[#D9D9D9]/10 hover:bg-[#4A0E0E]/10 transition-colors"
                            >
                              <td className="px-3 py-3 text-sm text-[#B3B3B3]">{sanitizeText(item.name)}</td>
                              <td className="px-3 py-3">
                                <div className="flex items-center justify-end gap-1.5">
                                  <BattlxIcon
                                    icon="coins"
                                    className="w-4 h-4 text-[#9B8B6C]"
                                  />
                                  <span className="text-sm font-medium text-[#9B8B6C]">
                                    {referral.base.levelUp[
                                      item.level
                                    ].toLocaleString()}
                                  </span>
                                </div>
                              </td>
                              <td className="px-3 py-3">
                                <div className="flex items-center justify-end gap-1.5">
                                  <BattlxIcon icon="coins" className="w-4 h-4 text-[#9B8B6C]" />
                                  <span className="text-sm font-medium text-[#9B8B6C]">
                                    {(
                                      referral.premium.levelUp[item.level] || 0
                                    ).toLocaleString()}
                                  </span>
                                </div>
                              </td>
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </>
            )}

            <p className="mt-8 text-base font-bold uppercase tracking-wider text-[#9B8B6C]">
              Your Dark Alliance {" "}
              {referredUsersData?.meta
                ? `(${referredUsersData.meta.total})`
                : null}
            </p>

            {isReferredUsersLoading ? (
              <div className="flex items-center justify-center w-full h-16">
                <div className="w-6 h-6 border-2 border-t-[#9B8B6C] rounded-full border-[#4A0E0E] animate-spin"></div>
              </div>
            ) : referredUsersData?.data?.length ? (
              <div className="mt-4 space-y-3">
                {referredUsersData.data.map((item, key) => (
                  <div
                    key={key}
                    className="flex items-center justify-between px-5 py-4 bg-[#120D0E] rounded-xl border border-[#B3B3B3]/20 relative overflow-hidden transform transition-all duration-500 hover:scale-[1.02] shadow-[0_4px_20px_rgba(74,14,14,0.4)] border-t-2 border-t-[#4A0E0E]/40">
                    {/* Gothic decorative elements */}
                    <div className="absolute inset-0 pointer-events-none bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.07)_0px,rgba(155,139,108,0.07)_1px,transparent_1px,transparent_10px)]"></div>
                    <div className="absolute top-0 left-0 w-3 h-3 border-t border-l border-[#9B8B6C]/30"></div>
                    <div className="absolute top-0 right-0 w-3 h-3 border-t border-r border-[#9B8B6C]/30"></div>
                    <div className="absolute bottom-0 left-0 w-3 h-3 border-b border-l border-[#9B8B6C]/30"></div>
                    <div className="absolute bottom-0 right-0 w-3 h-3 border-b border-r border-[#9B8B6C]/30"></div>
                    <div className="relative z-10 flex items-center justify-between w-full">
                    <div className="flex items-center gap-3">
                      <BattlxIcon
                        icon="avatar"
                        className="w-10 h-10 text-[#9B8B6C]"
                      />
                      <div>
                        <p className="text-base font-medium text-[#B3B3B3] font-gothic">
                          {sanitizeText(`${item.first_name} ${item.last_name}`)}
                        </p>
                        <p className="text-sm text-[#9B8B6C]/70">{item.level?.name && sanitizeText(item.level.name)}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <BattlxIcon
                        icon="coins"
                        className="w-5 h-5 opacity-90 text-[#9B8B6C]"
                      />
                      <span className="text-base font-medium text-[#9B8B6C]">
                        {compactNumber(item.balance)}
                      </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center px-5 mt-4 border-2 border-dashed rounded-xl border-[#B3B3B3]/20 h-16 bg-[#120D0E] shadow-[0_4px_20px_rgba(74,14,14,0.4)] relative overflow-hidden">
                <div className="absolute inset-0 pointer-events-none bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]"></div>
                <div className="absolute top-0 left-0 w-3 h-3 border-t border-l border-[#9B8B6C]/20"></div>
                <div className="absolute top-0 right-0 w-3 h-3 border-t border-r border-[#9B8B6C]/20"></div>
                <div className="absolute bottom-0 left-0 w-3 h-3 border-b border-l border-[#9B8B6C]/20"></div>
                <div className="absolute bottom-0 right-0 w-3 h-3 border-b border-r border-[#9B8B6C]/20"></div>
                <p className="text-sm font-medium text-center text-[#B3B3B3]/50 relative z-10 font-gothic">
                  Your alliance awaits its first member
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="fixed bottom-40 left-0 right-0 px-7 sm:px-9">
          {nextTask && (
            <div className="bg-[#120D0E] rounded-xl p-4 border border-[#B3B3B3]/20 relative overflow-hidden shadow-[0_4px_20px_rgba(74,14,14,0.4)] border-t-2 border-t-[#4A0E0E]/40">
              {/* Gothic diagonal pattern */}
              <div className="absolute inset-0 pointer-events-none bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.07)_0px,rgba(155,139,108,0.07)_1px,transparent_1px,transparent_10px)]"></div>
              <div className="relative z-10">
              <div className="flex justify-between mb-2">
                <span className="text-[#9B8B6C]">Next Reward: {nextTask.reward.toLocaleString()}</span>
                <span className="text-[#9B8B6C]">{currentFriendsCount}/{nextTask.number_of_referrals}</span>
              </div>
              <div className="h-2 bg-[#1A1617] rounded overflow-hidden border-t border-[#B3B3B3]/10 shadow-[0_2px_8px_rgba(74,14,14,0.3)_inset]">
                <div
                  className="h-full bg-gradient-to-r from-[#4A0E0E] to-[#9B8B6C]/70 transition-all duration-500 ease-out"
                  style={{ width: `${progressPercent}%` }}
                />
              </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex gap-3 mt-4">
          <Button
            className="flex-shrink-0 bg-[#120D0E] text-[#9B8B6C] border border-[#B3B3B3]/20 hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_20px_rgba(74,14,14,0.4)] px-5 py-3 transform hover:scale-105 active:scale-95 relative overflow-hidden"
            onClick={handleCopy}
          >
            <div className="absolute inset-0 pointer-events-none bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.07)_0px,rgba(155,139,108,0.07)_1px,transparent_1px,transparent_10px)]"></div>
            <div className="absolute top-0 left-0 w-3 h-3 border-t border-l border-[#9B8B6C]/30"></div>
            <div className="absolute top-0 right-0 w-3 h-3 border-t border-r border-[#9B8B6C]/30"></div>
            <div className="absolute bottom-0 left-0 w-3 h-3 border-b border-l border-[#9B8B6C]/30"></div>
            <div className="absolute bottom-0 right-0 w-3 h-3 border-b border-r border-[#9B8B6C]/30"></div>
            <div className="relative z-10">
            <CopyIcon className="w-5 h-5" />
            </div>
          </Button>
          <Button
            className="flex-1 bg-[#120D0E] text-[#9B8B6C] border border-[#B3B3B3]/20 hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_20px_rgba(74,14,14,0.4)] px-5 py-3 text-base transform hover:scale-105 active:scale-95 relative overflow-hidden"
            onClick={handleShare}
          >
            <div className="absolute inset-0 pointer-events-none bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.07)_0px,rgba(155,139,108,0.07)_1px,transparent_1px,transparent_10px)]"></div>
            <div className="absolute top-0 left-0 w-3 h-3 border-t border-l border-[#9B8B6C]/30"></div>
            <div className="absolute top-0 right-0 w-3 h-3 border-t border-r border-[#9B8B6C]/30"></div>
            <div className="absolute bottom-0 left-0 w-3 h-3 border-b border-l border-[#9B8B6C]/30"></div>
            <div className="absolute bottom-0 right-0 w-3 h-3 border-b border-r border-[#9B8B6C]/30"></div>
            <div className="relative z-10 font-gothic tracking-wide">
            Summon an Ally
            </div>
          </Button>
        </div>
      </div>
    </div>
  );
}
