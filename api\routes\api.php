<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ClickerController;
use App\Http\Controllers\GameController;
use App\Http\Controllers\LevelController;
use App\Http\Controllers\PopupController;
use App\Http\Controllers\ReferralTaskController;
use App\Http\Controllers\TelegramUserController;
use App\Http\Controllers\UserMissionController;
use App\Http\Controllers\UserTaskController;
use App\Http\Controllers\WalletController;
use App\Http\Controllers\PrizeTreeController;
use App\Http\Controllers\AchievementPointController;
use App\Http\Controllers\PetController;
use App\Http\Controllers\PetShopController;
use App\Http\Controllers\CollectibleController;
use App\Http\Controllers\MysteryBoxController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::post('/auth/telegram-user', [AuthController::class, 'telegramUser']);

// popup messages
Route::get('/popups', [PopupController::class, 'index']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {

    Route::get('referred-users', [TelegramUserController::class, 'referredUsers']);

    // Clicker game routes
    Route::prefix('clicker')->group(function () {
        // Sync user data
        Route::get('/sync', [ClickerController::class, 'sync']);

        // Tapping
        Route::post('/tap', [ClickerController::class, 'tap']);

        // Boosters
        Route::post('/buy-booster', [ClickerController::class, 'buyBooster']);

        // Booster packs
        Route::post('/buy-booster-pack', [ClickerController::class, 'buyBoosterPack']);

        // Daily tasks
        Route::get('/daily-tasks', [ClickerController::class, 'listDailyTasks']);
        Route::post('/claim-daily-task', [ClickerController::class, 'claimDailyTaskReward']);

        // Regular tasks
        Route::get('tasks', [UserTaskController::class, 'index']);
        Route::post('tasks/{task}', [UserTaskController::class, 'store']);
        Route::post('tasks/{task}/claim', [UserTaskController::class, 'claim']);

        // Referral tasks
        Route::get('referral-tasks', [ReferralTaskController::class, 'index']);
        Route::post('referral-tasks/{task}/complete', [ReferralTaskController::class, 'complete']);

        // Leaderboard
        Route::get('/leaderboard', [ClickerController::class, 'listLeaderboard']);

        // Daily booster (energy restore)
        Route::post('/use-daily-booster', [ClickerController::class, 'useDailyBooster']);

        // Wallet
        Route::get('/wallet', [WalletController::class, 'index']);
        Route::post('/set-ton-wallet', [WalletController::class, 'store']);

        // Missions
        Route::get('missions', [UserMissionController::class, 'index']);

        Route::post('mission-levels/{missionLevel}', [UserMissionController::class, 'store']);

        Route::post('/test', function (Request $request) {
            $request->validate([
                'hash' => 'required|string',
                'source' => 'required|string',
                'destination' => 'required|string',
                'amount' => 'required|numeric',
                'amountInNano' => 'required|string',
            ]);
            $response = Http::get("https://testnet.toncenter.com/api/v3/transactionsByMessage", [
                'msg_hash' => $request->hash,
                'limit' => 1,
                'offset' => 0,
                'sort' => 'desc',
            ]);
            $body = $response->json();
            if (!$response->ok() || !isset($body['transactions'][0]['out_msgs'][0])) {
                return response()->json(['message' => 'Transaction not found'], 404);
            }
            $outMsg = $body['transactions'][0]['out_msgs'][0];
            $isValid = $outMsg['value'] === $request->amountInNano
                && strcasecmp($outMsg['source'], $request->source) === 0
                && strcasecmp($outMsg['destination'], $request->destination) === 0;
            return [
                'source' => $outMsg['source'],
                'destination' => $outMsg['destination'],
                'value' => $outMsg['value'],
                'is_valid' => $isValid
            ];
        });

    });

    Route::get('levels', [LevelController::class, 'index']);

    // Game routes
    Route::prefix('game')->group(function () {
        Route::post('/score', [GameController::class, 'updateScore']);
        Route::get('/leaderboard', [GameController::class, 'leaderboard']);
        Route::get('/play-availability', [GameController::class, 'checkPlayAvailability']);
        Route::post('/use-play', [GameController::class, 'usePlay']);
        Route::post('/unlock', [GameController::class, 'unlockGame']);
    });
    
    // Prize Tree routes
    Route::prefix('prizes')->group(function () {
        Route::get('/trees', [PrizeTreeController::class, 'index']);
        Route::get('/trees/{id}', [PrizeTreeController::class, 'show']);
        Route::get('/user', [PrizeTreeController::class, 'getUserPrizes']);
        Route::post('/unlock', [PrizeTreeController::class, 'unlockPrize']);
        Route::post('/equip', [PrizeTreeController::class, 'equipPrize']);
        Route::post('/unequip', [PrizeTreeController::class, 'unequipPrize']);
        Route::get('/transactions', [PrizeTreeController::class, 'getTransactions']);
    });
    
    // Achievement Point routes
    Route::prefix('achievement-points')->group(function () {
        Route::post('/award', [AchievementPointController::class, 'awardPoints']);
        Route::post('/award-mission', [AchievementPointController::class, 'awardMissionPoints']);
        Route::post('/award-tap-milestone', [AchievementPointController::class, 'awardTapMilestonePoints']);
    });

    // Pet System routes
    Route::prefix('pets')->group(function () {
        // Pet collection and management
        Route::get('/', [PetController::class, 'index']);
        Route::get('/featured', [PetController::class, 'getFeaturedPet']);
        Route::get('/needing-attention', [PetController::class, 'getPetsNeedingAttention']);
        Route::post('/purchase', [PetController::class, 'purchase']);
        Route::put('/{pet}/featured', [PetController::class, 'setFeaturedPet']);
        Route::put('/{pet}/nickname', [PetController::class, 'updateNickname']);
        Route::put('/{pet}/favorite', [PetController::class, 'toggleFavorite']);
        Route::post('/{pet}/interact', [PetController::class, 'interact']);
        Route::get('/{pet}/interactions', [PetController::class, 'getInteractionHistory']);
    });

    // Pet Shop routes
    Route::prefix('pet-shop')->group(function () {
        Route::get('/', [PetShopController::class, 'index']);
        Route::get('/featured', [PetShopController::class, 'getFeaturedPets']);
        Route::get('/category/{category}', [PetShopController::class, 'getByCategory']);
        Route::get('/{petTemplate}', [PetShopController::class, 'show']);
    });

    // Collectibles routes
    Route::prefix('collectibles')->group(function () {
        Route::get('/', [CollectibleController::class, 'index']);
        Route::get('/collections', [CollectibleController::class, 'getCollectionSets']);
        Route::get('/collections/{collectionSet}', [CollectibleController::class, 'getCollectionSetDetails']);
        Route::post('/collections/{collectionSet}/claim', [CollectibleController::class, 'claimCollectionRewards']);
        Route::get('/category/{category}', [CollectibleController::class, 'getByCategory']);
        Route::get('/rarity/{rarity}', [CollectibleController::class, 'getByRarity']);
        Route::get('/recent', [CollectibleController::class, 'getRecentlyObtained']);
        Route::get('/statistics', [CollectibleController::class, 'getStatistics']);
    });

    // Mystery Box routes
    Route::prefix('mystery-boxes')->group(function () {
        Route::get('/', [MysteryBoxController::class, 'index']);
        Route::get('/all-types', [MysteryBoxController::class, 'getAllBoxTypes']);
        Route::get('/unlocked', [MysteryBoxController::class, 'getUnlockedBoxes']);
        Route::post('/open', [MysteryBoxController::class, 'openBox']);
        Route::get('/history', [MysteryBoxController::class, 'getOpeningHistory']);
        Route::get('/statistics', [MysteryBoxController::class, 'getStatistics']);
        Route::get('/{boxType}/preview', [MysteryBoxController::class, 'previewRewards']);
        Route::get('/{mysteryBoxType}', [MysteryBoxController::class, 'show']);
    });
});
