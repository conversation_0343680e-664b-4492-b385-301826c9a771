<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_collection_progress', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->string('set_id', 50); // References collection_sets.set_id
            
            $table->integer('collectibles_owned')->default(0);
            $table->integer('total_collectibles');
            $table->decimal('completion_percentage', 5, 2)->default(0.00);
            
            $table->boolean('is_completed')->default(false);
            $table->timestamp('completed_at')->nullable();
            $table->boolean('rewards_claimed')->default(false);
            $table->timestamp('rewards_claimed_at')->nullable();
            
            $table->json('owned_collectible_ids'); // Array of collectible IDs owned
            $table->json('missing_collectible_ids'); // Array of collectible IDs still needed
            
            $table->timestamps();
            
            $table->unique(['telegram_user_id', 'set_id']);
            $table->index(['telegram_user_id', 'is_completed']);
            
            $table->foreign('set_id')
                  ->references('set_id')
                  ->on('collection_sets')
                  ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_collection_progress');
    }
};
