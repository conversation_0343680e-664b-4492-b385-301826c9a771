export interface GameConfig {
  id: string;
  title: string;
  description: string;
  thumbnail?: string;
  canvasId: string;
  unlockRequirement: {
    type: 'level' | 'balance';
    value: number;
  };
}

export interface LoadingStatus {
  success: number;
  total: number;
  failed: number;
}

/**
 * Core game instance interface matching engine's expectations
 */
export interface GameInstance {
  // Core lifecycle methods
  init?: () => void;
  start?: () => void;
  destroy?: () => void;

  // Asset loading
  load: (
    onReady: () => void,
    onProgress: (status: LoadingStatus) => void
  ) => void;

  // Audio methods
  playBgm?: () => void;
  pauseBgm?: () => void;
  playAudio?: (id: string) => void;
  pauseAudio?: (id: string) => void;

  // State management
  setVariable?: (name: string, value: any) => void;
  getVariable?: (name: string) => any;

  // Event and resource management
  clearEventListeners?: () => void;
  resetGameState?: () => void;
}

export const GAMES: Record<string, GameConfig> = {
  tower: {
    id: 'tower',
    title: 'Tower Game',
    description: 'Stack blocks to build the highest tower!',
    thumbnail: '/game/thumbnails/tower.png',
    unlockRequirement: {
      type: 'level',
      value: 1
    },
    canvasId: 'tower-game-canvas'
  },
  rabbit: {
    id: 'rabbit',
    title: 'Rabbit Game',
    description: 'Help the rabbit collect diamonds and avoid obstacles!',
    thumbnail: '/game/thumbnails/rabbit.png',
    unlockRequirement: {
      type: 'balance',
      value: 5000
    },
    canvasId: 'rabbit-game-canvas'
  },
  slash: {
    id: 'slash',
    title: 'Slash Game',
    description: 'Fast-paced action slashing game!',
    thumbnail: '/game/thumbnails/slash.png',
    unlockRequirement: {
      type: 'balance',
      value: 5000
    },
    canvasId: 'slash-game-canvas'
  }
};

// Dynamic game module imports
export const gameModules: Record<string, () => Promise<(options: any) => GameInstance>> = {
  tower: async () => {
    try {
      const module = await import('../tower_game/src/index');
      return module.TowerGame;
    } catch (error) {
      console.error('Error loading Tower Game module:', error);
      throw new Error('Failed to load game module');
    }
  },
  rabbit: async () => {
    try {
      const module = await import('../rabbit_game/src/main');
      if (!module.rabbitGame) {
         throw new Error("rabbitGame function not found in module");
      }
      return module.rabbitGame;
    } catch (error) {
      console.error('Error loading Rabbit Game module:', error);
      throw new Error('Failed to load game module');
    }
  },
  slash: async () => {
    try {
      const module = await import('../slash_game/src/main');
      if (!module.slashGame) {
        throw new Error("slashGame function not found in module");
      }
      return module.slashGame;
    } catch (error) {
      console.error('Error loading Slash Game module:', error);
      throw new Error('Failed to load game module');
    }
  }
};
