# Prize Tree System - Integrations (Part 1)

## Integration with Existing Systems

This document outlines how the Prize Tree system will integrate with existing features in the BattlX application, including missions, the combo system, energy system, and more.

## 1. Integration with Mission System

### 1.1 Mission Completion Rewards

Modify the mission completion logic to award achievement points:

```php
// app/Http/Controllers/API/MissionController.php

// In the completeMission method
public function completeMission(Request $request)
{
    // Existing mission completion logic
    // ...
    
    // Award achievement points for completing the mission
    $achievementPointService = new AchievementPointService();
    $achievementPointReward = $mission->difficulty; // Base reward on mission difficulty
    
    $achievementPointService->awardPoints(
        $user->id,
        $achievementPointReward,
        'mission_complete',
        $mission->id,
        "Completed mission: {$mission->name}"
    );
    
    // Return response with achievement points included
    return response()->json([
        'message' => 'Mission completed successfully',
        'rewards' => [
            // Existing rewards
            // ...
            'achievement_points' => $achievementPointReward
        ]
    ]);
}
```

### 1.2 Mission Card Rewards

Integrate mission cards with the Prize Tree system:

```php
// app/Services/MissionService.php

/**
 * Award a card to a user upon completing a mission.
 */
public function awardCardToUser($userId, $missionId)
{
    $mission = Mission::find($missionId);
    
    if (!$mission || !$mission->has_card_reward) {
        return null;
    }
    
    // Get the card associated with this mission
    $card = Card::where('mission_id', $missionId)->first();
    
    if (!$card) {
        return null;
    }
    
    // Check if the user already has this card
    $userCard = UserCard::where('telegram_user_id', $userId)
        ->where('card_id', $card->id)
        ->first();
        
    if ($userCard) {
        // User already has this card, award achievement points instead
        $achievementPointService = new AchievementPointService();
        $achievementPointService->awardPoints(
            $userId,
            2, // Award 2 points for duplicate cards
            'duplicate_card',
            $card->id,
            "Received duplicate card: {$card->name}"
        );
        
        return [
            'type' => 'achievement_points',
            'amount' => 2,
            'card_name' => $card->name
        ];
    }
    
    // Award the card to the user
    UserCard::create([
        'telegram_user_id' => $userId,
        'card_id' => $card->id,
        'acquired_at' => now()
    ]);
    
    // Check if this completes a card collection
    $this->checkCardCollectionCompletion($userId, $card);
    
    return [
        'type' => 'card',
        'card' => $card
    ];
}

/**
 * Check if a user has completed a card collection.
 */
private function checkCardCollectionCompletion($userId, $card)
{
    // Get the collection this card belongs to
    $collectionId = $card->collection_id;
    
    if (!$collectionId) {
        return;
    }
    
    // Get all cards in this collection
    $collectionCards = Card::where('collection_id', $collectionId)->get();
    $collectionCardIds = $collectionCards->pluck('id')->toArray();
    
    // Get user's cards in this collection
    $userCardIds = UserCard::where('telegram_user_id', $userId)
        ->whereIn('card_id', $collectionCardIds)
        ->pluck('card_id')
        ->toArray();
    
    // Check if user has all cards in the collection
    $isComplete = count(array_intersect($collectionCardIds, $userCardIds)) === count($collectionCardIds);
    
    if ($isComplete) {
        // Get or create user collection record
        $userCollection = UserCollection::firstOrCreate([
            'telegram_user_id' => $userId,
            'collection_type' => 'card_set',
            'collection_id' => $collectionId
        ]);
        
        // Update collection status if not already complete
        if (!$userCollection->is_complete) {
            $userCollection->is_complete = true;
            $userCollection->completed_at = now();
            $userCollection->collected_items = $collectionCardIds;
            $userCollection->save();
            
            // Award achievement points for completing the collection
            $achievementPointService = new AchievementPointService();
            $achievementPointService->awardPoints(
                $userId,
                5, // Award 5 points for completing a collection
                'collection_complete',
                $userCollection->id,
                "Completed card collection: {$collectionId}"
            );
        }
    }
}
```

### 1.3 Special Mission Types

Create special missions that award unique prizes:

```php
// app/Http/Controllers/API/MissionController.php

/**
 * Get special missions that award unique prizes.
 */
public function getSpecialMissions(Request $request)
{
    $user = $request->user();
    
    // Get special missions
    $specialMissions = Mission::where('is_special', true)
        ->where('is_active', true)
        ->get();
    
    // Get user's completed special missions
    $completedMissionIds = UserMission::where('telegram_user_id', $user->id)
        ->where('is_completed', true)
        ->pluck('mission_id')
        ->toArray();
    
    // Add completion status to missions
    $specialMissions->each(function ($mission) use ($completedMissionIds) {
        $mission->is_completed = in_array($mission->id, $completedMissionIds);
        
        // Get the prize associated with this mission
        $prize = Prize::where('reward_data->source', 'special_mission')
            ->where('reward_data->mission_id', $mission->id)
            ->first();
            
        if ($prize) {
            $mission->prize = [
                'id' => $prize->id,
                'name' => $prize->name,
                'description' => $prize->description,
                'reward_type' => $prize->reward_type,
                'reward_details' => $prize->getRewardDetails()
            ];
        }
    });
    
    return response()->json($specialMissions);
}
```

## 2. Integration with Combo System

### 2.1 Combo Milestone Achievement Points

Award achievement points for reaching combo milestones:

```php
// app/Services/ComboService.php

/**
 * Process combo milestones and award achievement points.
 */
public function processComboMilestones($user, $comboCount)
{
    // Define milestone thresholds
    $milestones = [10, 25, 50, 100, 250, 500, 1000];
    
    // Check if the current combo count has reached a milestone
    foreach ($milestones as $milestone) {
        if ($comboCount === $milestone) {
            // Award achievement points
            $achievementPointService = new AchievementPointService();
            
            // Award more points for higher milestones
            $points = 1;
            if ($milestone >= 100) $points = 3;
            if ($milestone >= 500) $points = 5;
            
            $achievementPointService->awardPoints(
                $user->id,
                $points,
                'combo_milestone',
                $milestone,
                "Reached combo milestone: {$milestone}x"
            );
            
            // Dispatch event for frontend notification
            event(new ComboMilestoneReached($user, $milestone, $points));
            
            break;
        }
    }
}
```

### 2.2 Combo Visual Effects from Prize Tree

Implement visual effects for combos based on unlocked prizes:

```php
// app/Http/Controllers/API/ComboController.php

/**
 * Get the user's combo visual effects.
 */
public function getComboEffects(Request $request)
{
    $user = $request->user();
    
    // Get user's equipped cosmetic prizes
    $equippedPrizes = $user->prizes()
        ->wherePivot('is_equipped', true)
        ->whereHas('prize', function ($query) {
            $query->where('reward_type', 'cosmetic');
        })
        ->get();
    
    // Filter for combo-related effects
    $comboEffects = [];
    
    foreach ($equippedPrizes as $userPrize) {
        $prize = $userPrize->prize;
        $rewardDetails = $prize->getRewardDetails();
        
        if ($rewardDetails['type'] === 'combo_effect') {
            $comboEffects[] = [
                'id' => $prize->id,
                'name' => $prize->name,
                'effect_data' => $rewardDetails['visual_data'] ?? []
            ];
        }
    }
    
    return response()->json($comboEffects);
}
```

### 2.3 Combo Cosmetic Rewards

Create special cosmetic rewards for achieving high combos:

```php
// app/Services/ComboService.php

/**
 * Check for and award combo-based cosmetic rewards.
 */
public function checkComboRewards($user, $maxCombo)
{
    // Define combo thresholds for rewards
    $comboRewards = [
        50 => 'bronze_combo_effect',
        100 => 'silver_combo_effect',
        250 => 'gold_combo_effect',
        500 => 'platinum_combo_effect',
        1000 => 'diamond_combo_effect'
    ];
    
    // Get the highest threshold the user has reached
    $highestThreshold = 0;
    foreach ($comboRewards as $threshold => $rewardId) {
        if ($maxCombo >= $threshold && $threshold > $highestThreshold) {
            $highestThreshold = $threshold;
        }
    }
    
    if ($highestThreshold === 0) {
        return null; // No threshold reached
    }
    
    // Check if the user already has this reward
    $rewardId = $comboRewards[$highestThreshold];
    
    // Get the prize associated with this reward ID
    $prize = Prize::where('reward_data->reward_id', $rewardId)->first();
    
    if (!$prize) {
        return null; // Prize not found
    }
    
    // Check if user already has this prize
    $userPrize = UserPrize::where('telegram_user_id', $user->id)
        ->where('prize_id', $prize->id)
        ->first();
        
    if ($userPrize) {
        return null; // User already has this prize
    }
    
    // Award the prize to the user
    $prizeService = new PrizeService(new AchievementPointService());
    
    // For combo rewards, we don't deduct achievement points
    DB::transaction(function () use ($user, $prize) {
        UserPrize::create([
            'telegram_user_id' => $user->id,
            'prize_id' => $prize->id,
            'unlocked_at' => now()
        ]);
    });
    
    return [
        'prize' => $prize,
        'threshold' => $highestThreshold
    ];
}
```

## 3. Integration with Energy System

### 3.1 Energy Cosmetic Effects

Implement visual effects for energy based on unlocked prizes:

```php
// app/Http/Controllers/API/EnergyController.php

/**
 * Get the user's energy visual effects.
 */
public function getEnergyEffects(Request $request)
{
    $user = $request->user();
    
    // Get user's equipped cosmetic prizes
    $equippedPrizes = $user->prizes()
        ->wherePivot('is_equipped', true)
        ->whereHas('prize', function ($query) {
            $query->where('reward_type', 'cosmetic');
        })
        ->get();
    
    // Filter for energy-related effects
    $energyEffects = [];
    
    foreach ($equippedPrizes as $userPrize) {
        $prize = $userPrize->prize;
        $rewardDetails = $prize->getRewardDetails();
        
        if ($rewardDetails['type'] === 'energy_effect') {
            $energyEffects[] = [
                'id' => $prize->id,
                'name' => $prize->name,
                'effect_data' => $rewardDetails['visual_data'] ?? []
            ];
        }
    }
    
    return response()->json($energyEffects);
}
```

### 3.2 Energy Milestone Achievement Points

Award achievement points for energy usage milestones:

```php
// app/Services/EnergyService.php

/**
 * Track energy usage and award achievement points for milestones.
 */
public function trackEnergyUsage($user, $amount)
{
    // Get or create energy usage stats
    $energyStats = UserEnergyStat::firstOrCreate(
        ['telegram_user_id' => $user->id],
        ['total_energy_used' => 0]
    );
    
    // Update total energy used
    $energyStats->total_energy_used += $amount;
    $energyStats->save();
    
    // Check for milestones
    $milestones = [100, 500, 1000, 5000, 10000];
    
    foreach ($milestones as $milestone) {
        // Check if this update crossed a milestone
        if ($energyStats->total_energy_used >= $milestone && 
            ($energyStats->total_energy_used - $amount) < $milestone) {
            
            // Award achievement points
            $achievementPointService = new AchievementPointService();
            
            // Award more points for higher milestones
            $points = 1;
            if ($milestone >= 1000) $points = 2;
            if ($milestone >= 5000) $points = 3;
            
            $achievementPointService->awardPoints(
                $user->id,
                $points,
                'energy_milestone',
                $milestone,
                "Used {$milestone} total energy"
            );
        }
    }
}
```

### 3.3 Energy Booster Prizes

Implement energy boosters as prizes:

```php
// app/Services/PrizeService.php

/**
 * Apply energy booster prize effects.
 */
private function applyEnergyBoosterPrize($userId, $prize)
{
    $rewardDetails = $prize->getRewardDetails();
    
    if ($prize->reward_type !== 'booster' || 
        ($rewardDetails['type'] ?? '') !== 'energy_booster') {
        return;
    }
    
    $user = \App\Models\TelegramUser::find($userId);
    
    if (!$user) {
        return;
    }
    
    $boostAmount = $rewardDetails['amount'] ?? 0;
    $boostDuration = $rewardDetails['duration'] ?? 0; // in hours
    
    if ($boostAmount > 0 && $boostDuration > 0) {
        // Create energy booster record
        EnergyBooster::create([
            'telegram_user_id' => $userId,
            'multiplier' => $boostAmount,
            'expires_at' => now()->addHours($boostDuration),
            'source' => 'prize',
            'source_id' => $prize->id
        ]);
        
        // Notify the user
        // This would depend on your notification system
    }
}
```
