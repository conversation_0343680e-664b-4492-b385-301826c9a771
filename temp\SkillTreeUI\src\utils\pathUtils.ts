interface Position {
  x: number;
  y: number;
}

export const getConnectorPath = (start: Position, end: Position): string => {
  // Convert percentage positions to values between 0-100
  const startX = start.x;
  const startY = start.y;
  const endX = end.x;
  const endY = end.y;
  
  // Calculate control points for a curved path
  const midY = (startY + endY) / 2;
  
  // Create SVG path
  return `M${startX},${startY} C${startX},${midY} ${endX},${midY} ${endX},${endY}`;
};