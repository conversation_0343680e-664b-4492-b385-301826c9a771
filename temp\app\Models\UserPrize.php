<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserPrize extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id',
        'prize_id',
        'unlocked_at',
        'is_equipped'
    ];

    protected $casts = [
        'unlocked_at' => 'datetime',
        'is_equipped' => 'boolean'
    ];

    /**
     * Get the user that owns this prize.
     */
    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    /**
     * Get the prize that is unlocked.
     */
    public function prize()
    {
        return $this->belongsTo(Prize::class);
    }
    
    /**
     * Equip this prize (for cosmetics, titles, etc.)
     */
    public function equip()
    {
        // First, unequip any other prizes of the same type
        $prize = $this->prize;
        
        if (in_array($prize->reward_type, ['cosmetic', 'title', 'emote'])) {
            // Get the specific subtype for cosmetics
            $subtype = null;
            
            if ($prize->reward_type === 'cosmetic') {
                $rewardDetails = $prize->getRewardDetails();
                $subtype = $rewardDetails['type'] ?? null;
            }
            
            // Unequip other prizes of the same type/subtype
            UserPrize::where('telegram_user_id', $this->telegram_user_id)
                ->whereHas('prize', function ($query) use ($prize, $subtype) {
                    $query->where('reward_type', $prize->reward_type);
                    
                    if ($subtype) {
                        $query->whereJsonContains('reward_data->type', $subtype);
                    }
                })
                ->where('id', '!=', $this->id)
                ->update(['is_equipped' => false]);
        }
        
        // Equip this prize
        $this->is_equipped = true;
        $this->save();
        
        return $this;
    }
    
    /**
     * Unequip this prize
     */
    public function unequip()
    {
        $this->is_equipped = false;
        $this->save();
        
        return $this;
    }
}
