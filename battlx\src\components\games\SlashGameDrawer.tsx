import { useMemo } from 'react';
import { Button } from '../ui/button';
import Drawer from '../ui/drawer';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { BattlxIcon } from '@/components/icons/BattlxIcon';
import { useUserStore } from '@/store/user-store';
import { gameApi } from '@/lib/game-api';

const UNLOCK_PRICE = 5000;

type SlashGameDrawerProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export default function SlashGameDrawer({ open, onOpenChange }: SlashGameDrawerProps) {
  const { balance, slash_game_unlocked } = useUserStore(state => ({
    balance: state.balance,
    slash_game_unlocked: state.slash_game_unlocked
  }));

  const insufficientBalance = useMemo(() => {
    return balance < UNLOCK_PRICE;
  }, [balance]);

  const unlockMutation = useMutation({
    mutationFn: () => gameApi.unlockGame('slash'),
    onSuccess: (response) => {
      if (response.success && response.user) {
        toast.success('Slash Game unlocked successfully');
        useUserStore.setState((state) => ({
          ...state,
          ...response.user
        }));
        onOpenChange(false);
      } else {
        toast.error(response.message || 'Failed to unlock game');
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'An error occurred while unlocking the game');
    },
  });

  if (slash_game_unlocked) return null;

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <div className="!rounded-none [&_[data-drawer-content]]:!rounded-none">
        <img
          src="/game/thumbnails/slash.png"
          alt="Slash Game"
          className="object-contain h-32 mx-auto opacity-80 rounded-none [filter:sepia(50%)_saturate(50%)_brightness(80%)_contrast(120%)]"
        />
        <h2 className="mt-6 text-2xl font-medium text-center text-[#9B8B6C]">
          Slash Game
        </h2>
        <div className="flex flex-col mx-auto mt-4 w-fit">
          <p className="text-xs text-center text-[#B3B3B3]/80">Unlock Price</p>
        </div>

        <Button
          className="w-full mt-6 bg-[#1A1617] border border-[#B3B3B3]/20 text-[#9B8B6C] hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_15px_rgba(74,14,14,0.3)] disabled:opacity-50 disabled:cursor-not-allowed rounded-none"
          disabled={unlockMutation.isPending || insufficientBalance}
          onClick={() => unlockMutation.mutate()}
        >
          {unlockMutation.isPending && (
            <BattlxIcon icon="loading" className="w-6 h-6 mr-2 animate-spin" />
          )}
          {insufficientBalance ? "Insufficient Balance" : "Unlock Game"}
        </Button>
      </div>
    </Drawer>
  );
}

