/**
 * Background Entity
 * Handles the scrolling background and platforms
 */
class Background {
    /**
     * @param {Engine} engine - Game engine
     */
    constructor(engine) {
        this.engine = engine;

        // Background properties
        this.bgImage = engine.getImage('background');
        this.platformImage = engine.getImage('platform');
        this.bgWidth = this.bgImage.width;
        this.bgHeight = this.bgImage.height;
        this.platformWidth = this.platformImage.width * 2; // Scale factor
        this.platformHeight = this.platformImage.height * 2; // Scale factor

        // Scrolling properties
        this.speed = CONSTANTS.GAME.BACKGROUND_SPEED;
        this.bgX = [0, this.bgWidth];
        this.platformX = [0, this.platformWidth];

        // Ground position
        this.groundY = engine.height - 60; // Player position + half player height
    }

    /**
     * Update the background
     * @param {number} deltaTime - Time since last update
     * @param {number} playerY - Player Y position for parallax effect
     */
    update(deltaTime, playerY) {
        // Move background pieces
        for (let i = 0; i < this.bgX.length; i++) {
            this.bgX[i] -= this.speed * deltaTime;

            // Reset position when off screen
            if (this.bgX[i] <= -this.bgWidth) {
                this.bgX[i] = this.bgX[(i + 1) % 2] + this.bgWidth - 10; // Increased overlap to prevent gaps
            }
        }

        // Move platform pieces
        for (let i = 0; i < this.platformX.length; i++) {
            this.platformX[i] -= this.speed * deltaTime;

            // Reset position when off screen
            if (this.platformX[i] <= -this.platformWidth) {
                this.platformX[i] = this.platformX[(i + 1) % 2] + this.platformWidth - 10; // Increased overlap to prevent gaps
            }
        }
    }

    /**
     * Increase the background speed
     * @param {number} amount - Amount to increase
     */
    increaseSpeed(amount) {
        this.speed += amount;
    }

    /**
     * Get the ground Y position
     * @returns {number} - Ground Y position
     */
    getGroundY() {
        return this.groundY;
    }

    /**
     * Render the background
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} playerY - Player Y position for parallax effect
     */
    render(ctx, playerY) {
        // Apply parallax effect based on player's vertical position
        const parallaxFactor = 0.1;
        const parallaxOffset = -playerY * parallaxFactor;

        // Draw background pieces
        for (let i = 0; i < this.bgX.length; i++) {
            ctx.drawImage(
                this.bgImage,
                this.bgX[i],
                parallaxOffset,
                this.bgWidth + 2, // Add 2 pixels to width to prevent gaps
                this.bgHeight
            );
        }

        // Draw platform pieces
        for (let i = 0; i < this.platformX.length; i++) {
            ctx.drawImage(
                this.platformImage,
                this.platformX[i],
                this.groundY - this.platformHeight / 2,
                this.platformWidth + 2, // Add 2 pixels to width to prevent gaps
                this.platformHeight
            );
        }

        // Draw ground collision line in debug mode
        if (this.engine.debug) {
            ctx.strokeStyle = 'yellow';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(0, this.groundY);
            ctx.lineTo(this.engine.width, this.groundY);
            ctx.stroke();
        }
    }
}
