# Slash Game Backend Implementation Plan

## Overview
This document outlines the backend implementation plan for the Slash game in the Telegram web app. The Slash game will follow similar patterns to the existing Tower and Rabbit games, with appropriate modifications for the Fruit Ninja-style gameplay.

## Current Status Analysis
- The database already has the `slash_game_unlocked` field in the `telegram_users` table
- The `canPlaySlashGame()` method is implemented in the `TelegramUser` model
- The `GameController` has basic support for the Slash game in the `checkPlayAvailability` and `usePlay` methods
- The Slash game follows the Rabbit game pattern with unlimited plays once unlocked
- The frontend is attempting to load the Slash game module from `slash_game/src/main.ts` but it's missing

## Backend Implementation Tasks

### 1. Fix the `/api/game/use-play` Endpoint
The current error is related to the `use-play` endpoint. The issue appears to be that the frontend is not sending the required `game_id` parameter.

#### Current Implementation Issues:
```php
// In GameController.php
public function usePlay(Request $request)
{
    try {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'paid' => 'required|boolean',
            'quantity' => 'required|integer|min:1|max:100'
        ]);

        // Missing game_id validation
        // ...
    }
}
```

#### Required Changes:
1. Update the validator to require the `game_id` parameter
2. Modify the method to handle different games appropriately
3. Ensure the Slash game follows the Rabbit game pattern with unlimited plays

```php
// Updated validator
$validator = Validator::make($request->all(), [
    'game_id' => 'required|string|in:tower,rabbit,slash',
    'paid' => 'required|boolean',
    'quantity' => 'required|integer|min:1|max:100'
]);

// Updated game handling
$gameId = $request->game_id;

// For Rabbit and Slash games (unlimited plays)
if ($gameId === 'rabbit' || $gameId === 'slash') {
    return response()->json([
        'success' => true,
        'data' => [
            'plays_remaining' => 999, // Unlimited plays
            'balance' => $user->balance
        ]
    ]);
}

// Tower game logic (limited plays)
// ...existing Tower game logic...
```

### 2. Update Game Stats Tracking
Implement proper game statistics tracking for the Slash game.

#### Implementation:
```php
// In GameController.php - updateScore method
public function updateScore(Request $request)
{
    $validator = Validator::make($request->all(), [
        'score' => 'required|integer|min:0',
        'game_id' => 'required|string|in:tower,rabbit,slash'
    ]);
    
    // ...existing validation logic...
    
    try {
        $user = $request->user();
        $score = $request->score;
        $gameId = $request->game_id;
        
        // Update user's overall game score
        if ($score > 0) {
            $user->game_score += $score;
            $user->save();
        }
        
        // Update game-specific stats
        $gameStat = GameStat::firstOrCreate(
            [
                'telegram_user_id' => $user->id,
                'game_id' => $gameId
            ],
            [
                'high_score' => 0,
                'total_score' => 0,
                'plays' => 0
            ]
        );
        
        // Update high score if needed
        if ($score > $gameStat->high_score) {
            $gameStat->high_score = $score;
        }
        
        // Update total score and plays
        $gameStat->total_score += $score;
        $gameStat->plays += 1;
        $gameStat->save();
        
        // Award achievement points based on score milestones
        $this->awardScoreAchievements($user, $score, $gameId);
        
        return response()->json([
            'success' => true,
            'data' => [
                'game_score' => $user->game_score,
                'high_score' => $gameStat->high_score
            ]
        ]);
    } catch (\Exception $e) {
        // ...error handling...
    }
}
```

### 3. Achievement Points for Slash Game
Implement achievement points for the Slash game.

#### Implementation:
```php
// In GameController.php
private function awardScoreAchievements($user, $score, $gameId)
{
    // Define score milestones for achievements
    $milestones = [100, 500, 1000, 5000, 10000];
    
    // Get the appropriate achievement service
    $achievementService = app(AchievementPointService::class);
    
    // Check if score exceeds any milestone
    foreach ($milestones as $milestone) {
        if ($score >= $milestone) {
            // Award points for reaching milestone
            $achievementService->awardPoints(
                $user->id,
                1, // 1 point per milestone
                'game_score',
                $gameId,
                "Scored {$milestone}+ points in {$gameId} game"
            );
            
            // Only award for the highest milestone reached
            break;
        }
    }
}
```

### 4. Update GameController to Handle Slash Game Properly
Ensure the GameController properly handles the Slash game in all relevant methods.

```php
// In GameController.php - unlockGame method
public function unlockGame(Request $request)
{
    // ...existing validation logic...
    
    // Unlock the game
    switch ($gameId) {
        case 'tower':
            $user->tower_game_unlocked = true;
            $user->tower_game_plays = 15;
            $user->tower_game_plays_reset_at = now();
            break;
        case 'rabbit':
            $user->rabbit_game_unlocked = true;
            break;
        case 'slash':
            $user->slash_game_unlocked = true;
            break;
    }
    
    // ...existing logic...
}
```
