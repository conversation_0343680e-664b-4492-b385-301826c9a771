import { cn } from "@/lib/utils";
import { HTMLAttributes } from "react";

export const iconMap = {
  coin: "A",
  lock: "B",
  coins: "E",
  coin2: "D",
  coin3: "C",
  coin4: "F",
  chest: "G",
  missions: "H",
  dailytasks: "I",
  explore: "J",
  loading: "K",
  leaderboard: "L",
  avatar: "M",
  boost: "N",
  wallet: "O",
  bounty: "P",
  x: "Q",
  extrapower: "R",
  bolt: "S",
  friends: "T",
  game: "U",
  youtube: "V",
} as const;

export type IconType = keyof typeof iconMap;

interface HelloIconProps extends HTMLAttributes<HTMLSpanElement> {
  icon: IconType;
  size?: "sm" | "md" | "lg";
  className?: string;
}

const sizeClasses = {
  sm: "text-base",
  md: "text-xl",
  lg: "text-2xl",
};

export function BattlxIcon({ icon, size = "md", className, ...props }: HelloIconProps) {
  return (
    <span 
      className={cn(
        "battlx-icon inline-block", 
        sizeClasses[size],
        className
      )} 
      {...props}
    >
      {iconMap[icon]}
    </span>
  );
}

export default BattlxIcon;
