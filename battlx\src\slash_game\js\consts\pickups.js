// Pickup constants
const PICKUPS = {
    [PickupType.GEM]: {
        name: 'Experience Gem',
        description: 'Increases experience',
        tips: '',
        frameName: 'gem_pickup.png',
        pickedupAmount: 0,
        rarity: 0,
        unlocksAt: 100000,
        value: 1,
        inTreasures: false,
        seen: true
    },
    [PickupType.COIN]: {
        name: 'Coin',
        description: 'Adds 1 coin',
        tips: '',
        frameName: 'coin_pickup.png',
        pickedupAmount: 0,
        rarity: 50,
        unlocksAt: 0,
        value: 1,
        inTreasures: false,
        seen: true
    },
    [PickupType.ROAST]: {
        name: 'Roast Meat',
        description: 'Restores 30 health points',
        tips: '',
        frameName: 'roast_pickup.png',
        pickedupAmount: 0,
        rarity: 50,
        unlocksAt: 0,
        value: 30,
        inTreasures: false
    },
    [PickupType.VACUUM]: {
        name: 'Magnet Candy',
        description: 'Collects all experience gems',
        tips: 'Drop rate affected by luck.',
        frameName: 'vacum_pickup.png',
        pickedupAmount: 0,
        rarity: 15,
        unlocksAt: 12,
        value: 0,
        isRare: true
    },
    [PickupType.WEAPON]: {
        name: 'Weapon',
        description: '',
        tips: '',
        frameName: '',
        pickedupAmount: 0,
        rarity: 0,
        unlocksAt: 1e6,
        inTreasures: false,
        hidden: true,
        value: 0
    }
};

// Attach to window object for global access
window.PICKUPS = PICKUPS;
