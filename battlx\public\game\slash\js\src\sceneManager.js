// SceneManager class for handling game scenes
class SceneManager {
    constructor() {
        this.currentScene = null;
        this.scenes = {};
        this.isTransitioning = false;
    }

    // Add a scene
    addScene(key, scene) {
        this.scenes[key] = scene;
    }

    // Start a scene
    startScene(key) {
        if (!this.scenes[key]) {
            console.error(`Scene '${key}' not found`);
            return;
        }

        this.currentScene = this.scenes[key];

        if (this.currentScene.init) {
            this.currentScene.init();
        }
    }

    // Enter level up scene
    enterLevelUp() {
        if (this.isTransitioning) return;
        this.isTransitioning = true;

        // Pause the main scene
        if (Game.core) {
            // Force pause state to true
            Game.core.isPaused = true;
        }

        // Show level up UI
        if (Game.core && Game.core.levelUpUI) {
            try {
                // Generate level up options
                const options = this.generateLevelUpOptions();

                // Show the level up UI
                Game.core.levelUpUI.show(options, (option) => {
                    this.handleLevelUpSelection(option);
                });
            } catch (error) {
                // Ensure game continues even if UI fails
                this.resumeFromLevelUp();
            }
        } else {
            // Resume game if UI is not available
            this.resumeFromLevelUp();
        }
    }

    // Resume from level up scene
    resumeFromLevelUp() {
        if (!this.isTransitioning) return;

        // Hide level up UI
        if (Game.core && Game.core.levelUpUI) {
            try {
                Game.core.levelUpUI.hide();
            } catch (error) {
                // Error handling without logging
            }
        }

        // Resume the main scene
        if (Game.core) {
            // Force unpause
            Game.core.isPaused = false;
        }

        this.isTransitioning = false;
    }

    // Generate level up options
    generateLevelUpOptions() {
        const options = [];

        if (!Game.core) return options;

        // Add weapon upgrades
        for (const weapon of Game.core.weapons) {
            if (weapon.level < 8) { // Max level is 8
                const weaponData = WEAPONS[weapon.bulletType];
                if (weaponData) {
                    options.push({
                        type: 'weapon',
                        bulletType: weapon.bulletType,
                        name: weapon.name,
                        description: `Level ${weapon.level + 1}: ${this.getWeaponUpgradeDescription(weapon)}`,
                        frameName: weapon.frameName,
                        level: weapon.level
                    });
                }
            }
        }

        // Add new weapons
        const availableWeapons = this.getAvailableWeapons();
        for (const weaponType of availableWeapons) {
            const weaponData = WEAPONS[weaponType];
            if (weaponData && weaponData[0]) {
                options.push({
                    type: 'new_weapon',
                    bulletType: weaponType,
                    name: weaponData[0].name,
                    description: weaponData[0].description,
                    frameName: weaponData[0].frameName
                });
            }
        }

        // Add power-ups
        const powerUps = [
            WeaponType.AMOUNT,
            WeaponType.AREA,
            WeaponType.COOLDOWN,
            WeaponType.SPEED,
            WeaponType.DURATION,
            WeaponType.ARMOR,
            WeaponType.MAXHEALTH,
            WeaponType.GROWTH,
            WeaponType.MOVESPEED,
            WeaponType.LUCK
        ];

        for (const powerUp of powerUps) {
            options.push({
                type: 'power_up',
                powerUpType: powerUp,
                name: this.getPowerUpName(powerUp),
                description: this.getPowerUpDescription(powerUp),
                frameName: this.getPowerUpFrameName(powerUp)
            });
        }

        // Shuffle and pick 4 options
        return this.shuffleArray(options).slice(0, 4);
    }

    // Get weapon upgrade description
    getWeaponUpgradeDescription(weapon) {
        const nextLevel = weapon.level + 1;
        const weaponData = WEAPONS[weapon.bulletType];

        if (!weaponData || !weaponData[nextLevel]) {
            return 'Upgrade weapon';
        }

        const upgrade = weaponData[nextLevel];
        const descriptions = [];

        if (upgrade.power) {
            descriptions.push(`Power +${upgrade.power * 100}%`);
        }

        if (upgrade.area) {
            descriptions.push(`Area +${upgrade.area * 100}%`);
        }

        if (upgrade.speed) {
            descriptions.push(`Speed +${upgrade.speed * 100}%`);
        }

        if (upgrade.amount) {
            descriptions.push(`Amount +${upgrade.amount}`);
        }

        return descriptions.join(', ') || 'Upgrade weapon';
    }

    // Get available weapons (not yet owned)
    getAvailableWeapons() {
        const availableWeapons = [];

        if (!Game.core) return availableWeapons;

        // Check all weapon types
        for (const weaponType in WEAPONS) {
            if (WEAPONS.hasOwnProperty(weaponType)) {
                // Skip if already owned
                const hasWeapon = Game.core.weapons.some(weapon =>
                    weapon.bulletType === weaponType
                );

                // Skip if not unlocked
                const weaponData = WEAPONS[weaponType][0];
                const isUnlocked = weaponData && weaponData.isUnlocked;

                if (!hasWeapon && isUnlocked) {
                    availableWeapons.push(weaponType);
                }
            }
        }

        return availableWeapons;
    }

    // Get power-up name
    getPowerUpName(powerUpType) {
        switch (powerUpType) {
            case WeaponType.AMOUNT:
                return 'Amount';
            case WeaponType.AREA:
                return 'Area';
            case WeaponType.COOLDOWN:
                return 'Cooldown';
            case WeaponType.SPEED:
                return 'Speed';
            case WeaponType.DURATION:
                return 'Duration';
            case WeaponType.ARMOR:
                return 'Armor';
            case WeaponType.MAXHEALTH:
                return 'Max Health';
            case WeaponType.GROWTH:
                return 'Growth';
            case WeaponType.MOVESPEED:
                return 'Move Speed';
            case WeaponType.LUCK:
                return 'Luck';
            default:
                return 'Unknown';
        }
    }

    // Get power-up description
    getPowerUpDescription(powerUpType) {
        switch (powerUpType) {
            case WeaponType.AMOUNT:
                return 'Increase projectile count';
            case WeaponType.AREA:
                return 'Increase attack area';
            case WeaponType.COOLDOWN:
                return 'Decrease attack cooldown';
            case WeaponType.SPEED:
                return 'Increase projectile speed';
            case WeaponType.DURATION:
                return 'Increase effect duration';
            case WeaponType.ARMOR:
                return 'Increase damage reduction';
            case WeaponType.MAXHEALTH:
                return 'Increase maximum health';
            case WeaponType.GROWTH:
                return 'Increase experience gain';
            case WeaponType.MOVESPEED:
                return 'Increase movement speed';
            case WeaponType.LUCK:
                return 'Increase item drop chance';
            default:
                return 'Unknown power-up';
        }
    }

    // Get power-up frame name
    getPowerUpFrameName(powerUpType) {
        switch (powerUpType) {
            case WeaponType.AMOUNT:
                return 'amount_powerup.png';
            case WeaponType.AREA:
                return 'area_powerup.png';
            case WeaponType.COOLDOWN:
                return 'cooldown_powerup.png';
            case WeaponType.SPEED:
                return 'speed_powerup.png';
            case WeaponType.DURATION:
                return 'duration_powerup.png';
            case WeaponType.ARMOR:
                return 'armor_powerup.png';
            case WeaponType.MAXHEALTH:
                return 'maxhealth_powerup.png';
            case WeaponType.GROWTH:
                return 'growth_powerup.png';
            case WeaponType.MOVESPEED:
                return 'movespeed_powerup.png';
            case WeaponType.LUCK:
                return 'luck_powerup.png';
            default:
                return '';
        }
    }

    // Handle level up selection
    handleLevelUpSelection(option) {
        if (!Game.core) return;

        switch (option.type) {
            case 'weapon':
                // Upgrade existing weapon
                const weapon = Game.core.weapons.find(w =>
                    w.bulletType === option.bulletType
                );

                if (weapon) {
                    // Use the new levelUp method
                    weapon.levelUp();

                    // Update UI
                    if (Game.core.mainUI) {
                        Game.core.mainUI.updateWeaponIcon(weapon.bulletType, weapon.level);
                    }
                }
                break;

            case 'new_weapon':
                // Add new weapon
                Game.core.addWeapon(option.bulletType);
                break;

            case 'power_up':
                // Apply power-up
                this.applyPowerUp(option.powerUpType);
                break;
        }

        // Resume from level up
        this.resumeFromLevelUp();
    }

    // Apply a power-up
    applyPowerUp(powerUpType) {
        if (!Game.core || !Game.core.player) return;

        const player = Game.core.player;
        let level = 1;

        // Check if power-up already exists
        if (Game.core.mainUI) {
            const icon = Game.core.mainUI.powerUpIcons.find(icon =>
                icon.type === powerUpType
            );

            if (icon) {
                level = icon.level + 1;
            }
        }

        // Apply power-up effect
        switch (powerUpType) {
            case WeaponType.AMOUNT:
                player.amount += 1;
                break;
            case WeaponType.AREA:
                player.area += 0.1;
                break;
            case WeaponType.COOLDOWN:
                player.cooldown += 0.1;
                break;
            case WeaponType.SPEED:
                player.speed += 0.1;
                break;
            case WeaponType.DURATION:
                player.duration += 0.1;
                break;
            case WeaponType.ARMOR:
                player.armor += 10;
                break;
            case WeaponType.MAXHEALTH:
                player.maxHp += 20;
                player.hp += 20;
                break;
            case WeaponType.GROWTH:
                player.growth += 0.1;
                break;
            case WeaponType.MOVESPEED:
                player.moveSpeed += 0.1;
                break;
            case WeaponType.LUCK:
                player.luck += 0.1;
                break;
        }

        // Update UI
        if (Game.core.mainUI) {
            Game.core.mainUI.updatePowerUpIcon(powerUpType, level);
        }

        if (Game.core.playerUI) {
            Game.core.playerUI.update();
        }
    }

    // Shuffle an array (Fisher-Yates algorithm)
    shuffleArray(array) {
        const newArray = [...array];
        for (let i = newArray.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
        }
        return newArray;
    }
}

// Attach to window object for global access
window.SceneManager = SceneManager;
