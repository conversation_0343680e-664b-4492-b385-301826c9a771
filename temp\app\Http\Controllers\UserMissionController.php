<?php

namespace App\Http\Controllers;

use App\Models\Mission;
use App\Models\MissionLevel;
use App\Models\MissionCompletion;
use App\Models\TelegramUserMission;
use App\Models\User;
use App\Services\AchievementPointService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserMissionController extends Controller
{
    protected $achievementPointService;
    
    public function __construct(AchievementPointService $achievementPointService)
    {
        $this->achievementPointService = $achievementPointService;
    }
    
    public function index(Request $request)
    {
        $user = $request->user();

        $missions = Mission::query()
            ->with([
                'nextLevel' => fn ($q) => $q->whereNotIn(
                    'id',
                    fn ($q) =>
                    $q->select('mission_level_id')
                        ->from('telegram_user_missions')
                        ->where('telegram_user_id', $user->id)
                ),
                'requiredMission',
                'completions' => fn($q) => $q->where('telegram_user_id', $user->id)
            ])
            ->withSum(['levels as production_per_hour' => fn ($q) => $q->whereIn(
                'id',
                fn ($q) =>
                $q->select('mission_level_id')
                    ->from('telegram_user_missions')
                    ->where('telegram_user_id', $user->id)
            )], 'production_per_hour')
            ->when($request->get('type'), fn ($q) => $q->where('missions.mission_type_id', $request->get('type')))
            ->get()
            ->map(function ($mission) use ($user) {
                $mission->is_completed = $mission->isCompletedBy($user->id);
                $mission->is_unlocked = !$mission->required_mission_id ||
                    $mission->requiredMission->isCompletedBy($user->id);
                return $mission;
            });

        return response()->json($missions);
    }

    public function store(Request $request, MissionLevel $missionLevel)
    {
        $user = $request->user();

        if ($user->balance < $missionLevel->cost) {
            return response()->json([
                'message' => 'Insufficient balance',
            ], 400);
        }

        $missionCompleted = false;
        $dependentMissionsUnlocked = [];
        $nextLevel = null;

        DB::transaction(function () use ($user, $missionLevel, &$missionCompleted, &$dependentMissionsUnlocked, &$nextLevel) {
            // Create mission level purchase record
            TelegramUserMission::create([
                'telegram_user_id' => $user->id,
                'mission_level_id' => $missionLevel->id,
                'level' => $missionLevel->level,
            ]);

            // Update user stats
            $user->update([
                'production_per_hour' => $user->production_per_hour + $missionLevel->production_per_hour,
                'balance' => $user->balance - $missionLevel->cost,
            ]);

            // For boss missions (type 2), mark as completed when purchased
            if ($missionLevel->mission->mission_type_id === 2) {
                MissionCompletion::create([
                    'telegram_user_id' => $user->id,
                    'mission_id' => $missionLevel->mission_id,
                    'completed_at' => now(),
                ]);
                
                $missionCompleted = true;
                
                // Award achievement points for completing a mission
                $this->achievementPointService->awardPoints(
                    $user->id,
                    2, // Award 2 points for completing a mission
                    'mission_complete',
                    $missionLevel->mission_id,
                    "Completed mission: {$missionLevel->mission->name}"
                );

                // Check and unlock dependent missions
                $dependentMissions = Mission::where('required_mission_id', $missionLevel->mission_id)->get();
                foreach ($dependentMissions as $dependentMission) {
                    // Create level 1 for dependent mission to unlock it
                    $firstLevel = $dependentMission->levels()->where('level', 1)->first();
                    if ($firstLevel) {
                        TelegramUserMission::create([
                            'telegram_user_id' => $user->id,
                            'mission_level_id' => $firstLevel->id,
                            'level' => 1,
                        ]);
                        $dependentMissionsUnlocked[] = $dependentMission->name;
                    }
                }
            }

            // Get next level if available
            $nextLevel = $missionLevel->mission->levels()
                ->where('level', '>', $missionLevel->level)
                ->orderBy('level')
                ->first();
        });

        return response()->json([
            'message' => "Mission {$missionLevel->mission->name} upgraded to level {$missionLevel->level}",
            'next_level' => $nextLevel,
            'user' => $user,
            'mission_completed' => $missionCompleted,
            'dependent_missions_unlocked' => $dependentMissionsUnlocked,
            'achievement_points_awarded' => $missionCompleted ? 2 : 0
        ]);
    }
}
