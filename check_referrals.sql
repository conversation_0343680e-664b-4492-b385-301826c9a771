-- SQL queries to check referral system data

-- 1. Check all users and their referral relationships
SELECT 
    telegram_id,
    first_name,
    last_name,
    username,
    referred_by,
    balance,
    created_at
FROM telegram_users 
ORDER BY created_at DESC;

-- 2. Check referral relationships (who referred whom)
SELECT 
    referrer.telegram_id as referrer_id,
    referrer.first_name as referrer_name,
    referred.telegram_id as referred_id,
    referred.first_name as referred_name,
    referred.balance as referred_balance,
    referred.created_at as referred_joined_at
FROM telegram_users referrer
JOIN telegram_users referred ON referrer.telegram_id = referred.referred_by
ORDER BY referred.created_at DESC;

-- 3. Count referrals per user
SELECT 
    u.telegram_id,
    u.first_name,
    u.last_name,
    COUNT(r.id) as total_referrals,
    u.balance
FROM telegram_users u
LEFT JOIN telegram_users r ON u.telegram_id = r.referred_by
GROUP BY u.telegram_id, u.first_name, u.last_name, u.balance
HAVING COUNT(r.id) > 0
ORDER BY total_referrals DESC;

-- 4. Check users who joined via referral but referred_by is null (the bug we're fixing)
SELECT 
    telegram_id,
    first_name,
    last_name,
    referred_by,
    balance,
    created_at
FROM telegram_users 
WHERE referred_by IS NULL 
AND created_at > NOW() - INTERVAL '1 day'
ORDER BY created_at DESC;

-- 5. Check recent referral activity
SELECT 
    telegram_id,
    first_name,
    last_name,
    referred_by,
    balance,
    created_at
FROM telegram_users 
WHERE created_at > NOW() - INTERVAL '1 day'
ORDER BY created_at DESC;
