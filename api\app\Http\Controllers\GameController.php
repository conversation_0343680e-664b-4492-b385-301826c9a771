<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\TelegramUser;
use App\Models\UserAchievementPoint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class GameController extends Controller
{
    private const TOWER_GAME_PRICE = 5000;
    private const RABBIT_GAME_UNLOCK_PRICE = 5000; // Assuming same price for now
    private const SLASH_GAME_UNLOCK_PRICE = 5000; // Same price as Rabbit game
    private const PLAY_PRICE = 500; // Tower game specific

    /**
     * Unlock a game if user has sufficient balance
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unlockGame(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'game_id' => 'required|string|in:tower,rabbit,slash',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid game ID provided.'
            ], 400);
        }

        $gameId = $request->input('game_id');
        $user = $request->user();

        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 401);
        }

        $unlockField = "{$gameId}_game_unlocked";
        // Determine price based on game ID
        if ($gameId === 'tower') {
            $price = self::TOWER_GAME_PRICE;
        } elseif ($gameId === 'rabbit') {
            $price = self::RABBIT_GAME_UNLOCK_PRICE;
        } else { // slash
            $price = self::SLASH_GAME_UNLOCK_PRICE;
        }
        $gameTitle = ucfirst($gameId); // For messages

        try {
            // Check if game is already unlocked
            if ($user->$unlockField) {
                return response()->json([
                    'success' => false,
                    'message' => "{$gameTitle} game is already unlocked"
                ], 400);
            }

            // Check if user has sufficient balance
            if ($user->balance < $price) {
                $remaining = $price - $user->balance;
                return response()->json([
                    'success' => false,
                    'message' => "Insufficient balance. Need {$remaining} more coins to unlock {$gameTitle} game.",
                    'remaining' => $remaining
                ], 400);
            }

            DB::beginTransaction();
            try {
                // Deduct balance and unlock game
                $user->balance -= $price;
                $user->$unlockField = true;
                $user->save();

                // Award achievement points for unlocking the game
                $achievementPoints = UserAchievementPoint::firstOrCreate(
                    ['telegram_user_id' => $user->id],
                    ['total_earned' => 0, 'total_spent' => 0]
                );

                // Award 10 achievement points for unlocking a game
                $achievementPoints->total_earned += 10;
                $achievementPoints->save();

                // Record transaction
                DB::table('achievement_point_transactions')->insert([
                    'telegram_user_id' => $user->id,
                    'amount' => 10,
                    'type' => 'earn',
                    'source' => 'game_unlock',
                    'source_id' => null,
                    'description' => "Unlocked {$gameTitle} game",
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => "{$gameTitle} game unlocked successfully",
                    'user' => $user->fresh() // Return fresh user data
                ]);
            } catch (\Exception $e) {
                DB::rollback();
                throw $e; // Re-throw to be caught by outer catch
            }
        } catch (\Exception $e) {
            Log::error("{$gameTitle} game unlock failed:", [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'game_id' => $gameId
            ]);

            return response()->json([
                'success' => false,
                'message' => "Failed to unlock {$gameTitle} game"
            ], 500);
        }
    }

    /**
     * Check if user can play tower game
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkPlayAvailability(Request $request)
    {
        $validator = Validator::make($request->query(), [ // Check query parameters
            'game_id' => 'required|string|in:tower,rabbit,slash',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid game ID provided.'
            ], 400);
        }

        $gameId = $request->query('game_id');
        $user = $request->user();

        if (!$user) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 401);
        }

        try {
            $playStatus = null;
            if ($gameId === 'tower') {
                $playStatus = $user->canPlayTowerGame();
            } elseif ($gameId === 'rabbit') {
                $playStatus = $user->canPlayRabbitGame();
            } elseif ($gameId === 'slash') {
                $playStatus = $user->canPlaySlashGame();
            }
            // No need for an else here due to validator

            return response()->json([
                'success' => true,
                'data' => $playStatus
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to check play availability:', [
                'error' => $e->getMessage(),
                'user_id' => $user->id,
                'game_id' => $gameId
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to check play availability'
            ], 500);
        }
    }

    /**
     * Update the user's game score
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateScore(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'score' => 'required|integer|min:0',
                'game_id' => 'sometimes|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid score data'
                ], 400);
            }

            $user = $request->user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 401);
            }

            DB::beginTransaction();
            try {
                // Add the new score to the existing total
                $gameId = $request->input('game_id', 'tower');
                $newScore = (int)$request->score;

                // Validate the new score
                if ($newScore < 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Score cannot be negative'
                    ], 400);
                }

                $user->game_score = (int)$user->game_score + $newScore;
                $user->save();

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Score updated successfully',
                    'game_score' => $user->game_score,
                    'game_id' => $gameId
                ]);
            } catch (\Exception $e) {
                DB::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Game score update failed:', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id,
                'score' => $request->score
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update score'
            ], 500);
        }
    }

    /**
     * Use a play attempt
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function usePlay(Request $request)
    {
        try {
            $user = $request->user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 401);
            }

            $validator = Validator::make($request->all(), [
                'paid' => 'required|boolean',
                'quantity' => 'required|integer|min:1|max:100'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid request data'
                ], 400);
            }

            // Check and reset plays if needed
            $user->checkAndResetPlays();

            // Handle free play attempt
            if (!$request->paid) {
                if ($user->tower_game_plays <= 0) {
                    $canPlayStatus = $user->canPlayTowerGame();
                    return response()->json([
                        'success' => false,
                        'message' => 'No free plays remaining',
                        'data' => [
                            'plays_remaining' => 0,
                            'balance' => $user->balance,
                            'can_purchase' => $user->balance >= self::PLAY_PRICE,
                            'cost' => self::PLAY_PRICE
                        ]
                    ], 400);
                }
            }
            // Handle paid play attempt
            else {
                $totalCost = $request->quantity * self::PLAY_PRICE;
                if ($user->balance < $totalCost) {
                    return response()->json([
                        'success' => false,
                        'message' => "Insufficient balance. Need {$totalCost} points.",
                        'data' => [
                            'required' => $totalCost,
                            'balance' => $user->balance,
                            'plays_remaining' => $user->tower_game_plays
                        ]
                    ], 400);
                }
            }

            DB::beginTransaction();
            try {
                if ($request->paid) {
                    $totalCost = $request->quantity * self::PLAY_PRICE;
                    $user->balance -= $totalCost;
                    $user->tower_game_plays += $request->quantity;
                } else {
                    $user->tower_game_plays--;
                }

                $user->save();
                DB::commit();

                return response()->json([
                    'success' => true,
                    'data' => [
                        'plays_remaining' => $user->tower_game_plays,
                        'balance' => $user->balance
                    ]
                ]);
            } catch (\Exception $e) {
                DB::rollback();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Failed to process play attempt:', [
                'error' => $e->getMessage(),
                'user_id' => $request->user()?->id,
                'paid' => $request->paid ?? null,
                'quantity' => $request->quantity ?? null
            ]);

            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get the game leaderboard
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function leaderboard(Request $request)
    {
        $limit = $request->get('limit', 100);
        $limit = min($limit, 1000);
        $gameId = $request->get('game_id', 'tower');

        // Fetch users ordered by their total accumulated score
        $topUsers = TelegramUser::where('game_score', '>', 0)
            ->orderBy('game_score', 'desc')
            ->take($limit)
            ->get([
                'id',
                'first_name',
                'last_name',
                'username',
                'game_score'
            ]);

        return response()->json([
            'success' => true,
            'data' => $topUsers,
            'game_id' => $gameId
        ]);
    }
}
