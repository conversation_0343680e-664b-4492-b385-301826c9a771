import { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import LoadingPage from '@/components/LoadingPage';
import { gameApi } from '@/lib/game-api';
import { gameModules, GameInstance } from '../../games/registry';
import { useUserStore } from '../../store/user-store';
import { cn } from '@/lib/utils';
import TowerPlayDrawer from './TowerPlayDrawer';

interface GameWrapperProps {
  gameId: string;
  canvasId: string;
}

export const GameWrapper: React.FC<GameWrapperProps> = ({
  gameId,
  canvasId
}) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isGameOver, setIsGameOver] = useState(false);
  const [finalScore, setFinalScore] = useState(0);
  const [playStatus, setPlayStatus] = useState<{
    free: boolean;
    playsRemaining: number;
    balance: number;
  } | null>(null);
  const [showPlayDrawer, setShowPlayDrawer] = useState(false);

  // Game instance ref
  const gameInstance = useRef<GameInstance | null>(null);
  const gameContainer = useRef<HTMLDivElement>(null);

  // Canvas dimensions ref to ensure stable values
  const dimensionsRef = useRef({ width: 0, height: 0 });

  // User store methods
  const updateGameScore = useUserStore(state => state.updateGameScore);

  /**
   * Calculate game dimensions based on container
   */
  const calculateDimensions = useCallback(() => {
    if (!gameContainer.current) return null;

    const rect = gameContainer.current.getBoundingClientRect();
    const ratio = 1.5;

    let width = rect.width;
    let height = rect.height;

    if (rect.height / ratio < width) {
      width = Math.ceil(rect.height / ratio);
    }
    if (rect.width * ratio < height) {
      height = Math.ceil(width * ratio);
    }

    return { width, height };
  }, []);

  /**
   * Check if user can play and start game if allowed
   */
  const checkAndStartGame = useCallback(async (paid: boolean = false) => {
    // Reset error state at the beginning of check
    setError(null);
    try {
      // Pass gameId to checkPlayAvailability
      const availability = await gameApi.checkPlayAvailability(gameId);

      if (!availability.success || !availability.data) {
        // Handle specific case where backend might deny access without a clear reason yet
        if (availability.message?.includes('Unauthorized')) {
             navigate('/'); // Redirect home on auth issues
             return false;
        }
        throw new Error(availability.message || `Failed to check play availability for ${gameId}`);
      }

      const playData = availability.data;

      // If game is not unlocked, redirect to games page
      if (!playData.allowed && playData.reason === 'game_locked') {
        navigate('/games');
        return false;
      }

      // Show recharge drawer for any play availability issues (except locked game)
      if (!playData.allowed && playData.reason !== 'game_locked') {
        setPlayStatus({
          free: false,
          playsRemaining: playData.plays_remaining || 0,
          balance: playData.balance || 0
        });
        setShowPlayDrawer(true);
        return false;
      }

      // For paid plays, check balance before attempting
      if (paid && (playData.balance || 0) < 500) {
        setPlayStatus({
          free: false,
          playsRemaining: playData.plays_remaining || 0,
          balance: playData.balance || 0
        });
        setShowPlayDrawer(true);
        return false;
      }

      // For Rabbit Game, skip the usePlay API call since it has unlimited plays
      let playResult;
      if (gameId === 'rabbit' || 'slash') {
        // For Rabbit Game, just return a successful result without calling usePlay
        playResult = {
          success: true,
          data: {
            plays_remaining: 999, // Unlimited plays
            balance: playData.balance || 0
          }
        };
      } else {
        // For Tower Game and others, use a play attempt
        playResult = await gameApi.usePlay(paid, 1);
        if (!playResult.success || !playResult.data) {
          throw new Error(playResult.message || 'Failed to start game');
        }
      }

      // Update play status with adjusted balance for paid plays
      setPlayStatus({
        free: Boolean(playData.free),
        playsRemaining: playResult?.data?.plays_remaining || 0,
        balance: paid ?
          (playResult?.data?.balance || 0) : // Use new balance from result
          (playData.balance || 0) // Keep existing balance for free plays
      });

      return true;
    } catch (error) {
      console.error('Failed to check play availability:', error);
      if (error instanceof Error && error.message.includes('points to play')) {
        setShowPlayDrawer(true);
      } else {
        setError(error instanceof Error ? error.message : 'Failed to start game');
      }
      return false;
    }
  }, [navigate]);

  /**
   * Handle game initialization
   */
  const initGame = useCallback(async (paid: boolean = false) => {
    try {
      // Check if can play first
      if (!await checkAndStartGame(paid)) {
        return;
      }

      // Clear previous instance if it exists
      if (gameInstance.current?.destroy) {
        try {
          gameInstance.current.destroy();
          gameInstance.current = null;
        } catch (error) {
          console.warn('Error destroying previous game instance:', error);
        }
      }

      // Wait for container to be available and calculate dimensions
      await new Promise<void>((resolve) => {
        const checkContainer = () => {
          const dims = calculateDimensions();
          if (dims) {
            dimensionsRef.current = dims;
            resolve();
          } else {
            setTimeout(checkContainer, 50);
          }
        };
        checkContainer();
      });

      // Load game module
      const GameConstructor = await gameModules[gameId]();
      if (!GameConstructor) {
        throw new Error(`Game module not found: ${gameId}`);
      }

      // Create game instance with calculated dimensions
      const { width, height } = dimensionsRef.current;
      const game = GameConstructor({
        width,
        height,
        canvasId,
        soundOn: true,
        onGameOver: async (score: number) => {
          try {
            await gameApi.saveScore(score, gameId);
            setFinalScore(score);
            setIsGameOver(true);
            updateGameScore(score);
          } catch (error) {
            console.error('Error handling game over:', error);
          }
        }
      });

      // Store instance reference
      gameInstance.current = game;

      // Initialize canvas size
      const canvas = document.getElementById(canvasId) as HTMLCanvasElement;
      if (canvas) {
        canvas.style.width = `${width}px`;
        canvas.style.height = `${height}px`;
      }

      // Load game assets
      game.load(
        () => {
          if (gameInstance.current === game) {
            setIsLoading(false);
            // Initialize game after loading
            setTimeout(() => {
              if (gameInstance.current === game) {
                game.init?.();
                game.start?.();
                game.playBgm?.();
              }
            }, 0);
          }
        },
        (status) => {
          if (status.failed > 0) {
            setError('Failed to load game assets');
          }
        }
      );

    } catch (err) {
      console.error('Game initialization error:', err);
      setError(`Failed to initialize game: ${err instanceof Error ? err.message : String(err)}`);
    }
  }, [gameId, canvasId, updateGameScore, calculateDimensions, checkAndStartGame]);

  /**
   * Ensure complete cleanup of previous game instance
   */
  const cleanupGameInstance = useCallback(async () => {
    if (gameInstance.current) {
      try {
        // Clear event listeners first
        if (gameInstance.current.clearEventListeners) {
          await gameInstance.current.clearEventListeners();
        }
        // Perform full destroy
        if (gameInstance.current.destroy) {
          await gameInstance.current.destroy();
        }
        // Clear canvas
        const canvas = document.getElementById(canvasId) as HTMLCanvasElement;
        if (canvas) {
          const ctx = canvas.getContext('2d');
          ctx?.clearRect(0, 0, canvas.width, canvas.height);
        }
        gameInstance.current = null;
      } catch (error) {
        console.warn('Error during game cleanup:', error);
      }
    }
  }, [canvasId]);

  /**
   * Handle play again with proper cleanup
   */
  const handlePlayAgain = useCallback(async (paid: boolean = false) => {
    try {
      // Only check balance/show drawer for paid plays in Tower game
      if (gameId === 'tower' && paid && (!playStatus || playStatus.balance < 500)) {
        setShowPlayDrawer(true);
        return;
      }
      // For Rabbit game, or free plays in Tower, proceed directly

      setIsGameOver(false);
      setIsLoading(true);

      // Ensure complete cleanup before reinitializing
      await cleanupGameInstance();

      // Small delay to ensure DOM updates are processed
      await new Promise(resolve => setTimeout(resolve, 50));

      // Reinitialize game with paid status
      await initGame(paid);
    } catch (error) {
      console.error('Error starting new game:', error);
      setError(error instanceof Error ? error.message : 'Failed to start new game');
    }
  }, [cleanupGameInstance, initGame]);

  /**
   * Handle return to home with proper cleanup
   */
  const handleGoHome = async () => {
    await cleanupGameInstance();
    navigate('/games');
  };

  // Initialize game on mount
  useEffect(() => {
    // Add small delay to ensure DOM is ready
    const timer = setTimeout(() => {
      initGame(false);
    }, 50);

    // Cleanup on unmount
    return () => {
      clearTimeout(timer);
      // Use the same cleanup function for consistency
      cleanupGameInstance();
    };
  }, [initGame, cleanupGameInstance]);

  if (error) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-black">
        <div className="text-center text-red-500 p-4">
          <p className="mb-4">{error}</p>
          <button
            onClick={() => navigate('/games')}
            className="px-4 py-2 bg-primary rounded hover:opacity-80"
          >
            Back to Games
          </button>
        </div>
      </div>
    );
  }

  return (
    <div ref={gameContainer} className="fixed inset-0 bg-black flex items-center justify-center">
      {isLoading && !error && <LoadingPage />}
      <canvas
        id={canvasId}
        className={isLoading || error ? 'hidden' : 'block'}
        style={{
          maxWidth: '100vw',
          maxHeight: '100vh'
        }}
      />
      {isGameOver && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
          <div className="relative flex flex-col items-center bg-contain bg-center bg-no-repeat p-8"
               style={{ backgroundImage: "url('/game/main-modal-bg.png')" }}>
            <img
              src="/game/main-modal-over.png"
              alt="Game Over"
              className="w-64 mb-4"
            />
            <div className="text-white text-2xl mb-4">
              Score: {finalScore}
            </div>
            {playStatus && (
              <div className="text-[#9B8B6C] text-lg mb-6">
                {playStatus.free ? (
                  <span>{playStatus.playsRemaining} free plays remaining</span>
                ) : (
                  <span>{playStatus.balance} points available</span>
                )}
              </div>
            )}
            {/* --- Conditional Buttons for Game Over --- */}
            <div className="flex gap-4">
              {/* Always show Play Again (Free for Rabbit, potentially free for Tower) */}
              <button
                onClick={() => handlePlayAgain(false)} // Always try free first
                className={cn(
                  "hover:opacity-80 transition-opacity",
                  // Disable free play button only for Tower if no free plays remaining
                  gameId === 'tower' && (!playStatus || playStatus.playsRemaining === 0) && "opacity-50 cursor-not-allowed"
                )}
                // Disable free play button only for Tower if no free plays remaining
                disabled={gameId === 'tower' && (!playStatus || playStatus.playsRemaining === 0)}
              >
                <img
                  src="/game/main-modal-again-b.png"
                  alt="Play Again" // Changed alt text
                  className="w-32"
                />
              </button>

              {/* Only show Paid Play button for Tower game */}
              {gameId === 'tower' && (
                <button
                  onClick={() => handlePlayAgain(true)}
                  className={cn(
                    "hover:opacity-80 transition-opacity",
                    (!playStatus || playStatus.balance < 500) && "opacity-50 cursor-not-allowed"
                  )}
                  disabled={!playStatus || playStatus.balance < 500}
                  title="Play for 500 points"
                >
                  <img
                    src="/game/main-share-icon.png" // Consider a different icon if needed
                    alt="Paid Play"
                    className="w-32"
                  />
                </button>
              )}

              {/* Always show Home button */}
              <button
                onClick={handleGoHome}
                className="hover:opacity-80 transition-opacity"
              >
                <img
                  src="/game/main-home-icon.png"
                  alt="Home"
                  className="w-32"
                />
              </button>
            </div>
            {/* --- End Conditional Buttons --- */}
          </div>
        </div>
      )}
      {/* Conditionally render TowerPlayDrawer only for Tower game */}
      {gameId === 'tower' && (
          <TowerPlayDrawer
            open={showPlayDrawer}
            onOpenChange={setShowPlayDrawer}
          />
      )}
    </div>
  );
};
