import React from 'react';
import Fr<PERSON>zy<PERSON>eter from './FrenzyMeter';
import FrenzyButton from './FrenzyButton';
import FrenzyTimer from './FrenzyTimer';
import { useComboStore } from '@/store/combo-store';

/**
 * FrenzySystem component that wraps all frenzy-related components
 */
const FrenzySystem: React.FC = () => {
  const { frenzyActive } = useComboStore();

  return (
    <React.Fragment>
      <FrenzyMeter />
      <FrenzyButton />
      <FrenzyTimer />

      {/* Optimized overlay effect during Frenzy mode */}
      {frenzyActive && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            zIndex: 80,
            boxShadow: 'inset 0 0 100px rgba(255, 215, 0, 0.2)',
            backgroundColor: 'rgba(255, 215, 0, 0.03)',
            // Use simpler animation with transform instead of box-shadow for better performance
            animation: 'frenzy-pulse-optimized 3s infinite alternate',
            // Set CSS variables properly using the style object
            ...({
              '--frenzy-opacity': '0.2'
            } as React.CSSProperties)
          }}
        />
      )}
    </React.Fragment>
  );
};

export default FrenzySystem;
