import { useNavigate } from 'react-router-dom';
import { useCallback, useEffect, useState } from 'react';
import TowerPlayDrawer from '@/components/games/TowerPlayDrawer';
import { BattlxIcon } from '@/components/icons/BattlxIcon';
import ListItem from '@/components/ListItem';
import TowerGameDrawer from '@/components/games/TowerGameDrawer';
import RabbitGameDrawer from '@/components/games/RabbitGameDrawer';
import SlashGameDrawer from '@/components/games/SlashGameDrawer';
import { GAMES } from '../games/registry';
import { useUserStore } from '../store/user-store';
import { cn } from '@/lib/utils';

/**
 * GamesList Component
 *
 * A page that displays all available games with:
 * - Game thumbnails and descriptions
 * - Unlock status based on user level/balance
 * - Navigation to individual games
 */
export function GamesList() {
    const navigate = useNavigate();
    const userStore = useUserStore();
    const [showUnlockDrawer, setShowUnlockDrawer] = useState(false);
    const [showPlayDrawer, setShowPlayDrawer] = useState(false);
    const [selectedGameId, setSelectedGameId] = useState<string>('');

    // Access Telegram WebApp
    const webApp = window.Telegram.WebApp;

    useEffect(() => {
        // Apply consistent Telegram WebApp styling
        webApp.setHeaderColor("#000");
        webApp.setBackgroundColor("#000");
        webApp.expand();

        // Check if the app supports fullscreen mode (Bot API 8.0+)
        if (webApp.isVersionAtLeast('8.0')) {
            // Request fullscreen mode for better gaming experience
            // Use type assertion to bypass TypeScript errors for newer API methods
            (webApp as any).requestFullscreen();
        }
    }, []);

    // Helper to check if a game is unlocked
    const isGameUnlocked = useCallback((gameId: string) => {
        switch (gameId) {
            case 'tower':
                return userStore.tower_game_unlocked;
            case 'rabbit':
                return userStore.rabbit_game_unlocked;
            case 'slash':
                return userStore.slash_game_unlocked;
            default:
                console.warn(`Unknown game ID: ${gameId}`);
                return false;
        }
    }, [userStore.tower_game_unlocked, userStore.rabbit_game_unlocked, userStore.slash_game_unlocked]);

    const handleGameClick = (game: any, isUnlocked: boolean) => {
        console.log('Game clicked:', {
            gameId: game.id,
            isUnlocked,
            currentState: { selectedGameId, showUnlockDrawer }
        });

        if (!isUnlocked) {
            setSelectedGameId(game.id);
            setShowUnlockDrawer(true);
            console.log('Opening unlock drawer for:', game.id);
            return;
        }
        navigate(`/games/${game.id}`);
    };

    const handleRecharge = () => {
        setShowPlayDrawer(true);
    };

    return (
        <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1 min-h-screen">
            <div className="flex flex-col flex-1 w-full h-full px-6 py-8 pb-24 mt-12 modal-body relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]">
                <BattlxIcon
                    icon="game"
                    className="w-18 h-28 text-8xl mx-auto opacity-80 text-[#9B8B6C]"
                />
                <h1 className="text-2xl font-gothic font-bold text-center uppercase text-[#9B8B6C]">
                    Games
                </h1>
                <p className="mt-2 font-gothic text-center text-[#B3B3B3]">Available Games</p>

                <div className="mt-4 grid grid-cols-2 gap-4 sm:grid-cols-3">
                    {Object.values(GAMES).map(game => {
                        const isUnlocked = isGameUnlocked(game.id);

                        return (
                            <ListItem
                                key={game.id}
                                title={game.title}
                                subtitle={<span className="text-sm opacity-80">{game.description}</span>}
                                icon={
                                    game.thumbnail ? (
                                        <img
                                            src={game.thumbnail}
                                            alt={game.title}
                                            className="w-full h-full object-cover opacity-80 [filter:sepia(50%)_saturate(50%)_brightness(80%)_contrast(120%)]"
                                        />
                                    ) : (
                                        <BattlxIcon icon="game" className="w-full h-full" />
                                    )
                                }
                                action={
                                    !isUnlocked ? (
                                        <BattlxIcon icon="lock" className="w-6 h-6 text-[#9B8B6C]" />
                                    ) : undefined
                                }
                                variant={game.id === 'tower' ? 'square' : 'default'}
                                className={cn(
                                    "shadow-[0_4px_15px_rgba(74,14,14,0.3)]",
                                    !isUnlocked && "cursor-pointer hover:bg-[#4A0E0E]/30"
                                )}
                                onClick={() => handleGameClick(game, isUnlocked)}
                            />
                        );
                    })}
                </div>

                {/* Show message if no games are available */}
                {Object.keys(GAMES).length === 0 && (
                    <div className="text-center py-10">
                        <p className="text-[#B3B3B3]">No games available yet. Check back soon!</p>
                    </div>
                )}

                {/* Recharge Games Button */}
                <div className="flex justify-center mt-8 mb-4">
                    <button
                        onClick={handleRecharge}
                        className="px-8 py-3 bg-[#1A1617] border border-[#B3B3B3]/20 rounded-full text-[#9B8B6C] hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_15px_rgba(74,14,14,0.3)] relative before:absolute before:inset-0 before:rounded-full before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] text-lg font-gothic"
                    >
                        Recharge Games
                    </button>
                </div>

                {/* Game Unlock Drawers */}
                <TowerGameDrawer
                    open={showUnlockDrawer && selectedGameId === 'tower'}
                    onOpenChange={setShowUnlockDrawer}
                />
                <RabbitGameDrawer
                    open={showUnlockDrawer && selectedGameId === 'rabbit'}
                    onOpenChange={setShowUnlockDrawer}
                />
                <SlashGameDrawer
                    open={showUnlockDrawer && selectedGameId === 'slash'}
                    onOpenChange={setShowUnlockDrawer}
                />

                {/* Tower Play Purchase Drawer */}
                <TowerPlayDrawer
                    open={showPlayDrawer}
                    onOpenChange={setShowPlayDrawer}
                />
            </div>
        </div>
    );
}
