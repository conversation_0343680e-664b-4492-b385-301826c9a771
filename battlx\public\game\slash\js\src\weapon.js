// Weapon class for handling weapons and bullets
class BaseWeapon {
    constructor(bulletType) {
        this.bulletType = bulletType;
        this.level = 1;
        this.name = '';
        this.frameName = '';
        this.description = '';
        this.tips = '';
        this.isUnlocked = false;
        this.rarity = 0;
        this.interval = 1000;
        this.repeatInterval = 0;
        this.power = 1;
        this.area = 1;
        this.speed = 1;
        this.amount = 1;
        this.hitsWalls = false;
        this.lastFired = 0;
        this.owner = null;

        // Load weapon data
        this.loadWeaponData();
    }

    // Load weapon data from constants
    loadWeaponData() {
        const weaponData = WEAPONS[this.bulletType] ? WEAPONS[this.bulletType][0] : null;
        if (!weaponData) return;

        this.name = weaponData.name;
        this.frameName = weaponData.frameName;
        this.description = weaponData.description;
        this.tips = weaponData.tips;
        this.isUnlocked = weaponData.isUnlocked;
        this.rarity = weaponData.rarity;
        this.interval = weaponData.interval;
        this.repeatInterval = weaponData.repeatInterval;
        this.power = weaponData.power;
        this.area = weaponData.area;
        this.speed = weaponData.speed;
        this.amount = weaponData.amount;
        this.hitsWalls = weaponData.hitsWalls;
    }

    // Set the owner of the weapon
    setOwner(owner) {
        this.owner = owner;
    }

    // Reset the firing timer
    resetFiringTimer() {
        this.lastFired = 0;
    }

    // Update the weapon
    update(deltaTime) {
        if (!this.owner) return;

        // Update firing timer
        this.lastFired += deltaTime;

        // Check if it's time to fire
        if (this.lastFired >= this.interval / this.owner.cooldown) {
            this.fire();
            this.lastFired = 0;
        }
    }

    // Fire the weapon
    fire() {
        if (!this.owner) return;
        if (!Game.core) return;

        // Special handling for leg weapon to add opposite direction projectile
        if (this.bulletType === WeaponType.LEG) {
            // Create bullets based on amount
            for (let i = 0; i < this.amount; i++) {
                // Create the main projectile
                this.createBullet();

                // Create opposite direction projectile if level > 1
                if (this.level > 1) {
                    this.createBullet(true); // true indicates opposite direction
                }
            }
        } else {
            // Normal behavior for other weapons
            // Create bullets based on amount
            for (let i = 0; i < this.amount; i++) {
                this.createBullet();
            }
        }
    }

    // Create a bullet
    createBullet(oppositeDirection = false) {
        if (!this.owner) return;
        if (!Game.core) return;

        // Create a new bullet
        const bullet = new BaseBullet(
            this.owner.x,
            this.owner.y,
            this.bulletType,
            this,
            oppositeDirection
        );

        // Add to game
        Game.core.bullets.push(bullet);

        return bullet;
    }

    // Clean up the weapon
    cleanUp() {
        // Remove all bullets of this type
        if (Game.core) {
            Game.core.bullets = Game.core.bullets.filter(bullet =>
                bullet.weapon !== this
            );
        }
    }

    // Level up the weapon
    levelUp() {
        // Get the upgrade data for the current level
        const upgradeData = WEAPONS[this.bulletType][this.level];
        if (!upgradeData) {
            console.log(`No upgrade data for ${this.name} level ${this.level}`);
            return false;
        }

        console.log(`Upgrading ${this.name} to level ${this.level + 1}`);

        // Apply the upgrades
        for (const key in upgradeData) {
            if (this.hasOwnProperty(key)) {
                // For interval, negative values mean reduction
                if (key === 'interval') {
                    this.interval += upgradeData[key]; // Can be negative to reduce interval
                    this.interval = Math.max(100, this.interval); // Minimum interval
                } else {
                    this.addProperty(key, upgradeData[key]);
                }
            }
        }

        // Increment the level
        this.level++;

        // Reset firing timer to apply new interval
        this.resetFiringTimer();

        return true;
    }

    // Add a property value (helper for levelUp)
    addProperty(key, value) {
        if (this.hasOwnProperty(key)) {
            this[key] += value;
        }
    }
}

// Bullet class for handling weapon projectiles
class BaseBullet {
    constructor(x, y, bulletType, weapon, oppositeDirection = false) {
        this.x = x;
        this.y = y;
        this.bulletType = bulletType;
        this.weapon = weapon;
        this.direction = new Vector2(1, 0);
        this.speed = 5;
        this.damage = 1;
        this.radius = 10;
        this.lifetime = 0;
        this.maxLifetime = 5000;
        this.isDead = false;
        this.piercing = false;
        this.hitEnemies = [];
        this.oppositeDirection = oppositeDirection; // Flag for opposite direction projectiles

        // Initialize based on weapon type
        this.init();
    }

    // Initialize the bullet based on weapon type
    init() {
        if (!this.weapon || !this.weapon.owner) return;

        // Set damage based on weapon power and owner power
        this.damage = this.weapon.power * this.weapon.owner.power;

        // Set speed based on weapon speed
        this.speed = this.weapon.speed * 5;

        // Set radius based on weapon area
        this.radius = this.weapon.area * 10;

        // Set direction based on weapon type
        switch (this.bulletType) {
            case WeaponType.LEG:
                // Horizontal attack
                if (this.oppositeDirection) {
                    // Create a copy of the direction and reverse it
                    this.direction.copy(this.weapon.owner.lastFaceDirection);
                    this.direction.x *= -1;
                    this.direction.y *= -1;
                } else {
                    // Normal direction
                    this.direction.copy(this.weapon.owner.lastFaceDirection);
                }
                this.piercing = true;
                this.maxLifetime = 1000; // Limit range of leg bullet
                break;

            case WeaponType.BONE:
                // Rotating attack
                const boneAngle = Math.random() * Math.PI * 2;
                this.direction.x = Math.cos(boneAngle);
                this.direction.y = Math.sin(boneAngle);
                this.maxLifetime = 3000;
                break;

            case WeaponType.FIST:
                // Circular attack that moves outward with limited initial range
                const fistAngle = Math.random() * Math.PI * 2;
                this.direction.x = Math.cos(fistAngle);
                this.direction.y = Math.sin(fistAngle);
                this.piercing = true;

                // Set initial range based on weapon level
                if (this.weapon) {
                    // Start with much shorter range at level 1, increase with upgrades
                    const baseLifetime = 400; // Base lifetime for level 1 (significantly reduced)
                    const lifetimePerLevel = 250; // Additional lifetime per level (increased for better progression)
                    this.maxLifetime = baseLifetime + (this.weapon.level - 1) * lifetimePerLevel;
                } else {
                    this.maxLifetime = 400; // Default if weapon reference is missing
                }
                break;

            case WeaponType.ROCK:
                // Heavy projectile with limited penetration
                this.direction.copy(this.weapon.owner.lastFaceDirection);
                this.piercing = true;
                this.hitCount = 0;
                this.maxHitCount = 2; // Can hit up to 2 enemies
                this.maxLifetime = 2000;
                this.radius = this.radius * 1.5; // Larger hitbox
                break;

            case WeaponType.RADIOACTIVE:
                // Area damage that stays in place
                this.speed = 0; // Doesn't move
                this.maxLifetime = 2000;
                this.piercing = true;
                this.radius = this.radius * 2; // Large area of effect
                this.pulseTimer = 0;
                this.pulseInterval = 200; // Damage pulse every 200ms
                break;

            default:
                // Default direction is owner's facing direction
                this.direction.copy(this.weapon.owner.lastFaceDirection);
                break;
        }
    }

    // Update the bullet
    update(deltaTime) {
        if (this.isDead) return;

        // Update lifetime
        this.lifetime += deltaTime;
        if (this.lifetime >= this.maxLifetime) {
            this.die();
            return;
        }

        // Update position based on bullet type
        switch (this.bulletType) {
            case WeaponType.LEG:
                // Move in a straight line
                this.x += this.direction.x * this.speed * deltaTime / 16;
                this.y += this.direction.y * this.speed * deltaTime / 16;
                break;

            case WeaponType.BONE:
                // Orbit around the player
                if (this.weapon && this.weapon.owner) {
                    const angle = this.lifetime / 500; // Rotation speed
                    const distance = 50 + this.lifetime / 100; // Gradually increase distance
                    this.x = this.weapon.owner.x + Math.cos(angle) * distance;
                    this.y = this.weapon.owner.y + Math.sin(angle) * distance;
                }
                break;

            case WeaponType.FIST:
                // Move outward from player in a spiral pattern
                if (this.weapon && this.weapon.owner) {
                    const baseAngle = Math.atan2(this.direction.y, this.direction.x);
                    const angle = baseAngle + (this.lifetime / 1000) * Math.PI / 2; // Slight rotation
                    const distance = 50 + this.lifetime / 2; // Gradually increase distance
                    this.x = this.weapon.owner.x + Math.cos(angle) * distance;
                    this.y = this.weapon.owner.y + Math.sin(angle) * distance;
                }
                break;

            case WeaponType.ROCK:
                // Heavy projectile with gravity effect
                this.x += this.direction.x * this.speed * deltaTime / 16;
                this.y += this.direction.y * this.speed * deltaTime / 16;

                // Add slight gravity effect
                this.direction.y += 0.01 * deltaTime / 16;
                break;

            case WeaponType.RADIOACTIVE:
                // Stay in place and pulse damage
                this.pulseTimer += deltaTime;
                if (this.pulseTimer >= this.pulseInterval) {
                    this.pulseTimer = 0;
                    this.checkCollisions(); // Apply damage on pulse
                }
                break;

            default:
                // Default movement
                this.x += this.direction.x * this.speed * deltaTime / 16;
                this.y += this.direction.y * this.speed * deltaTime / 16;
                break;
        }

        // Check for collisions with enemies (except for RADIOACTIVE which pulses)
        if (this.bulletType !== WeaponType.RADIOACTIVE) {
            this.checkCollisions();
        }
    }

    // Check for collisions with enemies and destructibles
    checkCollisions() {
        if (!Game.core) return;

        // Check collisions with enemies
        for (const enemy of Game.core.enemies) {
            // Skip if enemy is dead or already hit (for non-piercing weapons)
            if (enemy.isDead || (!this.piercing && this.hitEnemies.includes(enemy))) {
                continue;
            }

            // Check collision
            const dist = distance(this.x, this.y, enemy.x, enemy.y);
            if (dist < this.radius + enemy.radius) {
                // Hit the enemy
                this.hitEnemy(enemy);

                // If not piercing, die after hitting one enemy
                if (!this.piercing) {
                    this.die();
                    return; // Exit early
                }
            }
        }

        // Check collisions with destructibles
        for (const destructible of Game.core.destructibles) {
            // Skip if destructible is dead
            if (destructible.isDead) {
                continue;
            }

            // Check collision
            const dist = distance(this.x, this.y, destructible.x, destructible.y);
            if (dist < this.radius + destructible.radius) {
                // Hit the destructible
                destructible.takeDamage(this.damage);

                // If not piercing, die after hitting
                if (!this.piercing) {
                    this.die();
                    return;
                }
            }
        }
    }

    // Hit an enemy
    hitEnemy(enemy) {
        // Apply damage
        const damage = this.damage;
        enemy.takeDamage(damage);

        // Add to hit enemies list
        this.hitEnemies.push(enemy);

        // Show damage number
        if (Game.core) {
            Game.core.showDamageAt(enemy.x, enemy.y, damage);
        }

        // Handle special weapon behaviors
        if (this.bulletType === WeaponType.ROCK) {
            // Increment hit count for rock
            this.hitCount = (this.hitCount || 0) + 1;

            // Die if hit count exceeds max
            if (this.hitCount >= this.maxHitCount) {
                this.die();
            }
        }
    }

    // Die
    die() {
        this.isDead = true;

        // Remove from game
        if (Game.core) {
            const index = Game.core.bullets.indexOf(this);
            if (index !== -1) {
                Game.core.bullets.splice(index, 1);
            }
        }
    }

    // Draw the bullet
    draw(ctx, camera, sprites) {
        if (this.isDead) return;
        if (!sprites) return;

        // Calculate screen position
        const screenX = this.x - camera.x + camera.width / 2;
        const screenY = this.y - camera.y + camera.height / 2;

        // Get the sprite
        const sprite = sprites[this.weapon.frameName];
        if (!sprite) {
            // Draw a fallback circle if sprite not found
            this.drawFallbackVisual(ctx, screenX, screenY);
            return;
        }

        ctx.save();

        // Apply special visual effects based on weapon type
        switch (this.bulletType) {
            case WeaponType.RADIOACTIVE:
                // Pulsing effect for radioactive
                const pulseScale = 1 + 0.2 * Math.sin(this.lifetime / 100);
                const alpha = 0.7 + 0.3 * Math.sin(this.lifetime / 50);
                ctx.globalAlpha = alpha;

                // Draw glow effect
                ctx.beginPath();
                ctx.arc(screenX, screenY, this.radius * pulseScale, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(0, 255, 0, 0.3)';
                ctx.fill();
                break;

            case WeaponType.ROCK:
                // Add shadow for rock
                ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
                ctx.shadowBlur = 10;
                ctx.shadowOffsetX = 5;
                ctx.shadowOffsetY = 5;
                break;

            case WeaponType.FIST:
                // Spinning effect for fist
                const spinAngle = this.lifetime / 100;
                ctx.translate(screenX, screenY);
                ctx.rotate(spinAngle);
                ctx.translate(-screenX, -screenY);
                break;

            default:
                // Apply rotation based on direction
                const angle = Math.atan2(this.direction.y, this.direction.x);
                ctx.translate(screenX, screenY);
                ctx.rotate(angle);
                ctx.translate(-screenX, -screenY);
                break;
        }

        // Draw the sprite
        const scale = this.weapon.area;
        ctx.drawImage(
            sprite,
            screenX - sprite.width / 2 * scale,
            screenY - sprite.height / 2 * scale,
            sprite.width * scale,
            sprite.height * scale
        );

        ctx.restore();

        // Draw debug collision circle
        if (Game.core && Game.core.debug) {
            ctx.beginPath();
            ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(255, 255, 0, 0.5)';
            ctx.stroke();
        }
    }

    // Draw fallback visual when sprite is not available
    drawFallbackVisual(ctx, screenX, screenY) {
        switch (this.bulletType) {
            case WeaponType.LEG:
                ctx.beginPath();
                ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(255, 255, 0, 0.8)';
                ctx.fill();
                break;

            case WeaponType.BONE:
                ctx.beginPath();
                ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.fill();
                break;

            case WeaponType.FIST:
                ctx.beginPath();
                ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
                ctx.fill();
                break;

            case WeaponType.ROCK:
                ctx.beginPath();
                ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(150, 75, 0, 0.8)';
                ctx.fill();
                break;

            case WeaponType.RADIOACTIVE:
                ctx.beginPath();
                ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(0, 255, 0, 0.5)';
                ctx.fill();

                // Inner circle
                ctx.beginPath();
                ctx.arc(screenX, screenY, this.radius * 0.6, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
                ctx.fill();
                break;

            default:
                ctx.beginPath();
                ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(255, 255, 0, 0.8)';
                ctx.fill();
                break;
        }
    }
}

// WeaponFactory class for creating weapons
class WeaponFactory {
    constructor() {
        this.weapons = {};
    }

    // Get a weapon of a specific type
    getWeapon(bulletType) {
        // Create the appropriate weapon type based on bulletType
        switch (bulletType) {
            case WeaponType.LEG:
                return new BaseWeapon(bulletType);
            case WeaponType.BONE:
                return new BaseWeapon(bulletType);
            case WeaponType.FIST:
                return new BaseWeapon(bulletType);
            case WeaponType.ROCK:
                return new BaseWeapon(bulletType);
            case WeaponType.RADIOACTIVE:
                return new BaseWeapon(bulletType);
            default:
                return new BaseWeapon(bulletType);
        }
    }
}

// Attach to window object for global access
window.BaseWeapon = BaseWeapon;
window.BaseBullet = BaseBullet;
window.WeaponFactory = WeaponFactory;
