<?php

namespace App\Http\Controllers;

use App\Models\Collectible;
use App\Models\CollectibleTemplate;
use App\Models\CollectionSet;
use App\Models\UserCollectionProgress;
use App\Services\CollectibleService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class CollectibleController extends Controller
{
    protected CollectibleService $collectibleService;

    public function __construct(CollectibleService $collectibleService)
    {
        $this->collectibleService = $collectibleService;
    }

    /**
     * Get user's collectible collection
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $category = $request->query('category');
        $rarity = $request->query('rarity');
        
        $query = $user->collectibles()->with('template');
        
        if ($category) {
            $query->byCategory($category);
        }
        
        if ($rarity) {
            $query->byRarity($rarity);
        }
        
        $collectibles = $query->orderBy('obtained_at', 'desc')->get();
        
        return response()->json([
            'success' => true,
            'collectibles' => $collectibles->map(function($collectible) {
                return [
                    'id' => $collectible->id,
                    'collectible_id' => $collectible->collectible_id,
                    'name' => $collectible->template->name,
                    'type' => $collectible->template->type,
                    'category' => $collectible->template->category,
                    'rarity' => $collectible->template->rarity,
                    'rarity_color' => $collectible->template->rarity_color,
                    'description' => $collectible->template->description,
                    'image_url' => $collectible->template->image_url,
                    'collection_set_id' => $collectible->template->collection_set_id,
                    'unlock_source' => $collectible->unlock_source,
                    'source_display' => $collectible->source_display,
                    'obtained_at' => $collectible->obtained_at,
                    'days_owned' => $collectible->days_owned,
                    'estimated_value' => $collectible->template->estimated_value
                ];
            }),
            'total_collectibles' => $collectibles->count(),
            'total_value' => $collectibles->sum(function($c) {
                return $c->template->estimated_value;
            })
        ]);
    }

    /**
     * Get collection sets and progress
     */
    public function getCollectionSets(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $collectionSets = CollectionSet::active()
                                     ->ordered()
                                     ->get();
        
        $setsWithProgress = $collectionSets->map(function($set) use ($user) {
            return $set->getProgressForUser($user);
        });
        
        return response()->json([
            'success' => true,
            'collection_sets' => $setsWithProgress,
            'total_sets' => $collectionSets->count(),
            'completed_sets' => $setsWithProgress->where('is_completed', true)->count()
        ]);
    }

    /**
     * Get detailed progress for a specific collection set
     */
    public function getCollectionSetDetails(Request $request, CollectionSet $collectionSet): JsonResponse
    {
        $user = $request->user();
        
        // Get or create user progress record
        $userProgress = UserCollectionProgress::firstOrCreate(
            [
                'telegram_user_id' => $user->id,
                'set_id' => $collectionSet->set_id
            ],
            [
                'collectibles_owned' => 0,
                'total_collectibles' => $collectionSet->total_collectibles,
                'completion_percentage' => 0,
                'owned_collectible_ids' => [],
                'missing_collectible_ids' => []
            ]
        );
        
        // Update progress
        $userProgress->updateProgress();
        
        $detailedProgress = $userProgress->getDetailedProgress();
        
        return response()->json([
            'success' => true,
            'collection_set' => $detailedProgress
        ]);
    }

    /**
     * Claim collection completion rewards
     */
    public function claimCollectionRewards(Request $request, CollectionSet $collectionSet): JsonResponse
    {
        $user = $request->user();
        
        $userProgress = UserCollectionProgress::where([
            'telegram_user_id' => $user->id,
            'set_id' => $collectionSet->set_id
        ])->first();
        
        if (!$userProgress) {
            return response()->json([
                'success' => false,
                'message' => 'Collection progress not found'
            ], 404);
        }
        
        try {
            $rewards = $userProgress->claimRewards();
            
            return response()->json([
                'success' => true,
                'message' => 'Collection rewards claimed successfully!',
                'rewards' => $rewards,
                'user_balance' => [
                    'balance' => $user->fresh()->balance,
                    'gems' => $user->fresh()->gems ?? 0,
                    'achievement_points' => $user->fresh()->achievement_points ?? 0
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get collectibles by category
     */
    public function getByCategory(Request $request, string $category): JsonResponse
    {
        $user = $request->user();
        
        $collectibles = $user->collectibles()
                            ->byCategory($category)
                            ->with('template')
                            ->orderBy('obtained_at', 'desc')
                            ->get();
        
        return response()->json([
            'success' => true,
            'category' => $category,
            'collectibles' => $collectibles->map(function($collectible) {
                return [
                    'collectible_id' => $collectible->collectible_id,
                    'name' => $collectible->template->name,
                    'type' => $collectible->template->type,
                    'rarity' => $collectible->template->rarity,
                    'image_url' => $collectible->template->image_url,
                    'obtained_at' => $collectible->obtained_at,
                    'unlock_source' => $collectible->unlock_source
                ];
            }),
            'count' => $collectibles->count()
        ]);
    }

    /**
     * Get collectibles by rarity
     */
    public function getByRarity(Request $request, string $rarity): JsonResponse
    {
        $user = $request->user();
        
        $collectibles = $user->collectibles()
                            ->byRarity($rarity)
                            ->with('template')
                            ->orderBy('obtained_at', 'desc')
                            ->get();
        
        return response()->json([
            'success' => true,
            'rarity' => $rarity,
            'collectibles' => $collectibles->map(function($collectible) {
                return [
                    'collectible_id' => $collectible->collectible_id,
                    'name' => $collectible->template->name,
                    'type' => $collectible->template->type,
                    'category' => $collectible->template->category,
                    'image_url' => $collectible->template->image_url,
                    'obtained_at' => $collectible->obtained_at,
                    'unlock_source' => $collectible->unlock_source
                ];
            }),
            'count' => $collectibles->count()
        ]);
    }

    /**
     * Get recently obtained collectibles
     */
    public function getRecentlyObtained(Request $request): JsonResponse
    {
        $user = $request->user();
        $days = $request->query('days', 7);
        
        $recentCollectibles = $user->collectibles()
                                  ->recentlyObtained($days)
                                  ->with('template')
                                  ->orderBy('obtained_at', 'desc')
                                  ->get();
        
        return response()->json([
            'success' => true,
            'recent_collectibles' => $recentCollectibles->map(function($collectible) {
                return [
                    'collectible_id' => $collectible->collectible_id,
                    'name' => $collectible->template->name,
                    'type' => $collectible->template->type,
                    'category' => $collectible->template->category,
                    'rarity' => $collectible->template->rarity,
                    'image_url' => $collectible->template->image_url,
                    'obtained_at' => $collectible->obtained_at,
                    'unlock_source' => $collectible->unlock_source,
                    'days_ago' => $collectible->obtained_at->diffInDays(now())
                ];
            }),
            'count' => $recentCollectibles->count(),
            'days_filter' => $days
        ]);
    }

    /**
     * Get collection statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $totalCollectibles = $user->collectibles()->count();
        $totalPossible = CollectibleTemplate::active()->count();
        
        $byCategory = $user->collectibles()
                          ->with('template')
                          ->get()
                          ->groupBy('template.category')
                          ->map(function($items, $category) {
                              return [
                                  'category' => $category,
                                  'count' => $items->count(),
                                  'total_value' => $items->sum('template.estimated_value')
                              ];
                          });
        
        $byRarity = $user->collectibles()
                        ->with('template')
                        ->get()
                        ->groupBy('template.rarity')
                        ->map(function($items, $rarity) {
                            return [
                                'rarity' => $rarity,
                                'count' => $items->count()
                            ];
                        });
        
        return response()->json([
            'success' => true,
            'statistics' => [
                'total_owned' => $totalCollectibles,
                'total_possible' => $totalPossible,
                'completion_percentage' => $totalPossible > 0 ? round(($totalCollectibles / $totalPossible) * 100, 1) : 0,
                'by_category' => $byCategory->values(),
                'by_rarity' => $byRarity->values(),
                'total_estimated_value' => $user->collectibles()->with('template')->get()->sum('template.estimated_value')
            ]
        ]);
    }
}
