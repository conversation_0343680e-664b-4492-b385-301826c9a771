<?php

namespace App\Http\Controllers;

use App\Models\ReferralTask;
use Illuminate\Http\Request;

class ReferralTaskController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        
        // Get all tasks with completion status
        $tasks = ReferralTask::orderBy('number_of_referrals')
            ->get()
            ->map(function ($task) use ($user) {
                $task->is_completed = $user->referralTasks()
                    ->where('referral_task_id', $task->id)
                    ->where('is_completed', true)
                    ->exists();
                return $task;
            });

        return response()->json($tasks);
    }

    public function complete(ReferralTask $referralTask)
    {
        $user = auth()->user();
        
        // Check if task is already completed
        if ($user->referralTasks()
            ->where('referral_task_id', $referralTask->id)
            ->where('is_completed', true)
            ->exists()
        ) {
            return response()->json(['message' => 'Task already completed'], 400);
        }

        // Check if user has enough referrals
        $referralCount = $user->referrals()->count();
        if ($referralCount >= $referralTask->number_of_referrals) {
            // Add reward to user balance
            $user->increment('balance', $referralTask->reward);
            
            // Mark task as completed
            $user->referralTasks()->attach($referralTask->id, [
                'is_completed' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            return response()->json([
                'message' => 'Task completed',
                'reward' => $referralTask->reward
            ]);
        }

        return response()->json([
            'message' => 'Not enough referrals',
            'required' => $referralTask->number_of_referrals,
            'current' => $referralCount
        ], 400);
    }
}
