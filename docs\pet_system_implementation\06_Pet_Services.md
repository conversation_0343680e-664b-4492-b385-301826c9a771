# Pet Services Implementation

## Overview
This document covers the creation of service classes for the Pet System business logic, including pet management, interactions, and reward calculations.

## Implementation Time: 2-3 days
## Complexity: High
## Dependencies: Eloquent models and controllers

## Core Pet Service

### PetService
```php
<?php
// File: api/app/Services/PetService.php

namespace App\Services;

use App\Models\Pet;
use App\Models\PetTemplate;
use App\Models\TelegramUser;
use App\Models\Collectible;
use App\Models\CollectibleTemplate;
use App\Models\MysteryBoxUnlock;
use App\Models\MysteryBoxType;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PetService
{
    protected CollectibleService $collectibleService;
    protected MysteryBoxService $mysteryBoxService;
    protected AchievementPointService $achievementPointService;

    public function __construct(
        CollectibleService $collectibleService,
        MysteryBoxService $mysteryBoxService,
        AchievementPointService $achievementPointService
    ) {
        $this->collectibleService = $collectibleService;
        $this->mysteryBoxService = $mysteryBoxService;
        $this->achievementPointService = $achievementPointService;
    }

    /**
     * Purchase a pet for a user
     */
    public function purchasePet(
        TelegramUser $user,
        PetTemplate $petTemplate,
        string $purchaseMethod,
        ?string $nickname = null
    ): array {
        // Validate purchase
        if (!$petTemplate->canBePurchasedBy($user)) {
            throw new \Exception('This pet cannot be purchased');
        }

        // Check if user already owns this pet
        if ($user->pets()->where('pet_template_id', $petTemplate->id)->exists()) {
            throw new \Exception('You already own this pet');
        }

        $cost = $petTemplate->getPurchaseCost($purchaseMethod);
        
        if ($cost <= 0) {
            throw new \Exception('Invalid purchase method');
        }

        // Check user balance
        $this->validateUserBalance($user, $purchaseMethod, $cost);

        DB::beginTransaction();
        
        try {
            // Deduct cost
            $this->deductCost($user, $purchaseMethod, $cost);

            // Create pet
            $pet = $this->createPet($user, $petTemplate, $nickname);

            // Unlock collectible reward
            $collectibleUnlocked = $this->unlockCollectibleReward($user, $petTemplate);

            // Unlock mystery boxes
            $mysteryBoxesUnlocked = $this->unlockMysteryBoxes($user, $petTemplate);

            // Award achievement points
            $this->awardPurchaseAchievementPoints($user, $petTemplate);

            // Log purchase
            $this->logPetPurchase($user, $petTemplate, $purchaseMethod, $cost);

            DB::commit();

            return [
                'pet' => $pet,
                'collectible_unlocked' => $collectibleUnlocked,
                'mystery_boxes_unlocked' => $mysteryBoxesUnlocked
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Pet purchase failed', [
                'user_id' => $user->id,
                'pet_template_id' => $petTemplate->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Process daily happiness decay for all pets
     */
    public function processDailyHappinessDecay(): array
    {
        $processed = 0;
        $errors = 0;

        Pet::with('template')
           ->chunk(100, function($pets) use (&$processed, &$errors) {
               foreach ($pets as $pet) {
                   try {
                       $pet->applyDailyHappinessDecay();
                       $processed++;
                   } catch (\Exception $e) {
                       $errors++;
                       Log::error('Failed to process happiness decay', [
                           'pet_id' => $pet->id,
                           'error' => $e->getMessage()
                       ]);
                   }
               }
           });

        return [
            'processed' => $processed,
            'errors' => $errors
        ];
    }

    /**
     * Get pets needing attention for notifications
     */
    public function getPetsNeedingAttentionForNotifications(): array
    {
        $petsNeedingAttention = Pet::with(['user', 'template'])
                                  ->where('happiness', '<', 30)
                                  ->whereHas('user', function($q) {
                                      $q->where('notifications_enabled', true);
                                  })
                                  ->get();

        $notifications = [];

        foreach ($petsNeedingAttention as $pet) {
            $notifications[] = [
                'user_id' => $pet->telegram_user_id,
                'pet_id' => $pet->id,
                'pet_name' => $pet->display_name,
                'happiness' => $pet->happiness,
                'message' => $this->generateAttentionMessage($pet),
                'type' => 'pet_needs_attention'
            ];
        }

        return $notifications;
    }

    /**
     * Calculate interaction rewards
     */
    public function calculateInteractionRewards(Pet $pet, string $interactionType): array
    {
        $baseRewards = $this->getBaseInteractionRewards($interactionType);
        
        // Apply happiness bonus
        $happinessMultiplier = $pet->happiness >= 80 ? 2.0 : 1.0;
        
        // Apply rarity bonus
        $rarityMultiplier = $this->getRarityMultiplier($pet->template->rarity);
        
        // Apply level bonus
        $levelMultiplier = 1 + ($pet->level * 0.01); // 1% per level
        
        $totalMultiplier = $happinessMultiplier * $rarityMultiplier * $levelMultiplier;
        
        return [
            'coins' => (int)($baseRewards['coins'] * $totalMultiplier),
            'experience' => $baseRewards['experience'],
            'happiness' => $baseRewards['happiness'],
            'materials' => $this->calculateMaterialReward($interactionType, $pet),
            'collectible' => $this->calculateCollectibleReward($interactionType, $pet),
            'multiplier_applied' => $totalMultiplier
        ];
    }

    /**
     * Process pet evolution
     */
    public function processPetEvolution(Pet $pet): bool
    {
        if (!$pet->can_evolve) {
            return false;
        }

        $nextEvolutionLevel = $pet->template->getNextEvolutionLevel($pet->level);
        
        if (!$nextEvolutionLevel || $pet->level < $nextEvolutionLevel) {
            return false;
        }

        DB::beginTransaction();
        
        try {
            $oldStage = $pet->evolution_stage;
            
            $pet->evolution_stage++;
            $pet->last_evolution = now();
            $pet->happiness = min($pet->template->max_happiness, $pet->happiness + 20);
            $pet->save();

            // Log evolution
            $pet->happinessLogs()->create([
                'happiness_before' => $pet->happiness - 20,
                'happiness_after' => $pet->happiness,
                'happiness_change' => 20,
                'change_reason' => 'evolution_bonus',
                'logged_at' => now()
            ]);

            // Award achievement points for evolution
            $this->achievementPointService->awardPoints(
                $pet->telegram_user_id,
                10,
                'pet_evolution',
                $pet->id,
                "Pet {$pet->display_name} evolved to stage {$pet->evolution_stage}"
            );

            DB::commit();

            Log::info('Pet evolved successfully', [
                'pet_id' => $pet->id,
                'old_stage' => $oldStage,
                'new_stage' => $pet->evolution_stage
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Pet evolution failed', [
                'pet_id' => $pet->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get user's collection statistics
     */
    public function getUserCollectionStats(TelegramUser $user): array
    {
        $totalPets = PetTemplate::active()->count();
        $ownedPets = $user->pets()->count();
        
        $petsByCategory = $user->pets()
                              ->join('pet_templates', 'pets.pet_template_id', '=', 'pet_templates.id')
                              ->selectRaw('pet_templates.category, COUNT(*) as count')
                              ->groupBy('pet_templates.category')
                              ->pluck('count', 'category')
                              ->toArray();

        $petsByRarity = $user->pets()
                            ->join('pet_templates', 'pets.pet_template_id', '=', 'pet_templates.id')
                            ->selectRaw('pet_templates.rarity, COUNT(*) as count')
                            ->groupBy('pet_templates.rarity')
                            ->pluck('count', 'rarity')
                            ->toArray();

        $totalInteractions = $user->petInteractions()->count();
        $interactionsToday = $user->petInteractions()
                                 ->whereDate('interaction_time', today())
                                 ->count();

        $averageHappiness = $user->pets()->avg('happiness') ?? 0;
        $averageLevel = $user->pets()->avg('level') ?? 0;

        return [
            'total_pets' => $ownedPets,
            'total_possible_pets' => $totalPets,
            'collection_percentage' => $totalPets > 0 ? round(($ownedPets / $totalPets) * 100, 1) : 0,
            'pets_by_category' => $petsByCategory,
            'pets_by_rarity' => $petsByRarity,
            'total_interactions' => $totalInteractions,
            'interactions_today' => $interactionsToday,
            'average_happiness' => round($averageHappiness, 1),
            'average_level' => round($averageLevel, 1),
            'pets_needing_attention' => $user->getPetsNeedingAttention()->count()
        ];
    }

    // Private helper methods

    private function validateUserBalance(TelegramUser $user, string $method, int $cost): void
    {
        switch ($method) {
            case 'coins':
                if ($user->balance < $cost) {
                    throw new \Exception('Insufficient coins');
                }
                break;
            case 'gems':
                if (($user->gems ?? 0) < $cost) {
                    throw new \Exception('Insufficient gems');
                }
                break;
            default:
                throw new \Exception('Invalid purchase method');
        }
    }

    private function deductCost(TelegramUser $user, string $method, int $cost): void
    {
        switch ($method) {
            case 'coins':
                $user->decrement('balance', $cost);
                break;
            case 'gems':
                $user->decrement('gems', $cost);
                break;
        }
    }

    private function createPet(TelegramUser $user, PetTemplate $petTemplate, ?string $nickname): Pet
    {
        $isFirstPet = $user->pets()->count() === 0;

        return $user->pets()->create([
            'pet_template_id' => $petTemplate->id,
            'level' => 1,
            'experience' => 0,
            'happiness' => $petTemplate->base_happiness,
            'daily_interaction_count' => 0,
            'daily_interaction_reset_date' => today(),
            'is_featured' => $isFirstPet, // First pet becomes featured automatically
            'nickname' => $nickname,
            'evolution_stage' => 0
        ]);
    }

    private function unlockCollectibleReward(TelegramUser $user, PetTemplate $petTemplate): ?array
    {
        if (!$petTemplate->collectible_reward_id) {
            return null;
        }

        return $this->collectibleService->unlockCollectible(
            $user,
            $petTemplate->collectible_reward_id,
            'pet_purchase',
            $petTemplate->id
        );
    }

    private function unlockMysteryBoxes(TelegramUser $user, PetTemplate $petTemplate): array
    {
        $unlockedBoxes = [];
        
        foreach ($petTemplate->mystery_box_unlocks as $boxType) {
            $result = $this->mysteryBoxService->unlockBoxType($user, $boxType, 'pet_purchase', $petTemplate->id);
            if ($result) {
                $unlockedBoxes[] = $result;
            }
        }

        return $unlockedBoxes;
    }

    private function awardPurchaseAchievementPoints(TelegramUser $user, PetTemplate $petTemplate): void
    {
        $points = match($petTemplate->rarity) {
            'common' => 5,
            'rare' => 10,
            'epic' => 25,
            'legendary' => 50,
            'mythic' => 100,
            default => 5
        };

        $this->achievementPointService->awardPoints(
            $user->id,
            $points,
            'pet_purchase',
            $petTemplate->id,
            "Purchased {$petTemplate->name} ({$petTemplate->rarity})"
        );
    }

    private function logPetPurchase(TelegramUser $user, PetTemplate $petTemplate, string $method, int $cost): void
    {
        Log::info('Pet purchased', [
            'user_id' => $user->id,
            'pet_template_id' => $petTemplate->id,
            'pet_name' => $petTemplate->name,
            'purchase_method' => $method,
            'cost' => $cost,
            'timestamp' => now()
        ]);
    }

    private function getBaseInteractionRewards(string $interactionType): array
    {
        return match($interactionType) {
            'feed' => ['coins' => 50, 'experience' => 10, 'happiness' => 20],
            'play' => ['coins' => 100, 'experience' => 15, 'happiness' => 30],
            'pet' => ['coins' => 25, 'experience' => 5, 'happiness' => 10],
            default => ['coins' => 0, 'experience' => 0, 'happiness' => 0]
        };
    }

    private function getRarityMultiplier(string $rarity): float
    {
        return match($rarity) {
            'common' => 1.0,
            'rare' => 1.2,
            'epic' => 1.5,
            'legendary' => 2.0,
            'mythic' => 3.0,
            default => 1.0
        };
    }

    private function calculateMaterialReward(string $interactionType, Pet $pet): int
    {
        $baseChance = match($interactionType) {
            'feed' => 30,
            'play' => 50,
            'pet' => 0,
            default => 0
        };

        $rarityBonus = match($pet->template->rarity) {
            'common' => 0,
            'rare' => 10,
            'epic' => 20,
            'legendary' => 30,
            'mythic' => 50,
            default => 0
        };

        $chance = $baseChance + $rarityBonus;
        
        return rand(1, 100) <= $chance ? 1 : 0;
    }

    private function calculateCollectibleReward(string $interactionType, Pet $pet): ?string
    {
        if ($interactionType !== 'play') {
            return null;
        }

        $baseChance = 10;
        $rarityBonus = match($pet->template->rarity) {
            'common' => 0,
            'rare' => 5,
            'epic' => 10,
            'legendary' => 15,
            'mythic' => 25,
            default => 0
        };

        $chance = $baseChance + $rarityBonus;
        
        if (rand(1, 100) <= $chance) {
            // Return a random collectible from the same category
            $collectibles = CollectibleTemplate::where('category', $pet->template->category)
                                              ->where('unlock_source', 'mystery_box')
                                              ->pluck('collectible_id')
                                              ->toArray();
            
            return $collectibles ? $collectibles[array_rand($collectibles)] : null;
        }

        return null;
    }

    private function generateAttentionMessage(Pet $pet): string
    {
        $messages = [
            "Your {$pet->template->name} {$pet->display_name} is feeling lonely! 🐾",
            "{$pet->display_name} needs some love and attention! ❤️",
            "Your pet {$pet->display_name} is waiting for you! 🎮",
            "{$pet->display_name} misses you! Come play together! 🎯"
        ];

        return $messages[array_rand($messages)];
    }
}
```

## Acceptance Criteria
- [x] Pet purchase system with validation and rewards
- [x] Daily happiness decay processing
- [x] Interaction reward calculations with multipliers
- [x] Pet evolution processing
- [x] Collection statistics generation
- [x] CollectibleService for collectible management
- [x] MysteryBoxService for box mechanics
- [x] NotificationService for user notifications
- [x] Service layer properly integrated with controllers
- [x] Proper error handling and logging
- [x] Achievement point integration

## Next Steps
1. Create CollectibleService for collectible management
2. Implement MysteryBoxService for box mechanics
3. Create scheduled jobs for daily tasks
4. Add notification service integration

## Troubleshooting
- Ensure database transactions for critical operations
- Validate user permissions and ownership
- Check calculation accuracy for rewards and multipliers
- Monitor performance for batch operations
