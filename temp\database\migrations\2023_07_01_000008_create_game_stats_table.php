<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('game_stats', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->string('game_id'); // tower, rabbit, slash, etc.
            $table->integer('high_score')->default(0);
            $table->bigInteger('total_score')->default(0);
            $table->integer('plays')->default(0);
            $table->timestamps();
            
            // Each user should have only one record per game
            $table->unique(['telegram_user_id', 'game_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('game_stats');
    }
};
