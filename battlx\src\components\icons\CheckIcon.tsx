export default function CheckIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="26"
      height="26"
      viewBox="0 0 26 26"
      fill="none"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 13C0 5.82272 5.82272 0 13 0C20.1773 0 26 5.82272 26 13C26 20.1773 20.1773 26 13 26C5.82272 26 0 20.1773 0 13ZM13 2C6.92728 2 2 6.92728 2 13C2 19.0727 6.92728 24 13 24C19.0727 24 24 19.0727 24 13C24 6.92728 19.0727 2 13 2Z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M17.0982 8.24695C17.5141 8.61033 17.5566 9.24206 17.1933 9.65796L13.9714 13.3455C13.608 13.7614 12.9763 13.8039 12.5604 13.4406C12.1445 13.0772 12.1019 12.4454 12.4653 12.0295L15.6872 8.34204C16.0505 7.92614 16.6823 7.88357 17.0982 8.24695ZM20.9914 8.24704C21.4073 8.61049 21.4497 9.24223 21.0863 9.65807L14.0944 17.6581C13.9122 17.8666 13.6516 17.9902 13.3748 17.9994C13.098 18.0087 12.8297 17.9027 12.634 17.7067L9.63711 14.7067C9.24679 14.316 9.24712 13.6828 9.63785 13.2925C10.0286 12.9022 10.6617 12.9025 11.0521 13.2933L13.2927 15.5362L19.5804 8.34193C19.9438 7.92608 20.5756 7.8836 20.9914 8.24704ZM5.6266 13.2925C6.01732 12.9022 6.65049 12.9025 7.04081 13.2933L10.0377 16.2933C10.428 16.684 10.4277 17.3172 10.0369 17.7075C9.64622 18.0978 9.01305 18.0975 8.62273 17.7067L5.62586 14.7067C5.23554 14.316 5.23587 13.6828 5.6266 13.2925Z"
        fill="currentColor"
      />
    </svg>
  );
}
