// Vector2 class for 2D vector operations
class Vector2 {
    constructor(x = 0, y = 0) {
        this.x = x;
        this.y = y;
    }
    
    // Set x and y components
    set(x, y) {
        this.x = x;
        this.y = y;
        return this;
    }
    
    // Copy values from another vector
    copy(vector) {
        this.x = vector.x;
        this.y = vector.y;
        return this;
    }
    
    // Clone this vector
    clone() {
        return new Vector2(this.x, this.y);
    }
    
    // Add another vector to this one
    add(vector) {
        this.x += vector.x;
        this.y += vector.y;
        return this;
    }
    
    // Subtract another vector from this one
    subtract(vector) {
        this.x -= vector.x;
        this.y -= vector.y;
        return this;
    }
    
    // Multiply this vector by a scalar
    multiply(scalar) {
        this.x *= scalar;
        this.y *= scalar;
        return this;
    }
    
    // Divide this vector by a scalar
    divide(scalar) {
        if (scalar !== 0) {
            this.x /= scalar;
            this.y /= scalar;
        }
        return this;
    }
    
    // Calculate the magnitude (length) of this vector
    magnitude() {
        return Math.sqrt(this.x * this.x + this.y * this.y);
    }
    
    // Calculate the squared magnitude (for performance when comparing distances)
    magnitudeSquared() {
        return this.x * this.x + this.y * this.y;
    }
    
    // Normalize this vector (make it unit length)
    normalize() {
        const mag = this.magnitude();
        if (mag > 0) {
            this.divide(mag);
        }
        return this;
    }
    
    // Calculate the dot product with another vector
    dot(vector) {
        return this.x * vector.x + this.y * vector.y;
    }
    
    // Calculate the distance to another vector
    distance(vector) {
        const dx = this.x - vector.x;
        const dy = this.y - vector.y;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    // Calculate the squared distance to another vector (for performance)
    distanceSquared(vector) {
        const dx = this.x - vector.x;
        const dy = this.y - vector.y;
        return dx * dx + dy * dy;
    }
    
    // Calculate the angle of this vector in radians
    angle() {
        return Math.atan2(this.y, this.x);
    }
    
    // Set this vector from an angle and magnitude
    fromAngle(angle, magnitude = 1) {
        this.x = Math.cos(angle) * magnitude;
        this.y = Math.sin(angle) * magnitude;
        return this;
    }
    
    // Rotate this vector by an angle in radians
    rotate(angle) {
        const cos = Math.cos(angle);
        const sin = Math.sin(angle);
        const x = this.x * cos - this.y * sin;
        const y = this.x * sin + this.y * cos;
        this.x = x;
        this.y = y;
        return this;
    }
    
    // Linear interpolation towards another vector
    lerp(vector, t) {
        this.x = this.x + (vector.x - this.x) * t;
        this.y = this.y + (vector.y - this.y) * t;
        return this;
    }
    
    // Check if this vector equals another vector
    equals(vector) {
        return this.x === vector.x && this.y === vector.y;
    }
    
    // Set this vector to zero
    zero() {
        this.x = 0;
        this.y = 0;
        return this;
    }
    
    // Static method to create a vector from an angle
    static fromAngle(angle, magnitude = 1) {
        return new Vector2(
            Math.cos(angle) * magnitude,
            Math.sin(angle) * magnitude
        );
    }
    
    // Static method to calculate the distance between two vectors
    static distance(v1, v2) {
        return v1.distance(v2);
    }
    
    // Static method to calculate the angle between two vectors
    static angle(v1, v2) {
        return Math.atan2(v2.y - v1.y, v2.x - v1.x);
    }
}
