// Stage class for handling game stages and enemy spawning
class Stage {
    constructor(stageType) {
        this.levelName = '';
        this.description = '';
        this.minute = 0;
        this.pause = 0;
        this.startingSpawns = 0;
        this.destructibleType = DestructibleType.GIFT;
        this.bgTextureName = '';
        this.maxTreasureLuck = 20;
        this.hasAttachedTreasure = false;
        this.hasTileset = false;
        this.levelType = stageType;

        // Phase system properties
        this.currentPhase = 1;
        this.gameTime = 0; // Time in seconds
        this.phaseTransitionTimes = [0, 120, 300, 480, 600, 900, 1200, 1500]; // Phase transition times in seconds

        // Warning message cooldowns
        this.lastSwarmWarningTime = 0;
        this.swarmWarningCooldown = 5; // 5 seconds between swarm warnings
        this.bossWarningShown = false; // Track if boss warning has been shown

        // Enemy spawn settings
        this.minimum = EnemySpawnConfig.BASE_SETTINGS.INITIAL_MIN_ENEMIES;
        this.maximum = EnemySpawnConfig.BASE_SETTINGS.INITIAL_MAX_ENEMIES;
        this.minimumMultiplier = 1;
        this.frequency = EnemySpawnConfig.BASE_SETTINGS.BASE_SPAWN_INTERVAL;
        this.destructibleFreq = 10000;
        this.destructibleChance = 5;
        this.destructibleChanceMax = 50;
        this.maxDestructibles = 10;

        // Enemy type tracking
        this.enemyTypeCounts = {}; // Tracks count of each enemy type
        this.enemyWeights = {}; // Weights for enemy type spawning

        this.enemies = [];
        this.bosses = [];
        this.pools = [];
        this.bossPools = [];
        this.stageEventManager = new StageEventManager(this);
        this.events = [];
        this.treasure = null;
        this.spawnTimer = null;
        this.destructibleTimer = null;

        // Initialize enemy type counts
        // Check if EnemyType is defined
        if (typeof EnemyType !== 'undefined') {
            for (const type in EnemyType) {
                if (typeof EnemyType[type] === 'string') {
                    this.enemyTypeCounts[EnemyType[type]] = 0;
                }
            }
        } else {
            console.error('EnemyType is not defined. Make sure enemyType.js is loaded properly.');
        }

        // Rectangle for spawning enemies
        this.rectOuter = { x: 0, y: 0, width: 0, height: 0 };
        this.rectInner = { x: 0, y: 0, width: 0, height: 0 };

        // Load stage data
        this.loadStageData();
    }

    // Load stage data from constants
    loadStageData() {
        // Convert levelType to stageType if needed
        const stageType = this.levelType || (window.StageType ? window.StageType.FOREST : 'FOREST'); // Default to FOREST if no type specified

        // Check if STAGES is defined and has the stage type
        if (typeof window.STAGES === 'undefined') {
            // STAGES is not defined
            return;
        }

        const stageData = window.STAGES[stageType] ? window.STAGES[stageType][0] : null;
        if (!stageData) {
            // No stage data found
            return;
        }

        // Loading stage data

        // Copy properties from stage data
        Object.assign(this, stageData);

        // Initialize enemy pools
        this.updateEnemyPools();
    }

    // Initialize the stage
    init() {
        // Update timers
        this.updateTimers();

        // Play events
        this.playEvents();

        // Initial spawns
        for (let i = 0; i < this.startingSpawns; i++) {
            this.spawnEnemiesInOuterRect();
        }

        // Spawn bosses if any are defined for this stage and boss spawning is not disabled
        // But only in phase 4 or later to prevent early boss spawns
        if (this.bosses && this.bosses.length > 0 && this.currentPhase >= 4) {
            // Check if boss spawning is disabled by checking the type limit
            const bossSpawningDisabled = typeof EnemySpawnConfig !== 'undefined' &&
                EnemySpawnConfig.TYPE_LIMITS[EnemyType.GHOST_BOSS1] === 0;

            if (!bossSpawningDisabled) {
                // Schedule boss spawn after a longer delay (increased from 5 seconds to 60 seconds)
                setTimeout(() => {
                    this.spawnBoss();
                }, 120000); // 120 seconds delay (2 minutes)
                // Boss spawn scheduled
            } else {
                // Boss spawning is disabled
            }
        } else if (this.bosses && this.bosses.length > 0) {
            // Boss spawning delayed until later phase
        }
    }

    // Update enemy spawn settings based on time and player level
    updateEnemySpawnSettings() {
        // Get current minute (capped at 5 for scaling purposes)
        const currentMinute = Math.min(this.minute, 5);

        // Get player level (capped at max scaling level)
        const playerLevel = Game.core && Game.core.player ?
            Math.min(Game.core.player.level, EnemySpawnConfig.BASE_SETTINGS.PLAYER_LEVEL_SCALING.MAX_LEVEL_SCALING) : 1;

        // Calculate minimum enemies based on time and player level
        const timeBasedEnemies = EnemySpawnConfig.BASE_SETTINGS.INITIAL_MIN_ENEMIES +
            (currentMinute * EnemySpawnConfig.BASE_SETTINGS.TIME_SCALING.ENEMIES_PER_MINUTE);

        const levelBasedEnemies = EnemySpawnConfig.BASE_SETTINGS.PLAYER_LEVEL_SCALING.ENEMIES_PER_LEVEL *
            (playerLevel - 1);

        // Set minimum and maximum enemies
        this.minimum = Math.floor(timeBasedEnemies + levelBasedEnemies);
        this.maximum = Math.min(
            this.minimum + 5, // Maximum is minimum + 5
            EnemySpawnConfig.BASE_SETTINGS.MAX_TOTAL_ENEMIES // But never more than the absolute maximum
        );

        // Calculate spawn frequency (gets faster over time)
        const timeBasedFrequencyReduction = currentMinute *
            EnemySpawnConfig.BASE_SETTINGS.TIME_SCALING.SPAWN_INTERVAL_REDUCTION_PER_MINUTE;

        this.frequency = Math.max(
            EnemySpawnConfig.BASE_SETTINGS.BASE_SPAWN_INTERVAL - timeBasedFrequencyReduction,
            EnemySpawnConfig.BASE_SETTINGS.MIN_SPAWN_INTERVAL // Never faster than minimum interval
        );

        // Updated enemy spawn settings
    }

    // Update enemy weights based on current minute
    updateEnemyWeights() {
        // Get current minute (capped at 5 for weights)
        const currentMinute = Math.min(this.minute, 5);

        // Get weights for current minute
        this.enemyWeights = EnemySpawnConfig.TYPE_WEIGHTS[currentMinute] ||
                           EnemySpawnConfig.TYPE_WEIGHTS[0]; // Fallback to minute 0 weights

        // Updated enemy weights
    }

    // Update enemy pools based on current stage data
    updateEnemyPools() {
        this.pools = [];

        // Create enemy pools for each enemy type
        if (this.enemies && this.enemies.length > 0) {
            for (const enemyType of this.enemies) {
                const pool = new EnemyGroup();
                pool.init(enemyType);
                this.pools.push(pool);
            }
        } else {
            // No enemies defined for this stage
        }

        // Create boss pools
        this.bossPools = [];
        if (this.bosses && this.bosses.length > 0) {
            for (const bossType of this.bosses) {
                const pool = new EnemyGroup();
                pool.init(bossType);
                this.bossPools.push(pool);
            }
        } else {
            // No bosses defined for this stage
        }

        // Reset enemy type counts
        for (const enemyType in this.enemyTypeCounts) {
            this.enemyTypeCounts[enemyType] = 0;
        }
    }

    // Update timers for spawning
    updateTimers() {
        // Clear existing timers
        if (this.spawnTimer) {
            clearInterval(this.spawnTimer);
        }

        if (this.destructibleTimer) {
            clearInterval(this.destructibleTimer);
        }

        // Set up spawn timer
        this.spawnTimer = setInterval(() => {
            this.spawnEnemiesInOuterRect();
        }, this.frequency);

        // Set up destructible timer
        this.destructibleTimer = setInterval(() => {
            // Prevent spawning during level transitions, game over, or when time is stopped
            if (!Game.core ||
                Game.core.isTimeStopped ||
                Game.core.isPaused ||
                Game.core.isGameOver ||
                (Game.core.sceneManager && Game.core.sceneManager.isTransitioning)) {
                return;
            }

            if (Math.random() * 100 < this.destructibleChance) {
                Game.core.makeDestructible(this.destructibleType);
            }
        }, this.destructibleFreq);
    }

    // Play stage events
    playEvents() {
        if (!this.events) return;

        for (const event of this.events) {
            this.stageEventManager.triggerEvent(event);
        }
    }

    // Update the stage data for a new minute
    updateData(stageData) {
        // Updating stage data

        this.events = [];
        this.bosses = [];
        this.treasure = null;

        // Copy properties from stage data
        for (const key in stageData) {
            if (this.hasOwnProperty(key)) {
                this[key] = stageData[key];
            }
        }

        // Update enemy spawn settings based on time and player level
        this.updateEnemySpawnSettings();

        // Update enemy weights based on current minute
        this.updateEnemyWeights();

        // Update pools and timers
        this.updateEnemyPools();
        this.updateTimers();
        this.playEvents();

        // Spawn some enemies immediately to show the new types
        for (let i = 0; i < 3; i++) {
            this.spawnEnemiesInOuterRect();
        }

        // Spawn a boss if available and boss spawning is not disabled
        // But only in phase 4 or later to prevent early boss spawns
        if (this.bosses && this.bosses.length > 0 && this.currentPhase >= 4) {
            // Check if boss spawning is disabled by checking the type limit
            const bossSpawningDisabled = typeof EnemySpawnConfig !== 'undefined' &&
                EnemySpawnConfig.TYPE_LIMITS[EnemyType.GHOST_BOSS1] === 0;

            if (!bossSpawningDisabled) {
                setTimeout(() => {
                    this.spawnBoss();
                }, 120000); // Delay boss spawn by 120 seconds (2 minutes)
                // Boss spawn scheduled
            } else {
                // Boss spawning is disabled
            }
        } else if (this.bosses && this.bosses.length > 0) {
            // Boss spawning delayed until later phase
        }
    }

    // Spawn a boss
    spawnBoss() {
        // Check if boss spawning is disabled by checking the type limit
        if (typeof EnemySpawnConfig !== 'undefined' &&
            EnemySpawnConfig.TYPE_LIMITS[EnemyType.GHOST_BOSS1] === 0) {
            // Boss spawning is disabled
            return;
        }

        if (!this.bossPools || this.bossPools.length === 0) {
            // No boss pools available
            return;
        }

        // Check if we've reached the boss limit
        if (Game.core) {
            // Count current GHOST_BOSS1 enemies
            const currentBossCount = Game.core.enemies.filter(enemy =>
                enemy.enemyType === EnemyType.GHOST_BOSS1 && !enemy.isDead
            ).length;

            // Get the boss type limit - force to 1 for better gameplay
            const bossLimit = 1; // Hard limit of 1 boss at a time

            if (currentBossCount >= bossLimit) {
                // Reached boss limit

                // Schedule another check after some time
                setTimeout(() => {
                    this.spawnBoss();
                }, 60000); // Check again in 1 minute

                return;
            }
        }

        // Enable all boss pools
        for (const bossPool of this.bossPools) {
            bossPool.enabled = true;
        }

        // Choose a random boss pool
        const randomIndex = Math.floor(Math.random() * this.bossPools.length);
        const bossPool = this.bossPools[randomIndex];

        if (!bossPool) {
            // No boss pool found
            return;
        }

        // Get a random position outside the screen
        const angle = Math.random() * Math.PI * 2;

        // Ensure boss spawns well outside the canvas
        const canvas = Game.core.canvas;
        const baseDistance = Math.max(canvas.width, canvas.height) * 1.2; // Use higher multiplier for boss
        const randomExtra = Math.random() * 150; // Add some randomness
        const distance = baseDistance + randomExtra;

        let x, y;
        if (Game.core && Game.core.player) {
            x = Game.core.player.x + Math.cos(angle) * distance;
            y = Game.core.player.y + Math.sin(angle) * distance;
        } else {
            x = Math.cos(angle) * 500;
            y = Math.sin(angle) * 500;
        }

        // Spawn the boss
        const boss = bossPool.spawnAt(x, y);
        if (boss) {
            boss.isTeleportOnCull = true;

            // Update enemy type count for proper tracking
            const enemyType = bossPool.enemyType;
            this.enemyTypeCounts[enemyType] = (this.enemyTypeCounts[enemyType] || 0) + 1;

            // Boss spawned successfully

            // Schedule next boss spawn check - reduced from 8 minutes to 1 minute for more responsive spawning
            setTimeout(() => {
                this.spawnBoss();
            }, 60000); // Check again in 1 minute
        } else {
            // Failed to spawn boss

            // Try again after a short delay
            setTimeout(() => {
                this.spawnBoss();
            }, 30000); // Try again in 30 seconds
        }

        // Treasure system not implemented in this version
        this.hasAttachedTreasure = false;
    }

    // Set treasure level based on chance and luck
    setTreasureLevelFromChance(treasure) {
        let luck = 1;
        if (Game.core && Game.core.player) {
            luck = Game.core.player.luck;
        }

        if (100 * Math.random() <= treasure.chances[0] * luck) {
            treasure.level = 3;
            return 3;
        } else if (100 * Math.random() <= treasure.chances[1] * luck) {
            treasure.level = 2;
            return 2;
        } else if (100 * Math.random() <= treasure.chances[2] * luck) {
            treasure.level = 1;
            return 1;
        }

        return 0;
    }

    // Spawn enemies in the outer rectangle
    spawnEnemiesInOuterRect() {
        // Prevent spawning during level transitions, game over, or when time is stopped
        if (!Game.core ||
            Game.core.isTimeStopped ||
            Game.core.isPaused ||
            Game.core.isGameOver ||
            (Game.core.sceneManager && Game.core.sceneManager.isTransitioning)) {
            return;
        }

        // Check if we've reached the maximum number of enemies
        const maxEnemies = this.maximum ||
            (typeof EnemySpawnConfig !== 'undefined' ? EnemySpawnConfig.BASE_SETTINGS.MAX_TOTAL_ENEMIES : 30);
        if (Game.core.enemies.length >= maxEnemies) {
            return;
        }

        let flag = false;

        // Choose an enemy type based on weights and type limits
        if (this.pools.length > 0) {
            // Get a pool using weighted selection
            const selectedPool = this.selectEnemyPoolWithWeights();

            if (selectedPool && selectedPool.enabled) {
                // Check if we've reached the limit for this enemy type
                const enemyType = selectedPool.enemyType;
                const currentCount = this.enemyTypeCounts[enemyType] || 0;
                const typeLimit = (typeof EnemySpawnConfig !== 'undefined' && EnemySpawnConfig.TYPE_LIMITS[enemyType]) ?
                    EnemySpawnConfig.TYPE_LIMITS[enemyType] : maxEnemies;

                if (currentCount >= typeLimit) {
                    // Reached limit for enemy type
                    return;
                }

                // Get a random position outside the inner rectangle but inside the outer rectangle
                const pos = this.getRandomPositionInOuterRect();

                if (Game.core && Game.core.player) {
                    const enemy = selectedPool.spawnAt(
                        Game.core.player.x + pos.x,
                        Game.core.player.y + pos.y
                    );

                    // Update enemy type count and log
                    if (enemy) {
                        this.enemyTypeCounts[enemyType] = currentCount + 1;
                        // Enemy spawned successfully
                    }
                } else {
                    selectedPool.spawnAt(pos.x, pos.y);
                    this.enemyTypeCounts[enemyType] = currentCount + 1;
                }

                flag = true;
            }
        } else {
            // No enemy pools available for spawning
        }

        if (flag) {
            this.swarmCheck();
        }
    }

    // Select an enemy pool using weighted selection
    selectEnemyPoolWithWeights() {
        // If no weights defined, use random selection
        if (!this.enemyWeights || Object.keys(this.enemyWeights).length === 0) {
            const randomPoolIndex = Math.floor(Math.random() * this.pools.length);
            return this.pools[randomPoolIndex];
        }

        // Calculate total weight of available enemy types
        let totalWeight = 0;
        const availablePools = [];

        for (const pool of this.pools) {
            if (pool && pool.enabled) {
                const enemyType = pool.enemyType;
                const weight = this.enemyWeights[enemyType] || 0;

                // Check if we've reached the limit for this enemy type
                const currentCount = this.enemyTypeCounts[enemyType] || 0;
                const typeLimit = (typeof EnemySpawnConfig !== 'undefined' && EnemySpawnConfig.TYPE_LIMITS[enemyType]) ?
                    EnemySpawnConfig.TYPE_LIMITS[enemyType] : this.maximum;

                // Only include enemy types that haven't reached their limit
                if (currentCount < typeLimit && weight > 0) {
                    totalWeight += weight;
                    availablePools.push({
                        pool: pool,
                        weight: weight
                    });
                }
            }
        }

        // If no available pools, return null
        if (availablePools.length === 0 || totalWeight === 0) {
            return null;
        }

        // Select a random value between 0 and totalWeight
        const randomValue = Math.random() * totalWeight;
        let cumulativeWeight = 0;

        // Find the enemy type that corresponds to the random value
        for (const item of availablePools) {
            cumulativeWeight += item.weight;
            if (randomValue <= cumulativeWeight) {
                return item.pool;
            }
        }

        // Fallback to first available pool
        return availablePools[0].pool;
    }

    // Check if more enemies need to be spawned to meet minimum
    swarmCheck() {
        if (Game.core && !Game.core.isTimeStopped) {
            const maxEnemies = this.maximum || 50; // Default to 50 if not specified
            const targetMinimum = Math.min(
                this.minimum * this.minimumMultiplier,
                maxEnemies
            );

            // Spawn enemies until we reach the minimum (but not exceeding maximum)
            for (let e = 0;
                 Game.core.enemies.length < targetMinimum &&
                 Game.core.enemies.length < maxEnemies &&
                 e < 10; // Limit to 10 spawns per swarm check to prevent lag
                 e++) {
                this.spawnEnemiesInOuterRect();
            }
        }
    }

    // Get a random position outside the visible canvas using the boss spawning approach
    getRandomPositionInOuterRect() {
        if (!Game.core || !Game.core.canvas) {
            return { x: 0, y: 0 };
        }

        // Use the same approach as boss spawning which works well
        // Generate a random angle and distance
        const angle = Math.random() * Math.PI * 2;

        // Ensure enemies spawn well outside the canvas
        const canvas = Game.core.canvas;
        // Increase the base distance multiplier from 0.6 to 1.0 to ensure enemies are further outside
        const baseDistance = Math.max(canvas.width, canvas.height) * 1.0; // Ensure they're definitely off-screen
        const randomExtra = Math.random() * 150; // Increase randomness from 100 to 150
        const distance = baseDistance + randomExtra;

        // Calculate position relative to canvas size
        const x = Math.cos(angle) * distance;
        const y = Math.sin(angle) * distance;

        // Enemy spawning at calculated position

        return { x, y };
    }

    // Update the stage
    update(deltaTime) {
        // Update game time (convert deltaTime from ms to seconds)
        this.gameTime += deltaTime / 1000;

        // Check for phase transitions
        this.updatePhase();
    }

    // Determine and update the current phase based on game time
    updatePhase() {
        // Determine which phase we should be in based on game time
        let newPhase = 1;
        for (let i = 1; i < this.phaseTransitionTimes.length; i++) {
            if (this.gameTime >= this.phaseTransitionTimes[i]) {
                newPhase = i + 1;
            }
        }

        // If phase has changed, update game settings
        if (newPhase !== this.currentPhase) {
            this.currentPhase = newPhase;
            // Transitioning to new phase

            // Update enemy spawn settings based on the new phase
            this.updatePhaseSettings();

            // Notify the player of the phase change
            if (Game.core && Game.core.showDamageAt) {
                Game.core.showDamageAt(
                    Game.core.player.x,
                    Game.core.player.y - 50,
                    `PHASE ${this.currentPhase}!`,
                    'rgba(255, 215, 0, 0.8)'
                );
            }
        }
    }

    // Update game settings based on the current phase
    updatePhaseSettings() {
        // Get phase settings from EnemySpawnConfig if available
        if (typeof EnemySpawnConfig !== 'undefined' &&
            EnemySpawnConfig.BASE_SETTINGS &&
            EnemySpawnConfig.BASE_SETTINGS.PHASE_SETTINGS &&
            EnemySpawnConfig.BASE_SETTINGS.PHASE_SETTINGS.length >= this.currentPhase) {

            // Get settings for current phase (array is 0-indexed, phases are 1-indexed)
            const phaseSettings = EnemySpawnConfig.BASE_SETTINGS.PHASE_SETTINGS[this.currentPhase - 1];

            // Apply settings
            this.minimum = phaseSettings.MIN_ENEMIES;
            this.maximum = phaseSettings.MAX_ENEMIES;
            this.frequency = phaseSettings.SPAWN_INTERVAL;

            // Applied phase settings from config
        } else {
            // Fallback to hardcoded settings if config not available

            // Phase-specific settings
            switch(this.currentPhase) {
                case 1: // Phase 1 (0-2 minutes): Introduction
                    this.minimum = 3;
                    this.maximum = 5;
                    this.frequency = 5000; // 5 seconds between spawns
                    break;

                case 2: // Phase 2 (2-5 minutes): Building Tension
                    this.minimum = 5;
                    this.maximum = 10;
                    this.frequency = 3000; // 3 seconds between spawns
                    break;

                case 3: // Phase 3 (5-8 minutes): Challenge
                    this.minimum = 8;
                    this.maximum = 15;
                    this.frequency = 2000; // 2 seconds between spawns
                    break;

                case 4: // Phase 4 (8-10 minutes): Mini-Boss
                    this.minimum = 12;
                    this.maximum = 20;
                    this.frequency = 1000; // 1 second between spawns
                    break;

                case 5: // Phase 5 (10-15 minutes): Escalation
                    this.minimum = 15;
                    this.maximum = 25;
                    this.frequency = 500; // 0.5 seconds between spawns
                    break;

                case 6: // Phase 6 (15+ minutes): Final Boss
                    this.minimum = 10; // Reduce regular enemies during boss fight
                    this.maximum = 20;
                    this.frequency = 2000; // Slow down regular spawns
                    break;
            }
        }

        // Phase-specific events
        switch(this.currentPhase) {
            case 2: // Phase 2 (2-5 minutes): Building Tension
                this.stageEventManager.spawnSwarm(EnemyType.BAT_SWARM, 5);
                break;

            case 3: // Phase 3 (5-8 minutes): Increasing Challenge
                this.stageEventManager.spawnSwarm(EnemyType.BAT_SWARM, 8);
                this.stageEventManager.spawnSwarm(EnemyType.GHOST_SWARM, 6);
                break;

            case 4: // Phase 4 (8-10 minutes): Mini-Boss Phase
                this.spawnMiniBoss();
                break;

            case 5: // Phase 5 (10-15 minutes): Advanced Challenge
                this.stageEventManager.spawnSwarm(EnemyType.BAT_SWARM, 12);
                this.stageEventManager.spawnSwarm(EnemyType.GHOST_SWARM, 10);
                break;

            case 6: // Phase 6 (15-20 minutes): Boss Phase
                this.spawnBoss();
                break;

            case 7: // Phase 7 (20-25 minutes): Chaos
                this.stageEventManager.spawnSwarm(EnemyType.BAT_SWARM, 15);
                this.stageEventManager.spawnSwarm(EnemyType.GHOST_SWARM, 15);
                this.spawnMiniBoss();
                break;

            case 8: // Phase 8 (25+ minutes): Infinite Challenge
                this.stageEventManager.spawnSwarm(EnemyType.BAT_SWARM, 18);
                this.stageEventManager.spawnSwarm(EnemyType.GHOST_SWARM, 15);
                this.spawnMiniBoss();
                this.spawnBoss();
                break;
        }

        // Update timers with new frequency
        this.updateTimers();

        // Update enemy weights based on the current phase
        this.updateEnemyWeightsForPhase();
    }

    // Update enemy weights based on the current phase
    updateEnemyWeightsForPhase() {
        // Map phase to minute for compatibility with existing weight system
        const minuteEquivalent = Math.min(this.currentPhase - 1, 5);

        // Get weights for current phase
        this.enemyWeights = EnemySpawnConfig.TYPE_WEIGHTS[minuteEquivalent] ||
                           EnemySpawnConfig.TYPE_WEIGHTS[0]; // Fallback to minute 0 weights

        // Updated enemy weights for current phase
    }

    // Spawn a mini-boss (HORN enemy)
    spawnMiniBoss() {
        if (!Game.core || !Game.core.player) return;

        // Check if we've reached the mini-boss limit
        const currentMiniBossCount = Game.core.enemies.filter(enemy =>
            enemy.enemyType === EnemyType.HORN && !enemy.isDead
        ).length;

        // Hard limit of 1 mini-boss at a time
        const miniBossLimit = 1;

        if (currentMiniBossCount >= miniBossLimit) {
            // Reached mini-boss limit
            return;
        }

        // Create a pool for the HORN enemy
        const pool = new EnemyGroup();
        pool.init(EnemyType.HORN);

        // Get a position outside the screen
        const angle = Math.random() * Math.PI * 2;
        // Increase distance to ensure it's outside the canvas
        const canvas = Game.core.canvas;
        const distance = Math.max(canvas.width, canvas.height) * 1.0; // Use same multiplier as regular enemies

        const x = Game.core.player.x + Math.cos(angle) * distance;
        const y = Game.core.player.y + Math.sin(angle) * distance;

        // Spawn the mini-boss
        const miniBoss = pool.spawnAt(x, y);

        if (miniBoss) {
            // Make the mini-boss more powerful
            miniBoss.maxHp *= 2;
            miniBoss.hp = miniBoss.maxHp;
            miniBoss.power *= 1.5;

            // Visual indicator for mini-boss
            if (Game.core.showDamageAt) {
                Game.core.showDamageAt(
                    Game.core.player.x,
                    Game.core.player.y - 50,
                    "MINI-BOSS APPROACHING!",
                    'rgba(255, 0, 0, 0.8)'
                );
            }

            // Mini-boss spawned
        }
    }

    // Spawn the final boss (GHOST_BOSS1)
    spawnBoss() {
        if (!Game.core || !Game.core.player) return;

        // Show warning message only once
        if (Game.core.showDamageAt && !this.bossWarningShown) {
            Game.core.showDamageAt(
                Game.core.player.x,
                Game.core.player.y - 50,
                "BOSS APPROACHING!",
                'rgba(255, 0, 0, 0.9)'
            );

            // Mark that we've shown the warning
            this.bossWarningShown = true;
        }

        // We no longer clear enemies when spawning the boss
        // Just log a message for debugging
        this.clearSomeEnemies();

        // Create a pool for the boss enemy
        const pool = new EnemyGroup();
        pool.init(EnemyType.GHOST_BOSS1);

        // Get a position outside the screen
        const angle = Math.random() * Math.PI * 2;
        // Increase distance to ensure it's outside the canvas
        const canvas = Game.core.canvas;
        const distance = Math.max(canvas.width, canvas.height) * 1.2; // Even further than regular enemies

        const x = Game.core.player.x + Math.cos(angle) * distance;
        const y = Game.core.player.y + Math.sin(angle) * distance;

        // Spawn the boss
        const boss = pool.spawnAt(x, y);

        if (boss) {
            // Make the boss more powerful based on player level
            const playerLevel = Game.core.player.level || 1;
            boss.maxHp *= (3 + playerLevel * 0.5); // Scale with player level
            boss.hp = boss.maxHp;
            boss.power *= (1.5 + playerLevel * 0.1);

            // Spawn boss guards
            this.spawnBossGuards(boss);

            // Final boss spawned
        }
    }

    // Spawn guards for the boss
    spawnBossGuards(boss) {
        if (!Game.core || !Game.core.player || !boss) return;

        // Create a pool for the HORN guards
        const pool = new EnemyGroup();
        pool.init(EnemyType.HORN);

        // Spawn 2-3 guards around the boss
        const guardCount = 2 + Math.floor(Math.random() * 2); // 2-3 guards

        for (let i = 0; i < guardCount; i++) {
            // Position guards in a circle around the boss
            const guardAngle = (i / guardCount) * Math.PI * 2;
            const guardDistance = 80; // 80 units from boss

            const x = boss.x + Math.cos(guardAngle) * guardDistance;
            const y = boss.y + Math.sin(guardAngle) * guardDistance;

            // Spawn the guard
            const guard = pool.spawnAt(x, y);

            if (guard) {
                // Make guards slightly stronger
                guard.maxHp *= 1.5;
                guard.hp = guard.maxHp;
                guard.power *= 1.2;

                // Boss guard spawned
            }
        }
    }

    // This method used to clear enemies, but now it just logs a message
    clearSomeEnemies() {
        // No longer removing enemies as per requirements
    }

    // Reset all enemy pools
    resetEnemyPools() {
        // Clear all enemy groups
        this.enemyGroups = {};

        // Reset enemy type counts
        for (const enemyType in this.enemyTypeCounts) {
            this.enemyTypeCounts[enemyType] = 0;
        }

        // Reinitialize enemy pools
        this.pools = [];
        this.bossPools = [];

        // Recreate enemy pools for each enemy type
        if (this.enemies && this.enemies.length > 0) {
            for (const enemyType of this.enemies) {
                const pool = new EnemyGroup();
                pool.init(enemyType);
                this.pools.push(pool);
            }
        }

        // Recreate boss pools
        if (this.bosses && this.bosses.length > 0) {
            for (const bossType of this.bosses) {
                const pool = new EnemyGroup();
                pool.init(bossType);
                this.bossPools.push(pool);
            }
        }

        // All enemy pools have been reset
    }
}

// StageEventManager class for handling stage events
class StageEventManager {
    constructor(stage) {
        this.stage = stage;
    }

    // Trigger a stage event
    triggerEvent(event) {
        if (!event || !event.eventType) return;

        // Set up the event with a delay
        setTimeout(() => {
            this.executeEvent(event);
        }, event.delay || 0);
    }

    // Execute a stage event
    executeEvent(event) {
        if (!event.eventType) return;

        // Check chance
        if (event.chance && Math.random() * 100 > event.chance) {
            return;
        }

        switch (event.eventType) {
            case StageEventType.GHOST_SWARM:
                // Reduced from 10 to 1 (10x less)
                this.spawnSwarm(EnemyType.GHOST1, event.amount || 0); //adjusting swarm
                break;
            case StageEventType.BAT_SWARM:
                // Reduced from 10 to 1 (10x less)
                this.spawnSwarm(EnemyType.BAT, event.amount || 0); //adjusting swarm
                break;
            default:
                console.warn('Unknown event type:', event.eventType);
                break;
        }

        // Repeat if needed
        if (event.repeat && event.repeat > 0) {
            const newEvent = { ...event, repeat: event.repeat - 1 };
            setTimeout(() => {
                this.triggerEvent(newEvent);
            }, event.delay || 5000);
        }
    }

    // Spawn a swarm of enemies
    spawnSwarm(enemyType, amount) {
        if (!Game.core || !Game.core.player) return;

        // Create a group for coordinated behavior
        const pool = new EnemyGroup();
        pool.init(enemyType);

        // Generate a base angle and position for the swarm
        const baseAngle = Math.random() * Math.PI * 2;

        // Use the same approach as boss spawning to ensure enemies spawn outside the canvas
        const canvas = Game.core.canvas;
        const baseDistance = Math.max(canvas.width, canvas.height) * 1.0; // Ensure they're definitely off-screen

        // Calculate base position
        const baseX = Game.core.player.x + Math.cos(baseAngle) * baseDistance;
        const baseY = Game.core.player.y + Math.sin(baseAngle) * baseDistance;

        // Determine formation type based on enemy type
        let formationType = 'SCATTERED';
        if (enemyType.includes('BAT')) {
            formationType = 'CIRCLE';
        } else if (enemyType.includes('GHOST')) {
            formationType = 'SCATTERED';
        }

        // Show swarm warning to player (with cooldown)
        const currentTime = this.gameTime;
        if (Game.core.showDamageAt && currentTime - this.lastSwarmWarningTime >= this.swarmWarningCooldown) {
            let swarmName = enemyType.includes('BAT') ? 'BAT' : 'GHOST';
            Game.core.showDamageAt(
                Game.core.player.x,
                Game.core.player.y - 30,
                `${swarmName} SWARM APPROACHING!`,
                'rgba(255, 100, 100, 0.8)'
            );

            // Update last warning time
            this.lastSwarmWarningTime = currentTime;
            console.log(`Swarm warning shown at game time ${currentTime.toFixed(1)}s`);
        }

        // Spawn enemies in formation
        for (let i = 0; i < amount; i++) {
            let x, y;

            // Position based on formation type
            if (formationType === 'CIRCLE') {
                // Circle formation - enemies arranged in a circle
                const formationAngle = (i / amount) * Math.PI * 2;
                const formationRadius = 50 + Math.random() * 30; // 50-80 units radius
                x = baseX + Math.cos(formationAngle) * formationRadius;
                y = baseY + Math.sin(formationAngle) * formationRadius;
            } else if (formationType === 'SCATTERED') {
                // Scattered formation - enemies randomly positioned around base point
                const scatterAngle = Math.random() * Math.PI * 2;
                const scatterDistance = Math.random() * 100; // 0-100 units from base
                x = baseX + Math.cos(scatterAngle) * scatterDistance;
                y = baseY + Math.sin(scatterAngle) * scatterDistance;
            } else {
                // Default - line formation
                x = baseX + (i - amount/2) * 30; // 30 units apart in a line
                y = baseY;
            }

            // Spawn the enemy
            const enemy = pool.spawnAt(x, y);

            if (enemy) {
                // Set formation type for coordinated behavior
                enemy.setFormationType(formationType);

                // Make swarm enemies slightly stronger
                enemy.maxHp *= 1.2;
                enemy.hp = enemy.maxHp;


            }
        }


    }
}
