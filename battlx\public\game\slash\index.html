<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vampire Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #f0f0f0;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-family: Arial, sans-serif;
        }
        #game-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            height: 100%;
            max-height: 600px;
            margin: 0 auto;
        }
        #game-canvas {
            display: block;
            background-color: #ffffff;
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <canvas id="game-canvas"></canvas>
    </div>

    <!-- Enums -->
    <script src="js/enums/characterType.js"></script>
    <script src="js/enums/destructibleType.js"></script>
    <script src="js/enums/enemyType.js"></script>
    <script src="js/enums/pickupType.js"></script>
    <script src="js/enums/sceneType.js"></script>
    <script src="js/enums/treasureType.js"></script>
    <script src="js/enums/weaponType.js"></script>
    <script src="js/enums/npcType.js"></script>
    <script src="js/enums/fixedTreasures.js"></script>

    <!-- Constants -->
    <script src="js/consts/characters.js"></script>
    <script src="js/consts/weapons.js"></script>
    <script src="js/consts/pickups.js"></script>
    <script src="js/consts/enemies.js"></script>
    <script src="js/consts/destructibles.js"></script>
    <script src="js/consts/enemySpawnConfig.js"></script>
    <script src="js/consts/stages.js"></script>

    <!-- Components -->
    <script src="js/src/utils.js"></script>
    <script src="js/src/vector2.js"></script>
    <script src="js/src/containmentRect.js"></script>
    <script src="js/src/bgManager.js"></script>
    <script src="js/src/player.js"></script>
    <script src="js/src/enemy.js"></script>
    <script src="js/src/enemyProjectile.js"></script>
    <script src="js/src/weapon.js"></script>
    <script src="js/src/pickup.js"></script>
    <script src="js/src/destructible.js"></script>
    <script src="js/src/stage.js"></script>
    <script src="js/src/ui.js"></script>
    <script src="js/src/sceneManager.js"></script>
    <script src="js/src/gameCore.js"></script>
    <script src="js/src/game.js"></script>

    <!-- Main -->
    <script src="js/main.js"></script>
</body>
</html>
