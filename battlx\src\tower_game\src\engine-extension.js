/**
 * Extends the base Engine class with additional functionality
 * @param {Engine} engine - The game engine instance to extend
 */
export const extendEngine = (engine) => {
  const eventListeners = new Set();
  const documentEventListeners = new Set();
  const audioElements = new Map();

  // Helper to safely add document event listener
  const addDocumentEventListener = (type, listener, options) => {
    document.addEventListener(type, listener, options);
    documentEventListeners.add({ type, listener, options });
  };

  // Helper to remove document event listeners
  const removeDocumentEventListeners = () => {
    documentEventListeners.forEach(({ type, listener, options }) => {
      document.removeEventListener(type, listener, options);
    });
    documentEventListeners.clear();
  };

  // Create safe audio container if it doesn't exist
  const getAudioContainer = () => {
    let container = document.querySelector('#game-audio-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'game-audio-container';
      document.body.appendChild(container);
    }
    return container;
  };

  // Override audio loading to track elements
  const originalAddAudio = engine.addAudio;
  engine.addAudio = function (id, path) {
    try {
      const audio = new Audio(path);
      audio.preload = 'auto';
      getAudioContainer().appendChild(audio);
      audioElements.set(id, audio);
      if (originalAddAudio) {
        originalAddAudio.call(this, id, path);
      }
      this.audios = this.audios || {};
      this.audios[id] = audio;
    } catch (error) {
      console.error('Error adding audio:', error);
    }
  };

  // Override audio playback with safety checks
  engine.playAudio = function (id, loop = false) {
    try {
      const audio = audioElements.get(id);
      if (audio) {
        audio.loop = loop;
        audio.currentTime = 0;
        const playPromise = audio.play();
        if (playPromise) {
          playPromise.catch(error => {
            console.warn('Audio playback prevented:', error);
          });
        }
      }
    } catch (error) {
      console.error('Error playing audio:', error);
    }
  };

  // Override audio pause with safety checks
  engine.pauseAudio = function (id) {
    try {
      const audio = audioElements.get(id);
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
    } catch (error) {
      console.error('Error pausing audio:', error);
    }
  };

  // Add method to remove audio with cleanup
  engine.removeAudio = function (id) {
    try {
      const audio = audioElements.get(id);
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
        audio.remove();
        audioElements.delete(id);
      }
      if (this.audios) {
        delete this.audios[id];
      }
    } catch (error) {
      console.error('Error removing audio:', error);
    }
  };

  // Override addEventListener to track listeners
  const originalAddEventListener = engine.canvas?.addEventListener;
  if (originalAddEventListener) {
    engine.canvas.addEventListener = function (type, listener, options) {
      eventListeners.add({ type, listener, options });
      originalAddEventListener.call(this, type, listener, options);
    };
  }

  // Add method to clear all event listeners
  engine.removeEventListeners = function () {
    try {
      // Clear canvas event listeners
      eventListeners.forEach(({ type, listener, options }) => {
        if (this.canvas) {
          this.canvas.removeEventListener(type, listener, options);
        }
      });
      eventListeners.clear();

      // Clear document event listeners
      removeDocumentEventListeners();

      // Reset touch and key handlers
      if (this.touchStartListener) {
        this.touchStartListener = () => {}; // Keep as no-op function instead of null
      }
      this.touchEndListener = () => {};
      this.touchMoveListener = () => {};
      this.keyDownListeners = {};
    } catch (error) {
      console.error('Error removing event listeners:', error);
    }
  };

  // Add method to stop animation
  engine.stopAnimate = function () {
    try {
      if (this._animationFrame) {
        cancelAnimationFrame(this._animationFrame);
        this._animationFrame = null;
      }
    } catch (error) {
      console.error('Error stopping animation:', error);
    }
  };

  // Add method to clear all instances
  engine.clearInstances = function () {
    try {
      this.instances = {};
      if (this.layers) {
        Object.keys(this.layers).forEach(layer => {
          this.layers[layer] = [];
        });
      }
    } catch (error) {
      console.error('Error clearing instances:', error);
    }
  };

  // Add safe destroy method that cleans up all resources
  engine.destroy = function () {
    try {
      this.stopAnimate();
      this.removeEventListeners();
      this.clearInstances();

      // Clean up all audio
      Array.from(audioElements.keys()).forEach(id => {
        this.removeAudio(id);
      });

      // Remove audio container
      const container = document.querySelector('#game-audio-container');
      if (container) {
        container.remove();
      }

      // Clear canvas
      if (this.canvas && this.ctx) {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      }
    } catch (error) {
      console.error('Error destroying engine:', error);
    }
  };

  return engine;
};
