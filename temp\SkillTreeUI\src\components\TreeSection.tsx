import { motion } from 'framer-motion';
import { useState } from 'react';
import { toast } from 'react-toastify';
import TreeNode from './TreeNode';
import { Section, Node } from '../types';
import { getConnectorPath } from '../utils/pathUtils';

interface TreeSectionProps {
  section: Section;
  isActive: boolean;
  onClick: () => void;
}

const TreeSection = ({ section, isActive, onClick }: TreeSectionProps) => {
  const [expandedNode, setExpandedNode] = useState<string | null>(null);
  
  const handleNodeClick = (node: Node) => {
    if (node.unlocked) {
      setExpandedNode(prev => prev === node.id ? null : node.id);
    } else if (canUnlock(node)) {
      unlockNode(node);
    } else {
      toast.info(`You need to complete previous nodes to unlock this reward!`);
    }
  };
  
  const canUnlock = (node: Node): boolean => {
    return node.level <= 1;
  };
  
  const unlockNode = (node: Node) => {
    toast.success(`${node.title} unlocked!`);
  };
  
  return (
    <motion.div 
      className={`relative bg-abyss-200/80 border rounded-lg p-6 h-[600px] overflow-hidden backdrop-blur-sm
        ${isActive 
          ? 'border-brass-300 shadow-outer-glow' 
          : 'border-brass-300/30'
        }
      `}
      whileHover={{ scale: 1.01 }}
      onClick={onClick}
    >
      <div className="absolute inset-0 opacity-10 bg-ornate-pattern pointer-events-none" />
      
      <h2 className="text-2xl font-bold mb-4 text-brass-300 relative">
        {section.title}
        <div className="h-px w-full bg-gradient-to-r from-transparent via-brass-300/50 to-transparent mt-2" />
      </h2>
      <p className="text-stone-300 mb-6 text-sm">{section.description}</p>
      
      <div className="relative h-[450px]">
        <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1 }}>
          {section.nodes.map(node => (
            node.connections?.map(targetId => {
              const targetNode = section.nodes.find(n => n.id === targetId);
              if (!targetNode) return null;
              
              return (
                <motion.path
                  key={`${node.id}-${targetId}`}
                  d={getConnectorPath(node.position, targetNode.position)}
                  stroke={node.unlocked ? "#9B8B6C" : "#453D2C"}
                  strokeWidth={2}
                  fill="none"
                  initial={{ pathLength: 0, opacity: 0.2 }}
                  animate={{ 
                    pathLength: 1, 
                    opacity: 1,
                    stroke: node.unlocked ? "#9B8B6C" : "#453D2C"
                  }}
                  transition={{ duration: 1, delay: 0.2 }}
                />
              );
            })
          ))}
        </svg>
        
        {section.nodes.map(node => (
          <TreeNode
            key={node.id}
            node={node}
            isExpanded={expandedNode === node.id}
            onClick={() => handleNodeClick(node)}
          />
        ))}
      </div>
    </motion.div>
  );
};

export default TreeSection;