import { useMemo, useState, useCallback, useEffect } from "react";
import { $http } from "../lib/http";
import { TonConnectButton, useTonWallet, useTonAddress } from '@tonconnect/ui-react';
import { useUserStore } from "../store/user-store";
import { BattlxIcon } from "@/components/icons/BattlxIcon";

export default function Wallet() {
  const wallet = useTonWallet();
  const userFriendlyAddress = useTonAddress();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [storedWallet, setStoredWallet] = useState<string | null>(null);
  const user = useUserStore();

  // Calculate total by adding balance and game_score
  const totalScore = (user.balance || 0) + (user.game_score || 0);

  const fetchWalletAddress = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await $http.get('/clicker/wallet');
      if (response.data.success && response.data.ton_wallet) {
        setStoredWallet(response.data.ton_wallet);
      }
    } catch (err) {
      console.error('Error fetching wallet address:', err);
      setError('Failed to fetch wallet address');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch wallet address when component mounts
  useEffect(() => {
    fetchWalletAddress();
  }, [fetchWalletAddress]);

  const storeWalletAddress = useCallback(async (address: string) => {
    try {
      setIsLoading(true);
      setError(null);
      await $http.post('/clicker/set-ton-wallet', { ton_wallet: address });
    } catch (err) {
      setError('Failed to store wallet address');
      console.error('Error storing wallet address:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Effect to store wallet address when connected
  useMemo(() => {
    if (wallet && userFriendlyAddress) {
      storeWalletAddress(userFriendlyAddress);
    }
  }, [wallet, userFriendlyAddress, storeWalletAddress]);

  return (
    <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1">
      <div className="flex flex-col flex-1 w-full px-6 py-8 pb-24 mt-2 modal-body">
        {!wallet || !userFriendlyAddress ? (
          // Show connection screen when wallet is not connected
          <>
            <div className="text-center">
              <h1 className="mt-4 text-2xl font-bold font-gothic text-center uppercase text-[#9B8B6C]">
                Connect Your Wallet
              </h1>
              <p className="mt-2 text-sm text-[#B3B3B3]/80">
                Connect your TON wallet to withdraw tokens or convert them to TON
              </p>
            </div>

            <div className="flex flex-col items-center mt-8 space-y-6">
              <TonConnectButton />

              {isLoading && (
                <div className="text-center text-[#9B8B6C] p-4 bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 shadow-[0_4px_15px_rgba(74,14,14,0.3)]">
                  <p>Connecting wallet...</p>
                  <p className="mt-1 text-sm text-[#B3B3B3]/80">Please wait while we establish the connection</p>
                </div>
              )}

              {error && (
                <div className="text-center text-[#FF6B6B] p-4 bg-[#1A1617] rounded-xl border border-[#FF6B6B]/20 shadow-[0_4px_15px_rgba(74,14,14,0.3)]">
                  <p>{error}</p>
                  <p className="mt-1 text-sm text-[#B3B3B3]/80">Please try connecting again</p>
                </div>
              )}
            </div>
          </>
        ) : (
          // Show wallet interface when connected
          <div className="flex flex-col">
            {/* Wallet SVG at the top */}
            <div className="flex justify-center -mb-10">
              <img src="/images/wallet.svg" alt="Wallet" className="w-15 h-15" />
            </div>

            {/* SCORE Section (Current Game Points) */}
            <div className="w-full bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)]">
              <div className="flex items-center justify-between p-4">
                <div className="flex items-center space-x-3">
                  <BattlxIcon icon="coins" className="w-6 h-6 text-[#9B8B6C]" />
                  <div className="flex flex-col">
                    <span className="text-lg font-bold text-[#9B8B6C]">
                      {Math.floor(totalScore).toLocaleString()}
                    </span>
                    <span className="text-sm text-[#B3B3B3]/80">SCORE</span>
                  </div>
                </div>
                <div className="flex-1 max-w-[180px] ml-4">
                  <p className="text-xs text-[#B3B3B3]/70 text-right leading-tight">
                    Your game score will be converted to BATTLX tokens based on conversion rates
                  </p>
                </div>
              </div>
            </div>

            {/* BATTLX Section (Calculated Tokens - Coming Soon) */}
            <div className="w-full bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)] mt-4">
              {/* Top section */}
              <div className="flex items-center justify-between p-4 border-b border-[#B3B3B3]/20">
                <div className="flex items-center space-x-3">
                  <BattlxIcon icon="coins" className="w-6 h-6 text-[#9B8B6C]" />
                  <div className="flex flex-col">
                    <span className="text-lg font-bold text-[#9B8B6C]">0</span>
                    <span className="text-sm text-[#B3B3B3]/80">BATTLX</span>
                  </div>
                </div>
                <button
                  disabled
                  className="px-4 py-2 text-sm font-medium text-[#B3B3B3]/60 bg-[#1A1617] border border-[#B3B3B3]/20 rounded-lg opacity-50 cursor-not-allowed"
                >
                  Max
                </button>
              </div>
              {/* Bottom section */}
              <div className="flex items-center justify-between p-4">
                <span className="text-sm text-[#B3B3B3]/60">Coming soon</span>
                <button
                  disabled
                  className="px-4 py-2 text-sm font-medium text-[#B3B3B3]/60 bg-[#1A1617] border border-[#B3B3B3]/20 rounded-lg opacity-50 cursor-not-allowed"
                >
                  Withdrawal
                </button>
              </div>
            </div>

            {/* TON Section (Converted Tokens - Coming Soon) */}
            <div className="w-full bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)] mt-4">
              {/* Top section */}
              <div className="flex items-center justify-between p-4 border-b border-[#B3B3B3]/20">
                <div className="flex items-center space-x-3">
                  <BattlxIcon icon="coins" className="w-6 h-6 text-[#9B8B6C]" />
                  <div className="flex flex-col">
                    <span className="text-lg font-bold text-[#9B8B6C]">0</span>
                    <span className="text-sm text-[#B3B3B3]/80">TON</span>
                  </div>
                </div>
                <button
                  disabled
                  className="px-4 py-2 text-sm font-medium text-[#B3B3B3]/60 bg-[#1A1617] border border-[#B3B3B3]/20 rounded-lg opacity-50 cursor-not-allowed"
                >
                  Max
                </button>
              </div>
              {/* Bottom section */}
              <div className="flex items-center justify-between p-4">
                <span className="text-sm text-[#B3B3B3]/60">Coming soon</span>
                <button
                  disabled
                  className="px-4 py-2 text-sm font-medium text-[#B3B3B3]/60 bg-[#1A1617] border border-[#B3B3B3]/20 rounded-lg opacity-50 cursor-not-allowed"
                >
                  Convert
                </button>
              </div>
            </div>

            {/* Note Section */}
            <div className="p-4 bg-[#1A1617]/50 rounded-xl border border-[#B3B3B3]/10 mt-4">
              <p className="text-sm text-[#B3B3B3]/80">
                <span className="font-medium text-[#9B8B6C]">Note:</span> BATTLX tokens can be converted to TON for withdrawal. Token conversion and withdrawal features are coming soon.
              </p>
            </div>

            {/* Wallet Address and Disconnect Section */}
            <div className="flex flex-col space-y-2 p-4 bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)] mt-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-[#9B8B6C]">Your wallet address:</span>
                <TonConnectButton />
              </div>
              <p className="break-all text-sm text-[#B3B3B3]/80 font-mono">
                {storedWallet || userFriendlyAddress}
              </p>
              {storedWallet && storedWallet !== userFriendlyAddress && (
                <p className="text-xs text-yellow-500">
                  Note: Your connected wallet is different from your stored wallet
                </p>
              )}
            </div>

            {/* Loading and Error States */}
            {isLoading && (
              <div className="text-center text-[#9B8B6C] p-4 bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 shadow-[0_4px_15px_rgba(74,14,14,0.3)] mt-4">
                <p>Processing...</p>
                <p className="mt-1 text-sm text-[#B3B3B3]/80">Please wait</p>
              </div>
            )}

            {error && (
              <div className="text-center text-[#FF6B6B] p-4 bg-[#1A1617] rounded-xl border border-[#FF6B6B]/20 shadow-[0_4px_15px_rgba(74,14,14,0.3)] mt-4">
                <p>{error}</p>
                <p className="mt-1 text-sm text-[#B3B3B3]/80">Please try again</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
