<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Prize extends Model
{
    use HasFactory;

    protected $fillable = [
        'prize_tree_id',
        'name',
        'description',
        'icon',
        'tier',
        'position',
        'category',
        'cost',
        'is_root',
        'reward_type',
        'reward_data',
        'is_premium'
    ];

    protected $casts = [
        'is_root' => 'boolean',
        'is_premium' => 'boolean',
        'reward_data' => 'array'
    ];

    /**
     * Get the prize tree that owns this prize.
     */
    public function prizeTree()
    {
        return $this->belongsTo(PrizeTree::class);
    }

    /**
     * Get the prerequisites for this prize.
     */
    public function prerequisites()
    {
        return $this->belongsToMany(
            Prize::class,
            'prize_prerequisites',
            'prize_id',
            'prerequisite_prize_id'
        );
    }

    /**
     * Get the prizes that have this prize as a prerequisite.
     */
    public function unlocks()
    {
        return $this->belongsToMany(
            Prize::class,
            'prize_prerequisites',
            'prerequisite_prize_id',
            'prize_id'
        );
    }

    /**
     * Get the users who have unlocked this prize.
     */
    public function users()
    {
        return $this->belongsToMany(
            TelegramUser::class,
            'user_prizes',
            'prize_id',
            'telegram_user_id'
        )->withTimestamps()->withPivot('unlocked_at', 'is_equipped');
    }
    
    /**
     * Get the reward details based on the reward type.
     */
    public function getRewardDetails()
    {
        switch ($this->reward_type) {
            case 'cosmetic':
                // Return cosmetic details
                return [
                    'type' => $this->reward_data['type'] ?? 'slash_effect',
                    'visual_data' => $this->reward_data['visual_data'] ?? [],
                    'preview_image' => $this->reward_data['preview_image'] ?? null
                ];
                
            case 'card':
                // Return card details
                return [
                    'card_id' => $this->reward_data['card_id'] ?? null,
                    'rarity' => $this->reward_data['rarity'] ?? 'common'
                ];
                
            case 'balance':
                // Return balance amount
                return [
                    'amount' => $this->reward_data['amount'] ?? 0,
                    'currency' => $this->reward_data['currency'] ?? 'coins'
                ];
                
            case 'booster':
                // Return booster details
                return [
                    'type' => $this->reward_data['booster_type'] ?? '',
                    'multiplier' => $this->reward_data['multiplier'] ?? 1,
                    'duration' => $this->reward_data['duration'] ?? 0 // in hours
                ];
                
            case 'special_item':
                // Return special item details
                return [
                    'item_id' => $this->reward_data['item_id'] ?? null,
                    'item_type' => $this->reward_data['item_type'] ?? 'collectible'
                ];
                
            case 'title':
                // Return title details
                return [
                    'title' => $this->reward_data['title'] ?? '',
                    'color' => $this->reward_data['color'] ?? '#9B8B6C'
                ];
                
            case 'emote':
                // Return emote details
                return [
                    'emote' => $this->reward_data['emote'] ?? '',
                    'animation' => $this->reward_data['animation'] ?? null
                ];
                
            default:
                return null;
        }
    }
}
