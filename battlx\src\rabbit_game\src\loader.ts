/**
 * Rabbit Game Script Loader
 * 
 * This module handles loading all the necessary JavaScript files for the Rabbit Game
 * before the game is initialized.
 */

// List of scripts to load in order
const scripts = [
  // Engine
  '/game/rabbit/js/engine.js',
  
  // Game Components
  '/game/rabbit/js/src/constants.js',
  '/game/rabbit/js/src/utils.js',
  '/game/rabbit/js/src/assetLoader.js',
  '/game/rabbit/js/src/inputHandler.js',
  
  // Game Entities
  '/game/rabbit/js/src/entities/player.js',
  '/game/rabbit/js/src/entities/turtle.js',
  '/game/rabbit/js/src/entities/diamond.js',
  '/game/rabbit/js/src/entities/background.js',
  
  // Game States
  '/game/rabbit/js/src/states/gameState.js',
  '/game/rabbit/js/src/states/menuState.js',
  '/game/rabbit/js/src/states/playState.js',
  '/game/rabbit/js/src/states/gameOverState.js'
];

// Track loading status
let loadedScripts = 0;
let totalScripts = scripts.length;
let loadingPromise: Promise<void> | null = null;

/**
 * Load all scripts required for the Rabbit Game
 * @returns Promise that resolves when all scripts are loaded
 */
export const loadGameScripts = (): Promise<void> => {
  // Return existing promise if already loading
  if (loadingPromise) {
    return loadingPromise;
  }
  
  // Create new loading promise
  loadingPromise = new Promise((resolve, reject) => {
    // Skip loading if scripts are already loaded
    // Check if Engine and AssetLoader are available
    if ((window as any).Engine && (window as any).AssetLoader) {
      console.log('Rabbit Game engine components already loaded');
      console.log('Engine:', (window as any).Engine);
      console.log('AssetLoader:', (window as any).AssetLoader);
      resolve();
      return;
    } else {
      console.log('Engine or AssetLoader not found in window object');
      console.log('Window keys:', Object.keys(window));
    }
    
    console.log('Loading Rabbit Game scripts...');
    
    // Load scripts in sequence
    const loadNextScript = (index: number) => {
      if (index >= scripts.length) {
        console.log('All Rabbit Game scripts loaded successfully');
        resolve();
        return;
      }
      
      const script = document.createElement('script');
      script.src = scripts[index];
      script.async = false; // Load in order
      script.type = 'text/javascript'; // Ensure correct type
      
      console.log(`Loading script ${index + 1}/${scripts.length}: ${scripts[index]}`);
      
      script.onload = () => {
        loadedScripts++;
        console.log(`Script loaded successfully: ${scripts[index]}`);
        
        // Check if Engine or AssetLoader was loaded in this script
        if (scripts[index].includes('engine.js') && (window as any).Engine) {
          console.log('Engine class found after loading engine.js:', (window as any).Engine);
        }
        
        if (scripts[index].includes('assetLoader.js') && (window as any).AssetLoader) {
          console.log('AssetLoader class found after loading assetLoader.js:', (window as any).AssetLoader);
        }
        
        loadNextScript(index + 1);
      };
      
      script.onerror = (error) => {
        console.error(`Failed to load script: ${scripts[index]}`, error);
        reject(new Error(`Failed to load script: ${scripts[index]}`));
      };
      
      // Append to document.head instead of document.body for better script loading
      document.head.appendChild(script);
    };
    
    // Start loading scripts
    loadNextScript(0);
  });
  
  return loadingPromise;
};

/**
 * Get the current loading progress
 * @returns Object with loading status
 */
export const getLoadingProgress = () => {
  return {
    loaded: loadedScripts,
    total: totalScripts,
    progress: loadedScripts / totalScripts
  };
};