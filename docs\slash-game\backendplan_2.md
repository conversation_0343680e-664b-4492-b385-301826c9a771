# Prize Tree System - Backend Implementation (Part 2)

## Models (Continued)

### 4. UserAchievementPoint Model

```php
// Create model
php artisan make:model UserAchievementPoint

// Model file
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserAchievementPoint extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id',
        'total_earned',
        'total_spent'
    ];

    /**
     * Get the user that owns these achievement points.
     */
    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    /**
     * Get the available achievement points.
     */
    public function getAvailablePointsAttribute()
    {
        return $this->total_earned - $this->total_spent;
    }
}
```

### 5. AchievementPointTransaction Model

```php
// Create model
php artisan make:model AchievementPointTransaction

// Model file
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AchievementPointTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id',
        'amount',
        'type',
        'source',
        'source_id',
        'description'
    ];

    /**
     * Get the user that owns this transaction.
     */
    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }
    
    /**
     * Get the related source entity based on the source type.
     */
    public function sourceEntity()
    {
        if (!$this->source_id) {
            return null;
        }
        
        switch ($this->source) {
            case 'prize_unlock':
                return Prize::find($this->source_id);
                
            case 'mission_complete':
                return Mission::find($this->source_id);
                
            case 'level_up':
                return null; // Level doesn't have a dedicated model
                
            default:
                return null;
        }
    }
}
```

### 6. TelegramUser Model Extension

Add the following relationships to the existing TelegramUser model:

```php
// Add to the TelegramUser model

/**
 * Get the prizes that the user has unlocked.
 */
public function prizes()
{
    return $this->belongsToMany(
        Prize::class,
        'user_prizes',
        'telegram_user_id',
        'prize_id'
    )->withTimestamps()->withPivot('unlocked_at', 'is_equipped');
}

/**
 * Get the user's achievement points.
 */
public function achievementPoints()
{
    return $this->hasOne(UserAchievementPoint::class, 'telegram_user_id');
}

/**
 * Get the user's achievement point transactions.
 */
public function achievementPointTransactions()
{
    return $this->hasMany(AchievementPointTransaction::class, 'telegram_user_id');
}

/**
 * Get the available achievement points.
 */
public function getAvailableAchievementPointsAttribute()
{
    $achievementPoints = $this->achievementPoints;
    
    if (!$achievementPoints) {
        return 0;
    }
    
    return $achievementPoints->available_points;
}

/**
 * Get the user's equipped prizes by type.
 */
public function getEquippedPrizesAttribute()
{
    $equippedPrizes = [];
    
    $userPrizes = $this->prizes()
        ->wherePivot('is_equipped', true)
        ->get();
    
    foreach ($userPrizes as $prize) {
        $equippedPrizes[$prize->reward_type] = $prize;
    }
    
    return $equippedPrizes;
}
```

## Controllers

### 1. PrizeTreeController

```php
// Create controller
php artisan make:controller API/PrizeTreeController

// Controller file
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\PrizeTree;
use Illuminate\Http\Request;

class PrizeTreeController extends Controller
{
    /**
     * Get all active prize trees.
     */
    public function index()
    {
        $prizeTrees = PrizeTree::where('is_active', true)
            ->orderBy('display_order')
            ->get();
            
        return response()->json($prizeTrees);
    }

    /**
     * Get a specific prize tree with its prizes.
     */
    public function show($id)
    {
        $prizeTree = PrizeTree::with(['prizes' => function ($query) {
            $query->orderBy('tier')->orderBy('position');
        }])->findOrFail($id);
        
        // Format the prizes to include prerequisites
        $prizes = $prizeTree->prizes->map(function ($prize) {
            $prerequisites = $prize->prerequisites->pluck('id')->toArray();
            return array_merge($prize->toArray(), [
                'prerequisites' => $prerequisites,
                'reward_details' => $prize->getRewardDetails()
            ]);
        });
        
        // Create connections between prizes based on prerequisites
        $connections = [];
        foreach ($prizes as $prize) {
            foreach ($prize['prerequisites'] as $prerequisiteId) {
                $connections[] = [
                    'startNodeId' => $prerequisiteId,
                    'endNodeId' => $prize['id']
                ];
            }
        }
        
        return response()->json([
            'id' => $prizeTree->id,
            'name' => $prizeTree->name,
            'description' => $prizeTree->description,
            'icon' => $prizeTree->icon,
            'theme_color' => $prizeTree->theme_color,
            'is_seasonal' => $prizeTree->is_seasonal,
            'available_until' => $prizeTree->available_until,
            'nodes' => $prizes,
            'connections' => $connections
        ]);
    }
}
```

### 2. UserPrizeController

```php
// Create controller
php artisan make:controller API/UserPrizeController

// Controller file
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Prize;
use App\Models\UserPrize;
use App\Models\UserAchievementPoint;
use App\Models\AchievementPointTransaction;
use App\Services\PrizeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserPrizeController extends Controller
{
    protected $prizeService;
    
    public function __construct(PrizeService $prizeService)
    {
        $this->prizeService = $prizeService;
    }
    
    /**
     * Get the user's prizes and achievement points.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        // Get user's unlocked prizes
        $userPrizes = UserPrize::where('telegram_user_id', $user->id)
            ->with('prize')
            ->get();
            
        // Get user's achievement points
        $achievementPoints = UserAchievementPoint::firstOrCreate(
            ['telegram_user_id' => $user->id],
            ['total_earned' => 0, 'total_spent' => 0]
        );
        
        // Get equipped prizes
        $equippedPrizes = $userPrizes->where('is_equipped', true)
            ->map(function ($userPrize) {
                return [
                    'prize_id' => $userPrize->prize_id,
                    'reward_type' => $userPrize->prize->reward_type,
                    'reward_details' => $userPrize->prize->getRewardDetails()
                ];
            });
        
        return response()->json([
            'achievement_points' => $achievementPoints->available_points,
            'unlocked_prizes' => $userPrizes->pluck('prize_id')->toArray(),
            'equipped_prizes' => $equippedPrizes,
            'user_prizes' => $userPrizes->map(function ($userPrize) {
                return [
                    'prize_id' => $userPrize->prize_id,
                    'unlocked_at' => $userPrize->unlocked_at->toIso8601String(),
                    'is_equipped' => $userPrize->is_equipped,
                    'prize' => [
                        'name' => $userPrize->prize->name,
                        'description' => $userPrize->prize->description,
                        'icon' => $userPrize->prize->icon,
                        'reward_type' => $userPrize->prize->reward_type,
                        'reward_details' => $userPrize->prize->getRewardDetails()
                    ]
                ];
            })
        ]);
    }

    /**
     * Unlock a prize for the user.
     */
    public function unlock(Request $request)
    {
        $request->validate([
            'prize_id' => 'required|exists:prizes,id'
        ]);
        
        $user = $request->user();
        $prizeId = $request->prize_id;
        
        // Use the prize service to handle the unlock
        $result = $this->prizeService->unlockPrize($user->id, $prizeId);
        
        if (!$result['success']) {
            return response()->json([
                'message' => $result['message']
            ], 400);
        }
        
        return response()->json([
            'message' => 'Prize unlocked successfully',
            'prize' => $result['prize'],
            'remaining_points' => $result['remaining_points']
        ]);
    }
    
    /**
     * Equip a prize.
     */
    public function equip(Request $request)
    {
        $request->validate([
            'prize_id' => 'required|exists:prizes,id'
        ]);
        
        $user = $request->user();
        $prizeId = $request->prize_id;
        
        // Check if the user has this prize
        $userPrize = UserPrize::where('telegram_user_id', $user->id)
            ->where('prize_id', $prizeId)
            ->first();
            
        if (!$userPrize) {
            return response()->json([
                'message' => 'You have not unlocked this prize'
            ], 400);
        }
        
        // Equip the prize
        $userPrize->equip();
        
        return response()->json([
            'message' => 'Prize equipped successfully',
            'prize_id' => $prizeId,
            'reward_type' => $userPrize->prize->reward_type,
            'reward_details' => $userPrize->prize->getRewardDetails()
        ]);
    }
    
    /**
     * Unequip a prize.
     */
    public function unequip(Request $request)
    {
        $request->validate([
            'prize_id' => 'required|exists:prizes,id'
        ]);
        
        $user = $request->user();
        $prizeId = $request->prize_id;
        
        // Check if the user has this prize
        $userPrize = UserPrize::where('telegram_user_id', $user->id)
            ->where('prize_id', $prizeId)
            ->first();
            
        if (!$userPrize) {
            return response()->json([
                'message' => 'You have not unlocked this prize'
            ], 400);
        }
        
        // Unequip the prize
        $userPrize->unequip();
        
        return response()->json([
            'message' => 'Prize unequipped successfully',
            'prize_id' => $prizeId
        ]);
    }
}
```

### 3. AchievementPointController

```php
// Create controller
php artisan make:controller API/AchievementPointController

// Controller file
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\UserAchievementPoint;
use App\Models\AchievementPointTransaction;
use App\Services\AchievementPointService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AchievementPointController extends Controller
{
    protected $achievementPointService;
    
    public function __construct(AchievementPointService $achievementPointService)
    {
        $this->achievementPointService = $achievementPointService;
    }
    
    /**
     * Award achievement points to a user.
     */
    public function award(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:telegram_users,id',
            'amount' => 'required|integer|min:1',
            'source' => 'required|string',
            'source_id' => 'nullable|integer',
            'description' => 'nullable|string'
        ]);
        
        $userId = $request->user_id;
        $amount = $request->amount;
        
        // Use the service to award points
        $result = $this->achievementPointService->awardPoints(
            $userId,
            $amount,
            $request->source,
            $request->source_id,
            $request->description
        );
        
        if (!$result['success']) {
            return response()->json([
                'message' => 'Failed to award achievement points',
                'error' => $result['message']
            ], 500);
        }
        
        return response()->json([
            'message' => 'Achievement points awarded successfully',
            'total_points' => $result['total_points']
        ]);
    }

    /**
     * Get the user's achievement point transactions.
     */
    public function transactions(Request $request)
    {
        $user = $request->user();
        
        $transactions = AchievementPointTransaction::where('telegram_user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);
            
        return response()->json($transactions);
    }
}
```

## Services

### 1. PrizeService

Create a service to handle prize-related logic:

```php
// Create service directory if it doesn't exist
mkdir -p app/Services

// Create service file
// app/Services/PrizeService.php
namespace App\Services;

use App\Models\Prize;
use App\Models\UserPrize;
use App\Models\UserAchievementPoint;
use App\Models\AchievementPointTransaction;
use Illuminate\Support\Facades\DB;

class PrizeService
{
    protected $achievementPointService;
    
    public function __construct(AchievementPointService $achievementPointService)
    {
        $this->achievementPointService = $achievementPointService;
    }
    
    /**
     * Unlock a prize for a user.
     *
     * @param int $userId
     * @param int $prizeId
     * @return array
     */
    public function unlockPrize($userId, $prizeId)
    {
        // Check if the user already has this prize
        $existingPrize = UserPrize::where('telegram_user_id', $userId)
            ->where('prize_id', $prizeId)
            ->first();
            
        if ($existingPrize) {
            return [
                'success' => false,
                'message' => 'Prize already unlocked'
            ];
        }
        
        // Get the prize and its prerequisites
        $prize = Prize::with('prerequisites')->findOrFail($prizeId);
        
        // Check if the user has all prerequisites
        $userPrizeIds = UserPrize::where('telegram_user_id', $userId)
            ->pluck('prize_id')
            ->toArray();
            
        $prerequisiteIds = $prize->prerequisites->pluck('id')->toArray();
        
        $missingPrerequisites = array_diff($prerequisiteIds, $userPrizeIds);
        
        if (!empty($missingPrerequisites)) {
            return [
                'success' => false,
                'message' => 'Missing prerequisites',
                'missing_prerequisites' => $missingPrerequisites
            ];
        }
        
        // Check if the user has enough achievement points
        $achievementPoints = UserAchievementPoint::firstOrCreate(
            ['telegram_user_id' => $userId],
            ['total_earned' => 0, 'total_spent' => 0]
        );
        
        if ($achievementPoints->available_points < $prize->cost) {
            return [
                'success' => false,
                'message' => 'Not enough achievement points',
                'required' => $prize->cost,
                'available' => $achievementPoints->available_points
            ];
        }
        
        // Unlock the prize and update achievement points in a transaction
        DB::beginTransaction();
        
        try {
            // Create user prize
            $userPrize = UserPrize::create([
                'telegram_user_id' => $userId,
                'prize_id' => $prizeId,
                'unlocked_at' => now()
            ]);
            
            // Deduct achievement points
            $deductResult = $this->achievementPointService->deductPoints(
                $userId,
                $prize->cost,
                'prize_unlock',
                $prizeId,
                "Unlocked prize: {$prize->name}"
            );
            
            if (!$deductResult['success']) {
                throw new \Exception($deductResult['message']);
            }
            
            // Apply prize effects based on reward type
            $this->applyPrizeEffects($userId, $prize);
            
            DB::commit();
            
            return [
                'success' => true,
                'message' => 'Prize unlocked successfully',
                'prize' => [
                    'id' => $prize->id,
                    'name' => $prize->name,
                    'reward_type' => $prize->reward_type,
                    'reward_details' => $prize->getRewardDetails()
                ],
                'remaining_points' => $deductResult['remaining_points']
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            return [
                'success' => false,
                'message' => 'Failed to unlock prize: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Apply the effects of a prize to the user.
     *
     * @param int $userId
     * @param Prize $prize
     * @return void
     */
    private function applyPrizeEffects($userId, $prize)
    {
        $user = \App\Models\TelegramUser::find($userId);
        
        if (!$user) {
            return;
        }
        
        switch ($prize->reward_type) {
            case 'balance':
                // Award balance
                $rewardDetails = $prize->getRewardDetails();
                $amount = $rewardDetails['amount'] ?? 0;
                $currency = $rewardDetails['currency'] ?? 'coins';
                
                if ($amount > 0) {
                    if ($currency === 'coins') {
                        $user->balance += $amount;
                        $user->save();
                    }
                    // Handle other currency types if needed
                }
                break;
                
            case 'booster':
                // Apply booster
                $rewardDetails = $prize->getRewardDetails();
                $boosterType = $rewardDetails['type'] ?? '';
                $multiplier = $rewardDetails['multiplier'] ?? 1;
                $duration = $rewardDetails['duration'] ?? 0;
                
                if ($duration > 0) {
                    // Create a booster record
                    // This would depend on your booster implementation
                    // Example:
                    // Booster::create([
                    //     'telegram_user_id' => $userId,
                    //     'type' => $boosterType,
                    //     'multiplier' => $multiplier,
                    //     'expires_at' => now()->addHours($duration)
                    // ]);
                }
                break;
                
            // Handle other reward types as needed
        }
    }
}
```

### 2. AchievementPointService

Create a service to handle achievement point logic:

```php
// app/Services/AchievementPointService.php
namespace App\Services;

use App\Models\UserAchievementPoint;
use App\Models\AchievementPointTransaction;
use Illuminate\Support\Facades\DB;

class AchievementPointService
{
    /**
     * Award achievement points to a user.
     *
     * @param int $userId
     * @param int $amount
     * @param string $source
     * @param int|null $sourceId
     * @param string|null $description
     * @return array
     */
    public function awardPoints($userId, $amount, $source, $sourceId = null, $description = null)
    {
        DB::beginTransaction();
        
        try {
            // Update or create user achievement points
            $achievementPoints = UserAchievementPoint::firstOrCreate(
                ['telegram_user_id' => $userId],
                ['total_earned' => 0, 'total_spent' => 0]
            );
            
            $achievementPoints->total_earned += $amount;
            $achievementPoints->save();
            
            // Record transaction
            AchievementPointTransaction::create([
                'telegram_user_id' => $userId,
                'amount' => $amount,
                'type' => 'earn',
                'source' => $source,
                'source_id' => $sourceId,
                'description' => $description
            ]);
            
            DB::commit();
            
            return [
                'success' => true,
                'message' => 'Achievement points awarded successfully',
                'total_points' => $achievementPoints->available_points
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            return [
                'success' => false,
                'message' => 'Failed to award achievement points: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Deduct achievement points from a user.
     *
     * @param int $userId
     * @param int $amount
     * @param string $source
     * @param int|null $sourceId
     * @param string|null $description
     * @return array
     */
    public function deductPoints($userId, $amount, $source, $sourceId = null, $description = null)
    {
        DB::beginTransaction();
        
        try {
            // Get user achievement points
            $achievementPoints = UserAchievementPoint::where('telegram_user_id', $userId)->first();
            
            if (!$achievementPoints || $achievementPoints->available_points < $amount) {
                return [
                    'success' => false,
                    'message' => 'Not enough achievement points',
                    'required' => $amount,
                    'available' => $achievementPoints ? $achievementPoints->available_points : 0
                ];
            }
            
            // Update achievement points
            $achievementPoints->total_spent += $amount;
            $achievementPoints->save();
            
            // Record transaction
            AchievementPointTransaction::create([
                'telegram_user_id' => $userId,
                'amount' => -$amount,
                'type' => 'spend',
                'source' => $source,
                'source_id' => $sourceId,
                'description' => $description
            ]);
            
            DB::commit();
            
            return [
                'success' => true,
                'message' => 'Achievement points deducted successfully',
                'remaining_points' => $achievementPoints->available_points
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            return [
                'success' => false,
                'message' => 'Failed to deduct achievement points: ' . $e->getMessage()
            ];
        }
    }
}
```

## Routes

Add the following routes to your API routes file:

```php
// routes/api.php

// Prize Trees
Route::get('/prize-trees', [PrizeTreeController::class, 'index']);
Route::get('/prize-trees/{id}', [PrizeTreeController::class, 'show']);

// User Prizes
Route::get('/user-prizes', [UserPrizeController::class, 'index']);
Route::post('/unlock-prize', [UserPrizeController::class, 'unlock']);
Route::post('/equip-prize', [UserPrizeController::class, 'equip']);
Route::post('/unequip-prize', [UserPrizeController::class, 'unequip']);

// Achievement Points
Route::post('/award-achievement-points', [AchievementPointController::class, 'award'])->middleware('admin');
Route::get('/achievement-point-transactions', [AchievementPointController::class, 'transactions']);
```
