<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('mission_completions', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key (BIGINT in PostgreSQL)
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade'); // Foreign key to telegram_users
            $table->foreignId('mission_id')->constrained('missions')->onDelete('cascade'); // Foreign key to missions
            $table->timestamp('completed_at'); // TIMESTAMP column
            $table->timestamps(); // created_at and updated_at columns

            // Unique constraint on the combination of telegram_user_id and mission_id
            $table->unique(['telegram_user_id', 'mission_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mission_completions'); // Drop the table if it exists
    }
};