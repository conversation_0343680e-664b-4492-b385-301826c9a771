# Phase 2: Territory Control System Implementation

## Overview
Implement a strategic territory control system where guilds compete for map dominance, resource control, and passive income generation.

## Backend Implementation

### 1. Database Schema

#### Territory System Tables
```php
// 2024_XX_XX_create_territory_system_tables.php

Schema::create('guilds', function (Blueprint $table) {
    $table->id();
    $table->string('name')->unique();
    $table->string('tag', 5)->unique(); // Guild abbreviation
    $table->text('description')->nullable();
    $table->string('banner_url')->nullable();
    $table->foreignId('leader_id')->constrained('telegram_users');
    $table->integer('level')->default(1);
    $table->integer('experience')->default(0);
    $table->integer('member_limit')->default(20);
    $table->integer('treasury')->default(0); // Guild shared resources
    $table->json('perks')->nullable(); // Guild bonuses
    $table->timestamps();
});

Schema::create('guild_members', function (Blueprint $table) {
    $table->id();
    $table->foreignId('guild_id')->constrained()->onDelete('cascade');
    $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
    $table->enum('role', ['member', 'officer', 'leader']);
    $table->integer('contribution_points')->default(0);
    $table->timestamp('joined_at');
    $table->timestamps();
    
    $table->unique(['guild_id', 'telegram_user_id']);
});

Schema::create('territories', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->integer('x_coordinate'); // Hex grid position
    $table->integer('y_coordinate');
    $table->enum('terrain_type', ['plains', 'forest', 'mountain', 'desert', 'water']);
    $table->enum('resource_type', ['gold', 'energy', 'materials', 'rare_gems']);
    $table->integer('resource_generation_rate'); // per hour
    $table->integer('defense_bonus')->default(0);
    $table->foreignId('owner_guild_id')->nullable()->constrained('guilds');
    $table->timestamp('captured_at')->nullable();
    $table->integer('capture_points')->default(100); // Health of territory
    $table->json('special_features')->nullable(); // Unique territory bonuses
    $table->timestamps();
    
    $table->unique(['x_coordinate', 'y_coordinate']);
});

Schema::create('territory_battles', function (Blueprint $table) {
    $table->id();
    $table->foreignId('territory_id')->constrained()->onDelete('cascade');
    $table->foreignId('attacker_guild_id')->constrained('guilds');
    $table->foreignId('defender_guild_id')->nullable()->constrained('guilds');
    $table->enum('status', ['preparing', 'active', 'completed', 'cancelled']);
    $table->timestamp('scheduled_at');
    $table->timestamp('started_at')->nullable();
    $table->timestamp('ended_at')->nullable();
    $table->integer('attacker_score')->default(0);
    $table->integer('defender_score')->default(0);
    $table->json('battle_log')->nullable();
    $table->timestamps();
});

Schema::create('territory_battle_participants', function (Blueprint $table) {
    $table->id();
    $table->foreignId('territory_battle_id')->constrained()->onDelete('cascade');
    $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
    $table->enum('side', ['attacker', 'defender']);
    $table->integer('damage_dealt')->default(0);
    $table->integer('damage_taken')->default(0);
    $table->integer('points_earned')->default(0);
    $table->timestamps();
});

Schema::create('guild_alliances', function (Blueprint $table) {
    $table->id();
    $table->foreignId('guild_1_id')->constrained('guilds')->onDelete('cascade');
    $table->foreignId('guild_2_id')->constrained('guilds')->onDelete('cascade');
    $table->enum('status', ['pending', 'active', 'expired']);
    $table->timestamp('expires_at');
    $table->timestamps();
    
    $table->unique(['guild_1_id', 'guild_2_id']);
});

Schema::create('resource_collections', function (Blueprint $table) {
    $table->id();
    $table->foreignId('guild_id')->constrained()->onDelete('cascade');
    $table->foreignId('territory_id')->constrained()->onDelete('cascade');
    $table->enum('resource_type', ['gold', 'energy', 'materials', 'rare_gems']);
    $table->integer('amount');
    $table->timestamp('collected_at');
    $table->timestamps();
});
```

### 2. Models

#### Guild Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Guild extends Model
{
    protected $fillable = [
        'name', 'tag', 'description', 'banner_url', 'leader_id',
        'level', 'experience', 'member_limit', 'treasury', 'perks'
    ];

    protected $casts = [
        'perks' => 'array'
    ];

    public function leader()
    {
        return $this->belongsTo(TelegramUser::class, 'leader_id');
    }

    public function members()
    {
        return $this->hasMany(GuildMember::class);
    }

    public function territories()
    {
        return $this->hasMany(Territory::class, 'owner_guild_id');
    }

    public function territoryBattles()
    {
        return $this->hasMany(TerritoryBattle::class, 'attacker_guild_id')
            ->orWhere('defender_guild_id', $this->id);
    }

    public function alliances()
    {
        return $this->hasMany(GuildAlliance::class, 'guild_1_id')
            ->orWhere('guild_2_id', $this->id);
    }

    public function getTotalPowerAttribute()
    {
        return $this->members()->sum('contribution_points') + ($this->level * 100);
    }

    public function getHourlyResourcesAttribute()
    {
        return $this->territories()->sum('resource_generation_rate');
    }

    public function canAcceptNewMembers()
    {
        return $this->members()->count() < $this->member_limit;
    }

    public function addExperience(int $amount)
    {
        $this->experience += $amount;
        
        // Check for level up
        $requiredExp = $this->level * 1000;
        if ($this->experience >= $requiredExp) {
            $this->level++;
            $this->member_limit += 5; // Increase member capacity
            $this->experience -= $requiredExp;
        }
        
        $this->save();
    }
}
```

#### Territory Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Territory extends Model
{
    protected $fillable = [
        'name', 'x_coordinate', 'y_coordinate', 'terrain_type',
        'resource_type', 'resource_generation_rate', 'defense_bonus',
        'owner_guild_id', 'captured_at', 'capture_points', 'special_features'
    ];

    protected $casts = [
        'captured_at' => 'datetime',
        'special_features' => 'array'
    ];

    public function ownerGuild()
    {
        return $this->belongsTo(Guild::class, 'owner_guild_id');
    }

    public function battles()
    {
        return $this->hasMany(TerritoryBattle::class);
    }

    public function getAdjacentTerritories()
    {
        // Hexagonal grid adjacent coordinates
        $adjacentCoords = [
            [$this->x_coordinate + 1, $this->y_coordinate],
            [$this->x_coordinate - 1, $this->y_coordinate],
            [$this->x_coordinate, $this->y_coordinate + 1],
            [$this->x_coordinate, $this->y_coordinate - 1],
            [$this->x_coordinate + 1, $this->y_coordinate - 1],
            [$this->x_coordinate - 1, $this->y_coordinate + 1],
        ];

        return Territory::whereIn('x_coordinate', array_column($adjacentCoords, 0))
            ->whereIn('y_coordinate', array_column($adjacentCoords, 1))
            ->get();
    }

    public function canBeAttackedBy(Guild $guild)
    {
        if ($this->owner_guild_id === $guild->id) {
            return false; // Can't attack own territory
        }

        // Check if guild has adjacent territory or is within attack range
        $adjacentTerritories = $this->getAdjacentTerritories();
        $guildTerritories = $guild->territories()->get();

        foreach ($guildTerritories as $guildTerritory) {
            if ($adjacentTerritories->contains('id', $guildTerritory->id)) {
                return true;
            }
        }

        return false;
    }

    public function generateResources()
    {
        if (!$this->owner_guild_id) {
            return 0;
        }

        $baseGeneration = $this->resource_generation_rate;
        $guildBonus = $this->ownerGuild->level * 0.1; // 10% per guild level
        $terrainBonus = $this->getTerrainBonus();

        return $baseGeneration * (1 + $guildBonus + $terrainBonus);
    }

    protected function getTerrainBonus()
    {
        return match ($this->terrain_type) {
            'plains' => 0.1,
            'forest' => 0.15,
            'mountain' => 0.2,
            'desert' => 0.05,
            'water' => 0.25,
            default => 0
        };
    }
}
```

### 3. Controllers

#### TerritoryController
```php
<?php

namespace App\Http\Controllers;

use App\Models\Territory;
use App\Models\Guild;
use App\Models\TerritoryBattle;
use App\Services\TerritoryService;
use Illuminate\Http\Request;

class TerritoryController extends Controller
{
    protected $territoryService;

    public function __construct(TerritoryService $territoryService)
    {
        $this->territoryService = $territoryService;
    }

    public function getMap(Request $request)
    {
        $territories = Territory::with(['ownerGuild'])
            ->get()
            ->map(function ($territory) {
                return [
                    'id' => $territory->id,
                    'name' => $territory->name,
                    'x' => $territory->x_coordinate,
                    'y' => $territory->y_coordinate,
                    'terrain_type' => $territory->terrain_type,
                    'resource_type' => $territory->resource_type,
                    'owner' => $territory->ownerGuild ? [
                        'id' => $territory->ownerGuild->id,
                        'name' => $territory->ownerGuild->name,
                        'tag' => $territory->ownerGuild->tag,
                        'color' => $this->getGuildColor($territory->ownerGuild->id)
                    ] : null,
                    'capture_points' => $territory->capture_points,
                    'defense_bonus' => $territory->defense_bonus
                ];
            });

        return response()->json([
            'territories' => $territories,
            'map_size' => ['width' => 20, 'height' => 15] // Configurable map dimensions
        ]);
    }

    public function attackTerritory(Request $request, Territory $territory)
    {
        $request->validate([
            'guild_id' => 'required|exists:guilds,id'
        ]);

        $user = $request->user();
        $guild = Guild::find($request->guild_id);

        // Verify user is member of attacking guild
        $membership = $guild->members()
            ->where('telegram_user_id', $user->id)
            ->first();

        if (!$membership) {
            return response()->json(['error' => 'Not a member of this guild'], 403);
        }

        // Check if territory can be attacked
        if (!$territory->canBeAttackedBy($guild)) {
            return response()->json(['error' => 'Territory not in attack range'], 400);
        }

        // Check for existing active battle
        $existingBattle = TerritoryBattle::where('territory_id', $territory->id)
            ->whereIn('status', ['preparing', 'active'])
            ->first();

        if ($existingBattle) {
            return response()->json(['error' => 'Territory already under attack'], 400);
        }

        $battle = $this->territoryService->initiateTerritoryAttack($guild, $territory);

        return response()->json([
            'message' => 'Territory attack initiated',
            'battle' => $battle,
            'preparation_time' => 300 // 5 minutes to prepare
        ]);
    }

    public function joinTerritoryBattle(Request $request, TerritoryBattle $battle)
    {
        $user = $request->user();

        if ($battle->status !== 'preparing') {
            return response()->json(['error' => 'Battle not in preparation phase'], 400);
        }

        $result = $this->territoryService->addParticipantToBattle($battle, $user);

        return response()->json($result);
    }

    public function getGuildTerritories(Request $request, Guild $guild)
    {
        $territories = $guild->territories()
            ->with(['battles' => function ($q) {
                $q->where('status', 'active')->latest();
            }])
            ->get();

        $totalResources = $territories->sum(function ($territory) {
            return $territory->generateResources();
        });

        return response()->json([
            'territories' => $territories,
            'total_hourly_resources' => $totalResources,
            'territory_count' => $territories->count()
        ]);
    }

    public function collectResources(Request $request, Territory $territory)
    {
        $user = $request->user();
        $guild = $user->guild;

        if (!$guild || $territory->owner_guild_id !== $guild->id) {
            return response()->json(['error' => 'Not authorized'], 403);
        }

        $collected = $this->territoryService->collectTerritoryResources($territory, $guild);

        return response()->json([
            'message' => 'Resources collected',
            'amount' => $collected,
            'resource_type' => $territory->resource_type
        ]);
    }

    protected function getGuildColor(int $guildId): string
    {
        $colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ];
        
        return $colors[$guildId % count($colors)];
    }
}
```

### 4. Services

#### TerritoryService
```php
<?php

namespace App\Services;

use App\Models\Territory;
use App\Models\Guild;
use App\Models\TerritoryBattle;
use App\Models\TerritoryBattleParticipant;
use App\Models\ResourceCollection;
use App\Models\TelegramUser;

class TerritoryService
{
    public function initiateTerritoryAttack(Guild $attackerGuild, Territory $territory)
    {
        $battle = TerritoryBattle::create([
            'territory_id' => $territory->id,
            'attacker_guild_id' => $attackerGuild->id,
            'defender_guild_id' => $territory->owner_guild_id,
            'status' => 'preparing',
            'scheduled_at' => now()->addMinutes(5) // 5 minute preparation time
        ]);

        // Notify both guilds
        $this->notifyGuildMembers($attackerGuild, 'territory_attack_initiated', $battle);
        
        if ($territory->ownerGuild) {
            $this->notifyGuildMembers($territory->ownerGuild, 'territory_under_attack', $battle);
        }

        return $battle;
    }

    public function addParticipantToBattle(TerritoryBattle $battle, TelegramUser $user)
    {
        $userGuild = $user->guild;
        
        if (!$userGuild) {
            return ['error' => 'User not in a guild'];
        }

        $side = null;
        if ($userGuild->id === $battle->attacker_guild_id) {
            $side = 'attacker';
        } elseif ($userGuild->id === $battle->defender_guild_id) {
            $side = 'defender';
        } else {
            return ['error' => 'User guild not involved in this battle'];
        }

        // Check if user already participating
        $existing = TerritoryBattleParticipant::where('territory_battle_id', $battle->id)
            ->where('telegram_user_id', $user->id)
            ->first();

        if ($existing) {
            return ['error' => 'Already participating in this battle'];
        }

        TerritoryBattleParticipant::create([
            'territory_battle_id' => $battle->id,
            'telegram_user_id' => $user->id,
            'side' => $side
        ]);

        return [
            'message' => 'Successfully joined territory battle',
            'side' => $side,
            'battle_id' => $battle->id
        ];
    }

    public function startTerritoryBattle(TerritoryBattle $battle)
    {
        $battle->update([
            'status' => 'active',
            'started_at' => now()
        ]);

        // Deduct energy from all participants
        $participants = $battle->participants()->with('user')->get();
        
        foreach ($participants as $participant) {
            $participant->user->decrement('available_energy', 75); // Higher cost for territory battles
        }

        broadcast(new \App\Events\TerritoryBattleStarted($battle));
    }

    public function collectTerritoryResources(Territory $territory, Guild $guild)
    {
        $lastCollection = ResourceCollection::where('guild_id', $guild->id)
            ->where('territory_id', $territory->id)
            ->latest('collected_at')
            ->first();

        $hoursSinceLastCollection = $lastCollection 
            ? now()->diffInHours($lastCollection->collected_at)
            : 24; // Max 24 hours of resources

        $hoursSinceLastCollection = min($hoursSinceLastCollection, 24);
        $resourceAmount = $territory->generateResources() * $hoursSinceLastCollection;

        if ($resourceAmount > 0) {
            ResourceCollection::create([
                'guild_id' => $guild->id,
                'territory_id' => $territory->id,
                'resource_type' => $territory->resource_type,
                'amount' => $resourceAmount,
                'collected_at' => now()
            ]);

            // Add to guild treasury
            $guild->increment('treasury', $resourceAmount);
        }

        return $resourceAmount;
    }

    protected function notifyGuildMembers(Guild $guild, string $eventType, $data)
    {
        $members = $guild->members()->with('user')->get();
        
        foreach ($members as $member) {
            // Send push notification or in-app notification
            broadcast(new \App\Events\GuildNotification($member->user, $eventType, $data));
        }
    }
}
```

## Frontend Implementation

### 1. Territory Map Component

```typescript
// src/components/territory/TerritoryMap.tsx
import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { $http } from '@/lib/http';

interface Territory {
  id: number;
  name: string;
  x: number;
  y: number;
  terrain_type: string;
  resource_type: string;
  owner: {
    id: number;
    name: string;
    tag: string;
    color: string;
  } | null;
  capture_points: number;
  defense_bonus: number;
}

export const TerritoryMap: React.FC = () => {
  const [selectedTerritory, setSelectedTerritory] = useState<Territory | null>(null);
  const [mapScale, setMapScale] = useState(1);

  const { data: mapData, isLoading } = useQuery({
    queryKey: ['territory-map'],
    queryFn: () => $http.get('/api/territories/map').then(res => res.data),
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  const attackMutation = useMutation({
    mutationFn: (territoryId: number) =>
      $http.post(`/api/territories/${territoryId}/attack`, {
        guild_id: 1 // Get from user's guild
      }),
    onSuccess: () => {
      alert('Territory attack initiated!');
    }
  });

  const renderHexagon = (territory: Territory) => {
    const hexSize = 30 * mapScale;
    const x = territory.x * hexSize * 1.5;
    const y = territory.y * hexSize * Math.sqrt(3) + (territory.x % 2) * hexSize * Math.sqrt(3) / 2;

    return (
      <g
        key={territory.id}
        transform={`translate(${x}, ${y})`}
        onClick={() => setSelectedTerritory(territory)}
        className="cursor-pointer hover:opacity-80"
      >
        <polygon
          points={`${hexSize},0 ${hexSize/2},${hexSize*Math.sqrt(3)/2} ${-hexSize/2},${hexSize*Math.sqrt(3)/2} ${-hexSize},0 ${-hexSize/2},${-hexSize*Math.sqrt(3)/2} ${hexSize/2},${-hexSize*Math.sqrt(3)/2}`}
          fill={territory.owner?.color || '#666'}
          stroke="#333"
          strokeWidth="2"
        />
        <text
          x="0"
          y="0"
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize={`${8 * mapScale}px`}
          fill="white"
        >
          {territory.name}
        </text>
        <text
          x="0"
          y={`${12 * mapScale}`}
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize={`${6 * mapScale}px`}
          fill="#ccc"
        >
          {territory.owner?.tag || 'FREE'}
        </text>
      </g>
    );
  };

  if (isLoading) {
    return <div className="loading">Loading territory map...</div>;
  }

  return (
    <div className="territory-map-container">
      <div className="map-controls">
        <button onClick={() => setMapScale(s => Math.min(s + 0.2, 2))}>Zoom In</button>
        <button onClick={() => setMapScale(s => Math.max(s - 0.2, 0.5))}>Zoom Out</button>
      </div>

      <svg
        width="100%"
        height="600"
        viewBox="0 0 1200 800"
        className="territory-map"
      >
        {mapData?.territories?.map((territory: Territory) => renderHexagon(territory))}
      </svg>

      {selectedTerritory && (
        <div className="territory-details-modal">
          <div className="modal-content">
            <h3>{selectedTerritory.name}</h3>
            <p>Terrain: {selectedTerritory.terrain_type}</p>
            <p>Resource: {selectedTerritory.resource_type}</p>
            <p>Owner: {selectedTerritory.owner?.name || 'Unclaimed'}</p>
            <p>Defense: {selectedTerritory.defense_bonus}</p>
            <p>Health: {selectedTerritory.capture_points}/100</p>
            
            {selectedTerritory.owner && (
              <button
                onClick={() => attackMutation.mutate(selectedTerritory.id)}
                disabled={attackMutation.isPending}
              >
                Attack Territory
              </button>
            )}
            
            <button onClick={() => setSelectedTerritory(null)}>Close</button>
          </div>
        </div>
      )}
    </div>
  );
};
```

## Next Steps

1. **Guild Management UI**: Create comprehensive guild interfaces
2. **Real-time Battle System**: Integrate territory battles with the battle engine
3. **Resource Management**: Implement guild treasury and resource trading
4. **Alliance System**: Add diplomatic features between guilds
5. **Territory Customization**: Allow guilds to upgrade their territories

This territory control system adds strategic depth and long-term engagement through guild cooperation and competition.
