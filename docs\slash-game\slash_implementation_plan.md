# Slash Game Implementation Plan

## Overview
This document outlines the implementation plan for integrating the existing Slash game into the Telegram web app. The Slash game is already a complete game written in ES6 and ready to play. We will follow the same integration pattern used for the Tower and Rabbit games, with no changes to the game itself.

## Current Status Analysis
- The database already has the `slash_game_unlocked` field in the `telegram_users` table
- The `canPlaySlashGame()` method is implemented in the `TelegramUser` model
- The `GameController` has basic support for the Slash game in the `checkPlayAvailability` and `usePlay` methods
- The Slash game follows the Rabbit game pattern with unlimited plays once unlocked
- The frontend is attempting to load the Slash game module from `slash_game/src/main.ts` but it's missing
- The game registry in `battlx/src/games/registry.ts` already includes the Slash game configuration
- The SlashGameDrawer component exists for unlocking the game

## Implementation Tasks

### 1. Create the Main Entry Point for the Slash Game
Create the missing `main.ts` file in the `battlx/src/slash_game/src` directory, following the pattern of the Rabbit game.

```typescript
// battlx/src/slash_game/src/main.ts
import { GameInstance } from '../../games/registry';

/**
 * Initialize and return a Slash Game instance
 * @param options Game options from GameWrapper
 * @returns GameInstance compatible with the registry
 */
export const slashGame = (options: any): GameInstance => {
  // Game instance reference
  let engine: any = null;
  let canvasElement: HTMLCanvasElement | null = null;
  let gameInitialized = false;

  // Create GameInstance wrapper
  const gameInstance: GameInstance = {
    // Load assets
    load: (onReady, onProgress) => {
      try {
        // Get the canvas element
        canvasElement = document.getElementById(options.canvasId) as HTMLCanvasElement;
        if (!canvasElement) {
          console.error(`Canvas element with ID ${options.canvasId} not found`);
          onProgress({
            success: 0,
            total: 1,
            failed: 1
          });
          return;
        }

        // Load all required scripts
        const scripts = [
          // Core Components
          '/game/slash/js/src/components/utils.js',
          '/game/slash/js/src/components/vector2.js',
          '/game/slash/js/src/components/containmentRect.js',
          '/game/slash/js/src/components/assetLoader.js',
          '/game/slash/js/src/components/inputHandler.js',
          '/game/slash/js/src/components/virtualJoystick.js',
          
          // Game Components
          '/game/slash/js/src/components/bgManager.js',
          '/game/slash/js/src/components/player.js',
          '/game/slash/js/src/components/enemy.js',
          '/game/slash/js/src/components/enemyProjectile.js',
          '/game/slash/js/src/components/weapon.js',
          '/game/slash/js/src/components/pickup.js',
          '/game/slash/js/src/components/destructible.js',
          '/game/slash/js/src/components/stage.js',
          '/game/slash/js/src/components/ui.js',
          '/game/slash/js/src/components/sceneManager.js',
          
          // Game Core
          '/game/slash/js/src/components/gameCore.js',
          '/game/slash/js/src/components/game.js',
          
          // Constants
          '/game/slash/js/consts/stages.js',
          '/game/slash/js/consts/enemies.js',
          '/game/slash/js/consts/weapons.js',
          '/game/slash/js/consts/treasures.js',
          
          // Main
          '/game/slash/js/main.js'
        ];

        let loaded = 0;
        const total = scripts.length;

        // Function to load scripts sequentially
        const loadScript = (index: number) => {
          if (index >= scripts.length) {
            // All scripts loaded
            initializeGame();
            return;
          }

          const script = document.createElement('script');
          script.src = scripts[index];
          script.async = false;

          script.onload = () => {
            loaded++;
            onProgress({
              success: loaded,
              total: total,
              failed: 0
            });
            loadScript(index + 1);
          };

          script.onerror = () => {
            console.error(`Failed to load script: ${scripts[index]}`);
            onProgress({
              success: loaded,
              total: total,
              failed: 1
            });
          };

          document.body.appendChild(script);
        };

        // Start loading scripts
        loadScript(0);

        // Initialize game after scripts are loaded
        const initializeGame = () => {
          try {
            // Check if Game class is available
            if (!(window as any).Game) {
              console.error('Game class not found in window object');
              onProgress({
                success: 0,
                total: 1,
                failed: 1
              });
              return;
            }

            // Create game instance
            const game = new (window as any).Game(canvasElement);
            engine = game;

            // Store integration callbacks in the game core
            if (options.setGameScore && (window as any).Game.core) {
              (window as any).Game.core.setGameScore = options.setGameScore;
            }
            if (options.onGameOver && (window as any).Game.core) {
              (window as any).Game.core.onGameOver = options.onGameOver;
            }

            // Initialize game
            game.start();
            gameInitialized = true;
            onReady();
          } catch (error) {
            console.error('Error initializing game:', error);
            onProgress({
              success: 0,
              total: 1,
              failed: 1
            });
          }
        };
      } catch (error) {
        console.error('Error loading game:', error);
        onProgress({
          success: 0,
          total: 1,
          failed: 1
        });
      }
    },

    // Start game
    start: () => {
      if (engine && gameInitialized) {
        engine.start();
      }
    },

    // Destroy game
    destroy: () => {
      if (!engine) return;
      
      try {
        // Stop the game loop
        engine.stop();
        
        // Clean up event listeners
        if (canvasElement) {
          canvasElement.removeEventListener('mousedown', engine.handleInput);
          canvasElement.removeEventListener('touchstart', engine.handleInput);
          window.removeEventListener('resize', engine.resizeCanvas);
        }
        
        // Clean up game objects
        if ((window as any).Game.core) {
          (window as any).Game.core.cleanUp();
        }
        
        // Clear canvas
        if (canvasElement) {
          const ctx = canvasElement.getContext('2d');
          if (ctx) {
            ctx.clearRect(0, 0, canvasElement.width, canvasElement.height);
          }
        }
      } catch (error) {
        console.error('Error destroying game:', error);
      }
    },

    // Play background music
    playBgm: () => {
      if ((window as any).Game.core && (window as any).Game.core.playBgm) {
        (window as any).Game.core.playBgm();
      }
    },

    // Pause background music
    pauseBgm: () => {
      if ((window as any).Game.core && (window as any).Game.core.pauseBgm) {
        (window as any).Game.core.pauseBgm();
      }
    },

    // Clear event listeners
    clearEventListeners: () => {
      if (canvasElement) {
        canvasElement.removeEventListener('mousedown', engine.handleInput);
        canvasElement.removeEventListener('touchstart', engine.handleInput);
        window.removeEventListener('resize', engine.resizeCanvas);
      }
    }
  };

  return gameInstance;
};
```

### 2. Update the GameWrapper Component
Ensure the GameWrapper component properly handles the Slash game in the frontend. The GameWrapper component already has logic for handling the Rabbit game with unlimited plays, so we just need to make sure it also applies to the Slash game.

```typescript
// In GameWrapper.tsx - checkAndStartGame method
// For Rabbit and Slash Games, skip the usePlay API call since they have unlimited plays
let playResult;
if (gameId === 'rabbit' || gameId === 'slash') {
  // For Rabbit and Slash Games, just return a successful result without calling usePlay
  playResult = {
    success: true,
    data: {
      plays_remaining: 999, // Unlimited plays
      balance: playData.balance || 0
    }
  };
} else {
  // For Tower Game and others, use a play attempt
  playResult = await gameApi.usePlay(paid, 1);
  if (!playResult.success || !playResult.data) {
    throw new Error(playResult.message || 'Failed to start game');
  }
}
```

### 3. Ensure the Game API Service Includes the game_id Parameter
Make sure the game API service includes the game_id parameter when calling the usePlay endpoint.

```typescript
// In game-api.ts - usePlay method
usePlay: async (paid: boolean = false, quantity: number = 1, gameId: string = 'tower') => {
  try {
    const { data } = await $http.post<ApiResponse<PlayResult>>('/game/use-play', {
      paid,
      quantity,
      game_id: gameId // Add game_id parameter
    });
    return data;
  } catch (error: any) {
    // Existing error handling...
  }
}
```

### 4. Ensure the GameController Properly Handles the Slash Game
The GameController already has support for the Slash game in the checkPlayAvailability and usePlay methods. We just need to make sure it's working correctly.

```php
// In GameController.php - usePlay method
public function usePlay(Request $request)
{
    $request->validate([
        'game_id' => 'required|string|in:tower,rabbit,slash',
        'paid' => 'boolean'
    ]);
    
    $user = $request->user();
    $gameId = $request->game_id;
    $paid = $request->paid ?? false;
    
    if ($gameId === 'tower') {
        $result = $user->useTowerGamePlay($paid);
        return response()->json($result);
    }
    
    // Rabbit and Slash games have unlimited plays once unlocked
    return response()->json([
        'success' => true,
        'message' => 'Game play started'
    ]);
}
```

### 5. Test the Integration
After implementing the above changes, test the integration to ensure the Slash game works correctly.

1. Test unlocking the Slash game
2. Test playing the Slash game
3. Test score submission
4. Test game over handling

## Summary
This implementation plan focuses on integrating the existing Slash game into the Telegram web app without making any changes to the game itself. We will follow the same integration pattern used for the Tower and Rabbit games, with the Slash game following the Rabbit game pattern of unlimited plays once unlocked. All game scores will be saved into the existing column in the telegram_users table.
