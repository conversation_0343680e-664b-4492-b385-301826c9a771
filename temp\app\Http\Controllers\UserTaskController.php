<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Services\AchievementPointService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserTaskController extends Controller
{
    protected $achievementPointService;
    
    public function __construct(AchievementPointService $achievementPointService)
    {
        $this->achievementPointService = $achievementPointService;
    }
    
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = $request->user();

        $tasks = Task::with(['type'])
            ->get()
            ->map(function ($task) use ($user) {
                $userTask = $user->tasks()->where('task_id', $task->id)->first();
                return [
                    'id' => $task->id,
                    'title' => $task->title,
                    'description' => $task->description,
                    'url' => $task->url,
                    'reward' => $task->reward,
                    'type' => $task->type,
                    'is_submitted' => $userTask ? $userTask->pivot->is_submitted : false,
                    'is_rewarded' => $userTask ? $userTask->pivot->is_rewarded : false,
                    'submitted_at' => $userTask ? $userTask->pivot->submitted_at : null,
                ];
            });

        return response()->json($tasks);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Task $task)
    {
        $user = $request->user();

        $userTask = $user->tasks()->where('task_id', $task->id)->first();

        if ($userTask) {
            return response()->json(['success' => false, 'message' => 'Task already submitted.'], 400);
        }

        $user->tasks()->attach($task->id, ['is_submitted' => true, 'submitted_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => 'Task submitted successfully. Waiting for approval.',
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function claim(Request $request, Task $task)
    {
        $user = $request->user();

        $userTask = $user->tasks()->where('task_id', $task->id)->first();

        if (!$userTask) {
            return response()->json(['success' => false, 'message' => 'Task not found.'], 404);
        }

        if ($userTask->pivot->is_rewarded) {
            return response()->json(['success' => false, 'message' => 'Task already rewarded.'], 400);
        }

        $claimed = false;
        DB::transaction(function () use ($task, &$claimed, $user, $userTask) {
            $userTask->pivot->is_rewarded = true;
            $userTask->pivot->save();

            $user->increment('balance', $task->reward);
            
            // Award achievement points for completing a task
            $this->achievementPointService->awardPoints(
                $user->id,
                1, // Award 1 point for each task
                'task_complete',
                $task->id,
                "Completed task: {$task->title}"
            );

            $claimed = true;
        });

        if (!$claimed) {
            return response()->json(['success' => false, 'message' => 'Unable to claim reward.'], 400);
        }

        return response()->json([
            'success' => true,
            'message' => "You have successfully claimed {$task->reward} coins from {$task->title}.",
            'achievement_points_awarded' => 1
        ]);
    }
}
