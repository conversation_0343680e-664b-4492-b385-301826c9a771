# Prize Tree System - Frontend Integration

This document outlines how the Prize Tree system is integrated into the frontend of the application.

## 1. Navigation

The Prize Tree is accessible through a new "Prizes" tab in the main navigation:

```jsx
// AppBar.jsx
const links = [
  { name: "Explore", link: "/", icon: "explore" },
  { name: "Missions", link: "/missions", icon: "missions" },
  { name: "Friends", link: "/friends", icon: "friends" },
  { name: "Bounty", link: "/earn", icon: "bounty" },
  { name: "Wallet", link: "/wallet", icon: "wallet" },
  { name: "Game", link: "/games", icon: "game" },
  { name: "Prizes", link: "/prizes", icon: "skill" }, // Added Prize Tree link
];
```

## 2. Routes

Two new routes have been added to the router:

```jsx
// router.jsx
{
  path: "prizes",
  element: <PrizeTree />, // Prize Tree page
},
{
  path: "prizes/gallery",
  element: <PrizeGallery />, // User's prize gallery
}
```

## 3. Prize Tree Page

The Prize Tree page (`PrizeTree.jsx`) is the main interface for viewing and interacting with the prize trees. It includes:

- A tree selector for switching between different prize trees
- A canvas for displaying the tree structure with nodes and connections
- A detail panel for viewing and unlocking prizes

```jsx
// PrizeTree.jsx
export default function PrizeTree() {
  const user = useUserStore();
  const queryClient = useQueryClient();
  const [selectedTree, setSelectedTree] = useState(null);
  const [selectedNode, setSelectedNode] = useState(null);
  
  // Fetch available prize trees
  const prizeTrees = useQuery({
    queryKey: ['prize-trees'],
    queryFn: () => $http.$get('/prizes/trees'),
  });
  
  // Fetch user's prizes and achievement points
  const userPrizes = useQuery({
    queryKey: ['user-prizes'],
    queryFn: () => $http.$get('/prizes/user'),
  });
  
  // Mutation for unlocking prizes
  const unlockPrize = useMutation({
    mutationFn: (prizeId) => $http.post('/prizes/unlock', { prize_id: prizeId }),
    onSuccess: (data) => {
      toast.success(data.message || 'Prize unlocked successfully!');
      queryClient.invalidateQueries({ queryKey: ['user-prizes'] });
      
      // Update user store if needed
      if (data.prize?.reward_type === 'balance' && data.prize?.reward_details?.amount) {
        useUserStore.setState((state) => ({
          ...state,
          balance: state.balance + data.prize.reward_details.amount
        }));
      }
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to unlock prize');
    }
  });
  
  // ... other code ...
}
```

### 3.1 Prize Tree Canvas

The `PrizeTreeCanvas` component renders the tree structure with nodes and connections:

```jsx
// PrizeTreeCanvas.jsx
export default function PrizeTreeCanvas({ tree, userPrizes, onNodeSelect }) {
  const canvasRef = useRef(null);
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  
  // Calculate node positions based on tier and position
  const calculateNodePositions = () => {
    // ... positioning logic ...
  };
  
  // Draw connections between nodes
  const drawConnections = (ctx, nodePositions) => {
    // ... connection drawing logic ...
  };
  
  // ... other code ...
  
  return (
    <div className="relative flex-1 mt-4 overflow-hidden border border-[#9B8B6C]/40 rounded-lg bg-[#120D0E]/80">
      {/* Canvas for connections */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full"
        onWheel={handleZoom}
        onMouseDown={handleDragStart}
        onMouseMove={handleDragMove}
        onMouseUp={handleDragEnd}
        onMouseLeave={handleDragEnd}
      />
      
      {/* Nodes */}
      <div 
        className="relative w-full h-full"
        style={{
          transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`
        }}
      >
        {tree.nodes.map(node => {
          // ... node rendering ...
        })}
      </div>
      
      {/* Zoom controls */}
      <div className="absolute bottom-4 right-4 flex space-x-2">
        {/* ... zoom buttons ... */}
      </div>
    </div>
  );
}
```

### 3.2 Prize Node Details

The `PrizeNodeDetails` component displays information about a selected prize and allows the user to unlock it:

```jsx
// PrizeNodeDetails.jsx
export default function PrizeNodeDetails({ node, isUnlocked, canUnlock, onUnlock, isLoading }) {
  // Render reward details based on reward type
  const renderRewardDetails = () => {
    // ... reward rendering logic ...
  };
  
  return (
    <motion.div 
      className="fixed bottom-24 left-1/2 transform -translate-x-1/2 w-[90%] max-w-md bg-[#120D0E] border border-[#9B8B6C]/40 rounded-lg shadow-[0_4px_15px_rgba(74,14,14,0.4)] p-4"
      initial={{ y: 50, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      exit={{ y: 50, opacity: 0 }}
    >
      {/* ... prize details ... */}
      
      {/* Cost and unlock button */}
      <div className="flex items-center justify-between mt-4 pt-4 border-t border-[#9B8B6C]/20">
        <div className="flex items-center">
          <BattlxIcon icon="skill" className="w-5 h-5 mr-1 text-[#9B8B6C]" />
          <span className="text-[#9B8B6C]">{node.cost} Points</span>
        </div>
        
        {isUnlocked ? (
          <div className="px-4 py-2 text-sm font-medium rounded-lg bg-[#9B8B6C]/20 text-[#9B8B6C]">
            Unlocked
          </div>
        ) : (
          <button
            className={`px-4 py-2 text-sm font-medium rounded-lg ${
              canUnlock 
                ? 'bg-[#9B8B6C] text-[#120D0E] hover:bg-[#9B8B6C]/90' 
                : 'bg-[#9B8B6C]/20 text-[#9B8B6C]/60 cursor-not-allowed'
            }`}
            onClick={onUnlock}
            disabled={!canUnlock || isLoading}
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-[#120D0E] border-t-transparent rounded-full animate-spin"></div>
            ) : (
              'Unlock'
            )}
          </button>
        )}
      </div>
    </motion.div>
  );
}
```

## 4. Prize Gallery

The Prize Gallery page (`PrizeGallery.jsx`) displays the user's unlocked prizes and allows them to equip cosmetic prizes:

```jsx
// PrizeGallery.jsx
export default function PrizeGallery() {
  return (
    <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1">
      <div className="flex flex-col flex-1 w-full h-full px-6 pb-20 mt-8 modal-body">
        <UserGameDetails className="mt-4" />
        
        <div className="flex items-center justify-between mt-6">
          <h1 className="text-xl font-bold text-[#9B8B6C]">My Prizes</h1>
          <a 
            href="/prizes" 
            className="px-3 py-1 text-sm rounded-lg bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40"
          >
            Prize Tree
          </a>
        </div>
        
        <div className="flex-1 mt-4 overflow-hidden border border-[#9B8B6C]/40 rounded-lg bg-[#120D0E]/80 p-4">
          <UserPrizeGallery />
        </div>
      </div>
    </div>
  );
}
```

### 4.1 User Prize Gallery

The `UserPrizeGallery` component displays the user's unlocked prizes with filtering options:

```jsx
// UserPrizeGallery.jsx
export default function UserPrizeGallery() {
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('all');
  
  // Fetch user's prizes
  const { data, isLoading, isError } = useQuery({
    queryKey: ['user-prizes'],
    queryFn: () => $http.$get('/prizes/user'),
  });
  
  // Mutation for equipping prizes
  const equipPrize = useMutation({
    mutationFn: (prizeId) => $http.post('/prizes/equip', { prize_id: prizeId }),
    onSuccess: () => {
      toast.success('Prize equipped successfully!');
      queryClient.invalidateQueries({ queryKey: ['user-prizes'] });
    },
    onError: (error) => {
      toast.error(error.response?.data?.message || 'Failed to equip prize');
    }
  });
  
  // ... other code ...
  
  return (
    <div className="flex flex-col h-full">
      {/* Achievement Points Display */}
      <div className="flex items-center justify-center mb-4 space-x-2">
        <BattlxIcon icon="skill" className="w-5 h-5 text-[#9B8B6C]" />
        <span className="text-lg font-bold text-[#9B8B6C]">
          {data?.achievement_points || 0} Achievement Points
        </span>
      </div>
      
      {/* Filter Tabs */}
      <div className="flex items-center justify-center mb-4 space-x-2 overflow-x-auto pb-2">
        {/* ... filter tabs ... */}
      </div>
      
      {/* Prize Grid */}
      <div className="grid grid-cols-2 gap-4 overflow-y-auto pb-4 sm:grid-cols-3">
        {filteredPrizes.map(prize => (
          <div 
            key={prize.prize_id}
            className={`flex flex-col items-center p-3 rounded-lg ${
              prize.is_equipped
                ? 'bg-[#9B8B6C]/20 border-2 border-[#9B8B6C]'
                : 'bg-[#120D0E] border border-[#9B8B6C]/40'
            }`}
          >
            {/* ... prize display ... */}
            
            {/* Equip/Unequip button (only for equippable prizes) */}
            {['cosmetic', 'title', 'emote'].includes(prize.prize.reward_type) && (
              <button
                className={`w-full mt-auto px-2 py-1 text-xs font-medium rounded-lg ${
                  prize.is_equipped
                    ? 'bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40'
                    : 'bg-[#9B8B6C] text-[#120D0E]'
                }`}
                onClick={() => handleToggleEquip(prize)}
                disabled={equipPrize.isPending || unequipPrize.isPending}
              >
                {equipPrize.isPending || unequipPrize.isPending ? (
                  <div className="w-3 h-3 mx-auto border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  prize.is_equipped ? 'Unequip' : 'Equip'
                )}
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
```

## 5. Achievement Points Display

Achievement points are displayed in various parts of the UI:

1. In the Prize Tree page header
2. In the Prize Gallery page header
3. In the user profile section

The points are fetched from the API and stored in the user store:

```javascript
// user-store.js
export const useUserStore = create((set, get) => ({
  // ... existing state ...
  
  // Achievement points
  achievement_points: 0,
  
  // ... existing methods ...
  
  /**
   * Update achievement points
   */
  updateAchievementPoints: (points) => {
    set({
      achievement_points: points
    });
  },
}));
```

## 6. API Integration

The frontend interacts with the backend through several API endpoints:

```javascript
// Prize Tree endpoints
$http.$get('/prizes/trees') // Get all prize trees
$http.$get(`/prizes/trees/${treeId}`) // Get a specific prize tree with its prizes

// User Prize endpoints
$http.$get('/prizes/user') // Get user's prizes and achievement points
$http.post('/prizes/unlock', { prize_id: prizeId }) // Unlock a prize
$http.post('/prizes/equip', { prize_id: prizeId }) // Equip a prize
$http.post('/prizes/unequip', { prize_id: prizeId }) // Unequip a prize

// Achievement Point endpoints
$http.$get('/prizes/transactions') // Get achievement point transactions
```

## 7. Styling

The Prize Tree system follows the gothic theme with minimal decorative elements:

- Dark backgrounds (`#120D0E`)
- Gold/bronze accents (`#9B8B6C`)
- Subtle diagonal patterns
- Minimalist UI elements

The tree visualization uses a canvas for drawing connections between nodes, with interactive elements for zooming and panning.

## 8. Responsive Design

The Prize Tree system is designed to work well on mobile devices:

- The tree canvas supports touch interactions for panning and zooming
- The prize gallery uses a responsive grid layout
- UI elements are sized appropriately for touch targets
- Modals and panels are positioned to avoid being obscured by the navigation bar
