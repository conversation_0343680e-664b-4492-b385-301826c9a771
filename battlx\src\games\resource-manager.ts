/**
 * Resource types that can be managed by the ResourceManager
 */
type ManagedResource = {
  destroy?: () => void;
  dispose?: () => void;
  pause?: () => void;
  stop?: () => void;
};

/**
 * Manages game resources to prevent memory leaks and ensure proper cleanup
 */
class ResourceManager {
  private static instance: ResourceManager;
  private resources: Map<string, Set<ManagedResource>>;
  private audioResources: Map<string, Set<HTMLAudioElement>>;
  private timers: Map<string, Set<number>>;

  private constructor() {
    this.resources = new Map();
    this.audioResources = new Map();
    this.timers = new Map();
  }

  static getInstance(): ResourceManager {
    if (!ResourceManager.instance) {
      ResourceManager.instance = new ResourceManager();
    }
    return ResourceManager.instance;
  }

  /**
   * Track a general resource for a game instance
   */
  trackResource(instanceId: string, resource: ManagedResource): void {
    if (!this.resources.has(instanceId)) {
      this.resources.set(instanceId, new Set());
    }
    this.resources.get(instanceId)!.add(resource);
  }

  /**
   * Track an audio resource for a game instance
   */
  trackAudio(instanceId: string, audio: HTMLAudioElement): void {
    if (!this.audioResources.has(instanceId)) {
      this.audioResources.set(instanceId, new Set());
    }
    this.audioResources.get(instanceId)!.add(audio);
  }

  /**
   * Track a timer for a game instance
   */
  trackTimer(instanceId: string, timerId: number): void {
    if (!this.timers.has(instanceId)) {
      this.timers.set(instanceId, new Set());
    }
    this.timers.get(instanceId)!.add(timerId);
  }

  /**
   * Release all resources for a game instance
   */
  releaseResources(instanceId: string): void {
    // Clean up general resources
    const resources = this.resources.get(instanceId);
    if (resources) {
      resources.forEach(resource => {
        try {
          if (resource.destroy) resource.destroy();
          if (resource.dispose) resource.dispose();
        } catch (error) {
          console.error('Error disposing resource:', error);
        }
      });
      resources.clear();
      this.resources.delete(instanceId);
    }

    // Clean up audio resources
    const audioResources = this.audioResources.get(instanceId);
    if (audioResources) {
      audioResources.forEach(audio => {
        try {
          audio.pause();
          audio.currentTime = 0;
          audio.src = '';
          audio.remove();
        } catch (error) {
          console.error('Error disposing audio resource:', error);
        }
      });
      audioResources.clear();
      this.audioResources.delete(instanceId);
    }

    // Clear timers
    const timers = this.timers.get(instanceId);
    if (timers) {
      timers.forEach(timerId => {
        try {
          clearTimeout(timerId);
          clearInterval(timerId);
        } catch (error) {
          console.error('Error clearing timer:', error);
        }
      });
      timers.clear();
      this.timers.delete(instanceId);
    }
  }

  /**
   * Pause all audio for a game instance
   */
  pauseAllAudio(instanceId: string): void {
    const audioResources = this.audioResources.get(instanceId);
    if (audioResources) {
      audioResources.forEach(audio => {
        try {
          audio.pause();
        } catch (error) {
          console.error('Error pausing audio:', error);
        }
      });
    }
  }

  /**
   * Remove a specific resource from tracking
   */
  untrackResource(instanceId: string, resource: ManagedResource): void {
    const resources = this.resources.get(instanceId);
    if (resources) {
      resources.delete(resource);
    }
  }

  /**
   * Check if an instance has any active resources
   */
  hasActiveResources(instanceId: string): boolean {
    return (
      (this.resources.get(instanceId)?.size ?? 0) > 0 ||
      (this.audioResources.get(instanceId)?.size ?? 0) > 0 ||
      (this.timers.get(instanceId)?.size ?? 0) > 0
    );
  }

  /**
   * Get resource counts for debugging
   */
  getResourceCounts(instanceId: string) {
    return {
      resources: this.resources.get(instanceId)?.size ?? 0,
      audio: this.audioResources.get(instanceId)?.size ?? 0,
      timers: this.timers.get(instanceId)?.size ?? 0
    };
  }
}

export const resourceManager = ResourceManager.getInstance();