# Deployment Documentation

## Overview
This document covers the complete deployment process for the Pet System, including environment setup, database migrations, configuration, and production deployment strategies.

## Implementation Time: 2-3 days
## Complexity: Medium-High
## Dependencies: Server infrastructure, database setup, CDN configuration

## Environment Setup

### Production Environment Requirements
```yaml
# File: deployment/docker-compose.prod.yml

version: '3.8'

services:
  # Laravel API
  api:
    build:
      context: ./api
      dockerfile: Dockerfile.prod
    container_name: battlx_api_prod
    restart: unless-stopped
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - APP_KEY=${APP_KEY}
      - DB_HOST=database
      - DB_DATABASE=${DB_DATABASE}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - QUEUE_CONNECTION=redis
    volumes:
      - ./api/storage:/var/www/html/storage
      - ./api/bootstrap/cache:/var/www/html/bootstrap/cache
    depends_on:
      - database
      - redis
    networks:
      - battlx_network

  # React Frontend
  frontend:
    build:
      context: ./battlx
      dockerfile: Dockerfile.prod
      args:
        - REACT_APP_API_URL=${REACT_APP_API_URL}
        - REACT_APP_CDN_URL=${REACT_APP_CDN_URL}
    container_name: battlx_frontend_prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    networks:
      - battlx_network

  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: battlx_db_prod
    restart: unless-stopped
    environment:
      - POSTGRES_DB=${DB_DATABASE}
      - POSTGRES_USER=${DB_USERNAME}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - battlx_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: battlx_redis_prod
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - battlx_network

  # Queue Worker
  queue_worker:
    build:
      context: ./api
      dockerfile: Dockerfile.prod
    container_name: battlx_queue_prod
    restart: unless-stopped
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600
    environment:
      - APP_ENV=production
      - DB_HOST=database
      - REDIS_HOST=redis
    volumes:
      - ./api/storage:/var/www/html/storage
    depends_on:
      - database
      - redis
    networks:
      - battlx_network

  # Scheduler
  scheduler:
    build:
      context: ./api
      dockerfile: Dockerfile.prod
    container_name: battlx_scheduler_prod
    restart: unless-stopped
    command: crond -f
    environment:
      - APP_ENV=production
    volumes:
      - ./api/storage:/var/www/html/storage
      - ./deployment/crontab:/etc/crontabs/root
    depends_on:
      - database
      - redis
    networks:
      - battlx_network

volumes:
  postgres_data:
  redis_data:

networks:
  battlx_network:
    driver: bridge
```

### Environment Configuration
```bash
# File: deployment/.env.production

# Application
APP_NAME="BattlX Pet System"
APP_ENV=production
APP_KEY=base64:your-generated-app-key-here
APP_DEBUG=false
APP_URL=https://run.gamebot.com

# Database
DB_CONNECTION=pgsql
DB_HOST=database
DB_PORT=5432
DB_DATABASE=battlx_production
DB_USERNAME=battlx_user
DB_PASSWORD=your-secure-password-here

# Redis
REDIS_HOST=redis
REDIS_PASSWORD=your-redis-password-here
REDIS_PORT=6379

# Queue
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database

# Cache
CACHE_DRIVER=redis
SESSION_DRIVER=redis

# Mail (for notifications)
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls

# Telegram Bot
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_WEBHOOK_URL=https://run.gamebot.com/api/telegram/webhook

# CDN and Assets
CDN_URL=https://cdn.gamebot.com
ASSET_URL=https://cdn.gamebot.com/assets

# Pet System Configuration
PET_SYSTEM_ENABLED=true
PET_HAPPINESS_DECAY_ENABLED=true
PET_INTERACTION_COOLDOWNS=true
MYSTERY_BOX_RATE_LIMITING=true

# Performance
CACHE_TTL_PETS=300
CACHE_TTL_TEMPLATES=3600
CACHE_TTL_LEADERBOARD=900

# Security
SANCTUM_STATEFUL_DOMAINS=run.gamebot.com
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=strict

# Monitoring
LOG_CHANNEL=stack
LOG_LEVEL=info
SENTRY_LARAVEL_DSN=your-sentry-dsn-here

# React App Environment
REACT_APP_API_URL=https://run.gamebot.com/api
REACT_APP_CDN_URL=https://cdn.gamebot.com
REACT_APP_ENVIRONMENT=production
REACT_APP_SENTRY_DSN=your-frontend-sentry-dsn
```

## Database Migration Strategy

### Migration Deployment Script
```bash
#!/bin/bash
# File: deployment/migrate.sh

set -e

echo "🚀 Starting Pet System Database Migration..."

# Backup current database
echo "📦 Creating database backup..."
docker exec battlx_db_prod pg_dump -U $DB_USERNAME $DB_DATABASE > "backup_$(date +%Y%m%d_%H%M%S).sql"

# Run migrations
echo "🔄 Running database migrations..."
docker exec battlx_api_prod php artisan migrate --force

# Seed pet system data
echo "🌱 Seeding pet system data..."
docker exec battlx_api_prod php artisan db:seed --class=PetSystemSeeder --force

# Update search indexes
echo "🔍 Updating search indexes..."
docker exec battlx_api_prod php artisan scout:import "App\Models\Pet"
docker exec battlx_api_prod php artisan scout:import "App\Models\CollectibleTemplate"

# Clear and warm up caches
echo "🔥 Clearing caches..."
docker exec battlx_api_prod php artisan cache:clear
docker exec battlx_api_prod php artisan config:cache
docker exec battlx_api_prod php artisan route:cache
docker exec battlx_api_prod php artisan view:cache

echo "♻️ Warming up caches..."
docker exec battlx_api_prod php artisan cache:warm-up

# Restart queue workers
echo "🔄 Restarting queue workers..."
docker restart battlx_queue_prod

echo "✅ Pet System deployment completed successfully!"
```

### Database Seeder for Production
```php
<?php
// File: api/database/seeders/PetSystemProductionSeeder.php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PetTemplate;
use App\Models\MysteryBoxType;
use App\Models\CollectibleTemplate;
use App\Models\CollectionSet;

class PetSystemProductionSeeder extends Seeder
{
    public function run(): void
    {
        $this->seedPetTemplates();
        $this->seedCollectionSets();
        $this->seedCollectibleTemplates();
        $this->seedMysteryBoxTypes();
    }

    private function seedPetTemplates(): void
    {
        $templates = [
            [
                'name' => 'Shadow Wolf',
                'category' => 'shadow',
                'rarity' => 'common',
                'description' => 'A mysterious wolf that lurks in the shadows.',
                'image_url' => '/assets/pets/shadow-wolf.png',
                'coin_cost' => 1000,
                'gem_cost' => 0,
                'base_happiness' => 50,
                'max_happiness' => 100,
                'happiness_decay_rate' => 2,
                'max_level' => 50,
                'evolution_levels' => [1, 10, 25, 50],
                'evolution_images' => [
                    '/assets/pets/shadow-wolf-1.png',
                    '/assets/pets/shadow-wolf-2.png',
                    '/assets/pets/shadow-wolf-3.png',
                    '/assets/pets/shadow-wolf-4.png'
                ],
                'mystery_box_unlocks' => ['shadow_basic'],
                'collectible_reward_id' => 'shadow_essence',
                'sort_order' => 1,
                'is_active' => true
            ],
            [
                'name' => 'Bone Dragon',
                'category' => 'undead',
                'rarity' => 'legendary',
                'description' => 'An ancient dragon risen from the dead.',
                'image_url' => '/assets/pets/bone-dragon.png',
                'coin_cost' => 50000,
                'gem_cost' => 100,
                'prize_tree_unlock_level' => 25,
                'is_premium_only' => true,
                'base_happiness' => 80,
                'max_happiness' => 150,
                'happiness_decay_rate' => 1,
                'max_level' => 100,
                'evolution_levels' => [1, 20, 50, 100],
                'evolution_images' => [
                    '/assets/pets/bone-dragon-1.png',
                    '/assets/pets/bone-dragon-2.png',
                    '/assets/pets/bone-dragon-3.png',
                    '/assets/pets/bone-dragon-4.png'
                ],
                'mystery_box_unlocks' => ['undead_rare', 'legendary_box'],
                'collectible_reward_id' => 'dragon_scale',
                'sort_order' => 10,
                'is_active' => true
            ]
            // Add more templates...
        ];

        foreach ($templates as $template) {
            PetTemplate::updateOrCreate(
                ['name' => $template['name']],
                $template
            );
        }
    }

    private function seedCollectionSets(): void
    {
        $sets = [
            [
                'set_id' => 'shadow_collection',
                'name' => 'Shadow Realm',
                'category' => 'shadow',
                'description' => 'Collectibles from the mysterious shadow realm.',
                'icon_url' => '/assets/collections/shadow-icon.png',
                'completion_rewards' => [
                    ['type' => 'coins', 'amount' => 10000],
                    ['type' => 'gems', 'amount' => 50],
                    ['type' => 'achievement_points', 'amount' => 500]
                ],
                'sort_order' => 1,
                'is_active' => true
            ]
            // Add more sets...
        ];

        foreach ($sets as $set) {
            CollectionSet::updateOrCreate(
                ['set_id' => $set['set_id']],
                $set
            );
        }
    }

    private function seedCollectibleTemplates(): void
    {
        $collectibles = [
            [
                'collectible_id' => 'shadow_essence',
                'name' => 'Shadow Essence',
                'type' => 'essence',
                'rarity' => 'common',
                'category' => 'shadow',
                'description' => 'A swirling mass of dark energy.',
                'image_url' => '/assets/collectibles/shadow-essence.png',
                'collection_set_id' => 'shadow_collection',
                'set_position' => 1,
                'unlock_source' => 'pet_reward',
                'is_active' => true
            ]
            // Add more collectibles...
        ];

        foreach ($collectibles as $collectible) {
            CollectibleTemplate::updateOrCreate(
                ['collectible_id' => $collectible['collectible_id']],
                $collectible
            );
        }
    }

    private function seedMysteryBoxTypes(): void
    {
        $boxTypes = [
            [
                'box_type' => 'shadow_basic',
                'display_name' => 'Shadow Mystery Box',
                'rarity' => 'common',
                'category' => 'shadow',
                'description' => 'Contains shadow realm collectibles.',
                'image_url' => '/assets/boxes/shadow-box.png',
                'coin_cost' => 5000,
                'gem_cost' => 10,
                'achievement_points_cost' => 100,
                'possible_rewards' => ['shadow_essence', 'shadow_crystal'],
                'reward_weights' => [70, 30],
                'guaranteed_rarity_level' => 1,
                'is_purchasable' => true,
                'sort_order' => 1,
                'is_active' => true
            ]
            // Add more box types...
        ];

        foreach ($boxTypes as $boxType) {
            MysteryBoxType::updateOrCreate(
                ['box_type' => $boxType['box_type']],
                $boxType
            );
        }
    }
}
```

## Frontend Build and Deployment

### Production Dockerfile
```dockerfile
# File: battlx/Dockerfile.prod

# Build stage
FROM node:18-alpine as build

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build arguments
ARG REACT_APP_API_URL
ARG REACT_APP_CDN_URL
ENV REACT_APP_API_URL=$REACT_APP_API_URL
ENV REACT_APP_CDN_URL=$REACT_APP_CDN_URL

# Build the app
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built app
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf
COPY nginx-default.conf /etc/nginx/conf.d/default.conf

# Copy SSL certificates
COPY ssl/ /etc/nginx/ssl/

# Expose ports
EXPOSE 80 443

CMD ["nginx", "-g", "daemon off;"]
```

### Nginx Configuration
```nginx
# File: battlx/nginx-default.conf

server {
    listen 80;
    server_name run.gamebot.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name run.gamebot.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    root /usr/share/nginx/html;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API proxy
    location /api/ {
        proxy_pass http://api:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # React app
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## Deployment Scripts

### Main Deployment Script
```bash
#!/bin/bash
# File: deployment/deploy.sh

set -e

# Configuration
ENVIRONMENT=${1:-production}
BACKUP_ENABLED=${2:-true}
SKIP_TESTS=${3:-false}

echo "🚀 Starting BattlX Pet System Deployment"
echo "Environment: $ENVIRONMENT"
echo "Backup enabled: $BACKUP_ENABLED"

# Load environment variables
source .env.$ENVIRONMENT

# Pre-deployment checks
echo "🔍 Running pre-deployment checks..."
./deployment/pre-deploy-checks.sh

# Run tests (unless skipped)
if [ "$SKIP_TESTS" != "true" ]; then
    echo "🧪 Running tests..."
    ./deployment/run-tests.sh
fi

# Create backup
if [ "$BACKUP_ENABLED" = "true" ]; then
    echo "📦 Creating backup..."
    ./deployment/backup.sh
fi

# Build and deploy
echo "🏗️ Building application..."
docker-compose -f docker-compose.$ENVIRONMENT.yml build

echo "🚀 Deploying application..."
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d

# Run migrations
echo "🔄 Running database migrations..."
./deployment/migrate.sh

# Health checks
echo "🏥 Running health checks..."
./deployment/health-check.sh

# Post-deployment tasks
echo "🔧 Running post-deployment tasks..."
./deployment/post-deploy.sh

echo "✅ Deployment completed successfully!"
echo "🌐 Application is available at: $APP_URL"
```

### Health Check Script
```bash
#!/bin/bash
# File: deployment/health-check.sh

set -e

echo "🏥 Running health checks..."

# Check API health
echo "Checking API health..."
API_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" $APP_URL/api/health)
if [ "$API_HEALTH" != "200" ]; then
    echo "❌ API health check failed (HTTP $API_HEALTH)"
    exit 1
fi
echo "✅ API is healthy"

# Check database connection
echo "Checking database connection..."
DB_CHECK=$(docker exec battlx_api_prod php artisan tinker --execute="DB::connection()->getPdo(); echo 'OK';" 2>/dev/null | tail -1)
if [ "$DB_CHECK" != "OK" ]; then
    echo "❌ Database connection failed"
    exit 1
fi
echo "✅ Database connection is healthy"

# Check Redis connection
echo "Checking Redis connection..."
REDIS_CHECK=$(docker exec battlx_redis_prod redis-cli ping)
if [ "$REDIS_CHECK" != "PONG" ]; then
    echo "❌ Redis connection failed"
    exit 1
fi
echo "✅ Redis connection is healthy"

# Check queue workers
echo "Checking queue workers..."
QUEUE_CHECK=$(docker exec battlx_api_prod php artisan queue:monitor --once 2>/dev/null | grep -c "Processing")
if [ "$QUEUE_CHECK" -eq "0" ]; then
    echo "⚠️ No queue workers are processing jobs"
else
    echo "✅ Queue workers are active"
fi

# Check frontend
echo "Checking frontend..."
FRONTEND_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" $APP_URL)
if [ "$FRONTEND_HEALTH" != "200" ]; then
    echo "❌ Frontend health check failed (HTTP $FRONTEND_HEALTH)"
    exit 1
fi
echo "✅ Frontend is healthy"

echo "🎉 All health checks passed!"
```

## Monitoring and Logging

### Application Monitoring
```php
<?php
// File: api/app/Http/Middleware/PerformanceMonitoring.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PerformanceMonitoring
{
    public function handle(Request $request, Closure $next)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();

        $response = $next($request);

        $endTime = microtime(true);
        $endMemory = memory_get_usage();

        $executionTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
        $memoryUsage = $endMemory - $startMemory;

        // Log slow requests
        if ($executionTime > 1000) { // Slower than 1 second
            Log::warning('Slow request detected', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'execution_time_ms' => $executionTime,
                'memory_usage_bytes' => $memoryUsage,
                'user_id' => $request->user()?->id
            ]);
        }

        // Add performance headers
        $response->headers->set('X-Execution-Time', round($executionTime, 2) . 'ms');
        $response->headers->set('X-Memory-Usage', round($memoryUsage / 1024, 2) . 'KB');

        return $response;
    }
}
```

## Acceptance Criteria
- [ ] Production environment properly configured
- [ ] Database migrations run successfully
- [ ] Frontend builds and deploys correctly
- [ ] SSL certificates configured
- [ ] Health checks pass
- [ ] Monitoring and logging operational
- [ ] Backup strategy implemented
- [ ] Rollback procedures documented

## Next Steps
1. Set up monitoring and analytics
2. Create user documentation
3. Implement A/B testing framework
4. Plan feature rollout strategy

## Troubleshooting
- Check Docker container logs for errors
- Verify environment variables are set correctly
- Ensure database permissions are configured
- Test SSL certificate validity
- Monitor application performance metrics
- Verify CDN configuration for static assets
