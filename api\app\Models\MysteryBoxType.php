<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MysteryBoxType extends Model
{
    use HasFactory;

    protected $fillable = [
        'box_type', 'display_name', 'rarity', 'category', 'description',
        'image_url', 'animation_url', 'coin_cost', 'gem_cost',
        'achievement_points_cost', 'possible_rewards', 'reward_weights',
        'guaranteed_rarity_level', 'unlock_requirements', 'is_purchasable',
        'is_active', 'sort_order'
    ];

    protected $casts = [
        'possible_rewards' => 'array',
        'reward_weights' => 'array',
        'unlock_requirements' => 'array',
        'is_purchasable' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function unlocks(): HasMany
    {
        return $this->hasMany(MysteryBoxUnlock::class, 'box_type', 'box_type');
    }

    public function openings(): HasMany
    {
        return $this->hasMany(MysteryBoxOpening::class, 'box_type', 'box_type');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePurchasable($query)
    {
        return $query->where('is_purchasable', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByRarity($query, $rarity)
    {
        return $query->where('rarity', $rarity);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    // Accessors
    public function getImageUrlAttribute($value)
    {
        return $value ? asset($value) : null;
    }

    public function getAnimationUrlAttribute($value)
    {
        return $value ? asset($value) : null;
    }

    public function getRarityColorAttribute()
    {
        return match($this->rarity) {
            'common' => '#9CA3AF',
            'rare' => '#3B82F6',
            'epic' => '#8B5CF6',
            'legendary' => '#F59E0B',
            'mythic' => '#EF4444',
            default => '#6B7280'
        };
    }

    // Business Logic Methods
    public function isUnlockedBy(TelegramUser $user): bool
    {
        return $user->mysteryBoxUnlocks()
            ->where('box_type', $this->box_type)
            ->exists();
    }

    public function canBePurchasedBy(TelegramUser $user, string $currency): bool
    {
        if (!$this->is_active || !$this->is_purchasable) {
            return false;
        }

        if (!$this->isUnlockedBy($user)) {
            return false;
        }

        $cost = $this->getCost($currency);
        if ($cost <= 0) {
            return false;
        }

        return match($currency) {
            'coins' => $user->balance >= $cost,
            'gems' => $user->gems >= $cost,
            'achievement_points' => $user->achievement_points >= $cost,
            default => false
        };
    }

    public function getCost(string $currency): int
    {
        return match($currency) {
            'coins' => $this->coin_cost,
            'gems' => $this->gem_cost,
            'achievement_points' => $this->achievement_points_cost,
            default => 0
        };
    }

    public function generateRewards(): array
    {
        $possibleRewards = $this->possible_rewards ?? [];
        $weights = $this->reward_weights ?? [];

        if (empty($possibleRewards) || empty($weights)) {
            return [];
        }

        // Ensure we have the same number of rewards and weights
        $rewardCount = min(count($possibleRewards), count($weights));
        $rewards = [];

        // Generate 1-3 rewards based on rarity
        $numRewards = match($this->rarity) {
            'common' => 1,
            'rare' => rand(1, 2),
            'epic' => rand(2, 3),
            'legendary' => rand(2, 3),
            'mythic' => 3,
            default => 1
        };

        for ($i = 0; $i < $numRewards; $i++) {
            $selectedReward = $this->selectWeightedReward($possibleRewards, $weights);
            if ($selectedReward && !in_array($selectedReward, $rewards)) {
                $rewards[] = $selectedReward;
            }
        }

        return $rewards;
    }

    private function selectWeightedReward(array $rewards, array $weights): ?string
    {
        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);
        
        $currentWeight = 0;
        foreach ($rewards as $index => $reward) {
            $currentWeight += $weights[$index] ?? 0;
            if ($random <= $currentWeight) {
                return $reward;
            }
        }

        return $rewards[0] ?? null;
    }

    public function open(TelegramUser $user, string $currency): array
    {
        if (!$this->canBePurchasedBy($user, $currency)) {
            throw new \Exception('Cannot purchase this mystery box');
        }

        $cost = $this->getCost($currency);
        
        // Deduct cost
        match($currency) {
            'coins' => $user->decrement('balance', $cost),
            'gems' => $user->decrement('gems', $cost),
            'achievement_points' => $user->decrement('achievement_points', $cost),
        };

        // Generate rewards
        $rewards = $this->generateRewards();
        $totalValue = 0;
        $containsRare = false;

        // Award collectibles to user
        foreach ($rewards as $collectibleId) {
            $collectibleTemplate = CollectibleTemplate::where('collectible_id', $collectibleId)->first();
            if ($collectibleTemplate) {
                // Check if user already owns this collectible
                if (!$collectibleTemplate->isOwnedBy($user)) {
                    $user->collectibles()->create([
                        'collectible_id' => $collectibleId,
                        'unlock_source' => 'mystery_box',
                        'source_reference' => $this->box_type,
                        'obtained_at' => now()
                    ]);
                }
                
                $totalValue += $collectibleTemplate->estimated_value;
                if (in_array($collectibleTemplate->rarity, ['epic', 'legendary', 'mythic'])) {
                    $containsRare = true;
                }
            }
        }

        // Log the opening
        $opening = $this->openings()->create([
            'telegram_user_id' => $user->id,
            'purchase_method' => $currency,
            'cost_paid' => $cost,
            'currency_used' => $currency,
            'rewards_received' => $rewards,
            'total_value' => $totalValue,
            'contained_rare_item' => $containsRare,
            'opened_at' => now()
        ]);

        return [
            'opening_id' => $opening->id,
            'rewards' => $rewards,
            'total_value' => $totalValue,
            'contained_rare' => $containsRare,
            'cost_paid' => $cost,
            'currency_used' => $currency
        ];
    }

    public function meetsUnlockRequirements(TelegramUser $user): bool
    {
        $requirements = $this->unlock_requirements ?? [];
        
        if (empty($requirements)) {
            return true; // No requirements means always unlocked
        }

        foreach ($requirements as $requirement) {
            // Check if user owns the required pet
            $petTemplate = PetTemplate::where('name', $requirement)->first();
            if ($petTemplate && $user->pets()->where('pet_template_id', $petTemplate->id)->exists()) {
                return true;
            }
        }

        return false;
    }
}
