<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class UserPrize extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id',
        'prize_id',
        'unlocked_at',
        'is_equipped'
    ];

    protected $casts = [
        'unlocked_at' => 'datetime',
        'is_equipped' => 'boolean'
    ];

    /**
     * Get the user that owns this prize.
     */
    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    /**
     * Get the prize that this user prize represents.
     */
    public function prize()
    {
        return $this->belongsTo(Prize::class);
    }

    /**
     * Equip this prize for the user.
     */
    public function equip()
    {
        DB::transaction(function () {
            // Get the prize to determine its type
            $prize = $this->prize;
            
            // Unequip any other prizes of the same type
            UserPrize::where('telegram_user_id', $this->telegram_user_id)
                ->whereHas('prize', function ($query) use ($prize) {
                    $query->where('reward_type', $prize->reward_type);
                })
                ->update(['is_equipped' => false]);
            
            // Equip this prize
            $this->is_equipped = true;
            $this->save();
        });
        
        return $this;
    }

    /**
     * Unequip this prize for the user.
     */
    public function unequip()
    {
        $this->is_equipped = false;
        $this->save();
        
        return $this;
    }
}
