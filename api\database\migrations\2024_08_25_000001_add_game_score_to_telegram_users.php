<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Add the 'game_score' column
        Schema::table('telegram_users', function (Blueprint $table) {
            $table->bigInteger('game_score')->default(0);
        });

        // Add CHECK constraint using raw SQL
        DB::statement('ALTER TABLE telegram_users ADD CONSTRAINT chk_game_score_non_negative CHECK (game_score >= 0)');
    }

    public function down()
    {
        // Drop the CHECK constraint
        DB::statement('ALTER TABLE telegram_users DROP CONSTRAINT IF EXISTS chk_game_score_non_negative');

        // Drop the 'game_score' column
        Schema::table('telegram_users', function (Blueprint $table) {
            $table->dropColumn('game_score');
        });
    }
};