import { RouterProvider } from "react-router-dom";
import router from "./router";
import { useEffect, useState } from "react";
import SplashScreen from "./components/partials/SplashScreen";
import FirstTimeScreen from "./components/partials/FirstTimeScreen";
import { $http, setBearerToken } from "./lib/http";
import { BoosterType, BoosterTypes, UserType } from "./types/UserType";
import { UserPrizesResponse } from "./types/PrizeTypes";
import { useUserStore } from "./store/user-store";
import { uesStore } from "./store";
import { usePrizeFeatureStore } from "./store/prize-feature-store";
import PlayOnYourMobile from "./pages/PlayOnYourMobile";
import { useDebounce } from "@uidotdev/usehooks";
import { toast } from "react-toastify";
import useTelegramInitData from "./hooks/useTelegramInitData";

const webApp = window.Telegram.WebApp;
const isDisktop = import.meta.env.DEV
  ? false
  : Telegram.WebApp.platform === "tdesktop";

function App() {
  const userStore = useUserStore();
  const { levels, levelUp } = uesStore();
  const { user, start_param } = useTelegramInitData();
  const [authLoaded, setAuthLoaded] = useState(false);
  const [showSplashScreen, setShowSplashScreen] = useState(true);
  const [isFirstLoad, setIsFirstLoad] = useState(false);
  const balance = useDebounce(userStore.balance, 500);

  useEffect(() => {
    webApp.setHeaderColor("#000");
    webApp.setBackgroundColor("#000");
    webApp.expand();

    // Check if the app supports fullscreen mode (Bot API 8.0+)
    if (webApp.isVersionAtLeast('8.0')) {
      // Request fullscreen mode
      // Use type assertion to bypass TypeScript errors for newer API methods
      (webApp as any).requestFullscreen();

      // Listen for viewport changes (compatible with older type definitions)
      webApp.onEvent('viewportChanged', () => {
        console.log('Viewport changed, height:', webApp.viewportHeight);
      });
    }
  }, []);

  useEffect(() => {
    const interval = setInterval(() => {
      useUserStore.setState((state) => {
        state.balance += state.production_per_hour / 3600;
        return state;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [userStore.production_per_hour]);

  useEffect(() => {
    if (!balance || !userStore.level?.level) return;
    const userLevel = userStore.level.level;
    const newLevels = levels.filter(
      (level) => balance >= level.from_balance && level.level > userLevel
    );
    const maxLevel = newLevels.reduce(
      (prev, current) => (prev.level > current.level ? prev : current),
      newLevels[0]
    );
    if (
      userStore.level?.level &&
      maxLevel?.level &&
      maxLevel.level > userStore.level.level
    ) {
      useUserStore.setState((state) => {
        state.level = maxLevel;
        state.max_energy += newLevels.length * levelUp.max_energy;
        state.earn_per_tap += newLevels.length * levelUp.earn_per_tap;
        return state;
      });
      toast.success(`You have leveled up to level ${maxLevel.level}`);
    }
  }, [balance, levels]);

  useEffect(() => {
    if (!user) return () => {};

    const signIn = async () => {
      if (localStorage.getItem("token") === null) {
        const referredBy = start_param?.replace("ref", "");

        // Debug logging for referral tracking (remove in production)
        if (import.meta.env.DEV) {
          console.log("Referral Debug:", {
            start_param,
            referred_by: referredBy,
            user_id: user.id,
            raw_initData: window.Telegram.WebApp.initData
          });
        }

        const { data } = await $http.post<{
          token: string;
          first_login: boolean;
        }>("/auth/telegram-user", {
          telegram_id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          username: user.username,
          referred_by: referredBy,
        });
        setBearerToken(data.token);
        setIsFirstLoad(data.first_login);
      }

      const data = await $http.$get<
        {
          user: UserType;
          boosters: Record<BoosterTypes, BoosterType>;
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } & Record<string, any>
      >("/clicker/sync");

      // Update user state including achievement points
      useUserStore.setState({
        ...data.user,
        achievement_points: data.user.achievement_points || 0,
      });

      uesStore.setState({
        totalDailyRewards: data.total_daily_rewards,
        boosters: data.boosters,
        dailyResetEnergy: data.daily_booster,
        maxLevel: data.max_level,
        levels: data.levels,
        levelUp: data.level_up,
        referral: data.referral,
        missionTypes: data.mission_types,
        totalReferals: data.total_referals,
      });

      // Fetch user prizes to determine which features are unlocked
      try {
        const prizesResponse = await $http.$get<UserPrizesResponse>('/prizes/user');
        usePrizeFeatureStore.getState().updateFromUserPrizes(prizesResponse);
      } catch (error) {
        console.error('Failed to fetch user prizes:', error);
        // Default to all features disabled if we can't fetch prizes
      }
    };

    signIn().then(() => setAuthLoaded(true));
  }, [user]);

  if (!user || isDisktop) return <PlayOnYourMobile />;

  if (showSplashScreen) {
    return (
      <SplashScreen
        authLoaded={authLoaded}
        onLoadComplete={() => setShowSplashScreen(false)}
      />
    );
  }

  if (isFirstLoad)
    return <FirstTimeScreen startGame={() => setIsFirstLoad(false)} />;

  return <RouterProvider router={router} />;
}

export default App;
