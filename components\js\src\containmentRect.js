// ContainmentRect class for checking if objects are within a certain area
class ContainmentRect {
    constructor(scale = 1, width = 0, height = 0) {
        this.index = 1;
        this.width = width > 0 ? width : Game.core.canvas.width * scale;
        this.height = height > 0 ? height : Game.core.canvas.height * scale;
        this.x = 0;
        this.y = 0;
        this.checkFrequency = 30; // Only check every 30 frames
        this.frameCount = 0;
    }

    // Check if a point is inside the rectangle
    contains(x, y) {
        return (
            x >= this.x &&
            x <= this.x + this.width &&
            y >= this.y &&
            y <= this.y + this.height
        );
    }

    // Check if an object is outside the rectangle and despawn it if needed
    despawnIfOutside(objects) {
        // Only check periodically to avoid frequent despawns/respawns
        this.frameCount++;
        if (this.frameCount % this.checkFrequency !== 0) {
            return false;
        }

        // Get the next object to check
        const obj = objects[this.index % objects.length];
        this.index++;

        if (!obj) return false;
        if (!obj.isCullable && !obj.isTeleportOnCull) return false;

        // Don't despawn enemies unless they're defeated
        if (obj.enemyType && !obj.isDead) {
            return false;
        }

        // Calculate distance from player
        const dx = Math.abs(Game.core.player.x - obj.x);
        const dy = Math.abs(Game.core.player.y - obj.y);

        // Use a larger buffer for despawning (2x the containment rect)
        // This ensures objects are well outside the visible area
        const bufferWidth = this.width * 2;
        const bufferHeight = this.height * 2;

        // Check if object is far outside the containment area
        if (dx > bufferWidth / 2 || dy > bufferHeight / 2) {
            // For enemies, teleport them to a new position instead of despawning
            if (obj.enemyType && obj.isTeleportOnCull) {
                obj.onTeleportOnCull();
                return false;
            }

            // For pickups and bullets, despawn normally
            // But never despawn destructibles or enemies
            if (!obj.enemyType && !obj.destructibleType) {
                obj.despawn();
            }
        }

        return true;
    }

    // Check if an object is inside the rectangle
    containsObject(obj) {
        const dx = Math.abs(Game.core.player.x - obj.x);
        const dy = Math.abs(Game.core.player.y - obj.y);
        return this.contains(dx, dy);
    }

    // Update the rectangle position to be centered on the player
    update() {
        if (Game.core.player) {
            this.x = Game.core.player.x - this.width / 2;
            this.y = Game.core.player.y - this.height / 2;
        }
    }

    // Draw the rectangle for debugging
    draw(ctx, camera) {
        ctx.save();
        ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
        ctx.lineWidth = 2;
        ctx.strokeRect(
            this.x - camera.x + camera.width / 2,
            this.y - camera.y + camera.height / 2,
            this.width,
            this.height
        );
        ctx.restore();
    }
}

// Attach to window object for global access
window.ContainmentRect = ContainmentRect;
