<?php

use App\Models\TelegramUser;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        // Create the 'tasks' table
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('url')->nullable();
            $table->integer('reward'); // Ensure reward is non-negative via CHECK constraint
            $table->foreignId('type_id')->constrained('task_types'); // Foreign key to task_types table
            $table->timestamps(); // created_at and updated_at columns
        });

        // Add CHECK constraint for 'reward' column using raw SQL
        DB::statement('ALTER TABLE tasks ADD CONSTRAINT chk_reward_non_negative CHECK (reward >= 0)');

        // Create the 'telegram_user_tasks' table
        Schema::create('telegram_user_tasks', function (Blueprint $table) {
            $table->id(); // Auto-incrementing primary key
            $table->foreignIdFor(TelegramUser::class)->constrained()->onDelete('cascade'); // Foreign key to telegram_users table
            $table->foreignId('task_id')->constrained()->onDelete('cascade');
            $table->boolean('is_submitted')->default(false);
            $table->boolean('is_rewarded')->default(false);
            $table->timestamp('submitted_at')->nullable();
            $table->timestamps();

            // Add unique constraint to prevent duplicate task submissions
            $table->unique(['telegram_user_id', 'task_id'], 'unique_user_task');
        });
    }

    public function down()
    {
        // Drop the CHECK constraint for 'reward'
        DB::statement('ALTER TABLE tasks DROP CONSTRAINT IF EXISTS chk_reward_non_negative');

        // Drop the tables
        Schema::dropIfExists('telegram_user_tasks');
        Schema::dropIfExists('tasks');
    }
};