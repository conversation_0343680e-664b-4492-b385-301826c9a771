<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PetHappinessLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'pet_id', 'happiness_before', 'happiness_after', 'happiness_change',
        'change_reason', 'interaction_id', 'notes', 'logged_at'
    ];

    protected $casts = [
        'logged_at' => 'datetime',
    ];

    // Relationships
    public function pet(): BelongsTo
    {
        return $this->belongsTo(Pet::class);
    }

    // Scopes
    public function scopePositiveChanges($query)
    {
        return $query->where('happiness_change', '>', 0);
    }

    public function scopeNegativeChanges($query)
    {
        return $query->where('happiness_change', '<', 0);
    }

    public function scopeByReason($query, $reason)
    {
        return $query->where('change_reason', $reason);
    }

    // Accessors
    public function getChangeTypeAttribute(): string
    {
        if ($this->happiness_change > 0) {
            return 'increase';
        } elseif ($this->happiness_change < 0) {
            return 'decrease';
        }
        return 'no_change';
    }

    public function getReasonDisplayAttribute(): string
    {
        return match($this->change_reason) {
            'interaction_feed' => 'Fed pet',
            'interaction_play' => 'Played with pet',
            'interaction_pet' => 'Petted',
            'daily_decay' => 'Daily happiness decay',
            'evolution_bonus' => 'Evolution happiness bonus',
            'admin_adjustment' => 'Admin adjustment',
            default => ucfirst(str_replace('_', ' ', $this->change_reason))
        };
    }
}
