# Monitoring and Analytics Implementation - Part 2: User Analytics and Behavior Tracking

## Overview
This part covers user behavior analytics, engagement metrics, and business intelligence for the Pet System.

## User Behavior Analytics

### Analytics Service
```php
<?php
// File: api/app/Services/AnalyticsService.php

namespace App\Services;

use App\Models\TelegramUser;
use App\Models\Pet;
use App\Models\PetInteraction;
use App\Models\MysteryBoxOpening;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class AnalyticsService
{
    /**
     * Track user event
     */
    public function trackEvent(int $userId, string $event, array $properties = []): void
    {
        $eventData = [
            'user_id' => $userId,
            'event' => $event,
            'properties' => json_encode($properties),
            'timestamp' => now(),
            'session_id' => $this->getSessionId($userId),
            'user_agent' => request()->userAgent(),
            'ip_address' => request()->ip()
        ];

        // Store in database
        DB::table('analytics_events')->insert($eventData);

        // Store in cache for real-time analytics
        $this->updateRealTimeMetrics($event, $properties);
    }

    /**
     * Get user engagement metrics
     */
    public function getUserEngagementMetrics(int $userId, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        return [
            'total_sessions' => $this->getUserSessions($userId, $startDate),
            'total_playtime_minutes' => $this->getUserPlaytime($userId, $startDate),
            'pet_interactions' => $this->getUserPetInteractions($userId, $startDate),
            'mystery_boxes_opened' => $this->getUserMysteryBoxes($userId, $startDate),
            'achievements_earned' => $this->getUserAchievements($userId, $startDate),
            'retention_data' => $this->getUserRetention($userId),
            'engagement_score' => $this->calculateEngagementScore($userId, $startDate)
        ];
    }

    /**
     * Get pet system analytics
     */
    public function getPetSystemAnalytics(int $days = 30): array
    {
        $startDate = now()->subDays($days);

        return [
            'overview' => $this->getPetSystemOverview($startDate),
            'pet_popularity' => $this->getPetPopularity($startDate),
            'interaction_patterns' => $this->getInteractionPatterns($startDate),
            'evolution_metrics' => $this->getEvolutionMetrics($startDate),
            'happiness_distribution' => $this->getHappinessDistribution(),
            'user_progression' => $this->getUserProgression($startDate)
        ];
    }

    /**
     * Get mystery box analytics
     */
    public function getMysteryBoxAnalytics(int $days = 30): array
    {
        $startDate = now()->subDays($days);

        return [
            'opening_statistics' => $this->getBoxOpeningStats($startDate),
            'revenue_metrics' => $this->getBoxRevenueMetrics($startDate),
            'reward_distribution' => $this->getRewardDistribution($startDate),
            'user_spending_patterns' => $this->getUserSpendingPatterns($startDate),
            'box_popularity' => $this->getBoxPopularity($startDate)
        ];
    }

    /**
     * Get real-time dashboard metrics
     */
    public function getRealTimeDashboard(): array
    {
        return [
            'active_users' => $this->getActiveUsers(),
            'current_sessions' => $this->getCurrentSessions(),
            'recent_interactions' => $this->getRecentInteractions(),
            'recent_purchases' => $this->getRecentPurchases(),
            'system_health' => $this->getSystemHealthMetrics(),
            'trending_pets' => $this->getTrendingPets(),
            'live_events' => $this->getLiveEvents()
        ];
    }

    private function getSessionId(int $userId): string
    {
        $cacheKey = "user_session_{$userId}";
        $sessionId = Cache::get($cacheKey);
        
        if (!$sessionId) {
            $sessionId = uniqid('session_', true);
            Cache::put($cacheKey, $sessionId, 1800); // 30 minutes
        }
        
        return $sessionId;
    }

    private function updateRealTimeMetrics(string $event, array $properties): void
    {
        $minute = now()->format('Y-m-d-H-i');
        $cacheKey = "realtime_metrics_{$minute}";
        
        $metrics = Cache::get($cacheKey, [
            'events' => [],
            'user_count' => 0,
            'pet_interactions' => 0,
            'mystery_boxes_opened' => 0
        ]);

        $metrics['events'][] = $event;
        
        if ($event === 'pet_interaction') {
            $metrics['pet_interactions']++;
        } elseif ($event === 'mystery_box_opened') {
            $metrics['mystery_boxes_opened']++;
        }

        Cache::put($cacheKey, $metrics, 3600);
    }

    private function getUserSessions(int $userId, Carbon $startDate): int
    {
        return DB::table('analytics_events')
                ->where('user_id', $userId)
                ->where('timestamp', '>=', $startDate)
                ->distinct('session_id')
                ->count();
    }

    private function getUserPlaytime(int $userId, Carbon $startDate): int
    {
        $sessions = DB::table('analytics_events')
                     ->where('user_id', $userId)
                     ->where('timestamp', '>=', $startDate)
                     ->select('session_id', 
                             DB::raw('MIN(timestamp) as start_time'),
                             DB::raw('MAX(timestamp) as end_time'))
                     ->groupBy('session_id')
                     ->get();

        $totalMinutes = 0;
        foreach ($sessions as $session) {
            $start = Carbon::parse($session->start_time);
            $end = Carbon::parse($session->end_time);
            $totalMinutes += $start->diffInMinutes($end);
        }

        return $totalMinutes;
    }

    private function getUserPetInteractions(int $userId, Carbon $startDate): array
    {
        return DB::table('pet_interactions')
                ->where('telegram_user_id', $userId)
                ->where('interaction_time', '>=', $startDate)
                ->select('interaction_type', DB::raw('COUNT(*) as count'))
                ->groupBy('interaction_type')
                ->pluck('count', 'interaction_type')
                ->toArray();
    }

    private function getUserMysteryBoxes(int $userId, Carbon $startDate): int
    {
        return DB::table('mystery_box_openings')
                ->where('telegram_user_id', $userId)
                ->where('opened_at', '>=', $startDate)
                ->count();
    }

    private function getUserAchievements(int $userId, Carbon $startDate): int
    {
        return DB::table('achievement_points')
                ->where('telegram_user_id', $userId)
                ->where('created_at', '>=', $startDate)
                ->count();
    }

    private function getUserRetention(int $userId): array
    {
        $user = TelegramUser::find($userId);
        if (!$user) return [];

        $registrationDate = $user->created_at;
        $daysSinceRegistration = $registrationDate->diffInDays(now());
        
        $retentionDays = [1, 3, 7, 14, 30];
        $retention = [];

        foreach ($retentionDays as $day) {
            if ($daysSinceRegistration >= $day) {
                $targetDate = $registrationDate->addDays($day);
                $wasActive = DB::table('analytics_events')
                              ->where('user_id', $userId)
                              ->whereDate('timestamp', $targetDate)
                              ->exists();
                $retention["day_{$day}"] = $wasActive;
            }
        }

        return $retention;
    }

    private function calculateEngagementScore(int $userId, Carbon $startDate): float
    {
        $metrics = [
            'sessions' => $this->getUserSessions($userId, $startDate),
            'playtime' => $this->getUserPlaytime($userId, $startDate),
            'interactions' => array_sum($this->getUserPetInteractions($userId, $startDate)),
            'purchases' => $this->getUserMysteryBoxes($userId, $startDate),
            'achievements' => $this->getUserAchievements($userId, $startDate)
        ];

        // Weighted scoring system
        $weights = [
            'sessions' => 0.2,
            'playtime' => 0.3,
            'interactions' => 0.3,
            'purchases' => 0.1,
            'achievements' => 0.1
        ];

        $score = 0;
        foreach ($metrics as $metric => $value) {
            $normalizedValue = min($value / 100, 1); // Normalize to 0-1
            $score += $normalizedValue * $weights[$metric] * 100;
        }

        return round($score, 2);
    }

    private function getPetSystemOverview(Carbon $startDate): array
    {
        return [
            'total_pets' => Pet::count(),
            'new_pets' => Pet::where('created_at', '>=', $startDate)->count(),
            'total_interactions' => PetInteraction::where('interaction_time', '>=', $startDate)->count(),
            'unique_interacting_users' => PetInteraction::where('interaction_time', '>=', $startDate)
                                                       ->distinct('telegram_user_id')
                                                       ->count(),
            'average_pet_level' => Pet::avg('level'),
            'pets_evolved' => Pet::where('evolution_stage', '>', 0)->count()
        ];
    }

    private function getPetPopularity(Carbon $startDate): array
    {
        return DB::table('pets')
                ->join('pet_templates', 'pets.pet_template_id', '=', 'pet_templates.id')
                ->select('pet_templates.name', DB::raw('COUNT(*) as count'))
                ->where('pets.created_at', '>=', $startDate)
                ->groupBy('pet_templates.name')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->pluck('count', 'name')
                ->toArray();
    }

    private function getInteractionPatterns(Carbon $startDate): array
    {
        $hourlyPattern = DB::table('pet_interactions')
                          ->where('interaction_time', '>=', $startDate)
                          ->select(DB::raw('EXTRACT(hour FROM interaction_time) as hour'),
                                  DB::raw('COUNT(*) as count'))
                          ->groupBy('hour')
                          ->orderBy('hour')
                          ->pluck('count', 'hour')
                          ->toArray();

        $dailyPattern = DB::table('pet_interactions')
                         ->where('interaction_time', '>=', $startDate)
                         ->select(DB::raw('EXTRACT(dow FROM interaction_time) as day'),
                                 DB::raw('COUNT(*) as count'))
                         ->groupBy('day')
                         ->orderBy('day')
                         ->pluck('count', 'day')
                         ->toArray();

        return [
            'hourly' => $hourlyPattern,
            'daily' => $dailyPattern,
            'by_type' => DB::table('pet_interactions')
                          ->where('interaction_time', '>=', $startDate)
                          ->select('interaction_type', DB::raw('COUNT(*) as count'))
                          ->groupBy('interaction_type')
                          ->pluck('count', 'interaction_type')
                          ->toArray()
        ];
    }

    private function getEvolutionMetrics(Carbon $startDate): array
    {
        return [
            'total_evolutions' => DB::table('analytics_events')
                                   ->where('event', 'pet_evolved')
                                   ->where('timestamp', '>=', $startDate)
                                   ->count(),
            'evolution_by_stage' => DB::table('pets')
                                     ->select('evolution_stage', DB::raw('COUNT(*) as count'))
                                     ->groupBy('evolution_stage')
                                     ->pluck('count', 'evolution_stage')
                                     ->toArray(),
            'average_evolution_time' => $this->getAverageEvolutionTime()
        ];
    }

    private function getHappinessDistribution(): array
    {
        $ranges = [
            '0-20' => [0, 20],
            '21-40' => [21, 40],
            '41-60' => [41, 60],
            '61-80' => [61, 80],
            '81-100' => [81, 100]
        ];

        $distribution = [];
        foreach ($ranges as $label => $range) {
            $count = Pet::whereBetween('happiness', $range)->count();
            $distribution[$label] = $count;
        }

        return $distribution;
    }

    private function getUserProgression(Carbon $startDate): array
    {
        return [
            'new_users' => TelegramUser::where('created_at', '>=', $startDate)->count(),
            'users_with_pets' => TelegramUser::whereHas('pets')->count(),
            'users_with_multiple_pets' => TelegramUser::has('pets', '>', 1)->count(),
            'average_pets_per_user' => Pet::count() / max(TelegramUser::count(), 1)
        ];
    }

    private function getBoxOpeningStats(Carbon $startDate): array
    {
        return [
            'total_openings' => MysteryBoxOpening::where('opened_at', '>=', $startDate)->count(),
            'unique_users' => MysteryBoxOpening::where('opened_at', '>=', $startDate)
                                              ->distinct('telegram_user_id')
                                              ->count(),
            'average_per_user' => MysteryBoxOpening::where('opened_at', '>=', $startDate)->count() / 
                                 max(MysteryBoxOpening::where('opened_at', '>=', $startDate)
                                                     ->distinct('telegram_user_id')
                                                     ->count(), 1)
        ];
    }

    private function getBoxRevenueMetrics(Carbon $startDate): array
    {
        $revenue = DB::table('mystery_box_openings')
                    ->where('opened_at', '>=', $startDate)
                    ->select(
                        DB::raw('SUM(cost_amount) as total_revenue'),
                        DB::raw('AVG(cost_amount) as average_spend'),
                        'cost_currency'
                    )
                    ->groupBy('cost_currency')
                    ->get();

        return $revenue->mapWithKeys(function($item) {
            return [$item->cost_currency => [
                'total_revenue' => $item->total_revenue,
                'average_spend' => round($item->average_spend, 2)
            ]];
        })->toArray();
    }

    private function getRewardDistribution(Carbon $startDate): array
    {
        return DB::table('mystery_box_openings')
                ->join('mystery_box_rewards', 'mystery_box_openings.id', '=', 'mystery_box_rewards.opening_id')
                ->join('collectible_templates', 'mystery_box_rewards.collectible_id', '=', 'collectible_templates.collectible_id')
                ->where('mystery_box_openings.opened_at', '>=', $startDate)
                ->select('collectible_templates.rarity', DB::raw('COUNT(*) as count'))
                ->groupBy('collectible_templates.rarity')
                ->pluck('count', 'rarity')
                ->toArray();
    }

    private function getUserSpendingPatterns(Carbon $startDate): array
    {
        return DB::table('mystery_box_openings')
                ->where('opened_at', '>=', $startDate)
                ->select('telegram_user_id', 
                        DB::raw('COUNT(*) as total_purchases'),
                        DB::raw('SUM(cost_amount) as total_spent'))
                ->groupBy('telegram_user_id')
                ->having('total_purchases', '>', 0)
                ->orderBy('total_spent', 'desc')
                ->limit(100)
                ->get()
                ->toArray();
    }

    private function getBoxPopularity(Carbon $startDate): array
    {
        return DB::table('mystery_box_openings')
                ->join('mystery_box_types', 'mystery_box_openings.box_type', '=', 'mystery_box_types.box_type')
                ->where('mystery_box_openings.opened_at', '>=', $startDate)
                ->select('mystery_box_types.display_name', DB::raw('COUNT(*) as count'))
                ->groupBy('mystery_box_types.display_name')
                ->orderBy('count', 'desc')
                ->pluck('count', 'display_name')
                ->toArray();
    }

    private function getActiveUsers(): int
    {
        return Cache::remember('active_users_count', 300, function() {
            return DB::table('analytics_events')
                    ->where('timestamp', '>', now()->subMinutes(30))
                    ->distinct('user_id')
                    ->count();
        });
    }

    private function getCurrentSessions(): int
    {
        return Cache::remember('current_sessions_count', 60, function() {
            return DB::table('analytics_events')
                    ->where('timestamp', '>', now()->subMinutes(30))
                    ->distinct('session_id')
                    ->count();
        });
    }

    private function getRecentInteractions(): array
    {
        return PetInteraction::with(['pet.template', 'user'])
                            ->where('interaction_time', '>', now()->subMinutes(5))
                            ->orderBy('interaction_time', 'desc')
                            ->limit(10)
                            ->get()
                            ->map(function($interaction) {
                                return [
                                    'user' => $interaction->user->username ?? 'Unknown',
                                    'pet' => $interaction->pet->template->name ?? 'Unknown',
                                    'type' => $interaction->interaction_type,
                                    'time' => $interaction->interaction_time
                                ];
                            })
                            ->toArray();
    }

    private function getRecentPurchases(): array
    {
        return MysteryBoxOpening::with(['user', 'mysteryBoxType'])
                               ->where('opened_at', '>', now()->subMinutes(5))
                               ->orderBy('opened_at', 'desc')
                               ->limit(10)
                               ->get()
                               ->map(function($opening) {
                                   return [
                                       'user' => $opening->user->username ?? 'Unknown',
                                       'box_type' => $opening->mysteryBoxType->display_name ?? 'Unknown',
                                       'cost' => $opening->cost_amount,
                                       'currency' => $opening->cost_currency,
                                       'time' => $opening->opened_at
                                   ];
                               })
                               ->toArray();
    }

    private function getSystemHealthMetrics(): array
    {
        return [
            'database_connections' => $this->getDatabaseConnections(),
            'cache_hit_rate' => $this->getCacheHitRate(),
            'queue_size' => $this->getQueueSize(),
            'memory_usage' => memory_get_usage(true),
            'response_time' => $this->getAverageResponseTime()
        ];
    }

    private function getTrendingPets(): array
    {
        return DB::table('pet_interactions')
                ->join('pets', 'pet_interactions.pet_id', '=', 'pets.id')
                ->join('pet_templates', 'pets.pet_template_id', '=', 'pet_templates.id')
                ->where('pet_interactions.interaction_time', '>', now()->subHour())
                ->select('pet_templates.name', DB::raw('COUNT(*) as interactions'))
                ->groupBy('pet_templates.name')
                ->orderBy('interactions', 'desc')
                ->limit(5)
                ->pluck('interactions', 'name')
                ->toArray();
    }

    private function getLiveEvents(): array
    {
        return DB::table('analytics_events')
                ->where('timestamp', '>', now()->subMinutes(1))
                ->orderBy('timestamp', 'desc')
                ->limit(20)
                ->get(['event', 'timestamp', 'properties'])
                ->toArray();
    }

    private function getAverageEvolutionTime(): float
    {
        // This would require tracking evolution events
        return 0; // Placeholder
    }

    private function getDatabaseConnections(): int
    {
        try {
            $result = DB::select("SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'");
            return $result[0]->count ?? 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getCacheHitRate(): float
    {
        try {
            $info = \Illuminate\Support\Facades\Redis::info('stats');
            $hits = $info['keyspace_hits'] ?? 0;
            $misses = $info['keyspace_misses'] ?? 0;
            $total = $hits + $misses;
            
            return $total > 0 ? round(($hits / $total) * 100, 2) : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getQueueSize(): int
    {
        try {
            return \Illuminate\Support\Facades\Redis::llen('queues:default');
        } catch (\Exception $e) {
            return 0;
        }
    }

    private function getAverageResponseTime(): float
    {
        $recentRequests = Cache::get('performance:api_endpoints:' . date('Y-m-d-H'), []);
        
        if (empty($recentRequests)) {
            return 0;
        }

        $totalDuration = array_sum(array_column($recentRequests, 'duration_ms'));
        return round($totalDuration / count($recentRequests), 2);
    }
}
```

## Frontend Analytics Integration

### Analytics Hook
```typescript
// File: battlx/src/hooks/useAnalytics.ts

import { useCallback, useEffect } from 'react';
import { useUserStore } from '../stores/userStore';

interface AnalyticsEvent {
  event: string;
  properties?: Record<string, any>;
  timestamp?: string;
}

export const useAnalytics = () => {
  const { user } = useUserStore();

  const trackEvent = useCallback((event: string, properties: Record<string, any> = {}) => {
    if (!user) return;

    const eventData: AnalyticsEvent = {
      event,
      properties: {
        ...properties,
        user_id: user.id,
        timestamp: new Date().toISOString(),
        page_url: window.location.href,
        user_agent: navigator.userAgent
      }
    };

    // Send to backend
    fetch('/api/analytics/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${user.token}`
      },
      body: JSON.stringify(eventData)
    }).catch(error => {
      console.error('Analytics tracking failed:', error);
    });

    // Send to external analytics (if configured)
    if (window.gtag) {
      window.gtag('event', event, properties);
    }
  }, [user]);

  const trackPageView = useCallback((page: string) => {
    trackEvent('page_view', { page });
  }, [trackEvent]);

  const trackPetInteraction = useCallback((petId: string, interactionType: string) => {
    trackEvent('pet_interaction', {
      pet_id: petId,
      interaction_type: interactionType
    });
  }, [trackEvent]);

  const trackMysteryBoxOpen = useCallback((boxType: string, cost: number, currency: string) => {
    trackEvent('mystery_box_opened', {
      box_type: boxType,
      cost,
      currency
    });
  }, [trackEvent]);

  const trackPetPurchase = useCallback((templateId: string, cost: number, currency: string) => {
    trackEvent('pet_purchased', {
      template_id: templateId,
      cost,
      currency
    });
  }, [trackEvent]);

  // Auto-track page views
  useEffect(() => {
    const path = window.location.pathname;
    trackPageView(path);
  }, [trackPageView]);

  return {
    trackEvent,
    trackPageView,
    trackPetInteraction,
    trackMysteryBoxOpen,
    trackPetPurchase
  };
};
```

## Acceptance Criteria
- [ ] User behavior tracking implemented
- [ ] Engagement metrics calculation functional
- [ ] Pet system analytics operational
- [ ] Mystery box analytics working
- [ ] Real-time dashboard metrics active
- [ ] Frontend analytics integration complete
- [ ] Data retention policies configured

## Next Steps
1. Create user documentation
2. Implement A/B testing framework
3. Set up automated reporting
4. Create business intelligence dashboards

## Troubleshooting
- Verify analytics data collection accuracy
- Check event tracking implementation
- Monitor analytics database performance
- Test real-time metrics updates
- Validate user engagement calculations
