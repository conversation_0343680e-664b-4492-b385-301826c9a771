import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function compactNumber(num: number) {
  return num.toLocaleString(undefined, {
    maximumFractionDigits: 2,
    notation: "compact",
  });
}

export function sanitizeText(text: string | null | undefined): string {
  if (!text) return '';
  // Remove any HTML tags and encode special characters
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
    .trim();
}
