# PostgreSQL Migration Verification Guide

This document explains how to verify that your Laravel application's database schema is fully compatible with PostgreSQL after migration.

## Important Note

Before running verification, please review [Understanding Verification Results](POSTGRESQL_VERIFICATION_RESULTS.md) to understand how to interpret test output and distinguish between expected constraint validations and actual errors.

## Running Verification

1. Make sure your PostgreSQL database is configured in `.env`:
```env
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

2. From the api directory, run:
```bash
php artisan verify:postgresql-schema
```

Or use the test script for detailed analysis:
```bash
php test-postgresql.sh
```

## Verification Output

The verification process generates detailed logs in two locations:

1. Console output - Shows real-time progress
2. Log file - Located at `storage/logs/postgresql-verification.log`

### Log File Content
The log file contains:
- Timestamp for each operation
- Database connection details
- Table structure verification
- Constraint checks
- Performance metrics for CRUD operations
- Any errors or warnings

### Performance Statistics
The test script provides:
- Count of successful operations
- Count of failed operations
- Average operation times
- Detailed timing for each database operation

## Verification Tools

### 1. Command Line Verification Tool

The `verify:postgresql-schema` Artisan command performs live verification of your PostgreSQL database setup.

```bash
php artisan verify:postgresql-schema
```

This command verifies:
- Database connection
- Table existence and structure
- Column definitions and types
- Constraints (CHECK, FOREIGN KEY, UNIQUE)
- Relationships between tables
- Basic CRUD operations

#### Usage

1. Ensure your PostgreSQL database is running and configured in `.env`:
```env
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=your_database
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

2. Run the verification command:
```bash
php artisan verify:postgresql-schema
```

3. Review the output for any issues. The command will check:
   - Table structures
   - Column definitions
   - Constraints
   - Relationships
   - Data operations

### 2. Feature Test Suite

The PostgreSQL schema test suite provides automated testing of the database schema.

To run the tests:

```bash
php artisan test --filter=PostgresqlSchemaTest
```

The test suite verifies:
- PostgreSQL connection type
- CHECK constraints for unsigned columns
- Foreign key relationships
- Unique constraints
- Cascade deletes
- Complex CHECK constraints
- Timestamp column behavior

## What's Being Verified

### Tables and Structure
- All required tables exist
- Correct column types and definitions
- Primary keys and indexes
- Timestamp columns (created_at, updated_at)

### Constraints
- CHECK constraints for non-negative values
- Foreign key relationships
- Unique constraints
- Complex business rules (e.g., to_balance > from_balance in levels)

### Relationships
- TelegramUser -> Level
- Mission -> MissionType
- Task -> TaskType
- Other defined relationships

### Data Operations
- Insert operations respect constraints
- Update operations maintain data integrity
- Delete operations cascade properly
- Timestamp columns update automatically

## Troubleshooting

If verification fails, you'll see specific error messages indicating the issue. Common issues and solutions:

1. CHECK Constraint Failures:
   - Verify migration files use proper CHECK constraints
   - Check data doesn't violate constraints

2. Foreign Key Failures:
   - Ensure related tables exist
   - Verify foreign key definitions
   - Check cascade delete settings

3. Connection Issues:
   - Verify PostgreSQL is running
   - Check database credentials
   - Confirm database exists

## Manual Verification

You can also manually verify specific aspects using PostgreSQL commands:

1. View table constraints:
```sql
SELECT con.conname, con.contype, pg_get_constraintdef(con.oid)
FROM pg_constraint con
JOIN pg_class rel ON rel.oid = con.conrelid
WHERE rel.relname = 'your_table_name';
```

2. View foreign keys:
```sql
SELECT
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM
    information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu ON ccu.constraint_name = tc.constraint_name
WHERE tc.table_name = 'your_table_name' AND tc.constraint_type = 'FOREIGN KEY';
```

## Next Steps

After verification:
1. Review any failed checks
2. Address identified issues
3. Re-run verification
4. Document any schema updates
5. Update application code if needed

Remember to run these verifications in your testing environment before applying changes to production.