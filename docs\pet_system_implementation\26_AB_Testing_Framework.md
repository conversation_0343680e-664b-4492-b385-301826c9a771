# A/B Testing Framework Implementation

## Overview
This document covers the implementation of a comprehensive A/B testing framework for the Pet System, enabling data-driven optimization of features, UI/UX, and game mechanics.

## Implementation Time: 3-4 days
## Complexity: High
## Dependencies: Analytics system, feature flags, statistical analysis

## Backend A/B Testing Infrastructure

### Experiment Management Service
```php
<?php
// File: api/app/Services/ExperimentService.php

namespace App\Services;

use App\Models\TelegramUser;
use App\Models\Experiment;
use App\Models\ExperimentVariant;
use App\Models\ExperimentAssignment;
use App\Models\ExperimentEvent;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ExperimentService
{
    /**
     * Get user's experiment assignments
     */
    public function getUserExperiments(TelegramUser $user): array
    {
        $cacheKey = "user_experiments_{$user->id}";
        
        return Cache::remember($cacheKey, 3600, function() use ($user) {
            $activeExperiments = Experiment::active()->get();
            $assignments = [];

            foreach ($activeExperiments as $experiment) {
                $assignment = $this->getOrCreateAssignment($user, $experiment);
                if ($assignment) {
                    $assignments[$experiment->key] = [
                        'variant' => $assignment->variant->key,
                        'config' => $assignment->variant->config,
                        'assigned_at' => $assignment->created_at
                    ];
                }
            }

            return $assignments;
        });
    }

    /**
     * Get specific experiment variant for user
     */
    public function getUserVariant(TelegramUser $user, string $experimentKey): ?array
    {
        $experiment = Experiment::where('key', $experimentKey)
                               ->where('status', 'active')
                               ->first();

        if (!$experiment) {
            return null;
        }

        $assignment = $this->getOrCreateAssignment($user, $experiment);
        
        return $assignment ? [
            'variant' => $assignment->variant->key,
            'config' => $assignment->variant->config
        ] : null;
    }

    /**
     * Track experiment event
     */
    public function trackEvent(TelegramUser $user, string $experimentKey, string $eventType, array $properties = []): void
    {
        $assignment = ExperimentAssignment::whereHas('experiment', function($query) use ($experimentKey) {
            $query->where('key', $experimentKey);
        })->where('telegram_user_id', $user->id)->first();

        if (!$assignment) {
            return;
        }

        ExperimentEvent::create([
            'experiment_assignment_id' => $assignment->id,
            'event_type' => $eventType,
            'properties' => $properties,
            'timestamp' => now()
        ]);

        // Update real-time metrics
        $this->updateExperimentMetrics($assignment->experiment, $assignment->variant, $eventType);
    }

    /**
     * Get experiment results
     */
    public function getExperimentResults(string $experimentKey): array
    {
        $experiment = Experiment::where('key', $experimentKey)->first();
        
        if (!$experiment) {
            return [];
        }

        $variants = $experiment->variants()->with('assignments.events')->get();
        $results = [];

        foreach ($variants as $variant) {
            $assignments = $variant->assignments;
            $totalUsers = $assignments->count();
            
            if ($totalUsers === 0) {
                continue;
            }

            $events = $assignments->flatMap->events;
            $conversionEvents = $events->where('event_type', $experiment->primary_metric);
            $conversions = $conversionEvents->count();
            $conversionRate = $totalUsers > 0 ? ($conversions / $totalUsers) * 100 : 0;

            $results[$variant->key] = [
                'name' => $variant->name,
                'users' => $totalUsers,
                'conversions' => $conversions,
                'conversion_rate' => round($conversionRate, 2),
                'confidence_interval' => $this->calculateConfidenceInterval($conversions, $totalUsers),
                'statistical_significance' => $this->calculateSignificance($variant, $variants->first()),
                'secondary_metrics' => $this->getSecondaryMetrics($variant, $experiment)
            ];
        }

        return [
            'experiment' => [
                'key' => $experiment->key,
                'name' => $experiment->name,
                'status' => $experiment->status,
                'start_date' => $experiment->start_date,
                'end_date' => $experiment->end_date,
                'primary_metric' => $experiment->primary_metric
            ],
            'variants' => $results,
            'summary' => $this->generateExperimentSummary($results)
        ];
    }

    /**
     * Create new experiment
     */
    public function createExperiment(array $data): Experiment
    {
        DB::beginTransaction();
        
        try {
            $experiment = Experiment::create([
                'key' => $data['key'],
                'name' => $data['name'],
                'description' => $data['description'],
                'hypothesis' => $data['hypothesis'],
                'primary_metric' => $data['primary_metric'],
                'secondary_metrics' => $data['secondary_metrics'] ?? [],
                'target_audience' => $data['target_audience'] ?? [],
                'traffic_allocation' => $data['traffic_allocation'] ?? 100,
                'start_date' => $data['start_date'],
                'end_date' => $data['end_date'],
                'status' => 'draft'
            ]);

            // Create variants
            foreach ($data['variants'] as $variantData) {
                ExperimentVariant::create([
                    'experiment_id' => $experiment->id,
                    'key' => $variantData['key'],
                    'name' => $variantData['name'],
                    'description' => $variantData['description'],
                    'config' => $variantData['config'],
                    'traffic_percentage' => $variantData['traffic_percentage']
                ]);
            }

            DB::commit();
            return $experiment;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }

    /**
     * Start experiment
     */
    public function startExperiment(string $experimentKey): bool
    {
        $experiment = Experiment::where('key', $experimentKey)->first();
        
        if (!$experiment || $experiment->status !== 'draft') {
            return false;
        }

        // Validate experiment configuration
        if (!$this->validateExperiment($experiment)) {
            return false;
        }

        $experiment->update([
            'status' => 'active',
            'actual_start_date' => now()
        ]);

        // Clear related caches
        $this->clearExperimentCaches($experiment);

        return true;
    }

    /**
     * Stop experiment
     */
    public function stopExperiment(string $experimentKey, string $reason = ''): bool
    {
        $experiment = Experiment::where('key', $experimentKey)->first();
        
        if (!$experiment || $experiment->status !== 'active') {
            return false;
        }

        $experiment->update([
            'status' => 'completed',
            'actual_end_date' => now(),
            'stop_reason' => $reason
        ]);

        // Clear related caches
        $this->clearExperimentCaches($experiment);

        return true;
    }

    private function getOrCreateAssignment(TelegramUser $user, Experiment $experiment): ?ExperimentAssignment
    {
        // Check if user already has assignment
        $assignment = ExperimentAssignment::where('telegram_user_id', $user->id)
                                         ->where('experiment_id', $experiment->id)
                                         ->first();

        if ($assignment) {
            return $assignment;
        }

        // Check if user qualifies for experiment
        if (!$this->userQualifiesForExperiment($user, $experiment)) {
            return null;
        }

        // Assign user to variant
        $variant = $this->selectVariantForUser($user, $experiment);
        
        if (!$variant) {
            return null;
        }

        return ExperimentAssignment::create([
            'telegram_user_id' => $user->id,
            'experiment_id' => $experiment->id,
            'experiment_variant_id' => $variant->id,
            'assigned_at' => now()
        ]);
    }

    private function userQualifiesForExperiment(TelegramUser $user, Experiment $experiment): bool
    {
        $targetAudience = $experiment->target_audience;

        // Check traffic allocation
        $userHash = crc32($user->id . $experiment->key) % 100;
        if ($userHash >= $experiment->traffic_allocation) {
            return false;
        }

        // Check audience criteria
        if (!empty($targetAudience)) {
            foreach ($targetAudience as $criterion => $value) {
                if (!$this->checkAudienceCriterion($user, $criterion, $value)) {
                    return false;
                }
            }
        }

        return true;
    }

    private function selectVariantForUser(TelegramUser $user, Experiment $experiment): ?ExperimentVariant
    {
        $variants = $experiment->variants()->orderBy('traffic_percentage', 'desc')->get();
        
        if ($variants->isEmpty()) {
            return null;
        }

        // Use consistent hashing for variant assignment
        $userHash = crc32($user->id . $experiment->key . 'variant') % 100;
        $cumulativePercentage = 0;

        foreach ($variants as $variant) {
            $cumulativePercentage += $variant->traffic_percentage;
            if ($userHash < $cumulativePercentage) {
                return $variant;
            }
        }

        // Fallback to first variant
        return $variants->first();
    }

    private function checkAudienceCriterion(TelegramUser $user, string $criterion, $value): bool
    {
        switch ($criterion) {
            case 'registration_days_ago':
                $daysSinceRegistration = $user->created_at->diffInDays(now());
                return $daysSinceRegistration >= $value;
                
            case 'has_pets':
                return $user->pets()->exists() === $value;
                
            case 'pet_count_min':
                return $user->pets()->count() >= $value;
                
            case 'total_spent_min':
                $totalSpent = $user->mysteryBoxOpenings()->sum('cost_amount');
                return $totalSpent >= $value;
                
            case 'platform':
                return in_array($user->platform ?? 'web', (array) $value);
                
            default:
                return true;
        }
    }

    private function updateExperimentMetrics(Experiment $experiment, ExperimentVariant $variant, string $eventType): void
    {
        $cacheKey = "experiment_metrics_{$experiment->key}_{$variant->key}_{$eventType}";
        Cache::increment($cacheKey, 1);
        Cache::expire($cacheKey, 3600);
    }

    private function calculateConfidenceInterval(int $conversions, int $total): array
    {
        if ($total === 0) {
            return ['lower' => 0, 'upper' => 0];
        }

        $p = $conversions / $total;
        $z = 1.96; // 95% confidence interval
        $margin = $z * sqrt(($p * (1 - $p)) / $total);

        return [
            'lower' => max(0, round(($p - $margin) * 100, 2)),
            'upper' => min(100, round(($p + $margin) * 100, 2))
        ];
    }

    private function calculateSignificance(ExperimentVariant $variant, ExperimentVariant $control): array
    {
        // Simplified chi-square test
        $variantUsers = $variant->assignments()->count();
        $variantConversions = $variant->assignments()
                                   ->whereHas('events', function($query) use ($variant) {
                                       $query->where('event_type', $variant->experiment->primary_metric);
                                   })->count();

        $controlUsers = $control->assignments()->count();
        $controlConversions = $control->assignments()
                                    ->whereHas('events', function($query) use ($control) {
                                        $query->where('event_type', $control->experiment->primary_metric);
                                    })->count();

        if ($variantUsers === 0 || $controlUsers === 0) {
            return ['significant' => false, 'p_value' => 1.0];
        }

        $variantRate = $variantConversions / $variantUsers;
        $controlRate = $controlConversions / $controlUsers;
        
        // Simplified p-value calculation
        $pooledRate = ($variantConversions + $controlConversions) / ($variantUsers + $controlUsers);
        $standardError = sqrt($pooledRate * (1 - $pooledRate) * (1/$variantUsers + 1/$controlUsers));
        
        if ($standardError === 0) {
            return ['significant' => false, 'p_value' => 1.0];
        }

        $zScore = abs($variantRate - $controlRate) / $standardError;
        $pValue = 2 * (1 - $this->normalCDF($zScore));

        return [
            'significant' => $pValue < 0.05,
            'p_value' => round($pValue, 4),
            'z_score' => round($zScore, 4)
        ];
    }

    private function normalCDF(float $x): float
    {
        // Approximation of normal cumulative distribution function
        return 0.5 * (1 + erf($x / sqrt(2)));
    }

    private function getSecondaryMetrics(ExperimentVariant $variant, Experiment $experiment): array
    {
        $secondaryMetrics = [];
        
        foreach ($experiment->secondary_metrics as $metric) {
            $count = $variant->assignments()
                           ->whereHas('events', function($query) use ($metric) {
                               $query->where('event_type', $metric);
                           })->count();
            
            $total = $variant->assignments()->count();
            $rate = $total > 0 ? ($count / $total) * 100 : 0;
            
            $secondaryMetrics[$metric] = [
                'count' => $count,
                'rate' => round($rate, 2)
            ];
        }

        return $secondaryMetrics;
    }

    private function generateExperimentSummary(array $results): array
    {
        if (count($results) < 2) {
            return ['status' => 'insufficient_data'];
        }

        $variants = array_values($results);
        $control = $variants[0];
        $winner = null;
        $maxConversionRate = 0;

        foreach ($variants as $variant) {
            if ($variant['conversion_rate'] > $maxConversionRate) {
                $maxConversionRate = $variant['conversion_rate'];
                $winner = $variant;
            }
        }

        $improvement = $winner['conversion_rate'] - $control['conversion_rate'];
        $relativeImprovement = $control['conversion_rate'] > 0 ? 
            ($improvement / $control['conversion_rate']) * 100 : 0;

        return [
            'status' => 'active',
            'winner' => $winner['name'] ?? 'Unknown',
            'improvement' => round($improvement, 2),
            'relative_improvement' => round($relativeImprovement, 2),
            'confidence' => $winner['statistical_significance']['significant'] ?? false
        ];
    }

    private function validateExperiment(Experiment $experiment): bool
    {
        // Check if variants exist
        if ($experiment->variants()->count() < 2) {
            return false;
        }

        // Check if traffic percentages sum to 100
        $totalTraffic = $experiment->variants()->sum('traffic_percentage');
        if ($totalTraffic !== 100) {
            return false;
        }

        return true;
    }

    private function clearExperimentCaches(Experiment $experiment): void
    {
        // Clear user experiment caches
        $userIds = ExperimentAssignment::where('experiment_id', $experiment->id)
                                      ->pluck('telegram_user_id');
        
        foreach ($userIds as $userId) {
            Cache::forget("user_experiments_{$userId}");
        }

        // Clear experiment metrics cache
        foreach ($experiment->variants as $variant) {
            Cache::forget("experiment_metrics_{$experiment->key}_{$variant->key}_*");
        }
    }
}
```

### Experiment Models
```php
<?php
// File: api/app/Models/Experiment.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Experiment extends Model
{
    protected $fillable = [
        'key',
        'name',
        'description',
        'hypothesis',
        'primary_metric',
        'secondary_metrics',
        'target_audience',
        'traffic_allocation',
        'start_date',
        'end_date',
        'actual_start_date',
        'actual_end_date',
        'status',
        'stop_reason'
    ];

    protected $casts = [
        'secondary_metrics' => 'array',
        'target_audience' => 'array',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'actual_start_date' => 'datetime',
        'actual_end_date' => 'datetime'
    ];

    public function variants(): HasMany
    {
        return $this->hasMany(ExperimentVariant::class);
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(ExperimentAssignment::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('start_date', '<=', now())
                    ->where(function($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>', now());
                    });
    }
}
```

## Frontend A/B Testing Integration

### Experiment Hook
```typescript
// File: battlx/src/hooks/useExperiment.ts

import { useState, useEffect, useCallback } from 'react';
import { useUserStore } from '../stores/userStore';

interface ExperimentConfig {
  [key: string]: any;
}

interface ExperimentVariant {
  variant: string;
  config: ExperimentConfig;
}

export const useExperiment = (experimentKey: string) => {
  const { user } = useUserStore();
  const [variant, setVariant] = useState<ExperimentVariant | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }

    fetchExperimentVariant();
  }, [user, experimentKey]);

  const fetchExperimentVariant = async () => {
    try {
      const response = await fetch(`/api/experiments/${experimentKey}/variant`, {
        headers: {
          'Authorization': `Bearer ${user?.token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setVariant(data);
      } else {
        setVariant(null);
      }
    } catch (error) {
      console.error('Failed to fetch experiment variant:', error);
      setVariant(null);
    } finally {
      setLoading(false);
    }
  };

  const trackEvent = useCallback(async (eventType: string, properties: Record<string, any> = {}) => {
    if (!user || !variant) return;

    try {
      await fetch('/api/experiments/track', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.token}`
        },
        body: JSON.stringify({
          experiment_key: experimentKey,
          event_type: eventType,
          properties
        })
      });
    } catch (error) {
      console.error('Failed to track experiment event:', error);
    }
  }, [user, variant, experimentKey]);

  const getConfig = useCallback((key: string, defaultValue: any = null) => {
    return variant?.config?.[key] ?? defaultValue;
  }, [variant]);

  const isVariant = useCallback((variantKey: string) => {
    return variant?.variant === variantKey;
  }, [variant]);

  return {
    variant: variant?.variant,
    config: variant?.config,
    loading,
    trackEvent,
    getConfig,
    isVariant
  };
};
```

### Experiment Component Wrapper
```typescript
// File: battlx/src/components/experiments/ExperimentWrapper.tsx

import React, { ReactNode } from 'react';
import { useExperiment } from '../../hooks/useExperiment';

interface ExperimentWrapperProps {
  experimentKey: string;
  variants: Record<string, ReactNode>;
  fallback?: ReactNode;
  trackView?: boolean;
}

const ExperimentWrapper: React.FC<ExperimentWrapperProps> = ({
  experimentKey,
  variants,
  fallback,
  trackView = true
}) => {
  const { variant, loading, trackEvent } = useExperiment(experimentKey);

  React.useEffect(() => {
    if (variant && trackView) {
      trackEvent('view');
    }
  }, [variant, trackView, trackEvent]);

  if (loading) {
    return fallback ? <>{fallback}</> : null;
  }

  if (!variant || !variants[variant]) {
    return fallback ? <>{fallback}</> : null;
  }

  return <>{variants[variant]}</>;
};

export default ExperimentWrapper;
```

## Pet System A/B Test Examples

### Pet Purchase Flow Experiment
```typescript
// File: battlx/src/components/pets/PetPurchaseExperiment.tsx

import React from 'react';
import { useExperiment } from '../../hooks/useExperiment';
import PetPurchaseModalV1 from './PetPurchaseModalV1';
import PetPurchaseModalV2 from './PetPurchaseModalV2';

interface PetPurchaseExperimentProps {
  pet: any;
  isOpen: boolean;
  onClose: () => void;
  onPurchase: (method: string) => void;
}

const PetPurchaseExperiment: React.FC<PetPurchaseExperimentProps> = (props) => {
  const { isVariant, trackEvent } = useExperiment('pet_purchase_flow_v1');

  const handlePurchaseClick = (method: string) => {
    trackEvent('purchase_initiated', { method, pet_id: props.pet.id });
    props.onPurchase(method);
  };

  const handlePurchaseComplete = (method: string) => {
    trackEvent('purchase_completed', { method, pet_id: props.pet.id });
  };

  if (isVariant('simplified_flow')) {
    return (
      <PetPurchaseModalV2
        {...props}
        onPurchase={handlePurchaseClick}
        onPurchaseComplete={handlePurchaseComplete}
      />
    );
  }

  return (
    <PetPurchaseModalV1
      {...props}
      onPurchase={handlePurchaseClick}
      onPurchaseComplete={handlePurchaseComplete}
    />
  );
};

export default PetPurchaseExperiment;
```

### Mystery Box Opening Animation Test
```typescript
// File: battlx/src/components/mysterybox/BoxOpeningExperiment.tsx

import React from 'react';
import { useExperiment } from '../../hooks/useExperiment';
import StandardBoxOpening from './StandardBoxOpening';
import EnhancedBoxOpening from './EnhancedBoxOpening';

interface BoxOpeningExperimentProps {
  box: any;
  onComplete: () => void;
}

const BoxOpeningExperiment: React.FC<BoxOpeningExperimentProps> = (props) => {
  const { getConfig, trackEvent } = useExperiment('box_opening_animation_v1');

  const animationDuration = getConfig('animation_duration', 3000);
  const particleCount = getConfig('particle_count', 20);
  const useEnhancedEffects = getConfig('enhanced_effects', false);

  const handleAnimationComplete = () => {
    trackEvent('animation_completed', {
      box_type: props.box.type,
      animation_duration: animationDuration
    });
    props.onComplete();
  };

  if (useEnhancedEffects) {
    return (
      <EnhancedBoxOpening
        {...props}
        animationDuration={animationDuration}
        particleCount={particleCount}
        onComplete={handleAnimationComplete}
      />
    );
  }

  return (
    <StandardBoxOpening
      {...props}
      animationDuration={animationDuration}
      onComplete={handleAnimationComplete}
    />
  );
};

export default BoxOpeningExperiment;
```

## Acceptance Criteria
- [ ] Experiment management system operational
- [ ] User assignment algorithm working correctly
- [ ] Event tracking functional
- [ ] Statistical analysis implemented
- [ ] Frontend integration complete
- [ ] A/B test examples functional
- [ ] Results dashboard accessible

## Next Steps
1. Create automated reporting system
2. Implement advanced statistical methods
3. Add experiment scheduling features
4. Create experiment templates

## Troubleshooting
- Verify experiment configuration validity
- Check user assignment consistency
- Monitor event tracking accuracy
- Validate statistical calculations
- Test experiment isolation between features
