# Mystery Box System Database Implementation

## Overview
This document covers the database schema for the Mystery Box system, including box types, unlocks, and opening mechanics.

## Implementation Time: 1-2 days
## Complexity: Medium
## Dependencies: Pet system tables, existing prize tree system

## Additional Migration Files

### Migration 6: Mystery Box Types Table
```php
<?php
// File: api/database/migrations/2024_01_01_000006_create_mystery_box_types_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('mystery_box_types', function (Blueprint $table) {
            $table->id();
            $table->string('box_type', 50)->unique(); // 'common_shadow', 'rare_undead', etc.
            $table->string('display_name', 100);
            $table->enum('rarity', ['common', 'rare', 'epic', 'legendary', 'mythic']);
            $table->enum('category', ['shadow', 'undead', 'demon', 'spirit', 'beast', 'universal']);
            
            $table->text('description');
            $table->string('image_url');
            $table->string('animation_url')->nullable();
            
            // Purchase costs
            $table->integer('coin_cost')->default(0);
            $table->integer('gem_cost')->default(0);
            $table->integer('achievement_points_cost')->default(0);
            
            // Reward configuration
            $table->json('possible_rewards'); // Array of possible collectibles/items
            $table->json('reward_weights'); // Probability weights for each reward
            $table->integer('guaranteed_rarity_level')->default(1); // 1=common, 5=mythic
            
            // Unlock requirements
            $table->json('unlock_requirements'); // Pet IDs or prize tree levels required
            
            $table->boolean('is_purchasable')->default(true);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index(['category', 'rarity']);
            $table->index('is_active');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mystery_box_types');
    }
};
```

### Migration 7: Mystery Box Unlocks Table
```php
<?php
// File: api/database/migrations/2024_01_01_000007_create_mystery_box_unlocks_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('mystery_box_unlocks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->string('box_type', 50); // References mystery_box_types.box_type
            
            $table->enum('unlock_source', ['pet_purchase', 'prize_tree', 'collection_bonus', 'admin_grant']);
            $table->string('source_reference')->nullable(); // Pet ID, prize tree level, etc.
            $table->timestamp('unlocked_at');
            
            $table->timestamps();
            
            $table->unique(['telegram_user_id', 'box_type']);
            $table->index(['telegram_user_id', 'unlocked_at']);
            
            $table->foreign('box_type')
                  ->references('box_type')
                  ->on('mystery_box_types')
                  ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mystery_box_unlocks');
    }
};
```

### Migration 8: Mystery Box Openings Table
```php
<?php
// File: api/database/migrations/2024_01_01_000008_create_mystery_box_openings_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('mystery_box_openings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->string('box_type', 50);
            
            // Purchase information
            $table->enum('purchase_method', ['coins', 'gems', 'achievement_points', 'free']);
            $table->integer('cost_paid');
            $table->string('currency_used', 20);
            
            // Rewards received
            $table->json('rewards_received'); // Array of collectibles/items received
            $table->integer('total_value'); // Estimated value of rewards
            $table->boolean('contained_rare_item')->default(false);
            
            $table->timestamp('opened_at');
            $table->timestamps();
            
            $table->index(['telegram_user_id', 'opened_at']);
            $table->index(['box_type', 'opened_at']);
            
            $table->foreign('box_type')
                  ->references('box_type')
                  ->on('mystery_box_types')
                  ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mystery_box_openings');
    }
};
```

### Migration 9: Collection Sets Table
```php
<?php
// File: api/database/migrations/2024_01_01_000009_create_collection_sets_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('collection_sets', function (Blueprint $table) {
            $table->id();
            $table->string('set_id', 50)->unique(); // 'shadow_collection', 'undead_collection'
            $table->string('name', 100);
            $table->enum('category', ['shadow', 'undead', 'demon', 'spirit', 'beast']);
            $table->text('description');
            $table->string('icon_url');
            
            // Set completion rewards
            $table->json('completion_rewards'); // Coins, gems, exclusive collectibles
            $table->string('bonus_mystery_box_type')->nullable(); // Exclusive box unlocked
            
            // Set configuration
            $table->integer('total_collectibles'); // How many items in this set
            $table->integer('required_for_completion'); // May be less than total for partial sets
            
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index('category');
            $table->index('is_active');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('collection_sets');
    }
};
```

### Migration 10: User Collection Progress Table
```php
<?php
// File: api/database/migrations/2024_01_01_000010_create_user_collection_progress_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_collection_progress', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->string('set_id', 50); // References collection_sets.set_id
            
            $table->integer('collectibles_owned')->default(0);
            $table->integer('total_collectibles');
            $table->decimal('completion_percentage', 5, 2)->default(0.00);
            
            $table->boolean('is_completed')->default(false);
            $table->timestamp('completed_at')->nullable();
            $table->boolean('rewards_claimed')->default(false);
            $table->timestamp('rewards_claimed_at')->nullable();
            
            $table->json('owned_collectible_ids'); // Array of collectible IDs owned
            $table->json('missing_collectible_ids'); // Array of collectible IDs still needed
            
            $table->timestamps();
            
            $table->unique(['telegram_user_id', 'set_id']);
            $table->index(['telegram_user_id', 'is_completed']);
            
            $table->foreign('set_id')
                  ->references('set_id')
                  ->on('collection_sets')
                  ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_collection_progress');
    }
};
```

### Migration 11: Pet Happiness Logs Table
```php
<?php
// File: api/database/migrations/2024_01_01_000011_create_pet_happiness_logs_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pet_happiness_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pet_id')->constrained('pets')->onDelete('cascade');
            
            $table->integer('happiness_before');
            $table->integer('happiness_after');
            $table->integer('happiness_change');
            
            $table->enum('change_reason', [
                'interaction_feed',
                'interaction_play', 
                'interaction_pet',
                'daily_decay',
                'evolution_bonus',
                'admin_adjustment'
            ]);
            
            $table->string('interaction_id')->nullable(); // Reference to pet_interactions.id
            $table->text('notes')->nullable();
            
            $table->timestamp('logged_at');
            $table->timestamps();
            
            $table->index(['pet_id', 'logged_at']);
            $table->index('change_reason');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pet_happiness_logs');
    }
};
```

## Running Additional Migrations

### Step 1: Create Migration Files
```bash
cd api
php artisan make:migration create_mystery_box_types_table
php artisan make:migration create_mystery_box_unlocks_table
php artisan make:migration create_mystery_box_openings_table
php artisan make:migration create_collection_sets_table
php artisan make:migration create_user_collection_progress_table
php artisan make:migration create_pet_happiness_logs_table
```

### Step 2: Run Migrations
```bash
php artisan migrate
```

### Step 3: Verify All Tables
```bash
php artisan tinker
$tables = [
    'pet_templates', 'pets', 'pet_interactions', 'pet_happiness_logs',
    'collectible_templates', 'collectibles', 'collection_sets', 'user_collection_progress',
    'mystery_box_types', 'mystery_box_unlocks', 'mystery_box_openings'
];

foreach($tables as $table) {
    echo $table . ': ' . (Schema::hasTable($table) ? 'EXISTS' : 'MISSING') . "\n";
}
```

## Database Indexes for Performance

### Additional Indexes Migration
```php
<?php
// File: api/database/migrations/2024_01_01_000012_add_pet_system_indexes.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('pets', function (Blueprint $table) {
            $table->index(['happiness', 'last_fed']); // For finding pets needing attention
            $table->index(['level', 'experience']); // For evolution calculations
        });
        
        Schema::table('pet_interactions', function (Blueprint $table) {
            $table->index(['telegram_user_id', 'interaction_time', 'interaction_type']);
        });
        
        Schema::table('collectibles', function (Blueprint $table) {
            $table->index(['telegram_user_id', 'unlock_source']);
        });
        
        Schema::table('mystery_box_openings', function (Blueprint $table) {
            $table->index(['telegram_user_id', 'purchase_method', 'opened_at']);
        });
    }

    public function down(): void
    {
        Schema::table('pets', function (Blueprint $table) {
            $table->dropIndex(['happiness', 'last_fed']);
            $table->dropIndex(['level', 'experience']);
        });
        
        Schema::table('pet_interactions', function (Blueprint $table) {
            $table->dropIndex(['telegram_user_id', 'interaction_time', 'interaction_type']);
        });
        
        Schema::table('collectibles', function (Blueprint $table) {
            $table->dropIndex(['telegram_user_id', 'unlock_source']);
        });
        
        Schema::table('mystery_box_openings', function (Blueprint $table) {
            $table->dropIndex(['telegram_user_id', 'purchase_method', 'opened_at']);
        });
    }
};
```

## Acceptance Criteria
- [x] All 11 tables created successfully
- [x] Foreign key relationships established
- [x] Performance indexes added
- [x] JSON columns properly configured
- [x] Enum constraints working correctly

## Next Steps
1. Create database seeders for initial data
2. Implement Eloquent models
3. Set up API endpoints
4. Create frontend components

## Troubleshooting
- Ensure PostgreSQL supports JSON columns
- Check enum value consistency across tables
- Verify foreign key constraints don't conflict
