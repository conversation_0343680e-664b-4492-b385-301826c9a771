/**
 * Play State
 * Main gameplay state
 */
class PlayState extends GameState {
    /**
     * @param {Engine} engine - Game engine
     */
    constructor(engine) {
        super(engine);
        this.input = new InputHandler(engine);
        this.background = null;
        this.player = null;
        this.enemies = [];
        this.diamonds = [];
        this.gameMusic = null;

        // Game variables
        this.score = 0;
        this.scoreMultiplier = 1;
        this.gameSpeed = CONSTANTS.GAME.INITIAL_SPEED;
        this.backgroundSpeed = CONSTANTS.GAME.BACKGROUND_SPEED;
        this.spawnTimer = 0;
        this.nextSpawnTime = 0;
        this.speedIncreaseTimer = 0;
    }

    /**
     * Called when entering the state
     */
    enter() {
        // Reset game variables
        this.score = 0;
        this.scoreMultiplier = 1;
        this.gameSpeed = CONSTANTS.GAME.INITIAL_SPEED;
        this.backgroundSpeed = CONSTANTS.GAME.BACKGROUND_SPEED;
        this.enemies = [];
        this.diamonds = [];
        this.spawnTimer = 0;
        this.nextSpawnTime = this.getRandomSpawnTime();
        this.speedIncreaseTimer = 0;

        // Create background
        this.background = new Background(this.engine);

        // Create player
        this.player = new Player(this.engine, 100, 775);

        // Play game music
        this.gameMusic = this.engine.playSound('gameMusic', { volume: 0.3, loop: true });

        // Spawn initial enemy
        this.spawnEnemy();
    }

    /**
     * Called when exiting the state
     */
    exit() {
        // Stop game music
        if (this.gameMusic) {
            this.engine.stopSound(this.gameMusic);
        }

        // Save score and best score
        this.engine.setVariable('score', this.score);
        Utils.setBestScore(this.score);
    }

    /**
     * Update the state
     * @param {number} deltaTime - Time since last update
     */
    update(deltaTime) {
        // Update input
        this.input.update();

        // Update background
        this.background.update(deltaTime, this.player.y);

        // Update player
        this.player.update(deltaTime, this.input, this.background.getGroundY());

        // Update enemies
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            enemy.update(deltaTime);

            // Remove inactive enemies
            if (!enemy.isActive()) {
                this.enemies.splice(i, 1);
            }
        }

        // Update diamonds
        for (let i = this.diamonds.length - 1; i >= 0; i--) {
            const diamond = this.diamonds[i];
            diamond.update(deltaTime);

            // Remove inactive diamonds
            if (!diamond.isActive()) {
                this.diamonds.splice(i, 1);
            }
        }

        // Check collisions
        this.checkCollisions();

        // Spawn enemies and diamonds
        this.spawnTimer += deltaTime;
        if (this.spawnTimer >= this.nextSpawnTime) {
            this.spawnTimer = 0;

            // Adjust spawn time based on game speed and score to ensure appropriate enemy density
            // As speed increases, spawn time decreases to maintain challenge
            const speedFactor = Math.min(1, 300 / this.gameSpeed);

            // As score increases, slightly reduce spawn time for more challenge
            const scoreFactor = Math.max(0.7, 1 - (this.score / 2000)); // Reduces spawn time by up to 30% at high scores

            this.nextSpawnTime = this.getRandomSpawnTime() * speedFactor * scoreFactor;

            // Ensure minimum spawn time doesn't go below 0.5 seconds to prevent overwhelming the player
            this.nextSpawnTime = Math.max(0.5, this.nextSpawnTime);

            // Spawn enemy
            this.spawnEnemy();

            // Chance to spawn diamond - increased chance at higher speeds
            const diamondChance = CONSTANTS.GAME.DIAMOND_SPAWN_CHANCE * (1 + (this.gameSpeed - 300) / 300);
            if (Math.random() < diamondChance) {
                this.spawnDiamond();

                // Chance to spawn an additional diamond for more rewards
                if (Math.random() < 0.3) {
                    setTimeout(() => this.spawnDiamond(), 300);
                }
            }
        }

        // Increase game speed over time
        this.speedIncreaseTimer += deltaTime;
        if (this.speedIncreaseTimer >= 0.5) { // Increase speed more frequently
            this.speedIncreaseTimer = 0;
            this.increaseSpeed();
        }
    }

    /**
     * Render the state
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     */
    render(ctx) {
        // Draw background
        this.background.render(ctx, this.player.y);

        // Draw diamonds
        for (const diamond of this.diamonds) {
            diamond.render(ctx);
        }

        // Draw enemies
        for (const enemy of this.enemies) {
            enemy.render(ctx);
        }

        // Draw player
        this.player.render(ctx);

        // Draw score
        Utils.drawText(ctx, `SCORE: ${this.score}`, 100, 50, {
            fillStyle: '#FFFFFF',
            strokeStyle: '#000000',
            font: 'bold 24px Arial',
            textAlign: 'left',
            lineWidth: 3
        });
    }

    /**
     * Check for collisions between entities
     */
    checkCollisions() {
        const playerBox = this.player.getCollisionBox();

        // Check enemy collisions
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const enemy = this.enemies[i];
            const enemyBox = enemy.getCollisionBox();

            if (Utils.checkCollision(playerBox, enemyBox)) {
                if (!this.player.isGrounded()) {
                    // Player jumped on enemy
                    this.engine.playSound('hitSound', { volume: 0.5 });
                    enemy.deactivate();
                    this.enemies.splice(i, 1);

                    // Extra jump and score multiplier
                    this.player.jump();
                    if (this.player.extraJump) {
                        this.scoreMultiplier += 1;
                    } else {
                        this.player.extraJump = true;
                        this.scoreMultiplier = 1;
                    }

                    // Update score
                    const points = CONSTANTS.SCORE.ENEMY_KILL * this.scoreMultiplier;
                    this.updateScore(points);
                    this.player.showScoreText(this.scoreMultiplier > 1 ? `+${points}` : '+10', 1);
                } else {
                    // Player hit enemy while on ground - game over
                    this.engine.playSound('hurtSound', { volume: 0.5 });
                    this.engine.setState(CONSTANTS.STATES.GAME_OVER);
                }
            }
        }

        // Check diamond collisions
        for (let i = this.diamonds.length - 1; i >= 0; i--) {
            const diamond = this.diamonds[i];
            const diamondBox = diamond.getCollisionBox();

            if (Utils.checkCollision(playerBox, diamondBox)) {
                // Collect diamond
                this.engine.playSound('diamondSound', { volume: 0.5 });
                diamond.deactivate();
                this.diamonds.splice(i, 1);

                // Update score
                this.updateScore(CONSTANTS.SCORE.DIAMOND_COLLECT);
                this.player.showScoreText('+5', 1);
            }
        }
    }

    /**
     * Spawn an enemy
     * @param {number} xOffset - Optional X offset for positioning
     * @param {boolean} isGroupSpawn - Whether this is part of a group spawn
     */
    spawnEnemy(xOffset = 0, isGroupSpawn = false) {
        const x = this.engine.width + 100 + xOffset;
        const y = this.background.getGroundY() - 110; // Adjust to be on the ground
        const enemy = new Turtle(this.engine, x, y, this.gameSpeed);
        this.enemies.push(enemy);

        // Only consider spawning additional enemies if this isn't already a group spawn
        if (!isGroupSpawn) {
            // Calculate group spawn chance based on game speed and score
            // Higher chance at higher speeds/scores for more challenge
            const baseGroupChance = 0.4 + (this.score / 1000) * 0.2; // Increases with score up to +20%
            const speedFactor = 1 + (this.gameSpeed - 300) / 300; // Increases with speed
            const groupChance = Math.min(0.8, baseGroupChance * speedFactor); // Cap at 80% chance

            // Determine pattern type based on random chance
            if (Math.random() < groupChance && this.enemies.length < 6) {
                const patternType = Math.random();

                if (patternType < 0.4) {
                    // Pattern 1: Two enemies close together (challenging jump)
                    setTimeout(() => this.spawnEnemy(250, true), 150);
                } else if (patternType < 0.7) {
                    // Pattern 2: Three enemies in sequence (requires timing)
                    setTimeout(() => this.spawnEnemy(300, true), 200);
                    setTimeout(() => this.spawnEnemy(600, true), 400);
                } else {
                    // Pattern 3: Two enemies with larger gap (allows landing between)
                    setTimeout(() => this.spawnEnemy(450, true), 300);
                }
            }
        }
    }

    /**
     * Spawn a diamond
     */
    spawnDiamond() {
        const x = this.engine.width + 100;
        const y = Utils.random(300, this.background.getGroundY() - 150);
        const diamond = new Diamond(this.engine, x, y, this.gameSpeed);
        this.diamonds.push(diamond);
    }

    /**
     * Get a random spawn time for enemies
     * @returns {number} - Spawn time in seconds
     */
    getRandomSpawnTime() {
        return Utils.random(
            CONSTANTS.GAME.SPAWN_INTERVAL_MIN,
            CONSTANTS.GAME.SPAWN_INTERVAL_MAX
        );
    }

    /**
     * Increase the game speed
     */
    increaseSpeed() {
        // Calculate a speed multiplier based on current score to accelerate speed increases
        const scoreMultiplier = 1 + (this.score / 500); // Speed increases faster as score grows

        // Apply the speed increment with the multiplier
        this.gameSpeed += CONSTANTS.GAME.SPEED_INCREMENT * scoreMultiplier;
        this.backgroundSpeed += CONSTANTS.GAME.BACKGROUND_SPEED_INCREMENT * scoreMultiplier;
        this.background.increaseSpeed(CONSTANTS.GAME.BACKGROUND_SPEED_INCREMENT * scoreMultiplier);

        // Cap the speed at a reasonable maximum if needed
        if (this.gameSpeed > 1500) {
            this.gameSpeed = 1500;
        }
        if (this.backgroundSpeed > 500) {
            this.backgroundSpeed = 500;
        }
    }

    /**
     * Update the score
     * @param {number} amount - Amount to add to score
     */
    updateScore(amount) {
        this.score += amount;

        // Call the score update callback if available (for Laravel integration)
        const setGameScore = this.engine.getVariable('setGameScore');
        if (setGameScore && typeof setGameScore === 'function') {
            setGameScore(this.score);
        }
    }
}
