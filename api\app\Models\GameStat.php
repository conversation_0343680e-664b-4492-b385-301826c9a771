<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GameStat extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id',
        'game_id',
        'high_score',
        'total_score',
        'plays'
    ];

    /**
     * Get the user that owns these game stats.
     */
    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }
}
