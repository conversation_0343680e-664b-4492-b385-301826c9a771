/**
 * Player Entity
 * Represents the player character
 */
class Player {
    /**
     * @param {Engine} engine - Game engine
     * @param {number} x - X position
     * @param {number} y - Y position
     */
    constructor(engine, x, y) {
        this.engine = engine;
        this.x = x;
        this.y = y;
        this.width = 44 * 3; // Sprite width * scale
        this.height = 43 * 3; // Sprite height * scale
        this.scale = 3;

        // Physics
        this.velocityY = 0;
        this.gravity = CONSTANTS.PHYSICS.GRAVITY;
        this.jumpForce = CONSTANTS.PHYSICS.JUMP_FORCE;
        this.grounded = false;
        this.extraJump = false;

        // Animation
        this.runSprite = engine.createSprite('player', { animName: 'player-run' });
        this.jumpSprite = engine.createSprite('player', { animName: 'player-jump' });
        this.currentSprite = this.runSprite;

        // Only play animation if sprite is available
        if (this.currentSprite) {
            this.currentSprite.play('run');
        } else {
            console.error('Failed to create player sprite');
        }

        // Score display
        this.scoreCollectText = '';
        this.scoreCollectTimer = 0;

        // State
        this.state = CONSTANTS.PLAYER.RUNNING;
    }

    /**
     * Update the player
     * @param {number} deltaTime - Time since last update
     * @param {InputHandler} input - Input handler
     * @param {number} groundY - Ground Y position
     */
    update(deltaTime, input, groundY) {
        // Apply gravity
        this.velocityY += this.gravity * deltaTime;
        this.y += this.velocityY * deltaTime;

        // Check if on ground
        if (this.y >= groundY - this.height / 1) {
            this.y = groundY - this.height / 1;
            this.velocityY = 0;
            this.grounded = true;
            this.extraJump = false;

            if (this.state === CONSTANTS.PLAYER.JUMPING) {
                this.state = CONSTANTS.PLAYER.RUNNING;
                this.currentSprite = this.runSprite;
                this.currentSprite.play('run');
            }
        } else {
            this.grounded = false;
        }

        // Handle jump input
        if (input.isJumpPressed()) {
            if (this.grounded) {
                this.jump();
                this.engine.playSound('jumpSound', { volume: 0.2 });
            }
        }

        // Update current sprite
        if (this.currentSprite) {
            this.currentSprite.update(deltaTime);
        }

        // Update score collect text timer
        if (this.scoreCollectText) {
            this.scoreCollectTimer -= deltaTime;
            if (this.scoreCollectTimer <= 0) {
                this.scoreCollectText = '';
            }
        }
    }

    /**
     * Make the player jump
     */
    jump() {
        this.velocityY = -this.jumpForce;
        this.grounded = false;
        this.state = CONSTANTS.PLAYER.JUMPING;
        this.currentSprite = this.jumpSprite;

        if (this.currentSprite) {
            this.currentSprite.play('jump');
        }
    }

    /**
     * Check if the player is on the ground
     * @returns {boolean} - True if grounded
     */
    isGrounded() {
        return this.grounded;
    }

    /**
     * Display score collect text
     * @param {string} text - Text to display
     * @param {number} duration - Duration in seconds
     */
    showScoreText(text, duration = 1) {
        this.scoreCollectText = text;
        this.scoreCollectTimer = duration;
    }

    /**
     * Get the player's collision box
     * @returns {Object} - Collision box
     */
    getCollisionBox() {
        return {
            x: this.x - this.width / 2 + 20, // Adjust for better collision
            y: this.y - this.height / 2 + 10,
            width: this.width - 40,
            height: this.height - 20
        };
    }

    /**
     * Render the player
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     */
    render(ctx) {
        // Draw the player sprite
        if (this.currentSprite) {
            this.currentSprite.draw(ctx, this.x, this.y, this.scale);
        } else {
            // Fallback rendering if sprite is not available
            ctx.fillStyle = 'blue';
            ctx.fillRect(this.x - this.width/2, this.y - this.height/2, this.width, this.height);
        }

        // Draw score collect text
        if (this.scoreCollectText) {
            Utils.drawText(ctx, this.scoreCollectText, this.x, this.y - 50, {
                fillStyle: '#FFFF00',
                font: 'bold 24px Arial'
            });
        }

        // Draw collision box in debug mode
        if (this.engine.debug) {
            const box = this.getCollisionBox();
            ctx.strokeStyle = 'red';
            ctx.lineWidth = 2;
            ctx.strokeRect(box.x, box.y, box.width, box.height);
        }
    }
}
