<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the 'cache' table
        Schema::create('cache', function (Blueprint $table) {
            $table->string('key')->primary();
            $table->text('value');
            $table->integer('expiration')->unsigned(); // Ensure non-negative values
        });

        // Add CHECK constraint for 'expiration' in the 'cache' table
        DB::statement('ALTER TABLE cache ADD CONSTRAINT chk_cache_expiration_non_negative CHECK (expiration >= 0)');

        // Create the 'cache_locks' table
        Schema::create('cache_locks', function (Blueprint $table) {
            $table->string('key')->primary();
            $table->string('owner');
            $table->integer('expiration')->unsigned(); // Ensure non-negative values
        });

        // Add CHECK constraint for 'expiration' in the 'cache_locks' table
        DB::statement('ALTER TABLE cache_locks ADD CONSTRAINT chk_cache_locks_expiration_non_negative CHECK (expiration >= 0)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the CHECK constraints
        DB::statement('ALTER TABLE cache DROP CONSTRAINT IF EXISTS chk_cache_expiration_non_negative');
        DB::statement('ALTER TABLE cache_locks DROP CONSTRAINT IF EXISTS chk_cache_locks_expiration_non_negative');

        // Drop the tables
        Schema::dropIfExists('cache');
        Schema::dropIfExists('cache_locks');
    }
};