<?php

namespace App\Http\Controllers;

use App\Models\UserAchievementPoint;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AchievementPointController extends Controller
{
    /**
     * Award achievement points to a user
     */
    public function awardPoints(Request $request)
    {
        // Validate admin access
        if (!Auth::user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }
        
        $request->validate([
            'telegram_user_id' => 'required|exists:telegram_users,id',
            'amount' => 'required|integer|min:1',
            'source' => 'required|string',
            'source_id' => 'nullable|integer',
            'description' => 'nullable|string'
        ]);
        
        $userId = $request->telegram_user_id;
        $amount = $request->amount;
        $source = $request->source;
        $sourceId = $request->source_id;
        $description = $request->description;
        
        // Get or create user achievement points record
        $achievementPoints = UserAchievementPoint::firstOrCreate(
            ['telegram_user_id' => $userId],
            ['total_earned' => 0, 'total_spent' => 0]
        );
        
        // Award points
        DB::transaction(function () use ($achievementPoints, $userId, $amount, $source, $sourceId, $description) {
            // Update achievement points
            $achievementPoints->total_earned += $amount;
            $achievementPoints->save();
            
            // Record transaction
            DB::table('achievement_point_transactions')->insert([
                'telegram_user_id' => $userId,
                'amount' => $amount,
                'type' => 'earn',
                'source' => $source,
                'source_id' => $sourceId,
                'description' => $description,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        });
        
        return response()->json([
            'success' => true,
            'message' => 'Achievement points awarded successfully',
            'total_earned' => $achievementPoints->total_earned,
            'total_spent' => $achievementPoints->total_spent,
            'available_points' => $achievementPoints->total_earned - $achievementPoints->total_spent
        ]);
    }
    
    /**
     * Award achievement points for completing a mission
     */
    public function awardMissionPoints(Request $request)
    {
        $request->validate([
            'mission_id' => 'required|exists:missions,id'
        ]);
        
        $user = Auth::user();
        $missionId = $request->mission_id;
        
        // Check if the mission exists and get its reward
        $mission = DB::table('missions')->find($missionId);
        
        if (!$mission) {
            return response()->json([
                'success' => false,
                'message' => 'Mission not found'
            ], 404);
        }
        
        // Check if the user has completed the mission
        $completed = DB::table('telegram_user_missions')
            ->where('telegram_user_id', $user->id)
            ->where('mission_id', $missionId)
            ->where('is_completed', true)
            ->exists();
        
        if (!$completed) {
            return response()->json([
                'success' => false,
                'message' => 'Mission not completed'
            ], 400);
        }
        
        // Check if the user has already been awarded points for this mission
        $alreadyAwarded = DB::table('achievement_point_transactions')
            ->where('telegram_user_id', $user->id)
            ->where('source', 'mission_complete')
            ->where('source_id', $missionId)
            ->exists();
        
        if ($alreadyAwarded) {
            return response()->json([
                'success' => false,
                'message' => 'Points already awarded for this mission'
            ], 400);
        }
        
        // Award points
        $points = 5; // Default points for mission completion
        
        // Get or create user achievement points record
        $achievementPoints = UserAchievementPoint::firstOrCreate(
            ['telegram_user_id' => $user->id],
            ['total_earned' => 0, 'total_spent' => 0]
        );
        
        DB::transaction(function () use ($achievementPoints, $user, $missionId, $mission, $points) {
            // Update achievement points
            $achievementPoints->total_earned += $points;
            $achievementPoints->save();
            
            // Record transaction
            DB::table('achievement_point_transactions')->insert([
                'telegram_user_id' => $user->id,
                'amount' => $points,
                'type' => 'earn',
                'source' => 'mission_complete',
                'source_id' => $missionId,
                'description' => 'Completed mission: ' . $mission->name,
                'created_at' => now(),
                'updated_at' => now()
            ]);
        });
        
        return response()->json([
            'success' => true,
            'message' => 'Achievement points awarded successfully',
            'points_awarded' => $points,
            'available_points' => $achievementPoints->total_earned - $achievementPoints->total_spent
        ]);
    }
    
    /**
     * Award achievement points for reaching a tap milestone
     */
    public function awardTapMilestonePoints(Request $request)
    {
        $user = Auth::user();
        
        // Get user's tap stats
        $tapStats = DB::table('tap_stats')
            ->where('telegram_user_id', $user->id)
            ->first();
        
        if (!$tapStats) {
            return response()->json([
                'success' => false,
                'message' => 'Tap stats not found'
            ], 404);
        }
        
        // Define milestones and points
        $milestones = [
            1000 => 5,
            5000 => 10,
            10000 => 15,
            50000 => 25,
            100000 => 50,
            500000 => 100,
            1000000 => 200
        ];
        
        // Check if the user has reached a milestone
        $totalTaps = $tapStats->total_taps;
        $awardedPoints = 0;
        $reachedMilestone = null;
        
        foreach ($milestones as $milestone => $points) {
            if ($totalTaps >= $milestone) {
                // Check if the user has already been awarded points for this milestone
                $alreadyAwarded = DB::table('achievement_point_transactions')
                    ->where('telegram_user_id', $user->id)
                    ->where('source', 'tap_milestone')
                    ->where('source_id', $milestone)
                    ->exists();
                
                if (!$alreadyAwarded) {
                    $awardedPoints = $points;
                    $reachedMilestone = $milestone;
                    break;
                }
            }
        }
        
        if (!$reachedMilestone) {
            return response()->json([
                'success' => false,
                'message' => 'No new milestone reached'
            ], 400);
        }
        
        // Get or create user achievement points record
        $achievementPoints = UserAchievementPoint::firstOrCreate(
            ['telegram_user_id' => $user->id],
            ['total_earned' => 0, 'total_spent' => 0]
        );
        
        // Award points
        DB::transaction(function () use ($achievementPoints, $user, $awardedPoints, $reachedMilestone) {
            // Update achievement points
            $achievementPoints->total_earned += $awardedPoints;
            $achievementPoints->save();
            
            // Record transaction
            DB::table('achievement_point_transactions')->insert([
                'telegram_user_id' => $user->id,
                'amount' => $awardedPoints,
                'type' => 'earn',
                'source' => 'tap_milestone',
                'source_id' => $reachedMilestone,
                'description' => 'Reached tap milestone: ' . number_format($reachedMilestone) . ' taps',
                'created_at' => now(),
                'updated_at' => now()
            ]);
        });
        
        return response()->json([
            'success' => true,
            'message' => 'Achievement points awarded successfully',
            'milestone' => $reachedMilestone,
            'points_awarded' => $awardedPoints,
            'available_points' => $achievementPoints->total_earned - $achievementPoints->total_spent
        ]);
    }
}
