# React Components Implementation - Part 2: Pet Interaction Components

## Overview
This document covers the implementation of pet interaction components, including the interaction modal, evolution indicators, and pet management features.

## Implementation Time: 2-3 days
## Complexity: Medium-High
## Dependencies: Part 1 components, API integration

## Pet Interaction Components

### PetInteractionModal Component
```tsx
// File: battlx/src/components/pets/PetInteractionModal.tsx

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Pet } from '../../types/pet';
import { usePetStore } from '../../stores/petStore';
import { useUserStore } from '../../stores/userStore';
import InteractionButton from './InteractionButton';
import PetStats from './PetStats';
import PetEvolutionPanel from './PetEvolutionPanel';
import RewardDisplay from './RewardDisplay';

interface PetInteractionModalProps {
  pet: Pet;
  isOpen: boolean;
  onClose: () => void;
}

const PetInteractionModal: React.FC<PetInteractionModalProps> = ({
  pet,
  isOpen,
  onClose
}) => {
  const { interactWithPet, updatePetNickname, togglePetFavorite } = usePetStore();
  const { user } = useUserStore();
  
  const [activeTab, setActiveTab] = useState<'interact' | 'stats' | 'evolve'>('interact');
  const [isEditing, setIsEditing] = useState(false);
  const [nickname, setNickname] = useState(pet.nickname || '');
  const [lastRewards, setLastRewards] = useState<any>(null);
  const [showRewards, setShowRewards] = useState(false);

  useEffect(() => {
    setNickname(pet.nickname || '');
  }, [pet.nickname]);

  const handleInteraction = async (interactionType: string) => {
    try {
      const result = await interactWithPet(pet.id, interactionType);
      
      if (result.rewards) {
        setLastRewards(result.rewards);
        setShowRewards(true);
        
        // Hide rewards after 3 seconds
        setTimeout(() => setShowRewards(false), 3000);
      }
    } catch (error) {
      console.error('Interaction failed:', error);
    }
  };

  const handleNicknameUpdate = async () => {
    try {
      await updatePetNickname(pet.id, nickname);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update nickname:', error);
      setNickname(pet.nickname || '');
    }
  };

  const handleToggleFavorite = async () => {
    try {
      await togglePetFavorite(pet.id);
    } catch (error) {
      console.error('Failed to toggle favorite:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="modal-overlay"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="pet-interaction-modal"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Modal Header */}
          <div className="modal-header">
            <div className="pet-header-info">
              <img 
                src={pet.current_image} 
                alt={pet.display_name}
                className="pet-header-image"
              />
              
              <div className="pet-header-details">
                <div className="pet-name-section">
                  {isEditing ? (
                    <div className="nickname-edit">
                      <input
                        type="text"
                        value={nickname}
                        onChange={(e) => setNickname(e.target.value)}
                        maxLength={50}
                        className="nickname-input"
                        autoFocus
                      />
                      <button onClick={handleNicknameUpdate} className="save-btn">
                        ✓
                      </button>
                      <button onClick={() => setIsEditing(false)} className="cancel-btn">
                        ✕
                      </button>
                    </div>
                  ) : (
                    <div className="pet-name-display">
                      <h2>{pet.display_name}</h2>
                      <button 
                        onClick={() => setIsEditing(true)}
                        className="edit-name-btn"
                      >
                        ✏️
                      </button>
                    </div>
                  )}
                </div>
                
                <div className="pet-meta">
                  <span className={`rarity rarity-${pet.rarity}`}>
                    {pet.rarity.toUpperCase()}
                  </span>
                  <span className="category">{pet.category}</span>
                  <span className="level">Level {pet.level}</span>
                </div>
              </div>
            </div>

            <div className="modal-actions">
              <button
                onClick={handleToggleFavorite}
                className={`favorite-btn ${pet.is_favorite ? 'active' : ''}`}
              >
                {pet.is_favorite ? '❤️' : '🤍'}
              </button>
              
              <button onClick={onClose} className="close-btn">
                ✕
              </button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="tab-navigation">
            <button
              className={`tab ${activeTab === 'interact' ? 'active' : ''}`}
              onClick={() => setActiveTab('interact')}
            >
              Interact
            </button>
            <button
              className={`tab ${activeTab === 'stats' ? 'active' : ''}`}
              onClick={() => setActiveTab('stats')}
            >
              Stats
            </button>
            {pet.can_evolve && (
              <button
                className={`tab ${activeTab === 'evolve' ? 'active' : ''}`}
                onClick={() => setActiveTab('evolve')}
              >
                Evolve ⭐
              </button>
            )}
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'interact' && (
              <div className="interact-panel">
                <div className="happiness-display">
                  <div className="happiness-bar-large">
                    <div className="happiness-label">
                      <span>😊 Happiness</span>
                      <span>{pet.happiness_percentage}%</span>
                    </div>
                    <div className="happiness-bar">
                      <motion.div
                        className="happiness-fill"
                        initial={{ width: 0 }}
                        animate={{ width: `${pet.happiness_percentage}%` }}
                        style={{
                          backgroundColor: pet.happiness_percentage > 70 ? '#4ade80' :
                                         pet.happiness_percentage > 30 ? '#fbbf24' : '#ef4444'
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="interaction-buttons">
                  <InteractionButton
                    type="feed"
                    available={pet.can_feed}
                    energyCost={5}
                    userEnergy={user?.available_energy || 0}
                    onInteract={() => handleInteraction('feed')}
                    description="Feed your pet to increase happiness"
                  />
                  
                  <InteractionButton
                    type="play"
                    available={pet.can_play}
                    energyCost={10}
                    userEnergy={user?.available_energy || 0}
                    onInteract={() => handleInteraction('play')}
                    description="Play with your pet for better rewards"
                  />
                  
                  <InteractionButton
                    type="pet"
                    available={pet.can_pet}
                    energyCost={2}
                    userEnergy={user?.available_energy || 0}
                    onInteract={() => handleInteraction('pet')}
                    description="Pet your companion for a happiness boost"
                  />
                </div>

                {pet.interactions_today > 0 && (
                  <div className="daily-interactions">
                    <span>Today's interactions: {pet.interactions_today}</span>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'stats' && (
              <PetStats pet={pet} />
            )}

            {activeTab === 'evolve' && pet.can_evolve && (
              <PetEvolutionPanel pet={pet} />
            )}
          </div>

          {/* Reward Display */}
          <AnimatePresence>
            {showRewards && lastRewards && (
              <RewardDisplay
                rewards={lastRewards}
                onClose={() => setShowRewards(false)}
              />
            )}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default PetInteractionModal;
```

### InteractionButton Component
```tsx
// File: battlx/src/components/pets/InteractionButton.tsx

import React from 'react';
import { motion } from 'framer-motion';

interface InteractionButtonProps {
  type: 'feed' | 'play' | 'pet';
  available: boolean;
  energyCost: number;
  userEnergy: number;
  onInteract: () => void;
  description: string;
}

const InteractionButton: React.FC<InteractionButtonProps> = ({
  type,
  available,
  energyCost,
  userEnergy,
  onInteract,
  description
}) => {
  const icons = {
    feed: '🍖',
    play: '🎾',
    pet: '❤️'
  };

  const labels = {
    feed: 'Feed',
    play: 'Play',
    pet: 'Pet'
  };

  const canAfford = userEnergy >= energyCost;
  const isDisabled = !available || !canAfford;

  const getDisabledReason = () => {
    if (!available) return 'On cooldown';
    if (!canAfford) return 'Not enough energy';
    return '';
  };

  return (
    <motion.button
      className={`interaction-button ${type} ${isDisabled ? 'disabled' : ''}`}
      onClick={isDisabled ? undefined : onInteract}
      whileHover={isDisabled ? {} : { scale: 1.05 }}
      whileTap={isDisabled ? {} : { scale: 0.95 }}
      disabled={isDisabled}
    >
      <div className="button-icon">{icons[type]}</div>
      
      <div className="button-content">
        <div className="button-label">{labels[type]}</div>
        <div className="button-cost">⚡ {energyCost}</div>
      </div>

      <div className="button-description">{description}</div>

      {isDisabled && (
        <div className="disabled-overlay">
          <span>{getDisabledReason()}</span>
        </div>
      )}
    </motion.button>
  );
};

export default InteractionButton;
```

### PetStats Component
```tsx
// File: battlx/src/components/pets/PetStats.tsx

import React from 'react';
import { motion } from 'framer-motion';
import { Pet } from '../../types/pet';

interface PetStatsProps {
  pet: Pet;
}

const PetStats: React.FC<PetStatsProps> = ({ pet }) => {
  const stats = [
    {
      label: 'Level',
      value: pet.level,
      icon: '⭐',
      color: '#fbbf24'
    },
    {
      label: 'Experience',
      value: `${pet.experience}/${pet.experience + pet.experience_to_next_level}`,
      icon: '📈',
      color: '#3b82f6',
      progress: (pet.experience / (pet.experience + pet.experience_to_next_level)) * 100
    },
    {
      label: 'Happiness',
      value: `${pet.happiness}/${pet.template?.max_happiness || 100}`,
      icon: '😊',
      color: pet.happiness_percentage > 70 ? '#4ade80' : 
             pet.happiness_percentage > 30 ? '#fbbf24' : '#ef4444',
      progress: pet.happiness_percentage
    },
    {
      label: 'Evolution Stage',
      value: `${pet.evolution_stage + 1}/4`,
      icon: '🔄',
      color: '#8b5cf6'
    }
  ];

  return (
    <div className="pet-stats">
      <div className="stats-grid">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.label}
            className="stat-item"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <div className="stat-header">
              <span className="stat-icon">{stat.icon}</span>
              <span className="stat-label">{stat.label}</span>
            </div>
            
            <div className="stat-value" style={{ color: stat.color }}>
              {stat.value}
            </div>

            {stat.progress !== undefined && (
              <div className="stat-progress">
                <div className="progress-bar">
                  <motion.div
                    className="progress-fill"
                    initial={{ width: 0 }}
                    animate={{ width: `${stat.progress}%` }}
                    transition={{ duration: 0.8, delay: index * 0.1 + 0.3 }}
                    style={{ backgroundColor: stat.color }}
                  />
                </div>
                <span className="progress-text">{stat.progress.toFixed(1)}%</span>
              </div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Additional Info */}
      <div className="additional-stats">
        <div className="stat-row">
          <span>Category:</span>
          <span className="stat-value">{pet.category}</span>
        </div>
        
        <div className="stat-row">
          <span>Rarity:</span>
          <span className={`stat-value rarity-${pet.rarity}`}>
            {pet.rarity.toUpperCase()}
          </span>
        </div>
        
        <div className="stat-row">
          <span>Acquired:</span>
          <span className="stat-value">
            {new Date(pet.created_at).toLocaleDateString()}
          </span>
        </div>

        {pet.last_interaction && (
          <div className="stat-row">
            <span>Last Interaction:</span>
            <span className="stat-value">
              {new Date(pet.last_interaction).toLocaleString()}
            </span>
          </div>
        )}
      </div>

      {/* Mystery Box Unlocks */}
      {pet.mystery_box_unlocks && pet.mystery_box_unlocks.length > 0 && (
        <div className="unlocked-boxes">
          <h4>Mystery Boxes Unlocked:</h4>
          <div className="box-list">
            {pet.mystery_box_unlocks.map((boxType, index) => (
              <span key={index} className="box-tag">
                📦 {boxType.replace('_', ' ').toUpperCase()}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Collectible Reward */}
      {pet.collectible_reward && (
        <div className="collectible-reward">
          <h4>Collectible Reward:</h4>
          <span className="reward-tag">
            💎 {pet.collectible_reward}
          </span>
        </div>
      )}
    </div>
  );
};

export default PetStats;
```

### PetEvolutionPanel Component
```tsx
// File: battlx/src/components/pets/PetEvolutionPanel.tsx

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Pet } from '../../types/pet';
import { usePetStore } from '../../stores/petStore';

interface PetEvolutionPanelProps {
  pet: Pet;
}

const PetEvolutionPanel: React.FC<PetEvolutionPanelProps> = ({ pet }) => {
  const { evolvePet } = usePetStore();
  const [isEvolving, setIsEvolving] = useState(false);

  const handleEvolve = async () => {
    if (!pet.can_evolve) return;

    setIsEvolving(true);
    try {
      await evolvePet(pet.id);
    } catch (error) {
      console.error('Evolution failed:', error);
    } finally {
      setIsEvolving(false);
    }
  };

  const evolutionStages = [
    { stage: 0, name: 'Base Form', level: 1 },
    { stage: 1, name: 'First Evolution', level: 10 },
    { stage: 2, name: 'Second Evolution', level: 25 },
    { stage: 3, name: 'Final Evolution', level: 50 }
  ];

  return (
    <div className="pet-evolution-panel">
      <div className="evolution-header">
        <h3>Evolution Path</h3>
        <p>Transform your pet to unlock new appearances and abilities!</p>
      </div>

      <div className="evolution-stages">
        {evolutionStages.map((stage, index) => (
          <motion.div
            key={stage.stage}
            className={`evolution-stage ${
              pet.evolution_stage >= stage.stage ? 'completed' : 
              pet.evolution_stage + 1 === stage.stage && pet.can_evolve ? 'available' : 'locked'
            }`}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <div className="stage-image">
              {pet.template?.evolution_images?.[stage.stage] ? (
                <img 
                  src={pet.template.evolution_images[stage.stage]} 
                  alt={`${pet.name} ${stage.name}`}
                />
              ) : (
                <div className="placeholder-image">
                  {pet.evolution_stage >= stage.stage ? '✨' : '🔒'}
                </div>
              )}
            </div>

            <div className="stage-info">
              <div className="stage-name">{stage.name}</div>
              <div className="stage-requirement">Level {stage.level}</div>
              
              {pet.evolution_stage >= stage.stage && (
                <div className="stage-status completed">✓ Unlocked</div>
              )}
              
              {pet.evolution_stage + 1 === stage.stage && pet.can_evolve && (
                <div className="stage-status available">⭐ Ready!</div>
              )}
              
              {pet.evolution_stage + 1 === stage.stage && !pet.can_evolve && (
                <div className="stage-status progress">
                  Level {pet.level}/{stage.level}
                </div>
              )}
            </div>

            {index < evolutionStages.length - 1 && (
              <div className="evolution-arrow">→</div>
            )}
          </motion.div>
        ))}
      </div>

      {pet.can_evolve && (
        <motion.div
          className="evolution-action"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <button
            className="evolve-button"
            onClick={handleEvolve}
            disabled={isEvolving}
          >
            {isEvolving ? (
              <>
                <span className="loading-spinner" />
                Evolving...
              </>
            ) : (
              <>
                ⭐ Evolve Now!
              </>
            )}
          </button>
          
          <p className="evolution-description">
            Evolution will increase your pet's happiness and unlock new visual appearance!
          </p>
        </motion.div>
      )}

      {!pet.can_evolve && (
        <div className="evolution-requirements">
          <h4>Evolution Requirements:</h4>
          <p>
            Your pet needs to reach level{' '}
            {pet.template?.evolution_levels?.[pet.evolution_stage + 1] || 'MAX'} to evolve.
          </p>
          <p>Current level: {pet.level}</p>
          <p>Experience needed: {pet.experience_to_next_level}</p>
        </div>
      )}
    </div>
  );
};

export default PetEvolutionPanel;
```

### RewardDisplay Component
```tsx
// File: battlx/src/components/pets/RewardDisplay.tsx

import React from 'react';
import { motion } from 'framer-motion';

interface RewardDisplayProps {
  rewards: {
    coins?: number;
    experience?: number;
    happiness?: number;
    materials?: number;
    collectible?: any;
    multiplier_applied?: number;
  };
  onClose: () => void;
}

const RewardDisplay: React.FC<RewardDisplayProps> = ({ rewards, onClose }) => {
  const rewardItems = [];

  if (rewards.coins > 0) {
    rewardItems.push({
      icon: '🪙',
      label: 'Coins',
      value: `+${rewards.coins}`,
      color: '#fbbf24'
    });
  }

  if (rewards.experience > 0) {
    rewardItems.push({
      icon: '📈',
      label: 'Experience',
      value: `+${rewards.experience}`,
      color: '#3b82f6'
    });
  }

  if (rewards.happiness > 0) {
    rewardItems.push({
      icon: '😊',
      label: 'Happiness',
      value: `+${rewards.happiness}`,
      color: '#4ade80'
    });
  }

  if (rewards.materials > 0) {
    rewardItems.push({
      icon: '🔧',
      label: 'Materials',
      value: `+${rewards.materials}`,
      color: '#8b5cf6'
    });
  }

  if (rewards.collectible) {
    rewardItems.push({
      icon: '💎',
      label: 'Collectible',
      value: rewards.collectible.name,
      color: '#ef4444'
    });
  }

  return (
    <motion.div
      className="reward-display"
      initial={{ opacity: 0, scale: 0.8, y: 50 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.8, y: -50 }}
      transition={{ type: 'spring', damping: 15 }}
    >
      <div className="reward-header">
        <h3>Rewards Earned!</h3>
        {rewards.multiplier_applied > 1 && (
          <div className="multiplier-badge">
            {rewards.multiplier_applied.toFixed(1)}x Bonus!
          </div>
        )}
      </div>

      <div className="reward-items">
        {rewardItems.map((item, index) => (
          <motion.div
            key={item.label}
            className="reward-item"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            style={{ borderColor: item.color }}
          >
            <span className="reward-icon">{item.icon}</span>
            <div className="reward-info">
              <div className="reward-label">{item.label}</div>
              <div className="reward-value" style={{ color: item.color }}>
                {item.value}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      <button className="close-rewards" onClick={onClose}>
        Awesome! ✨
      </button>
    </motion.div>
  );
};

export default RewardDisplay;
```

## Acceptance Criteria
- [ ] Pet interaction modal fully functional
- [ ] All interaction types working correctly
- [ ] Pet stats display accurate information
- [ ] Evolution system operational
- [ ] Reward display shows correctly
- [ ] Nickname editing functional
- [ ] Favorite toggle working

## Next Steps
1. Continue with Part 3: Pet Shop Components
2. Implement mystery box components
3. Create collection page components
4. Add home screen integration

## Troubleshooting
- Test interaction cooldowns and limits
- Verify reward calculations and display
- Check evolution requirements and progression
- Test modal responsiveness on mobile
- Ensure proper error handling for failed interactions
