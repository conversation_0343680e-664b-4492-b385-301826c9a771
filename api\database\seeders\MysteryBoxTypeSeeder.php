<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MysteryBoxTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $mysteryBoxTypes = [
            // Shadow Boxes
            [
                'box_type' => 'common_shadow',
                'display_name' => 'Shadow Mystery Box',
                'rarity' => 'common',
                'category' => 'shadow',
                'description' => 'A mysterious box emanating dark energy, containing shadow realm collectibles.',
                'image_url' => '/images/boxes/shadow_common.png',
                'animation_url' => '/images/boxes/shadow_common_anim.gif',
                'coin_cost' => 5000,
                'gem_cost' => 10,
                'achievement_points_cost' => 100,
                'possible_rewards' => json_encode(['shadow_essence', 'shadow_crystal', 'dark_shard']),
                'reward_weights' => json_encode([60, 30, 10]),
                'guaranteed_rarity_level' => 1,
                'unlock_requirements' => json_encode(['shadow_wolf']),
                'sort_order' => 1
            ],
            [
                'box_type' => 'rare_shadow',
                'display_name' => 'Rare Shadow Box',
                'rarity' => 'rare',
                'category' => 'shadow',
                'description' => 'A box wreathed in shadows, containing rare shadow artifacts.',
                'image_url' => '/images/boxes/shadow_rare.png',
                'animation_url' => '/images/boxes/shadow_rare_anim.gif',
                'coin_cost' => 15000,
                'gem_cost' => 50,
                'achievement_points_cost' => 300,
                'possible_rewards' => json_encode(['raven_feather', 'shadow_crystal', 'void_fragment']),
                'reward_weights' => json_encode([50, 35, 15]),
                'guaranteed_rarity_level' => 2,
                'unlock_requirements' => json_encode(['dark_raven']),
                'sort_order' => 2
            ],
            [
                'box_type' => 'epic_shadow',
                'display_name' => 'Epic Shadow Box',
                'rarity' => 'epic',
                'category' => 'shadow',
                'description' => 'A box pulsing with void energy, containing epic shadow relics.',
                'image_url' => '/images/boxes/shadow_epic.png',
                'animation_url' => '/images/boxes/shadow_epic_anim.gif',
                'coin_cost' => 50000,
                'gem_cost' => 200,
                'achievement_points_cost' => 1000,
                'possible_rewards' => json_encode(['void_crystal', 'shadow_orb', 'nightmare_fragment']),
                'reward_weights' => json_encode([40, 40, 20]),
                'guaranteed_rarity_level' => 3,
                'unlock_requirements' => json_encode(['void_stalker']),
                'sort_order' => 3
            ],

            // Undead Boxes
            [
                'box_type' => 'common_undead',
                'display_name' => 'Undead Mystery Box',
                'rarity' => 'common',
                'category' => 'undead',
                'description' => 'A bone-carved box containing remnants from the undead realm.',
                'image_url' => '/images/boxes/undead_common.png',
                'animation_url' => '/images/boxes/undead_common_anim.gif',
                'coin_cost' => 5000,
                'gem_cost' => 10,
                'achievement_points_cost' => 100,
                'possible_rewards' => json_encode(['bone_fragment', 'skull_dust', 'grave_dirt']),
                'reward_weights' => json_encode([60, 30, 10]),
                'guaranteed_rarity_level' => 1,
                'unlock_requirements' => json_encode(['skeleton_minion']),
                'sort_order' => 4
            ],
            [
                'box_type' => 'rare_undead',
                'display_name' => 'Rare Undead Box',
                'rarity' => 'rare',
                'category' => 'undead',
                'description' => 'A coffin-shaped box containing rare necromantic artifacts.',
                'image_url' => '/images/boxes/undead_rare.png',
                'animation_url' => '/images/boxes/undead_rare_anim.gif',
                'coin_cost' => 15000,
                'gem_cost' => 50,
                'achievement_points_cost' => 300,
                'possible_rewards' => json_encode(['decay_potion', 'zombie_essence', 'cursed_bone']),
                'reward_weights' => json_encode([50, 35, 15]),
                'guaranteed_rarity_level' => 2,
                'unlock_requirements' => json_encode(['zombie_hound']),
                'sort_order' => 5
            ],

            // Universal Boxes
            [
                'box_type' => 'universal_starter',
                'display_name' => 'Starter Mystery Box',
                'rarity' => 'common',
                'category' => 'universal',
                'description' => 'A basic mystery box available to all players, containing various collectibles.',
                'image_url' => '/images/boxes/universal_starter.png',
                'animation_url' => '/images/boxes/universal_starter_anim.gif',
                'coin_cost' => 2500,
                'gem_cost' => 5,
                'achievement_points_cost' => 50,
                'possible_rewards' => json_encode(['shadow_essence', 'bone_fragment', 'spirit_dust', 'beast_claw']),
                'reward_weights' => json_encode([25, 25, 25, 25]),
                'guaranteed_rarity_level' => 1,
                'unlock_requirements' => json_encode([]),
                'sort_order' => 10
            ]
        ];

        foreach ($mysteryBoxTypes as $boxType) {
            DB::table('mystery_box_types')->updateOrInsert(
                ['box_type' => $boxType['box_type']],
                array_merge($boxType, [
                    'created_at' => now(),
                    'updated_at' => now()
                ])
            );
        }
    }
}
