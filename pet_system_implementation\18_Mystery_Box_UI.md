# Mystery Box UI Implementation

## Overview
This document covers the implementation of mystery box UI components, including box selection, opening animations, reward displays, and box management interfaces.

## Implementation Time: 3-4 days
## Complexity: High
## Dependencies: Animation libraries, reward system

## Mystery Box Components

### MysteryBoxShop Component
```tsx
// File: battlx/src/components/mysterybox/MysteryBoxShop.tsx

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMysteryBoxStore } from '../../stores/mysteryBoxStore';
import { useUserStore } from '../../stores/userStore';
import MysteryBoxCard from './MysteryBoxCard';
import BoxOpeningModal from './BoxOpeningModal';
import BoxPreviewModal from './BoxPreviewModal';
import LoadingSpinner from '../common/LoadingSpinner';

const MysteryBoxShop: React.FC = () => {
  const {
    availableBoxes,
    unlockedBoxes,
    loading,
    error,
    fetchAvailableBoxes,
    openMysteryBox
  } = useMysteryBoxStore();

  const { user } = useUserStore();
  
  const [selectedBox, setSelectedBox] = useState<any>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showOpening, setShowOpening] = useState(false);
  const [openingResults, setOpeningResults] = useState<any>(null);
  const [filter, setFilter] = useState<'all' | 'unlocked' | 'locked'>('all');

  useEffect(() => {
    fetchAvailableBoxes();
  }, [fetchAvailableBoxes]);

  const filteredBoxes = availableBoxes.filter(box => {
    switch (filter) {
      case 'unlocked':
        return box.is_unlocked;
      case 'locked':
        return !box.is_unlocked;
      default:
        return true;
    }
  });

  const handleBoxSelect = (box: any) => {
    setSelectedBox(box);
    setShowPreview(true);
  };

  const handleBoxOpen = async (box: any, purchaseMethod: string, quantity: number = 1) => {
    try {
      setShowPreview(false);
      setShowOpening(true);
      
      const results = await openMysteryBox(box.box_type, purchaseMethod, quantity);
      setOpeningResults(results);
      
      // Keep opening modal open to show results
      setTimeout(() => {
        setShowOpening(false);
        setOpeningResults(null);
      }, 5000);
      
    } catch (error) {
      console.error('Failed to open mystery box:', error);
      setShowOpening(false);
    }
  };

  if (loading && availableBoxes.length === 0) {
    return <LoadingSpinner message="Loading mystery boxes..." />;
  }

  return (
    <div className="mystery-box-shop">
      {/* Header */}
      <div className="shop-header">
        <h2>Mystery Box Shop</h2>
        <p>Discover rare collectibles and valuable rewards!</p>
        
        {/* User Balance */}
        <div className="user-balance">
          <div className="balance-item">
            <span className="balance-icon">🪙</span>
            <span className="balance-value">{user?.balance || 0}</span>
          </div>
          <div className="balance-item">
            <span className="balance-icon">💎</span>
            <span className="balance-value">{user?.gems || 0}</span>
          </div>
          <div className="balance-item">
            <span className="balance-icon">🏆</span>
            <span className="balance-value">{user?.achievement_points || 0}</span>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="shop-filters">
        <div className="filter-buttons">
          <button
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            All Boxes ({availableBoxes.length})
          </button>
          <button
            className={`filter-btn ${filter === 'unlocked' ? 'active' : ''}`}
            onClick={() => setFilter('unlocked')}
          >
            Unlocked ({unlockedBoxes.length})
          </button>
          <button
            className={`filter-btn ${filter === 'locked' ? 'active' : ''}`}
            onClick={() => setFilter('locked')}
          >
            Locked ({availableBoxes.length - unlockedBoxes.length})
          </button>
        </div>
      </div>

      {/* Box Grid */}
      <div className="box-grid">
        <AnimatePresence mode="popLayout">
          {filteredBoxes.map((box, index) => (
            <motion.div
              key={box.box_type}
              layout
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <MysteryBoxCard
                box={box}
                onSelect={() => handleBoxSelect(box)}
                userBalance={{
                  coins: user?.balance || 0,
                  gems: user?.gems || 0,
                  achievement_points: user?.achievement_points || 0
                }}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {filteredBoxes.length === 0 && (
        <div className="empty-shop">
          <div className="empty-icon">📦</div>
          <h3>No boxes found</h3>
          <p>Try adjusting your filters or unlock more boxes by collecting pets!</p>
        </div>
      )}

      {/* Modals */}
      <AnimatePresence>
        {showPreview && selectedBox && (
          <BoxPreviewModal
            box={selectedBox}
            isOpen={showPreview}
            onClose={() => setShowPreview(false)}
            onOpen={handleBoxOpen}
            userBalance={{
              coins: user?.balance || 0,
              gems: user?.gems || 0,
              achievement_points: user?.achievement_points || 0
            }}
          />
        )}

        {showOpening && selectedBox && (
          <BoxOpeningModal
            box={selectedBox}
            isOpen={showOpening}
            results={openingResults}
            onClose={() => setShowOpening(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default MysteryBoxShop;
```

### MysteryBoxCard Component
```tsx
// File: battlx/src/components/mysterybox/MysteryBoxCard.tsx

import React from 'react';
import { motion } from 'framer-motion';
import { getRarityColor, getRarityGradient } from '../../utils/rarityUtils';

interface MysteryBoxCardProps {
  box: {
    box_type: string;
    display_name: string;
    rarity: string;
    category: string;
    description: string;
    image_url: string;
    coin_cost: number;
    gem_cost: number;
    achievement_points_cost: number;
    is_unlocked: boolean;
    can_purchase: boolean;
    guaranteed_rarity_level: number;
    times_opened: number;
  };
  onSelect: () => void;
  userBalance: {
    coins: number;
    gems: number;
    achievement_points: number;
  };
}

const MysteryBoxCard: React.FC<MysteryBoxCardProps> = ({
  box,
  onSelect,
  userBalance
}) => {
  const rarityColor = getRarityColor(box.rarity);
  const rarityGradient = getRarityGradient(box.rarity);

  const canAffordCoins = userBalance.coins >= box.coin_cost;
  const canAffordGems = userBalance.gems >= box.gem_cost;
  const canAffordPoints = userBalance.achievement_points >= box.achievement_points_cost;

  const getAffordabilityStatus = () => {
    if (!box.is_unlocked) return 'locked';
    if (!box.can_purchase) return 'unavailable';
    if (canAffordCoins || canAffordGems || canAffordPoints) return 'affordable';
    return 'expensive';
  };

  const affordabilityStatus = getAffordabilityStatus();

  return (
    <motion.div
      className={`mystery-box-card ${affordabilityStatus}`}
      onClick={box.is_unlocked ? onSelect : undefined}
      whileHover={box.is_unlocked ? { scale: 1.02, y: -2 } : {}}
      whileTap={box.is_unlocked ? { scale: 0.98 } : {}}
      style={{
        background: box.is_unlocked ? rarityGradient : 'rgba(0,0,0,0.3)',
        borderColor: rarityColor,
      }}
    >
      {/* Rarity Border */}
      <div 
        className="rarity-border"
        style={{ borderColor: rarityColor }}
      />

      {/* Box Image */}
      <div className="box-image-container">
        <motion.img
          src={box.image_url}
          alt={box.display_name}
          className={`box-image ${box.is_unlocked ? 'unlocked' : 'locked'}`}
          animate={box.is_unlocked ? {
            rotateY: [0, 5, -5, 0],
            scale: [1, 1.02, 1]
          } : {}}
          transition={{
            duration: 3,
            repeat: Infinity,
            repeatType: 'loop'
          }}
        />

        {/* Lock Overlay */}
        {!box.is_unlocked && (
          <div className="lock-overlay">
            <span className="lock-icon">🔒</span>
          </div>
        )}

        {/* Guaranteed Rarity Badge */}
        <div className="guaranteed-badge">
          <span>⭐ {box.guaranteed_rarity_level}</span>
        </div>

        {/* Times Opened Badge */}
        {box.times_opened > 0 && (
          <div className="opened-badge">
            <span>📦 {box.times_opened}</span>
          </div>
        )}
      </div>

      {/* Box Info */}
      <div className="box-info">
        <h3 className="box-name">{box.display_name}</h3>
        <div className="box-category">{box.category}</div>
        
        <div className="rarity-indicator">
          <span 
            className={`rarity-text rarity-${box.rarity}`}
            style={{ color: rarityColor }}
          >
            {box.rarity.toUpperCase()}
          </span>
        </div>

        <p className="box-description">{box.description}</p>

        {/* Costs */}
        {box.is_unlocked && (
          <div className="box-costs">
            {box.coin_cost > 0 && (
              <div className={`cost-item ${canAffordCoins ? 'affordable' : 'expensive'}`}>
                <span className="cost-icon">🪙</span>
                <span className="cost-value">{box.coin_cost.toLocaleString()}</span>
              </div>
            )}
            
            {box.gem_cost > 0 && (
              <div className={`cost-item ${canAffordGems ? 'affordable' : 'expensive'}`}>
                <span className="cost-icon">💎</span>
                <span className="cost-value">{box.gem_cost.toLocaleString()}</span>
              </div>
            )}
            
            {box.achievement_points_cost > 0 && (
              <div className={`cost-item ${canAffordPoints ? 'affordable' : 'expensive'}`}>
                <span className="cost-icon">🏆</span>
                <span className="cost-value">{box.achievement_points_cost.toLocaleString()}</span>
              </div>
            )}
          </div>
        )}

        {/* Status */}
        <div className="box-status">
          {!box.is_unlocked ? (
            <span className="status-locked">🔒 Locked</span>
          ) : !box.can_purchase ? (
            <span className="status-unavailable">❌ Unavailable</span>
          ) : affordabilityStatus === 'affordable' ? (
            <span className="status-available">✅ Available</span>
          ) : (
            <span className="status-expensive">💰 Need more currency</span>
          )}
        </div>
      </div>

      {/* Hover Effect */}
      {box.is_unlocked && (
        <motion.div
          className="hover-effect"
          initial={{ opacity: 0 }}
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          <span>Click to preview</span>
        </motion.div>
      )}
    </motion.div>
  );
};

export default MysteryBoxCard;
```

### BoxOpeningModal Component
```tsx
// File: battlx/src/components/mysterybox/BoxOpeningModal.tsx

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import BoxOpeningAnimation from './BoxOpeningAnimation';
import RewardReveal from './RewardReveal';

interface BoxOpeningModalProps {
  box: any;
  isOpen: boolean;
  results: any;
  onClose: () => void;
}

const BoxOpeningModal: React.FC<BoxOpeningModalProps> = ({
  box,
  isOpen,
  results,
  onClose
}) => {
  const [stage, setStage] = useState<'opening' | 'revealing' | 'complete'>('opening');
  const [currentRewardIndex, setCurrentRewardIndex] = useState(0);

  useEffect(() => {
    if (results && stage === 'opening') {
      // Start revealing rewards after opening animation
      setTimeout(() => {
        setStage('revealing');
      }, 2000);
    }
  }, [results, stage]);

  useEffect(() => {
    if (stage === 'revealing' && results) {
      const totalRewards = results.reduce((total: number, result: any) => 
        total + result.rewards.length, 0
      );

      if (currentRewardIndex >= totalRewards) {
        setStage('complete');
      }
    }
  }, [currentRewardIndex, results, stage]);

  const handleRewardRevealed = () => {
    setCurrentRewardIndex(prev => prev + 1);
  };

  const handleClose = () => {
    setStage('opening');
    setCurrentRewardIndex(0);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="box-opening-modal"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <motion.div
          className="modal-content"
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          exit={{ scale: 0.8 }}
        >
          {stage === 'opening' && (
            <BoxOpeningAnimation
              box={box}
              onComplete={() => setStage('revealing')}
            />
          )}

          {stage === 'revealing' && results && (
            <RewardReveal
              results={results}
              currentIndex={currentRewardIndex}
              onRewardRevealed={handleRewardRevealed}
            />
          )}

          {stage === 'complete' && (
            <motion.div
              className="opening-complete"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <h2>🎉 Opening Complete!</h2>
              <p>All rewards have been added to your collection.</p>
              
              <div className="completion-summary">
                {results && results.map((result: any, index: number) => (
                  <div key={index} className="result-summary">
                    <h4>Box {index + 1}:</h4>
                    <div className="reward-count">
                      {result.rewards.length} reward{result.rewards.length !== 1 ? 's' : ''}
                    </div>
                    {result.contained_rare_item && (
                      <div className="rare-item-indicator">
                        ✨ Contained rare item!
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <button 
                className="close-btn"
                onClick={handleClose}
              >
                Awesome! ✨
              </button>
            </motion.div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default BoxOpeningModal;
```

### BoxOpeningAnimation Component
```tsx
// File: battlx/src/components/mysterybox/BoxOpeningAnimation.tsx

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface BoxOpeningAnimationProps {
  box: any;
  onComplete: () => void;
}

const BoxOpeningAnimation: React.FC<BoxOpeningAnimationProps> = ({
  box,
  onComplete
}) => {
  const [animationStage, setAnimationStage] = useState<'shake' | 'glow' | 'open'>('shake');

  useEffect(() => {
    const timer1 = setTimeout(() => setAnimationStage('glow'), 800);
    const timer2 = setTimeout(() => setAnimationStage('open'), 1500);
    const timer3 = setTimeout(() => onComplete(), 2000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, [onComplete]);

  return (
    <div className="box-opening-animation">
      <div className="animation-container">
        <motion.div
          className="box-container"
          animate={
            animationStage === 'shake' ? {
              x: [-2, 2, -2, 2, 0],
              y: [-1, 1, -1, 1, 0],
              rotate: [-1, 1, -1, 1, 0]
            } : animationStage === 'glow' ? {
              scale: [1, 1.1, 1.05],
              filter: [
                'brightness(1) drop-shadow(0 0 0px rgba(255,255,255,0))',
                'brightness(1.3) drop-shadow(0 0 20px rgba(255,255,255,0.8))',
                'brightness(1.2) drop-shadow(0 0 15px rgba(255,255,255,0.6))'
              ]
            } : {
              scale: [1.05, 1.3, 1.5],
              opacity: [1, 0.8, 0],
              rotate: [0, 180, 360]
            }
          }
          transition={{
            duration: animationStage === 'shake' ? 0.8 : 
                     animationStage === 'glow' ? 0.7 : 0.5,
            ease: animationStage === 'open' ? 'easeIn' : 'easeInOut'
          }}
        >
          <img 
            src={box.image_url} 
            alt={box.display_name}
            className="opening-box-image"
          />
        </motion.div>

        {/* Particle Effects */}
        {animationStage === 'open' && (
          <div className="particles">
            {[...Array(12)].map((_, i) => (
              <motion.div
                key={i}
                className="particle"
                initial={{ 
                  x: 0, 
                  y: 0, 
                  scale: 0,
                  opacity: 1
                }}
                animate={{
                  x: Math.cos(i * 30 * Math.PI / 180) * 100,
                  y: Math.sin(i * 30 * Math.PI / 180) * 100,
                  scale: [0, 1, 0],
                  opacity: [1, 1, 0]
                }}
                transition={{
                  duration: 1,
                  delay: i * 0.05,
                  ease: 'easeOut'
                }}
              />
            ))}
          </div>
        )}

        {/* Opening Text */}
        <motion.div
          className="opening-text"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <h2>Opening {box.display_name}...</h2>
          <div className="opening-dots">
            <motion.span
              animate={{ opacity: [0, 1, 0] }}
              transition={{ duration: 1, repeat: Infinity, delay: 0 }}
            >
              .
            </motion.span>
            <motion.span
              animate={{ opacity: [0, 1, 0] }}
              transition={{ duration: 1, repeat: Infinity, delay: 0.3 }}
            >
              .
            </motion.span>
            <motion.span
              animate={{ opacity: [0, 1, 0] }}
              transition={{ duration: 1, repeat: Infinity, delay: 0.6 }}
            >
              .
            </motion.span>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default BoxOpeningAnimation;
```

### RewardReveal Component
```tsx
// File: battlx/src/components/mysterybox/RewardReveal.tsx

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { getRarityColor } from '../../utils/rarityUtils';

interface RewardRevealProps {
  results: any[];
  currentIndex: number;
  onRewardRevealed: () => void;
}

const RewardReveal: React.FC<RewardRevealProps> = ({
  results,
  currentIndex,
  onRewardRevealed
}) => {
  const [currentReward, setCurrentReward] = useState<any>(null);
  const [showReward, setShowReward] = useState(false);

  useEffect(() => {
    // Get the current reward to display
    let rewardCount = 0;
    let targetReward = null;

    for (const result of results) {
      for (const reward of result.rewards) {
        if (rewardCount === currentIndex) {
          targetReward = reward;
          break;
        }
        rewardCount++;
      }
      if (targetReward) break;
    }

    if (targetReward) {
      setCurrentReward(targetReward);
      setShowReward(true);

      // Auto-advance after showing reward
      const timer = setTimeout(() => {
        setShowReward(false);
        setTimeout(() => {
          onRewardRevealed();
        }, 300);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [currentIndex, results, onRewardRevealed]);

  if (!currentReward) return null;

  const rarityColor = getRarityColor(currentReward.collectible?.rarity || 'common');

  return (
    <div className="reward-reveal">
      <AnimatePresence mode="wait">
        {showReward && (
          <motion.div
            className="reward-container"
            initial={{ scale: 0, rotate: -180, opacity: 0 }}
            animate={{ scale: 1, rotate: 0, opacity: 1 }}
            exit={{ scale: 0, rotate: 180, opacity: 0 }}
            transition={{ 
              type: 'spring',
              damping: 15,
              stiffness: 300
            }}
          >
            {/* Reward Card */}
            <motion.div
              className="reward-card"
              style={{ borderColor: rarityColor }}
              animate={{
                boxShadow: [
                  `0 0 0px ${rarityColor}`,
                  `0 0 20px ${rarityColor}`,
                  `0 0 10px ${rarityColor}`
                ]
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                repeatType: 'reverse'
              }}
            >
              {/* Reward Image */}
              <div className="reward-image-container">
                <motion.img
                  src={currentReward.collectible?.image_url || '/default-collectible.png'}
                  alt={currentReward.collectible?.name || 'Reward'}
                  className="reward-image"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.3, type: 'spring' }}
                />

                {/* Rarity Glow */}
                <motion.div
                  className="rarity-glow"
                  style={{ backgroundColor: rarityColor }}
                  animate={{
                    opacity: [0.3, 0.7, 0.3],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: 'reverse'
                  }}
                />
              </div>

              {/* Reward Info */}
              <div className="reward-info">
                <motion.h3
                  className="reward-name"
                  style={{ color: rarityColor }}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  {currentReward.collectible?.name || 'Mystery Reward'}
                </motion.h3>

                <motion.div
                  className="reward-rarity"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.7 }}
                >
                  <span 
                    className={`rarity-badge rarity-${currentReward.collectible?.rarity || 'common'}`}
                    style={{ backgroundColor: rarityColor }}
                  >
                    {(currentReward.collectible?.rarity || 'common').toUpperCase()}
                  </span>
                </motion.div>

                <motion.p
                  className="reward-description"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.9 }}
                >
                  {currentReward.collectible?.description || 'A mysterious collectible'}
                </motion.p>
              </div>

              {/* New Badge */}
              <motion.div
                className="new-badge"
                initial={{ scale: 0, rotate: -45 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ delay: 1.1, type: 'spring' }}
              >
                NEW!
              </motion.div>
            </motion.div>

            {/* Celebration Particles */}
            <div className="celebration-particles">
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  className="celebration-particle"
                  style={{ backgroundColor: rarityColor }}
                  initial={{ 
                    x: 0, 
                    y: 0, 
                    scale: 0,
                    opacity: 1
                  }}
                  animate={{
                    x: Math.cos(i * 45 * Math.PI / 180) * 80,
                    y: Math.sin(i * 45 * Math.PI / 180) * 80,
                    scale: [0, 1, 0],
                    opacity: [1, 1, 0]
                  }}
                  transition={{
                    duration: 1.5,
                    delay: 0.3 + i * 0.1,
                    ease: 'easeOut'
                  }}
                />
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Progress Indicator */}
      <div className="reveal-progress">
        <div className="progress-text">
          Reward {currentIndex + 1} of {
            results.reduce((total, result) => total + result.rewards.length, 0)
          }
        </div>
        
        <div className="progress-bar">
          <motion.div
            className="progress-fill"
            initial={{ width: 0 }}
            animate={{ 
              width: `${((currentIndex + 1) / results.reduce((total, result) => total + result.rewards.length, 0)) * 100}%` 
            }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>
    </div>
  );
};

export default RewardReveal;
```

## Acceptance Criteria
- [ ] Mystery box shop displays available boxes correctly
- [ ] Box opening animations smooth and engaging
- [ ] Reward reveal system functional with proper timing
- [ ] Multiple box opening supported
- [ ] Proper cost validation and balance checking
- [ ] Rarity-based visual effects working
- [ ] Mobile-optimized touch interactions

## Next Steps
1. Create collectible page components
2. Implement collection set displays
3. Add state management integration
4. Create comprehensive testing suite

## Troubleshooting
- Test animation performance on lower-end devices
- Verify reward data matches backend responses
- Check box opening sequence timing
- Test multiple box opening scenarios
- Ensure proper error handling for failed openings
