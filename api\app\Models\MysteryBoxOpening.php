<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MysteryBoxOpening extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id', 'box_type', 'purchase_method', 'cost_paid',
        'currency_used', 'rewards_received', 'total_value',
        'contained_rare_item', 'opened_at'
    ];

    protected $casts = [
        'rewards_received' => 'array',
        'contained_rare_item' => 'boolean',
        'opened_at' => 'datetime',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function mysteryBoxType(): BelongsTo
    {
        return $this->belongsTo(MysteryBoxType::class, 'box_type', 'box_type');
    }

    // Scopes
    public function scopeByMethod($query, $method)
    {
        return $query->where('purchase_method', $method);
    }

    public function scopeWithRareItems($query)
    {
        return $query->where('contained_rare_item', true);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('opened_at', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->where('opened_at', '>=', now()->startOfWeek());
    }

    public function scopeThisMonth($query)
    {
        return $query->where('opened_at', '>=', now()->startOfMonth());
    }

    // Accessors
    public function getMethodDisplayAttribute(): string
    {
        return match($this->purchase_method) {
            'coins' => 'Coins',
            'gems' => 'Gems',
            'achievement_points' => 'Achievement Points',
            'free' => 'Free',
            default => ucfirst($this->purchase_method)
        };
    }

    public function getRewardCountAttribute(): int
    {
        return count($this->rewards_received ?? []);
    }

    public function getProfitLossAttribute(): int
    {
        return $this->total_value - $this->cost_paid;
    }

    public function getRewardDetailsAttribute(): array
    {
        $rewards = $this->rewards_received ?? [];
        $details = [];

        foreach ($rewards as $collectibleId) {
            $template = CollectibleTemplate::where('collectible_id', $collectibleId)->first();
            if ($template) {
                $details[] = [
                    'id' => $collectibleId,
                    'name' => $template->name,
                    'rarity' => $template->rarity,
                    'type' => $template->type,
                    'image_url' => $template->image_url,
                    'estimated_value' => $template->estimated_value
                ];
            }
        }

        return $details;
    }

    // Business Logic Methods
    public function wasSuccessful(): bool
    {
        return $this->total_value >= $this->cost_paid;
    }

    public function getSuccessRating(): string
    {
        $ratio = $this->cost_paid > 0 ? $this->total_value / $this->cost_paid : 0;

        return match(true) {
            $ratio >= 3.0 => 'excellent',
            $ratio >= 2.0 => 'great',
            $ratio >= 1.5 => 'good',
            $ratio >= 1.0 => 'fair',
            default => 'poor'
        };
    }
}
