<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, check if the foreign key constraint exists and drop it
        $constraintExists = DB::select("
            SELECT constraint_name 
            FROM information_schema.table_constraints 
            WHERE table_name = 'telegram_users' 
            AND constraint_type = 'FOREIGN KEY' 
            AND constraint_name LIKE '%referred_by%'
        ");

        if (!empty($constraintExists)) {
            $constraintName = $constraintExists[0]->constraint_name;
            DB::statement("ALTER TABLE telegram_users DROP CONSTRAINT {$constraintName}");
        }

        // Add the correct foreign key constraint
        // Note: We're not adding a foreign key constraint because referred_by references telegram_id
        // which is not a primary key, and PostgreSQL requires foreign keys to reference primary keys
        // or unique columns. Since telegram_id is unique, we could add it, but it's not necessary
        // for the referral system to work properly.
        
        // Instead, we'll add an index for performance
        Schema::table('telegram_users', function (Blueprint $table) {
            $table->index('referred_by', 'idx_telegram_users_referred_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('telegram_users', function (Blueprint $table) {
            $table->dropIndex('idx_telegram_users_referred_by');
        });
    }
};
