<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pet_interactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->foreignId('pet_id')->constrained('pets')->onDelete('cascade');
            
            $table->enum('interaction_type', ['feed', 'play', 'pet', 'train']);
            $table->integer('energy_cost');
            $table->integer('happiness_gained');
            $table->integer('experience_gained');
            
            // Rewards given
            $table->integer('coins_rewarded')->default(0);
            $table->integer('materials_rewarded')->default(0);
            $table->string('collectible_rewarded')->nullable();
            $table->boolean('bonus_applied')->default(false); // Happy pet bonus
            
            $table->timestamp('interaction_time');
            $table->timestamps();
            
            $table->index(['telegram_user_id', 'interaction_time']);
            $table->index(['pet_id', 'interaction_type']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pet_interactions');
    }
};
