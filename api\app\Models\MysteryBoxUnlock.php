<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MysteryBoxUnlock extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id', 'box_type', 'unlock_source',
        'source_reference', 'unlocked_at'
    ];

    protected $casts = [
        'unlocked_at' => 'datetime',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function mysteryBoxType(): BelongsTo
    {
        return $this->belongsTo(MysteryBoxType::class, 'box_type', 'box_type');
    }

    // Scopes
    public function scopeBySource($query, $source)
    {
        return $query->where('unlock_source', $source);
    }

    public function scopeRecentlyUnlocked($query, $days = 7)
    {
        return $query->where('unlocked_at', '>=', now()->subDays($days));
    }

    // Accessors
    public function getSourceDisplayAttribute(): string
    {
        return match($this->unlock_source) {
            'pet_purchase' => 'Pet Purchase',
            'prize_tree' => 'Prize Tree',
            'collection_bonus' => 'Collection Bonus',
            'admin_grant' => 'Admin Grant',
            default => ucfirst(str_replace('_', ' ', $this->unlock_source))
        };
    }

    public function getDaysUnlockedAttribute(): int
    {
        return $this->unlocked_at->diffInDays(now());
    }
}
