<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Use PostgreSQL's TRUNCATE CASCADE for proper cleanup
        \Illuminate\Support\Facades\DB::statement('TRUNCATE TABLE levels, daily_tasks, task_types, tasks, mission_types, missions, referral_tasks CASCADE');

        $this->call([
            LevelSeeder::class,
            DailyTaskSeeder::class,
            TaskTypeSeeder::class, // Add task types before tasks
            TaskSeeder::class,
            MissionTypeSeeder::class,
            MissionSeeder::class,
            ReferralTaskSeeder::class,
            PrizeTreeSeeder::class, // Add Prize Tree seeder
         ]);
     }
}
