import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { $http } from '@/lib/http';
import { useUserStore } from '@/store/user-store';
import PrizeTreeCanvas from '@/components/prizetree/PrizeTreeCanvas';
import PrizeNodeDetails from '@/components/prizetree/PrizeNodeDetails';
import PrizeTreeSelector from '@/components/prizetree/PrizeTreeSelector';
import UserGameDetails from '@/components/UserGameDetails';
import { BattlxIcon } from '@/components/icons/BattlxIcon';
import { toast } from 'react-toastify';
import {
  PrizeTree as PrizeTreeType,
  Prize,
  PrizeTreeResponse,
  UserPrizesResponse,
  UnlockPrizeResponse
} from '@/types/PrizeTypes';

export default function PrizeTree() {
  const queryClient = useQueryClient();
  const [selectedTree, setSelectedTree] = useState<PrizeTreeType | null>(null);
  const [selectedNode, setSelectedNode] = useState<Prize | null>(null);

  // Fetch available prize trees
  const prizeTrees = useQuery<PrizeTreeType[]>({
    queryKey: ['prize-trees'],
    queryFn: () => $http.$get('/prizes/trees'),
  });

  // Fetch user's prizes and achievement points
  const userPrizes = useQuery<UserPrizesResponse>({
    queryKey: ['user-prizes'],
    queryFn: () => $http.$get('/prizes/user'),
  });

  // Mutation for unlocking prizes
  const unlockPrize = useMutation<UnlockPrizeResponse, Error, number>({
    mutationFn: (prizeId: number) => $http.post('/prizes/unlock', { prize_id: prizeId }),
    onSuccess: (data) => {
      toast.success(data.message || 'Prize unlocked successfully!');
      queryClient.invalidateQueries({ queryKey: ['user-prizes'] });

      // Update user store if needed
      if (data.prize?.reward_type === 'balance' && data.prize?.reward_details?.amount) {
        useUserStore.setState((state) => ({
          ...state,
          balance: state.balance + data.prize!.reward_details.amount!
        }));
      }

      // Update achievement points
      if (data.remaining_points !== undefined) {
        useUserStore.setState((state) => ({
          ...state,
          achievement_points: data.remaining_points
        }));
      }
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to unlock prize');
    }
  });

  // Effect to select the first tree when data is loaded
  useEffect(() => {
    if (prizeTrees.data && prizeTrees.data.length > 0 && !selectedTree) {
      setSelectedTree(prizeTrees.data[0]);
    }
  }, [prizeTrees.data, selectedTree]);

  // Fetch selected tree details when a tree is selected
  const selectedTreeDetails = useQuery<PrizeTreeResponse>({
    queryKey: ['prize-tree', selectedTree?.id],
    queryFn: () => $http.$get(`/prizes/trees/${selectedTree!.id}`),
    enabled: !!selectedTree?.id,
  });

  // Handle tree selection
  const handleSelectTree = (tree: PrizeTreeType) => {
    setSelectedTree(tree);
    setSelectedNode(null);
  };

  // Handle node selection
  const handleNodeSelect = (node: Prize) => {
    setSelectedNode(node);
  };

  // Handle prize unlock
  const handleUnlockPrize = () => {
    if (selectedNode) {
      unlockPrize.mutate(selectedNode.id);
    }
  };

  // Handle closing node details
  const handleCloseNodeDetails = () => {
    setSelectedNode(null);
  };

  // Loading state
  if (prizeTrees.isLoading || userPrizes.isLoading) {
    return (
      <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1">
        <div className="flex flex-col flex-1 w-full h-full px-6 pb-20 mt-8 modal-body">
          <div className="flex items-center justify-center h-full">
            <div className="w-8 h-8 border-4 border-[#9B8B6C] border-t-transparent rounded-full animate-spin"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (prizeTrees.isError || userPrizes.isError) {
    return (
      <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1">
        <div className="flex flex-col flex-1 w-full h-full px-6 pb-20 mt-8 modal-body">
          <div className="flex flex-col items-center justify-center h-full">
            <div className="text-red-500 mb-2">Error loading prize trees</div>
            <button
              className="px-4 py-2 bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40 rounded-lg"
              onClick={() => {
                prizeTrees.refetch();
                userPrizes.refetch();
              }}
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1">
      <div className="flex flex-col flex-1 w-full h-full px-6 pb-20 mt-8 modal-body">
        <UserGameDetails className="mt-4" />

        {/* Achievement Points Display */}
        <div className="flex items-center justify-center mt-6 space-x-2">
          <BattlxIcon icon="coin" className="w-6 h-6 text-[#9B8B6C]" />
          <span className="text-xl font-bold text-[#9B8B6C]">
            {userPrizes.data?.achievement_points || 0} Achievement Points
          </span>
        </div>

        {/* Prize Tree Selector */}
        <PrizeTreeSelector
          trees={prizeTrees.data || []}
          selectedTree={selectedTree}
          onSelectTree={handleSelectTree}
        />

        {/* Main Prize Tree Canvas */}
        {selectedTree && selectedTreeDetails.data && (
          <PrizeTreeCanvas
            tree={selectedTreeDetails.data}
            userPrizes={userPrizes.data?.unlocked_prizes || []}
            onNodeSelect={handleNodeSelect}
          />
        )}

        {/* Prize Node Details Panel */}
        {selectedNode && (
          <PrizeNodeDetails
            node={selectedNode}
            isUnlocked={userPrizes.data?.unlocked_prizes?.includes(selectedNode.id) || false}
            canUnlock={
              (userPrizes.data?.achievement_points || 0) >= selectedNode.cost &&
              selectedNode.prerequisites.every(id =>
                userPrizes.data?.unlocked_prizes?.includes(id) || false
              )
            }
            onUnlock={handleUnlockPrize}
            isLoading={unlockPrize.isPending}
            onClose={handleCloseNodeDetails}
          />
        )}
      </div>
    </div>
  );
}
