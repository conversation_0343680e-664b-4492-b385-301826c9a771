# Slash Game Integration Guide: 10 - Deployment and Integration Checklist

This document provides a checklist for deploying the Slash game integration and verifying its successful operation in the target environment.

## 1. Deployment Steps

Follow these steps to deploy the Slash game integration:

*   **Backend Deployment:**
    *   Deploy the updated backend code, including any modifications to [`GameController.php`](api/app/Http/Controllers/GameController.php) and [`TelegramUser.php`](api/app/Models/TelegramUser.php) (although minimal changes are expected for core integration).
    *   Run any necessary database migrations to ensure the `telegram_users` table has the `slash_game_unlocked` column (this should already be in place based on the initial analysis of migrations).
    *   Ensure the backend API is accessible and running.
*   **Frontend Deployment:**
    *   Build the React frontend application, ensuring the Slash game code (`battlx/src/slash_game/`) is included in the build process and bundled correctly.
    *   Deploy the built frontend assets to the web server.
    *   Ensure the Slash game assets (images, audio, etc. from `battlx/src/slash_game/assets/`) are copied to the appropriate public directory on the web server, likely `/public/game/slash/`, matching the path used in the game's asset loading.
*   **Configuration:**
    *   Verify that the `SLASH_GAME_UNLOCK_PRICE` constant in [`GameController.php`](api/app/Http/Controllers/GameController.php) is set correctly in the deployed environment configuration.
    *   Ensure the frontend is configured to correctly point to the backend API endpoint.

## 2. Integration Verification Points

After deployment, perform the following checks to verify the successful integration of the Slash game:

*   **Game Listing:**
    *   Navigate to the game selection screen in the frontend.
    *   Verify that the Slash game is listed as an available game.
*   **Game Unlocking:**
    *   If the game is not unlocked for a test user, attempt to unlock it.
    *   Verify that the unlock process completes successfully and the user's balance is updated correctly on the frontend and backend.
    *   Verify that the Slash game is now marked as unlocked for the user.
*   **Game Loading and Start:**
    *   Select the unlocked Slash game to start a session.
    *   Verify that the game loads without errors and the loading screen (if implemented) functions correctly.
    *   Verify that the game starts and the gameplay is visible on the canvas.
*   **Gameplay and Scoring:**
    *   Play a session of the Slash game.
    *   Verify that core gameplay mechanics function as expected (movement, attacking, enemies, pickups).
    *   **Verify that collecting coins within the game increases the player's coin count displayed in the game or a game-specific UI element.**
*   **Game Over and Score Submission:**
    *   Reach the game over condition in a game session.
    *   Verify that the game over screen appears correctly.
    *   **Verify that the final coin amount collected during the session is displayed as the score on the game over screen.**
    *   **Verify that the final coin amount is successfully submitted to the backend via the API.** Monitor network requests or backend logs if necessary.
    *   **Verify that the submitted coin amount is correctly added to the user's total `game_score` on the backend (check the database or a user profile endpoint).**
*   **Unlimited Plays:**
    *   After completing a game session, attempt to start another session immediately.
    *   Verify that the user can start a new game without any limitations on play attempts.
*   **Error Handling:**
    *   If possible, simulate network errors or backend issues to verify that the frontend handles them gracefully and provides informative feedback to the user.

## 3. Rollback Procedures

In case of critical issues after deployment, be prepared to roll back to the previous working version. This typically involves:

*   Deploying the previous version of the backend code.
*   Reverting any database changes if necessary (use caution with migrations that involve data loss).
*   Deploying the previous version of the frontend code and assets.

By following this checklist, you can ensure a smooth deployment and successful integration of the Slash game.