<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB; // Added for potential raw SQL usage

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the 'jobs' table
        Schema::create('jobs', function (Blueprint $table) {
            $table->id();
            $table->string('queue')->index();
            $table->longText('payload');
            $table->smallInteger('attempts')->unsigned(); // SMALLINT with unsigned constraint
            $table->integer('reserved_at')->nullable()->unsigned(); // INTEGER with unsigned constraint
            $table->integer('available_at')->unsigned(); // INTEGER with unsigned constraint
            $table->integer('created_at')->unsigned(); // INTEGER with unsigned constraint
        });

        // Create the 'job_batches' table
        Schema::create('job_batches', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name');
            $table->integer('total_jobs')->unsigned(); // INTEGER with unsigned constraint
            $table->integer('pending_jobs')->unsigned(); // INTEGER with unsigned constraint
            $table->integer('failed_jobs')->unsigned(); // INTEGER with unsigned constraint
            $table->longText('failed_job_ids');
            $table->mediumText('options')->nullable();
            $table->integer('cancelled_at')->nullable(); // Nullable INTEGER
            $table->integer('created_at')->unsigned(); // INTEGER with unsigned constraint
            $table->integer('finished_at')->nullable(); // Nullable INTEGER
        });

        // Create the 'failed_jobs' table
        Schema::create('failed_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->text('connection');
            $table->text('queue');
            $table->longText('payload');
            $table->longText('exception');
            $table->timestamp('failed_at')->useCurrent(); // TIMESTAMP with current time
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jobs');
        Schema::dropIfExists('job_batches');
        Schema::dropIfExists('failed_jobs');
    }
};