# Slash Game Implementation Plan

## Overview
This document outlines the implementation plan for the Slash game itself, focusing on the game mechanics, swipe effects, and performance optimizations for a Fruit Ninja-style gameplay experience.

## Current Status Analysis
- There are existing Slash game files in `battlx/src/slash_game/js` and `battlx/public/game/slash`
- The game appears to be partially implemented with core components like GameCore, Game, Stage, etc.
- The game needs to be adapted for Fruit Ninja-style swipe effects with high performance

## Game Implementation Tasks

### 1. Optimize the Swipe Effect Implementation
Create a high-performance swipe effect system that works well on mobile devices.

```javascript
// battlx/public/game/slash/js/src/components/swipeEffect.js
class SwipeEffect {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.swipes = [];
        this.maxSwipes = 10; // Maximum number of swipes to keep in memory
        this.swipeLifetime = 500; // Swipe lifetime in milliseconds
        this.swipeWidth = 3; // Width of the swipe line
        this.swipeColor = 'rgba(255, 255, 255, 0.8)'; // Default color
    }

    // Add a new swipe point
    addSwipePoint(x, y, force = 1) {
        // Find the last swipe or create a new one
        let currentSwipe = this.swipes.length > 0 ? this.swipes[this.swipes.length - 1] : null;
        
        // If no current swipe or the last one is too old, create a new one
        if (!currentSwipe || Date.now() - currentSwipe.lastUpdate > 50) {
            currentSwipe = {
                points: [],
                createdAt: Date.now(),
                lastUpdate: Date.now(),
                color: this.swipeColor,
                width: this.swipeWidth * force
            };
            this.swipes.push(currentSwipe);
            
            // Remove oldest swipes if we exceed the maximum
            if (this.swipes.length > this.maxSwipes) {
                this.swipes.shift();
            }
        }
        
        // Add the point to the current swipe
        currentSwipe.points.push({ x, y });
        currentSwipe.lastUpdate = Date.now();
    }

    // Update swipes (remove old ones)
    update() {
        const now = Date.now();
        this.swipes = this.swipes.filter(swipe => now - swipe.createdAt < this.swipeLifetime);
    }

    // Draw all active swipes
    draw() {
        this.update();
        
        this.swipes.forEach(swipe => {
            if (swipe.points.length < 2) return;
            
            const age = Date.now() - swipe.createdAt;
            const opacity = 1 - (age / this.swipeLifetime);
            
            this.ctx.strokeStyle = swipe.color.replace('0.8', opacity.toFixed(2));
            this.ctx.lineWidth = swipe.width;
            this.ctx.lineCap = 'round';
            this.ctx.lineJoin = 'round';
            
            this.ctx.beginPath();
            this.ctx.moveTo(swipe.points[0].x, swipe.points[0].y);
            
            // Use quadratic curves for smoother lines
            for (let i = 1; i < swipe.points.length; i++) {
                const p1 = swipe.points[i - 1];
                const p2 = swipe.points[i];
                
                // Calculate control point (midpoint)
                const cpx = (p1.x + p2.x) / 2;
                const cpy = (p1.y + p2.y) / 2;
                
                this.ctx.quadraticCurveTo(p1.x, p1.y, cpx, cpy);
            }
            
            // Connect to the last point
            const last = swipe.points[swipe.points.length - 1];
            this.ctx.lineTo(last.x, last.y);
            
            this.ctx.stroke();
        });
    }
}
```

### 2. Implement Slashable Objects
Create a system for objects that can be slashed by the player.

```javascript
// battlx/public/game/slash/js/src/components/slashable.js
class Slashable {
    constructor(x, y, radius, type, value) {
        this.x = x;
        this.y = y;
        this.radius = radius;
        this.type = type; // 'fruit', 'bomb', 'powerup', etc.
        this.value = value; // Points or effect value
        this.slashed = false;
        this.slashAngle = 0;
        this.slashTime = 0;
        this.gravity = 0.2;
        this.velocityX = Math.random() * 4 - 2;
        this.velocityY = -Math.random() * 5 - 5;
        this.rotationSpeed = Math.random() * 0.1 - 0.05;
        this.rotation = Math.random() * Math.PI * 2;
        this.opacity = 1;
        this.sprite = null;
        this.slicedSprites = [null, null]; // Left and right halves
    }

    // Set the sprite for this object
    setSprite(sprite, slicedLeft = null, slicedRight = null) {
        this.sprite = sprite;
        this.slicedSprites = [slicedLeft, slicedRight];
    }

    // Update the object's position and state
    update() {
        if (!this.slashed) {
            // Normal physics for unslashed objects
            this.velocityY += this.gravity;
            this.x += this.velocityX;
            this.y += this.velocityY;
            this.rotation += this.rotationSpeed;
        } else {
            // Physics for slashed objects (halves fly apart)
            this.velocityY += this.gravity;
            
            // Left half
            this.slicedPositions = [
                {
                    x: this.x - Math.cos(this.slashAngle) * (Date.now() - this.slashTime) * 0.05,
                    y: this.y + this.velocityY,
                    rotation: this.rotation - (Date.now() - this.slashTime) * 0.001
                },
                // Right half
                {
                    x: this.x + Math.cos(this.slashAngle) * (Date.now() - this.slashTime) * 0.05,
                    y: this.y + this.velocityY,
                    rotation: this.rotation + (Date.now() - this.slashTime) * 0.001
                }
            ];
            
            // Fade out over time
            const age = Date.now() - this.slashTime;
            this.opacity = Math.max(0, 1 - (age / 1000));
        }
    }

    // Draw the object
    draw(ctx) {
        ctx.save();
        
        if (!this.slashed) {
            // Draw unslashed object
            ctx.globalAlpha = this.opacity;
            ctx.translate(this.x, this.y);
            ctx.rotate(this.rotation);
            
            if (this.sprite) {
                ctx.drawImage(this.sprite, -this.radius, -this.radius, this.radius * 2, this.radius * 2);
            } else {
                // Fallback to circle if no sprite
                ctx.fillStyle = this.type === 'bomb' ? '#333' : '#4CAF50';
                ctx.beginPath();
                ctx.arc(0, 0, this.radius, 0, Math.PI * 2);
                ctx.fill();
            }
        } else {
            // Draw slashed object (two halves)
            for (let i = 0; i < 2; i++) {
                ctx.save();
                ctx.globalAlpha = this.opacity;
                ctx.translate(this.slicedPositions[i].x, this.slicedPositions[i].y);
                ctx.rotate(this.slicedPositions[i].rotation);
                
                if (this.slicedSprites[i]) {
                    ctx.drawImage(this.slicedSprites[i], -this.radius, -this.radius, this.radius * 2, this.radius * 2);
                } else {
                    // Fallback to half circles if no sprites
                    ctx.fillStyle = this.type === 'bomb' ? '#333' : '#4CAF50';
                    ctx.beginPath();
                    ctx.arc(0, 0, this.radius, i === 0 ? Math.PI : 0, i === 0 ? 0 : Math.PI, i === 1);
                    ctx.fill();
                }
                
                ctx.restore();
            }
        }
        
        ctx.restore();
    }

    // Check if a point is inside this object
    containsPoint(x, y) {
        if (this.slashed) return false;
        
        const dx = this.x - x;
        const dy = this.y - y;
        return (dx * dx + dy * dy) <= (this.radius * this.radius);
    }

    // Slash this object with a given angle
    slash(angle) {
        if (this.slashed) return false;
        
        this.slashed = true;
        this.slashAngle = angle;
        this.slashTime = Date.now();
        
        return true;
    }

    // Check if the object is still visible
    isVisible(canvasWidth, canvasHeight) {
        if (this.slashed) {
            return this.opacity > 0;
        }
        
        return this.y - this.radius < canvasHeight && 
               this.x + this.radius > 0 && 
               this.x - this.radius < canvasWidth;
    }
}
```

### 3. Implement Swipe Detection and Collision
Create a system to detect swipes and check for collisions with slashable objects.

```javascript
// battlx/public/game/slash/js/src/components/swipeDetector.js
class SwipeDetector {
    constructor(canvas, swipeEffect) {
        this.canvas = canvas;
        this.swipeEffect = swipeEffect;
        this.isTracking = false;
        this.startPoint = { x: 0, y: 0 };
        this.currentPoint = { x: 0, y: 0 };
        this.swipePoints = [];
        this.minSwipeDistance = 10; // Minimum distance to register a swipe
        this.lastPointTime = 0;
        this.pointInterval = 10; // Minimum time between points in ms
        
        // Set up event listeners
        this.setupEventListeners();
    }

    // Set up event listeners for mouse and touch events
    setupEventListeners() {
        // Mouse events
        this.canvas.addEventListener('mousedown', this.handleStart.bind(this));
        this.canvas.addEventListener('mousemove', this.handleMove.bind(this));
        this.canvas.addEventListener('mouseup', this.handleEnd.bind(this));
        this.canvas.addEventListener('mouseleave', this.handleEnd.bind(this));
        
        // Touch events
        this.canvas.addEventListener('touchstart', this.handleStart.bind(this));
        this.canvas.addEventListener('touchmove', this.handleMove.bind(this));
        this.canvas.addEventListener('touchend', this.handleEnd.bind(this));
        this.canvas.addEventListener('touchcancel', this.handleEnd.bind(this));
    }

    // Handle start of swipe
    handleStart(e) {
        e.preventDefault();
        
        const point = this.getPointFromEvent(e);
        this.isTracking = true;
        this.startPoint = { ...point };
        this.currentPoint = { ...point };
        this.swipePoints = [{ ...point }];
        this.lastPointTime = Date.now();
        
        // Add first point to swipe effect
        this.swipeEffect.addSwipePoint(point.x, point.y);
    }

    // Handle move during swipe
    handleMove(e) {
        if (!this.isTracking) return;
        e.preventDefault();
        
        const point = this.getPointFromEvent(e);
        this.currentPoint = { ...point };
        
        // Only add points at certain intervals to avoid too many points
        const now = Date.now();
        if (now - this.lastPointTime >= this.pointInterval) {
            this.swipePoints.push({ ...point });
            this.lastPointTime = now;
            
            // Add point to swipe effect
            this.swipeEffect.addSwipePoint(point.x, point.y);
        }
    }

    // Handle end of swipe
    handleEnd(e) {
        if (!this.isTracking) return;
        e.preventDefault();
        
        this.isTracking = false;
        
        // Check if this was a valid swipe (minimum distance)
        const dx = this.currentPoint.x - this.startPoint.x;
        const dy = this.currentPoint.y - this.startPoint.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < this.minSwipeDistance) {
            // Too short to be a swipe
            return;
        }
        
        // Calculate swipe angle
        const angle = Math.atan2(dy, dx);
        
        // Return swipe data
        return {
            points: [...this.swipePoints],
            angle: angle,
            distance: distance,
            duration: Date.now() - this.lastPointTime
        };
    }

    // Get point coordinates from mouse or touch event
    getPointFromEvent(e) {
        let x, y;
        
        if (e.type.startsWith('touch')) {
            const touch = e.touches[0] || e.changedTouches[0];
            const rect = this.canvas.getBoundingClientRect();
            x = touch.clientX - rect.left;
            y = touch.clientY - rect.top;
        } else {
            const rect = this.canvas.getBoundingClientRect();
            x = e.clientX - rect.left;
            y = e.clientY - rect.top;
        }
        
        return { x, y };
    }

    // Check for collisions between a swipe and slashable objects
    checkCollisions(swipe, slashables) {
        if (!swipe || swipe.points.length < 2) return [];
        
        const slashedObjects = [];
        
        // Check each line segment in the swipe
        for (let i = 1; i < swipe.points.length; i++) {
            const p1 = swipe.points[i - 1];
            const p2 = swipe.points[i];
            
            // Check each slashable object
            for (const obj of slashables) {
                if (obj.slashed) continue;
                
                // Check if the line segment intersects with the object
                if (this.lineIntersectsCircle(p1.x, p1.y, p2.x, p2.y, obj.x, obj.y, obj.radius)) {
                    // Slash the object with the swipe angle
                    const angle = Math.atan2(p2.y - p1.y, p2.x - p1.x);
                    obj.slash(angle);
                    slashedObjects.push(obj);
                }
            }
        }
        
        return slashedObjects;
    }

    // Check if a line segment intersects with a circle
    lineIntersectsCircle(x1, y1, x2, y2, cx, cy, r) {
        // Vector from point 1 to point 2
        const dx = x2 - x1;
        const dy = y2 - y1;
        
        // Length of the line segment
        const length = Math.sqrt(dx * dx + dy * dy);
        
        // Unit vector in the direction of the line
        const udx = dx / length;
        const udy = dy / length;
        
        // Vector from point 1 to circle center
        const pcx = cx - x1;
        const pcy = cy - y1;
        
        // Project this vector onto the line direction
        const proj = pcx * udx + pcy * udy;
        
        // Find the closest point on the line segment to the circle center
        let closest;
        if (proj < 0) {
            closest = { x: x1, y: y1 }; // Point 1 is closest
        } else if (proj > length) {
            closest = { x: x2, y: y2 }; // Point 2 is closest
        } else {
            closest = { x: x1 + udx * proj, y: y1 + udy * proj }; // Projection point is closest
        }
        
        // Check if the closest point is within the circle
        const cdx = closest.x - cx;
        const cdy = closest.y - cy;
        const distSquared = cdx * cdx + cdy * cdy;
        
        return distSquared <= r * r;
    }
}
```
