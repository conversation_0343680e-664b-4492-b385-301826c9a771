<?php

namespace Database\Seeders;

use App\Models\Level;
use Illuminate\Database\Seeder;

class LevelSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $levels = [
            ['level' => 1, 'name' => 'Graveborn', 'from_balance' => 0, 'to_balance' => 50_000],
            ['level' => 2, 'name' => 'Shadowbound', 'from_balance' => 50_000, 'to_balance' => 200_000],
            ['level' => 3, 'name' => 'Tombweaver', 'from_balance' => 200_000, 'to_balance' => 800_000],
            ['level' => 4, 'name' => 'Soulforged', 'from_balance' => 800_000, 'to_balance' => 3_000_000],
            ['level' => 5, 'name' => 'Lich Overlord', 'from_balance' => 3_000_000, 'to_balance' => 10_000_000],
        ];

        foreach ($levels as $level) {
            Level::updateOrCreate(['level' => $level['level']], $level);
        }
    }
}
