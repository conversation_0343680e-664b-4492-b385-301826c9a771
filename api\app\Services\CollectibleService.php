<?php

namespace App\Services;

use App\Models\Collectible;
use App\Models\CollectibleTemplate;
use App\Models\CollectionSet;
use App\Models\UserCollectionProgress;
use App\Models\TelegramUser;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CollectibleService
{
    /**
     * Unlock a collectible for a user
     */
    public function unlockCollectible(
        TelegramUser $user,
        string $collectibleId,
        string $unlockSource,
        ?string $sourceReference = null
    ): ?array {
        $collectibleTemplate = CollectibleTemplate::where('collectible_id', $collectibleId)->first();
        
        if (!$collectibleTemplate) {
            Log::warning('Collectible template not found', ['collectible_id' => $collectibleId]);
            return null;
        }

        // Check if user already owns this collectible
        $existingCollectible = $user->collectibles()
            ->where('collectible_id', $collectibleId)
            ->first();

        if ($existingCollectible) {
            return null; // Already owned
        }

        DB::beginTransaction();
        
        try {
            // Create collectible for user
            $collectible = $user->collectibles()->create([
                'collectible_id' => $collectibleId,
                'unlock_source' => $unlockSource,
                'source_reference' => $sourceReference,
                'obtained_at' => now()
            ]);

            // Update collection progress
            $this->updateCollectionProgress($user, $collectibleTemplate);

            // Check for collection completion
            $completionRewards = $this->checkCollectionCompletion($user, $collectibleTemplate);

            DB::commit();

            Log::info('Collectible unlocked', [
                'user_id' => $user->id,
                'collectible_id' => $collectibleId,
                'unlock_source' => $unlockSource
            ]);

            return [
                'collectible' => $collectible,
                'template' => $collectibleTemplate,
                'completion_rewards' => $completionRewards
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to unlock collectible', [
                'user_id' => $user->id,
                'collectible_id' => $collectibleId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get user's collection progress for all sets
     */
    public function getUserCollectionProgress(TelegramUser $user): array
    {
        $collectionSets = CollectionSet::active()->ordered()->get();
        $progress = [];

        foreach ($collectionSets as $set) {
            $userProgress = UserCollectionProgress::firstOrCreate(
                [
                    'telegram_user_id' => $user->id,
                    'set_id' => $set->set_id
                ],
                [
                    'collectibles_owned' => 0,
                    'total_collectibles' => $set->total_collectibles,
                    'completion_percentage' => 0,
                    'owned_collectible_ids' => [],
                    'missing_collectible_ids' => []
                ]
            );

            // Update progress
            $userProgress->updateProgress();
            
            $progress[] = $userProgress->getDetailedProgress();
        }

        return $progress;
    }

    /**
     * Claim collection completion rewards
     */
    public function claimCollectionRewards(TelegramUser $user, CollectionSet $collectionSet): array
    {
        $userProgress = UserCollectionProgress::where([
            'telegram_user_id' => $user->id,
            'set_id' => $collectionSet->set_id
        ])->first();

        if (!$userProgress) {
            throw new \Exception('Collection progress not found');
        }

        if (!$userProgress->is_completed) {
            throw new \Exception('Collection not completed');
        }

        if ($userProgress->rewards_claimed) {
            throw new \Exception('Rewards already claimed');
        }

        DB::beginTransaction();
        
        try {
            $rewards = $collectionSet->awardCompletionRewards($user);

            $userProgress->rewards_claimed = true;
            $userProgress->rewards_claimed_at = now();
            $userProgress->save();

            // Unlock bonus mystery box if available
            if ($collectionSet->bonus_mystery_box_type) {
                $mysteryBoxService = app(MysteryBoxService::class);
                $mysteryBoxService->unlockBoxType(
                    $user,
                    $collectionSet->bonus_mystery_box_type,
                    'collection_bonus',
                    $collectionSet->set_id
                );
            }

            DB::commit();

            Log::info('Collection rewards claimed', [
                'user_id' => $user->id,
                'collection_set_id' => $collectionSet->set_id,
                'rewards' => $rewards
            ]);

            return $rewards;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to claim collection rewards', [
                'user_id' => $user->id,
                'collection_set_id' => $collectionSet->set_id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get collectibles by category for user
     */
    public function getUserCollectiblesByCategory(TelegramUser $user, string $category): array
    {
        $collectibles = $user->collectibles()
            ->whereHas('template', function($q) use ($category) {
                $q->where('category', $category);
            })
            ->with('template')
            ->orderBy('obtained_at', 'desc')
            ->get();

        return $collectibles->map(function($collectible) {
            return [
                'collectible_id' => $collectible->collectible_id,
                'name' => $collectible->template->name,
                'type' => $collectible->template->type,
                'rarity' => $collectible->template->rarity,
                'image_url' => $collectible->template->image_url,
                'obtained_at' => $collectible->obtained_at,
                'unlock_source' => $collectible->unlock_source,
                'days_owned' => $collectible->days_owned
            ];
        })->toArray();
    }

    /**
     * Get collectibles by rarity for user
     */
    public function getUserCollectiblesByRarity(TelegramUser $user, string $rarity): array
    {
        $collectibles = $user->collectibles()
            ->whereHas('template', function($q) use ($rarity) {
                $q->where('rarity', $rarity);
            })
            ->with('template')
            ->orderBy('obtained_at', 'desc')
            ->get();

        return $collectibles->map(function($collectible) {
            return [
                'collectible_id' => $collectible->collectible_id,
                'name' => $collectible->template->name,
                'type' => $collectible->template->type,
                'category' => $collectible->template->category,
                'image_url' => $collectible->template->image_url,
                'obtained_at' => $collectible->obtained_at,
                'unlock_source' => $collectible->unlock_source,
                'days_owned' => $collectible->days_owned
            ];
        })->toArray();
    }

    /**
     * Get recently obtained collectibles
     */
    public function getRecentlyObtainedCollectibles(TelegramUser $user, int $days = 7): array
    {
        $collectibles = $user->collectibles()
            ->recentlyObtained($days)
            ->with('template')
            ->orderBy('obtained_at', 'desc')
            ->get();

        return $collectibles->map(function($collectible) {
            return [
                'collectible_id' => $collectible->collectible_id,
                'name' => $collectible->template->name,
                'type' => $collectible->template->type,
                'category' => $collectible->template->category,
                'rarity' => $collectible->template->rarity,
                'image_url' => $collectible->template->image_url,
                'obtained_at' => $collectible->obtained_at,
                'unlock_source' => $collectible->unlock_source,
                'days_ago' => $collectible->obtained_at->diffInDays(now())
            ];
        })->toArray();
    }

    /**
     * Get collection statistics for user
     */
    public function getCollectionStatistics(TelegramUser $user): array
    {
        $totalCollectibles = $user->collectibles()->count();
        $totalPossible = CollectibleTemplate::active()->count();

        $byCategory = $user->collectibles()
            ->with('template')
            ->get()
            ->groupBy('template.category')
            ->map(function($items, $category) {
                return [
                    'category' => $category,
                    'count' => $items->count(),
                    'total_value' => $items->sum('template.estimated_value')
                ];
            });

        $byRarity = $user->collectibles()
            ->with('template')
            ->get()
            ->groupBy('template.rarity')
            ->map(function($items, $rarity) {
                return [
                    'rarity' => $rarity,
                    'count' => $items->count()
                ];
            });

        $byUnlockSource = $user->collectibles()
            ->selectRaw('unlock_source, COUNT(*) as count')
            ->groupBy('unlock_source')
            ->pluck('count', 'unlock_source')
            ->toArray();

        return [
            'total_owned' => $totalCollectibles,
            'total_possible' => $totalPossible,
            'completion_percentage' => $totalPossible > 0 ? round(($totalCollectibles / $totalPossible) * 100, 1) : 0,
            'by_category' => $byCategory->values(),
            'by_rarity' => $byRarity->values(),
            'by_unlock_source' => $byUnlockSource,
            'total_estimated_value' => $user->collectibles()->with('template')->get()->sum('template.estimated_value'),
            'recent_acquisitions' => $user->collectibles()->recentlyObtained(7)->count()
        ];
    }

    // Private helper methods

    private function updateCollectionProgress(TelegramUser $user, CollectibleTemplate $collectibleTemplate): void
    {
        $collectionSet = $collectibleTemplate->collectionSet;
        if (!$collectionSet) {
            return;
        }

        $userProgress = UserCollectionProgress::firstOrCreate(
            [
                'telegram_user_id' => $user->id,
                'set_id' => $collectionSet->set_id
            ],
            [
                'collectibles_owned' => 0,
                'total_collectibles' => $collectionSet->total_collectibles,
                'completion_percentage' => 0,
                'owned_collectible_ids' => [],
                'missing_collectible_ids' => []
            ]
        );

        $userProgress->updateProgress();
    }

    private function checkCollectionCompletion(TelegramUser $user, CollectibleTemplate $collectibleTemplate): ?array
    {
        $collectionSet = $collectibleTemplate->collectionSet;
        if (!$collectionSet) {
            return null;
        }

        $userProgress = UserCollectionProgress::where([
            'telegram_user_id' => $user->id,
            'set_id' => $collectionSet->set_id
        ])->first();

        if (!$userProgress || !$userProgress->is_completed || $userProgress->rewards_claimed) {
            return null;
        }

        // Collection just completed, return available rewards info
        return [
            'collection_completed' => true,
            'collection_name' => $collectionSet->name,
            'available_rewards' => $collectionSet->completion_rewards,
            'bonus_mystery_box' => $collectionSet->bonus_mystery_box_type
        ];
    }
}
