<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Users table
        Schema::create('users', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->bigInteger('telegram_id')->unique()->nullable();
            $table->string('email')->unique()->nullable();
            $table->string('password')->nullable();
            $table->string('username', 50)->nullable();
            $table->string('first_name', 100)->nullable();
            $table->string('last_name', 100)->nullable();
            $table->string('role', 20)->default('user');
            $table->string('remember_token', 100)->nullable();
            $table->timestamp('last_login_date')->nullable();
            $table->timestamps();

            $table->index('telegram_id');
            $table->index('email');
        });

        // Player statistics
        Schema::create('player_stats', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->bigInteger('game_score')->default(0);
            $table->bigInteger('score')->default(0);
            $table->integer('earn_per_tap')->default(1);
            $table->integer('production_per_hour')->default(0);
            $table->integer('login_streak')->default(0);
            $table->integer('level_id')->default(1);
            $table->timestamp('last_tap_date')->nullable();
            $table->timestamps();

            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');

            $table->index('user_id');
            $table->index('game_score');
        });

        // Energy system
        Schema::create('energy_system', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->integer('available_energy')->default(100);
            $table->integer('max_energy')->default(100);
            $table->integer('energy_limit_level')->default(1);
            $table->integer('multi_tap_level')->default(1);
            $table->timestamps();

            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');

            $table->index('user_id');
        });

        // Boosters and power-ups
        Schema::create('boosters', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->integer('booster_pack_2x')->default(0);
            $table->integer('booster_pack_3x')->default(0);
            $table->integer('booster_pack_7x')->default(0);
            $table->timestamp('booster_pack_active_until')->nullable();
            $table->integer('daily_booster_uses')->default(0);
            $table->timestamp('last_daily_booster_use')->nullable();
            $table->timestamps();

            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');

            $table->index('user_id');
        });

        // Wallet information
        Schema::create('wallet_info', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->string('ton_wallet')->nullable();
            $table->decimal('balance', 20, 8)->default(0);
            $table->timestamps();

            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');

            $table->index('user_id');
        });

        // Referral system
        Schema::create('referrals', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->uuid('referred_by');
            $table->timestamp('created_at')->nullable();

            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');
            $table->foreign('referred_by')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade');

            $table->index('user_id');
            $table->index('referred_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referrals');
        Schema::dropIfExists('wallet_info');
        Schema::dropIfExists('boosters');
        Schema::dropIfExists('energy_system');
        Schema::dropIfExists('player_stats');
        Schema::dropIfExists('users');
    }
};