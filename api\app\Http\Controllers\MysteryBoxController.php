<?php

namespace App\Http\Controllers;

use App\Models\MysteryBoxType;
use App\Models\MysteryBoxUnlock;
use App\Models\MysteryBoxOpening;
use App\Services\MysteryBoxService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class MysteryBoxController extends Controller
{
    protected MysteryBoxService $mysteryBoxService;

    public function __construct(MysteryBoxService $mysteryBoxService)
    {
        $this->mysteryBoxService = $mysteryBoxService;
    }

    /**
     * Get available mystery boxes for user
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $category = $request->query('category');
        $rarity = $request->query('rarity');
        
        $query = MysteryBoxType::active()->purchasable();
        
        if ($category) {
            $query->byCategory($category);
        }
        
        if ($rarity) {
            $query->byRarity($rarity);
        }
        
        $mysteryBoxTypes = $query->ordered()->get();
        
        $availableBoxes = $mysteryBoxTypes->map(function($boxType) use ($user) {
            $isUnlocked = $boxType->isUnlockedBy($user);
            $canPurchaseCoins = $isUnlocked && $boxType->canBePurchasedBy($user, 'coins');
            $canPurchaseGems = $isUnlocked && $boxType->canBePurchasedBy($user, 'gems');
            $canPurchaseAP = $isUnlocked && $boxType->canBePurchasedBy($user, 'achievement_points');
            
            return [
                'id' => $boxType->id,
                'box_type' => $boxType->box_type,
                'display_name' => $boxType->display_name,
                'category' => $boxType->category,
                'rarity' => $boxType->rarity,
                'rarity_color' => $boxType->rarity_color,
                'description' => $boxType->description,
                'image_url' => $boxType->image_url,
                'animation_url' => $boxType->animation_url,
                'coin_cost' => $boxType->coin_cost,
                'gem_cost' => $boxType->gem_cost,
                'achievement_points_cost' => $boxType->achievement_points_cost,
                'guaranteed_rarity_level' => $boxType->guaranteed_rarity_level,
                'is_unlocked' => $isUnlocked,
                'can_purchase_coins' => $canPurchaseCoins,
                'can_purchase_gems' => $canPurchaseGems,
                'can_purchase_achievement_points' => $canPurchaseAP,
                'unlock_requirements' => $boxType->unlock_requirements,
                'meets_requirements' => $boxType->meetsUnlockRequirements($user)
            ];
        });
        
        return response()->json([
            'success' => true,
            'mystery_boxes' => $availableBoxes,
            'user_balance' => [
                'balance' => $user->balance,
                'gems' => $user->gems ?? 0,
                'achievement_points' => $user->achievement_points ?? 0
            ]
        ]);
    }

    /**
     * Get user's unlocked mystery boxes
     */
    public function getUnlockedBoxes(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $unlockedBoxes = $user->mysteryBoxUnlocks()
                             ->with('mysteryBoxType')
                             ->orderBy('unlocked_at', 'desc')
                             ->get();
        
        return response()->json([
            'success' => true,
            'unlocked_boxes' => $unlockedBoxes->map(function($unlock) {
                return [
                    'box_type' => $unlock->box_type,
                    'display_name' => $unlock->mysteryBoxType->display_name,
                    'category' => $unlock->mysteryBoxType->category,
                    'rarity' => $unlock->mysteryBoxType->rarity,
                    'image_url' => $unlock->mysteryBoxType->image_url,
                    'unlock_source' => $unlock->unlock_source,
                    'source_display' => $unlock->source_display,
                    'unlocked_at' => $unlock->unlocked_at,
                    'days_unlocked' => $unlock->days_unlocked
                ];
            }),
            'total_unlocked' => $unlockedBoxes->count()
        ]);
    }

    /**
     * Open a mystery box (supports multiple boxes)
     */
    public function openBox(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'box_type' => 'required|string|exists:mystery_box_types,box_type',
            'purchase_method' => 'required|in:coins,gems,achievement_points',
            'quantity' => 'integer|min:1|max:10'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $boxType = $request->box_type;
        $purchaseMethod = $request->purchase_method;
        $quantity = $request->quantity ?? 1;

        try {
            $results = $this->mysteryBoxService->openBoxes(
                $user,
                $boxType,
                $purchaseMethod,
                $quantity
            );

            // Award achievement points for opening boxes
            $this->awardAchievementPoints($user, $quantity, $boxType);

            return response()->json([
                'success' => true,
                'message' => $quantity === 1
                    ? 'Mystery box opened successfully!'
                    : "{$quantity} mystery boxes opened successfully!",
                'results' => $results,
                'user_balance' => [
                    'balance' => $user->fresh()->balance,
                    'gems' => $user->fresh()->gems ?? 0,
                    'achievement_points' => $user->fresh()->achievement_points ?? 0
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get mystery box opening history
     */
    public function getOpeningHistory(Request $request): JsonResponse
    {
        $user = $request->user();
        $boxType = $request->query('box_type');
        $method = $request->query('method');
        
        $query = $user->mysteryBoxOpenings()->with('mysteryBoxType');
        
        if ($boxType) {
            $query->where('box_type', $boxType);
        }
        
        if ($method) {
            $query->byMethod($method);
        }
        
        $openings = $query->orderBy('opened_at', 'desc')
                         ->paginate(20);
        
        return response()->json([
            'success' => true,
            'openings' => $openings->items()->map(function($opening) {
                return [
                    'id' => $opening->id,
                    'box_type' => $opening->box_type,
                    'display_name' => $opening->mysteryBoxType->display_name,
                    'purchase_method' => $opening->purchase_method,
                    'method_display' => $opening->method_display,
                    'cost_paid' => $opening->cost_paid,
                    'currency_used' => $opening->currency_used,
                    'rewards_received' => $opening->rewards_received,
                    'reward_count' => $opening->reward_count,
                    'total_value' => $opening->total_value,
                    'contained_rare_item' => $opening->contained_rare_item,
                    'profit_loss' => $opening->profit_loss,
                    'success_rating' => $opening->getSuccessRating(),
                    'opened_at' => $opening->opened_at,
                    'reward_details' => $opening->reward_details
                ];
            }),
            'pagination' => [
                'current_page' => $openings->currentPage(),
                'last_page' => $openings->lastPage(),
                'per_page' => $openings->perPage(),
                'total' => $openings->total()
            ]
        ]);
    }

    /**
     * Get mystery box statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $totalOpenings = $user->mysteryBoxOpenings()->count();
        $totalSpent = $user->mysteryBoxOpenings()->sum('cost_paid');
        $totalValue = $user->mysteryBoxOpenings()->sum('total_value');
        $rareItemsFound = $user->mysteryBoxOpenings()->withRareItems()->count();
        
        $byMethod = $user->mysteryBoxOpenings()
                        ->select('purchase_method', DB::raw('count(*) as count'), DB::raw('sum(cost_paid) as total_spent'))
                        ->groupBy('purchase_method')
                        ->get();
        
        $byBoxType = $user->mysteryBoxOpenings()
                         ->with('mysteryBoxType')
                         ->get()
                         ->groupBy('box_type')
                         ->map(function($openings, $boxType) {
                             return [
                                 'box_type' => $boxType,
                                 'display_name' => $openings->first()->mysteryBoxType->display_name,
                                 'count' => $openings->count(),
                                 'total_spent' => $openings->sum('cost_paid'),
                                 'total_value' => $openings->sum('total_value'),
                                 'rare_items' => $openings->where('contained_rare_item', true)->count()
                             ];
                         });
        
        $recentOpenings = $user->mysteryBoxOpenings()
                              ->thisWeek()
                              ->count();
        
        return response()->json([
            'success' => true,
            'statistics' => [
                'total_openings' => $totalOpenings,
                'total_spent' => $totalSpent,
                'total_value_received' => $totalValue,
                'net_profit_loss' => $totalValue - $totalSpent,
                'rare_items_found' => $rareItemsFound,
                'success_rate' => $totalOpenings > 0 ? round(($totalValue >= $totalSpent ? 1 : 0) * 100, 1) : 0,
                'recent_openings_this_week' => $recentOpenings,
                'by_purchase_method' => $byMethod,
                'by_box_type' => $byBoxType->values(),
                'average_value_per_opening' => $totalOpenings > 0 ? round($totalValue / $totalOpenings, 2) : 0
            ]
        ]);
    }

    /**
     * Get mystery box details
     */
    public function show(Request $request, MysteryBoxType $mysteryBoxType): JsonResponse
    {
        $user = $request->user();
        
        return response()->json([
            'success' => true,
            'mystery_box' => [
                'id' => $mysteryBoxType->id,
                'box_type' => $mysteryBoxType->box_type,
                'display_name' => $mysteryBoxType->display_name,
                'category' => $mysteryBoxType->category,
                'rarity' => $mysteryBoxType->rarity,
                'rarity_color' => $mysteryBoxType->rarity_color,
                'description' => $mysteryBoxType->description,
                'image_url' => $mysteryBoxType->image_url,
                'animation_url' => $mysteryBoxType->animation_url,
                'coin_cost' => $mysteryBoxType->coin_cost,
                'gem_cost' => $mysteryBoxType->gem_cost,
                'achievement_points_cost' => $mysteryBoxType->achievement_points_cost,
                'possible_rewards' => $mysteryBoxType->possible_rewards,
                'reward_weights' => $mysteryBoxType->reward_weights,
                'guaranteed_rarity_level' => $mysteryBoxType->guaranteed_rarity_level,
                'unlock_requirements' => $mysteryBoxType->unlock_requirements,
                'is_unlocked' => $mysteryBoxType->isUnlockedBy($user),
                'can_purchase_coins' => $mysteryBoxType->canBePurchasedBy($user, 'coins'),
                'can_purchase_gems' => $mysteryBoxType->canBePurchasedBy($user, 'gems'),
                'can_purchase_achievement_points' => $mysteryBoxType->canBePurchasedBy($user, 'achievement_points'),
                'meets_requirements' => $mysteryBoxType->meetsUnlockRequirements($user)
            ]
        ]);
    }

    /**
     * Get all mystery box types (for shop display)
     */
    public function getAllBoxTypes(Request $request): JsonResponse
    {
        $user = $request->user();

        $allBoxTypes = MysteryBoxType::active()
                                   ->orderBy('sort_order')
                                   ->orderBy('rarity')
                                   ->get();

        $boxTypes = $allBoxTypes->map(function($boxType) use ($user) {
            $isUnlocked = $boxType->isUnlockedBy($user);

            return [
                'box_type' => $boxType->box_type,
                'display_name' => $boxType->display_name,
                'rarity' => $boxType->rarity,
                'category' => $boxType->category,
                'description' => $boxType->description,
                'image_url' => $boxType->image_url,
                'coin_cost' => $boxType->coin_cost,
                'gem_cost' => $boxType->gem_cost,
                'achievement_points_cost' => $boxType->achievement_points_cost,
                'is_unlocked' => $isUnlocked,
                'can_purchase' => $isUnlocked && $boxType->canBePurchasedBy($user),
                'unlock_requirements' => $boxType->unlock_requirements,
                'unlock_status' => $this->getUnlockStatus($boxType, $user)
            ];
        });

        return response()->json([
            'success' => true,
            'box_types' => $boxTypes
        ]);
    }

    /**
     * Preview mystery box rewards (without opening)
     */
    public function previewRewards(Request $request, string $boxType): JsonResponse
    {
        $mysteryBoxType = MysteryBoxType::where('box_type', $boxType)
                                      ->where('is_active', true)
                                      ->firstOrFail();

        $user = $request->user();

        if (!$mysteryBoxType->isUnlockedBy($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Mystery box not unlocked'
            ], 403);
        }

        $possibleRewards = $this->mysteryBoxService->getPossibleRewards($mysteryBoxType);

        return response()->json([
            'success' => true,
            'box_type' => $boxType,
            'display_name' => $mysteryBoxType->display_name,
            'possible_rewards' => $possibleRewards,
            'guaranteed_rarity_level' => $mysteryBoxType->guaranteed_rarity_level,
            'costs' => [
                'coins' => $mysteryBoxType->coin_cost,
                'gems' => $mysteryBoxType->gem_cost,
                'achievement_points' => $mysteryBoxType->achievement_points_cost
            ]
        ]);
    }

    // Private helper methods

    private function awardAchievementPoints($user, $quantity, $boxType): void
    {
        try {
            // Award 2 points per box opened
            $points = $quantity * 2;

            // Use existing achievement point system
            $achievementPoints = \App\Models\UserAchievementPoint::firstOrCreate(
                ['telegram_user_id' => $user->id],
                ['total_earned' => 0, 'total_spent' => 0]
            );

            $achievementPoints->total_earned += $points;
            $achievementPoints->save();

            // Record transaction
            DB::table('achievement_point_transactions')->insert([
                'telegram_user_id' => $user->id,
                'amount' => $points,
                'type' => 'earn',
                'source' => 'mystery_box_opening',
                'source_id' => null,
                'description' => "Opened {$quantity} {$boxType} mystery box(es)",
                'created_at' => now(),
                'updated_at' => now()
            ]);

        } catch (\Exception $e) {
            // Don't fail the box opening if achievement points fail
            \Log::error('Failed to award achievement points for mystery box opening', [
                'user_id' => $user->id,
                'quantity' => $quantity,
                'box_type' => $boxType,
                'error' => $e->getMessage()
            ]);
        }
    }

    private function getUnlockStatus(MysteryBoxType $boxType, $user): string
    {
        if ($boxType->isUnlockedBy($user)) {
            return 'unlocked';
        }

        $requirements = $boxType->unlock_requirements;

        if (empty($requirements)) {
            return 'locked';
        }

        // Check specific unlock requirements
        foreach ($requirements as $requirement) {
            if (isset($requirement['type'])) {
                switch ($requirement['type']) {
                    case 'pet_ownership':
                        $hasRequiredPet = $user->pets()
                                              ->whereHas('template', function($q) use ($requirement) {
                                                  $q->where('name', $requirement['pet_name']);
                                              })
                                              ->exists();
                        if (!$hasRequiredPet) {
                            return "requires_pet:{$requirement['pet_name']}";
                        }
                        break;

                    case 'prize_tree_level':
                        $userProgress = $user->prizeTreeProgress();
                        $currentLevel = $userProgress ? $userProgress->current_level : 0;
                        if ($currentLevel < $requirement['level']) {
                            return "requires_level:{$requirement['level']}";
                        }
                        break;

                    case 'collection_completion':
                        $collectionProgress = $user->getCollectionProgress();
                        if ($collectionProgress['overall_percentage'] < $requirement['percentage']) {
                            return "requires_collection:{$requirement['percentage']}%";
                        }
                        break;
                }
            }
        }

        return 'requirements_met';
    }
}
