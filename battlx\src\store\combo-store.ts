import { create } from "zustand";
import { useUserStore } from "./user-store";

// Constants
export const COMBO_ENERGY_COST_MULTIPLIER = 3; // Energy cost is 3x during combo mode
export const FRENZY_MULTIPLIER = 2; // Additional multiplier during Frenzy mode
export const FRENZY_DURATION = 10000; // Frenzy mode duration in milliseconds (10 seconds)
export const FRENZY_THRESHOLD = 10; // Combo multiplier needed to activate Frenzy
export const FRENZY_BUTTON_DURATION = 3000; // How long the Frenzy button appears (3 seconds)

type ComboLevel = 'none' | 'low' | 'medium' | 'high' | 'extreme' | 'legendary';

interface ComboStore {
  // State
  comboCount: number;
  comboMultiplier: number;
  comboReady: boolean;
  comboActive: boolean;
  lastHitTimestamp: number;
  comboLevel: ComboLevel;

  // Frenzy Mode State
  frenzyMeterValue: number;
  frenzyReady: boolean;
  frenzyActive: boolean;
  frenzyStartTime: number;
  frenzyEndTime: number;
  frenzyButtonShowTime: number;

  // Actions
  addCombo: () => void;
  resetCombo: () => void;
  activateCombo: () => void;
  updateComboMultiplier: () => void;
  checkComboTimeout: () => void;

  // Frenzy Actions
  incrementFrenzyMeter: (amount: number) => void;
  checkFrenzyEligibility: () => void;
  activateFrenzy: () => void;
  endFrenzy: () => void;
  checkFrenzyTimeout: () => void;
  getCurrentMultiplier: () => number;
}

export const useComboStore = create<ComboStore>((set, get) => ({
  comboCount: 0,
  comboMultiplier: 1,
  comboReady: false,
  comboActive: false,
  lastHitTimestamp: 0,
  comboLevel: 'none',

  // Frenzy Mode State
  frenzyMeterValue: 0,
  frenzyReady: false,
  frenzyActive: false,
  frenzyStartTime: 0,
  frenzyEndTime: 0,
  frenzyButtonShowTime: 0,

  addCombo: () => {
    const now = Date.now();
    const { lastHitTimestamp, comboCount, comboActive } = get();

    // Check if within combo window (1.5 seconds)
    if (now - lastHitTimestamp <= 1500 || comboCount === 0) {
      set(state => ({
        comboCount: state.comboCount + 1,
        lastHitTimestamp: now,
        comboReady: !comboActive && state.comboCount + 1 >= 5
      }));

      // Update multiplier if combo is active
      if (comboActive) {
        get().updateComboMultiplier();
      }
    } else {
      // Reset if outside time window
      get().resetCombo();
      set({ comboCount: 1, lastHitTimestamp: now });
    }
  },

  resetCombo: () => {
    set({
      comboCount: 0,
      comboMultiplier: 1,
      comboReady: false,
      comboActive: false,
      lastHitTimestamp: 0,
      comboLevel: 'none',
      frenzyMeterValue: 0, // Reset frenzy meter when combo resets
      frenzyReady: false   // Ensure frenzy button is hidden
    });

    // Reset combo multiplier in user store
    useUserStore.getState().setComboMultiplier(1);
  },

  activateCombo: () => {
    if (get().comboReady) {
      set({
        comboActive: true,
        comboReady: false,
        comboMultiplier: 1,
        comboLevel: 'low'
      });

      // Play activation sound
      const audio = new Audio();
      audio.src = "/sounds/combo-activate.mp3"; // We'll need to add this sound file
      audio.volume = 0.5;
      audio.play().catch(() => {
        // Silently fail if audio can't be played
        console.log("Could not play combo activation sound");
      });
    }
  },

  updateComboMultiplier: () => {
    const { comboCount } = get();

    // Calculate new multiplier based on combo count
    let newMultiplier = Math.min(100, Math.floor(comboCount / 5));
    if (newMultiplier < 1) newMultiplier = 1;

    // Determine combo level based on multiplier
    let newComboLevel: ComboLevel = 'low';
    if (newMultiplier >= 75) newComboLevel = 'legendary';
    else if (newMultiplier >= 50) newComboLevel = 'extreme';
    else if (newMultiplier >= 25) newComboLevel = 'high';
    else if (newMultiplier >= 10) newComboLevel = 'medium';

    set({
      comboMultiplier: newMultiplier,
      comboLevel: newComboLevel
    });

    // Update user store for global access
    useUserStore.getState().setComboMultiplier(newMultiplier);
  },

  checkComboTimeout: () => {
    const now = Date.now();
    const { lastHitTimestamp, comboActive, comboReady, frenzyActive } = get();

    // Reset combo if inactive for too long (1.5 seconds)
    if ((comboActive || comboReady) && now - lastHitTimestamp > 1500) {
      // If not in Frenzy mode, reset everything
      if (!frenzyActive) {
        get().resetCombo();
      } else {
        // If in Frenzy mode, just reset combo but keep Frenzy active
        set({
          comboCount: 0,
          comboMultiplier: 1,
          comboReady: false,
          comboActive: false,
          lastHitTimestamp: 0,
          comboLevel: 'none'
        });

        // Update user store with just the Frenzy multiplier
        // During Frenzy, if combo is lost, we still apply the Frenzy multiplier
        console.log(`Combo lost during Frenzy. Using Frenzy multiplier: ${FRENZY_MULTIPLIER}x`);
        useUserStore.getState().setComboMultiplier(FRENZY_MULTIPLIER);
      }
    }
  },

  // Frenzy Mode Actions
  incrementFrenzyMeter: (amount: number) => {
    // Only increment meter if combo is active and not already in Frenzy mode
    const { comboActive, frenzyActive } = get();

    if (comboActive && !frenzyActive) {
      set(state => ({
        frenzyMeterValue: Math.min(100, state.frenzyMeterValue + amount)
      }));

      // Check if meter is full
      if (get().frenzyMeterValue >= 100) {
        get().checkFrenzyEligibility();
      }
    }
  },

  checkFrenzyEligibility: () => {
    const { comboMultiplier, frenzyActive, frenzyReady } = get();
    const now = Date.now();

    // Check if combo multiplier is high enough and not already in Frenzy mode
    if (comboMultiplier >= FRENZY_THRESHOLD && !frenzyActive && !frenzyReady) {
      set({
        frenzyReady: true,
        frenzyButtonShowTime: now + FRENZY_BUTTON_DURATION
      });

      // Play sound to alert player
      const audio = new Audio();
      audio.src = "/sounds/combo-ready.mp3"; // Reuse the combo-ready sound
      audio.volume = 0.5;
      audio.play().catch(() => {
        console.log("Could not play frenzy ready sound");
      });
    }
  },

  activateFrenzy: () => {
    const { frenzyReady, comboMultiplier } = get();
    const now = Date.now();

    if (frenzyReady) {
      set({
        frenzyActive: true,
        frenzyReady: false,
        frenzyStartTime: now,
        frenzyEndTime: now + FRENZY_DURATION,
        frenzyMeterValue: 0 // Reset meter
      });

      // Play activation sound
      const audio = new Audio();
      audio.src = "/sounds/combo-activate.mp3"; // Reuse the combo activation sound
      audio.volume = 0.7;
      audio.play().catch(() => {
        console.log("Could not play frenzy activation sound");
      });

      // Calculate and update the total multiplier (combo * frenzy)
      const totalMultiplier = comboMultiplier * FRENZY_MULTIPLIER;
      console.log(`Frenzy activated! Combo: ${comboMultiplier}x, Frenzy: ${FRENZY_MULTIPLIER}x, Total: ${totalMultiplier}x`);
      useUserStore.getState().setComboMultiplier(totalMultiplier);
    }
  },

  endFrenzy: () => {
    set({
      frenzyActive: false,
      frenzyMeterValue: 0
    });

    // Reset to normal combo multiplier
    const { comboMultiplier } = get();
    console.log(`Frenzy ended. Returning to normal combo multiplier: ${comboMultiplier}x`);
    useUserStore.getState().setComboMultiplier(comboMultiplier);
  },

  checkFrenzyTimeout: () => {
    const now = Date.now();
    const { frenzyActive, frenzyEndTime, frenzyReady, frenzyButtonShowTime } = get();

    // End Frenzy mode if time is up
    if (frenzyActive && now >= frenzyEndTime) {
      get().endFrenzy();
    }

    // Hide Frenzy button if time is up
    if (frenzyReady && now >= frenzyButtonShowTime) {
      set({ frenzyReady: false });
    }
  },

  getCurrentMultiplier: () => {
    const { comboMultiplier, frenzyActive } = get();
    const multiplier = frenzyActive ? comboMultiplier * FRENZY_MULTIPLIER : comboMultiplier;
    return multiplier;
  }
}));