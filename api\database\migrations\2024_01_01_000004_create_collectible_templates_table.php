<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('collectible_templates', function (Blueprint $table) {
            $table->id();
            $table->string('collectible_id', 50)->unique(); // e.g., 'shadow_essence'
            $table->string('name', 100);
            $table->enum('type', ['artifact', 'trophy', 'relic', 'essence', 'scroll']);
            $table->enum('category', ['shadow', 'undead', 'demon', 'spirit', 'beast']);
            $table->enum('rarity', ['common', 'rare', 'epic', 'legendary', 'mythic']);
            $table->text('description');
            $table->string('image_url');
            
            // Collection set information
            $table->string('collection_set_id'); // e.g., 'shadow_collection'
            $table->integer('set_position'); // Order within the set
            
            // Unlock requirements
            $table->enum('unlock_source', ['pet_purchase', 'mystery_box', 'prize_tree', 'collection_bonus']);
            $table->string('unlock_requirement')->nullable(); // Pet ID, box type, etc.
            
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['category', 'rarity']);
            $table->index('collection_set_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('collectible_templates');
    }
};
