# Prize Tree System - Integrations (Part 2)

## 4. Integration with Slashing Mechanics

### 4.1 Slash Effect Cosmetics

Implement custom slash effects from the Prize Tree:

```php
// app/Http/Controllers/API/SlashController.php

/**
 * Get the user's slash visual effects.
 */
public function getSlashEffects(Request $request)
{
    $user = $request->user();
    
    // Get user's equipped cosmetic prizes
    $equippedPrizes = $user->prizes()
        ->wherePivot('is_equipped', true)
        ->whereHas('prize', function ($query) {
            $query->where('reward_type', 'cosmetic');
        })
        ->get();
    
    // Filter for slash-related effects
    $slashEffects = [];
    
    foreach ($equippedPrizes as $userPrize) {
        $prize = $userPrize->prize;
        $rewardDetails = $prize->getRewardDetails();
        
        if ($rewardDetails['type'] === 'slash_effect') {
            $slashEffects[] = [
                'id' => $prize->id,
                'name' => $prize->name,
                'effect_data' => $rewardDetails['visual_data'] ?? []
            ];
        }
    }
    
    return response()->json($slashEffects);
}
```

### 4.2 Slash Milestone Achievement Points

Award achievement points for slash milestones:

```php
// app/Services/SlashService.php

/**
 * Track slash count and award achievement points for milestones.
 */
public function trackSlashMilestones($user)
{
    // Get or create slash stats
    $slashStats = UserSlashStat::firstOrCreate(
        ['telegram_user_id' => $user->id],
        ['total_slashes' => 0]
    );
    
    // Increment total slashes
    $slashStats->total_slashes += 1;
    $slashStats->save();
    
    // Check for milestones
    $milestones = [100, 500, 1000, 5000, 10000, 50000, 100000];
    
    foreach ($milestones as $milestone) {
        // Check if this update crossed a milestone
        if ($slashStats->total_slashes === $milestone) {
            // Award achievement points
            $achievementPointService = new AchievementPointService();
            
            // Award more points for higher milestones
            $points = 1;
            if ($milestone >= 1000) $points = 2;
            if ($milestone >= 10000) $points = 3;
            if ($milestone >= 100000) $points = 5;
            
            $achievementPointService->awardPoints(
                $user->id,
                $points,
                'slash_milestone',
                $milestone,
                "Performed {$milestone} total slashes"
            );
            
            // For certain milestones, award special cosmetic prizes
            if (in_array($milestone, [1000, 10000, 100000])) {
                $this->awardSlashMilestonePrize($user, $milestone);
            }
        }
    }
}

/**
 * Award a special prize for slash milestones.
 */
private function awardSlashMilestonePrize($user, $milestone)
{
    // Map milestones to prize identifiers
    $milestonePrizes = [
        1000 => 'bronze_slash_effect',
        10000 => 'silver_slash_effect',
        100000 => 'gold_slash_effect'
    ];
    
    $prizeIdentifier = $milestonePrizes[$milestone] ?? null;
    
    if (!$prizeIdentifier) {
        return;
    }
    
    // Find the prize with this identifier
    $prize = Prize::where('reward_data->reward_id', $prizeIdentifier)->first();
    
    if (!$prize) {
        return;
    }
    
    // Check if user already has this prize
    $userPrize = UserPrize::where('telegram_user_id', $user->id)
        ->where('prize_id', $prize->id)
        ->first();
        
    if ($userPrize) {
        return;
    }
    
    // Award the prize to the user without deducting achievement points
    DB::transaction(function () use ($user, $prize) {
        UserPrize::create([
            'telegram_user_id' => $user->id,
            'prize_id' => $prize->id,
            'unlocked_at' => now()
        ]);
    });
    
    // Notify the user
    // This would depend on your notification system
}
```

### 4.3 Special Slash Patterns

Implement special slash pattern detection and rewards:

```php
// app/Services/SlashPatternService.php

/**
 * Detect and reward special slash patterns.
 */
public function detectSpecialPattern($user, $slashPoints)
{
    // Analyze the slash points to detect patterns
    $pattern = $this->analyzeSlashPattern($slashPoints);
    
    if (!$pattern) {
        return null;
    }
    
    // Check if this pattern has a reward
    $patternReward = $this->getPatternReward($pattern);
    
    if (!$patternReward) {
        return null;
    }
    
    // Award achievement points
    $achievementPointService = new AchievementPointService();
    $achievementPointService->awardPoints(
        $user->id,
        $patternReward['points'],
        'special_pattern',
        null,
        "Performed special slash pattern: {$pattern}"
    );
    
    // Check if there's a special prize for this pattern
    if (isset($patternReward['prize_identifier'])) {
        $this->awardPatternPrize($user, $patternReward['prize_identifier']);
    }
    
    return [
        'pattern' => $pattern,
        'points' => $patternReward['points'],
        'message' => $patternReward['message'] ?? "Special pattern: {$pattern}"
    ];
}

/**
 * Analyze slash points to detect patterns.
 */
private function analyzeSlashPattern($slashPoints)
{
    // This would contain logic to analyze the slash points
    // and detect patterns like circles, zigzags, etc.
    // Simplified example:
    
    // Check for circle pattern
    if ($this->isCirclePattern($slashPoints)) {
        return 'circle';
    }
    
    // Check for zigzag pattern
    if ($this->isZigzagPattern($slashPoints)) {
        return 'zigzag';
    }
    
    // Check for star pattern
    if ($this->isStarPattern($slashPoints)) {
        return 'star';
    }
    
    return null;
}

/**
 * Get the reward for a specific pattern.
 */
private function getPatternReward($pattern)
{
    $rewards = [
        'circle' => [
            'points' => 2,
            'message' => 'Perfect Circle! +2 Achievement Points',
            'prize_identifier' => 'circle_slash_effect'
        ],
        'zigzag' => [
            'points' => 1,
            'message' => 'Zigzag Pattern! +1 Achievement Point'
        ],
        'star' => [
            'points' => 3,
            'message' => 'Star Pattern! +3 Achievement Points',
            'prize_identifier' => 'star_slash_effect'
        ]
    ];
    
    return $rewards[$pattern] ?? null;
}

/**
 * Award a special prize for a slash pattern.
 */
private function awardPatternPrize($user, $prizeIdentifier)
{
    // Find the prize with this identifier
    $prize = Prize::where('reward_data->reward_id', $prizeIdentifier)->first();
    
    if (!$prize) {
        return;
    }
    
    // Check if user already has this prize
    $userPrize = UserPrize::where('telegram_user_id', $user->id)
        ->where('prize_id', $prize->id)
        ->first();
        
    if ($userPrize) {
        return;
    }
    
    // Award the prize to the user without deducting achievement points
    DB::transaction(function () use ($user, $prize) {
        UserPrize::create([
            'telegram_user_id' => $user->id,
            'prize_id' => $prize->id,
            'unlocked_at' => now()
        ]);
    });
}
```

## 5. Integration with Friend/Referral System

### 5.1 Referral Achievement Points

Award achievement points for successful referrals:

```php
// app/Services/ReferralService.php

/**
 * Process a successful referral and award achievement points.
 */
public function processSuccessfulReferral($referrer, $referee)
{
    // Existing referral logic
    // ...
    
    // Award achievement points to the referrer
    $achievementPointService = new AchievementPointService();
    $achievementPointService->awardPoints(
        $referrer->id,
        3, // Award 3 points for each successful referral
        'successful_referral',
        $referee->id,
        "Successfully referred user: {$referee->username}"
    );
    
    // Also award some achievement points to the new user
    $achievementPointService->awardPoints(
        $referee->id,
        2, // Award 2 points to the new user
        'joined_via_referral',
        $referrer->id,
        "Joined via referral from: {$referrer->username}"
    );
    
    // Check referral milestones for special prizes
    $this->checkReferralMilestones($referrer);
}

/**
 * Check for referral milestones and award special prizes.
 */
private function checkReferralMilestones($user)
{
    // Count total successful referrals
    $referralCount = ReferralLink::where('referrer_id', $user->id)
        ->where('status', 'completed')
        ->count();
    
    // Define milestones
    $milestones = [
        5 => 'bronze_referral_badge',
        10 => 'silver_referral_badge',
        25 => 'gold_referral_badge',
        50 => 'platinum_referral_badge',
        100 => 'diamond_referral_badge'
    ];
    
    // Check if user has reached a milestone
    foreach ($milestones as $count => $prizeIdentifier) {
        if ($referralCount === $count) {
            // Find the prize with this identifier
            $prize = Prize::where('reward_data->reward_id', $prizeIdentifier)->first();
            
            if (!$prize) {
                continue;
            }
            
            // Check if user already has this prize
            $userPrize = UserPrize::where('telegram_user_id', $user->id)
                ->where('prize_id', $prize->id)
                ->first();
                
            if ($userPrize) {
                continue;
            }
            
            // Award the prize to the user
            DB::transaction(function () use ($user, $prize) {
                UserPrize::create([
                    'telegram_user_id' => $user->id,
                    'prize_id' => $prize->id,
                    'unlocked_at' => now()
                ]);
            });
            
            // Notify the user
            // This would depend on your notification system
        }
    }
}
```

### 5.2 Friend Activity Rewards

Award achievement points for playing with friends:

```php
// app/Services/FriendService.php

/**
 * Track friend activity and award achievement points.
 */
public function trackFriendActivity($user, $friendId, $activityType)
{
    // Record the activity
    FriendActivity::create([
        'telegram_user_id' => $user->id,
        'friend_id' => $friendId,
        'activity_type' => $activityType,
        'occurred_at' => now()
    ]);
    
    // Count daily activities with this friend
    $dailyActivities = FriendActivity::where('telegram_user_id', $user->id)
        ->where('friend_id', $friendId)
        ->where('activity_type', $activityType)
        ->whereDate('occurred_at', now()->toDateString())
        ->count();
    
    // Award achievement points for the first activity of each type per day
    if ($dailyActivities === 1) {
        $achievementPointService = new AchievementPointService();
        $achievementPointService->awardPoints(
            $user->id,
            1, // Award 1 point for each first daily activity
            'friend_activity',
            $friendId,
            "Played with friend: Activity type {$activityType}"
        );
    }
    
    // Check for friend activity milestones
    $this->checkFriendActivityMilestones($user, $friendId);
}

/**
 * Check for friend activity milestones and award special prizes.
 */
private function checkFriendActivityMilestones($user, $friendId)
{
    // Count total activities with this friend
    $activityCount = FriendActivity::where('telegram_user_id', $user->id)
        ->where('friend_id', $friendId)
        ->count();
    
    // Define milestones
    $milestones = [
        10 => 'bronze_friendship_badge',
        50 => 'silver_friendship_badge',
        100 => 'gold_friendship_badge',
        500 => 'platinum_friendship_badge'
    ];
    
    // Check if user has reached a milestone
    foreach ($milestones as $count => $prizeIdentifier) {
        if ($activityCount === $count) {
            // Find the prize with this identifier
            $prize = Prize::where('reward_data->reward_id', $prizeIdentifier)->first();
            
            if (!$prize) {
                continue;
            }
            
            // Award the prize to the user
            $this->awardFriendshipPrize($user, $prize);
            
            // Also award to the friend
            $friend = TelegramUser::find($friendId);
            if ($friend) {
                $this->awardFriendshipPrize($friend, $prize);
            }
        }
    }
}

/**
 * Award a friendship prize to a user.
 */
private function awardFriendshipPrize($user, $prize)
{
    // Check if user already has this prize
    $userPrize = UserPrize::where('telegram_user_id', $user->id)
        ->where('prize_id', $prize->id)
        ->first();
        
    if ($userPrize) {
        return;
    }
    
    // Award the prize to the user
    DB::transaction(function () use ($user, $prize) {
        UserPrize::create([
            'telegram_user_id' => $user->id,
            'prize_id' => $prize->id,
            'unlocked_at' => now()
        ]);
    });
}
```

### 5.3 Social Sharing Rewards

Implement rewards for sharing achievements:

```php
// app/Http/Controllers/API/SocialController.php

/**
 * Share an achievement on social media.
 */
public function shareAchievement(Request $request)
{
    $request->validate([
        'achievement_type' => 'required|string',
        'achievement_id' => 'required|integer',
        'platform' => 'required|string'
    ]);
    
    $user = $request->user();
    $achievementType = $request->achievement_type;
    $achievementId = $request->achievement_id;
    $platform = $request->platform;
    
    // Record the share
    SocialShare::create([
        'telegram_user_id' => $user->id,
        'achievement_type' => $achievementType,
        'achievement_id' => $achievementId,
        'platform' => $platform,
        'shared_at' => now()
    ]);
    
    // Award achievement points (limit to once per day per platform)
    $todayShares = SocialShare::where('telegram_user_id', $user->id)
        ->where('platform', $platform)
        ->whereDate('shared_at', now()->toDateString())
        ->count();
    
    if ($todayShares === 1) {
        $achievementPointService = new AchievementPointService();
        $achievementPointService->awardPoints(
            $user->id,
            1, // Award 1 point for sharing once per day
            'social_share',
            null,
            "Shared achievement on {$platform}"
        );
    }
    
    // Check for sharing milestones
    $this->checkSharingMilestones($user);
    
    return response()->json([
        'message' => 'Achievement shared successfully',
        'points_awarded' => $todayShares === 1 ? 1 : 0
    ]);
}

/**
 * Check for sharing milestones and award special prizes.
 */
private function checkSharingMilestones($user)
{
    // Count total shares
    $shareCount = SocialShare::where('telegram_user_id', $user->id)->count();
    
    // Define milestones
    $milestones = [
        5 => 'bronze_social_badge',
        25 => 'silver_social_badge',
        50 => 'gold_social_badge',
        100 => 'platinum_social_badge'
    ];
    
    // Check if user has reached a milestone
    foreach ($milestones as $count => $prizeIdentifier) {
        if ($shareCount === $count) {
            // Find the prize with this identifier
            $prize = Prize::where('reward_data->reward_id', $prizeIdentifier)->first();
            
            if (!$prize) {
                continue;
            }
            
            // Check if user already has this prize
            $userPrize = UserPrize::where('telegram_user_id', $user->id)
                ->where('prize_id', $prize->id)
                ->first();
                
            if ($userPrize) {
                continue;
            }
            
            // Award the prize to the user
            DB::transaction(function () use ($user, $prize) {
                UserPrize::create([
                    'telegram_user_id' => $user->id,
                    'prize_id' => $prize->id,
                    'unlocked_at' => now()
                ]);
            });
        }
    }
}
```

## 6. Integration with Game Collection

### 6.1 Game Achievement Points

Award achievement points for game milestones:

```php
// app/Http/Controllers/API/GameController.php

/**
 * Save a game score and award achievement points.
 */
public function saveScore(Request $request)
{
    $request->validate([
        'game_id' => 'required|string',
        'score' => 'required|integer'
    ]);
    
    $user = $request->user();
    $gameId = $request->game_id;
    $score = $request->score;
    
    // Save the score
    $gameScore = GameScore::create([
        'telegram_user_id' => $user->id,
        'game_id' => $gameId,
        'score' => $score,
        'played_at' => now()
    ]);
    
    // Get user's high score for this game
    $highScore = GameScore::where('telegram_user_id', $user->id)
        ->where('game_id', $gameId)
        ->max('score');
    
    // Check if this is a new high score
    $isNewHighScore = $score === $highScore;
    
    // Award achievement points for new high scores
    if ($isNewHighScore) {
        $achievementPointService = new AchievementPointService();
        $achievementPointService->awardPoints(
            $user->id,
            1, // Award 1 point for each new high score
            'game_high_score',
            $gameId,
            "New high score in {$gameId}: {$score}"
        );
    }
    
    // Check for game-specific milestones
    $this->checkGameMilestones($user, $gameId, $score);
    
    return response()->json([
        'message' => 'Score saved successfully',
        'is_new_high_score' => $isNewHighScore,
        'high_score' => $highScore
    ]);
}

/**
 * Check for game-specific milestones and award prizes.
 */
private function checkGameMilestones($user, $gameId, $score)
{
    // Define game-specific score milestones
    $gameMilestones = [
        'tower' => [
            10 => 'tower_bronze_badge',
            20 => 'tower_silver_badge',
            30 => 'tower_gold_badge',
            50 => 'tower_platinum_badge'
        ],
        'rabbit' => [
            1000 => 'rabbit_bronze_badge',
            2500 => 'rabbit_silver_badge',
            5000 => 'rabbit_gold_badge',
            10000 => 'rabbit_platinum_badge'
        ],
        'slash' => [
            500 => 'slash_bronze_badge',
            1000 => 'slash_silver_badge',
            2500 => 'slash_gold_badge',
            5000 => 'slash_platinum_badge'
        ]
    ];
    
    // Check if this game has milestones
    if (!isset($gameMilestones[$gameId])) {
        return;
    }
    
    // Get milestones for this game
    $milestones = $gameMilestones[$gameId];
    
    // Find the highest milestone reached
    $reachedMilestone = null;
    $prizeIdentifier = null;
    
    foreach ($milestones as $milestone => $identifier) {
        if ($score >= $milestone && ($reachedMilestone === null || $milestone > $reachedMilestone)) {
            $reachedMilestone = $milestone;
            $prizeIdentifier = $identifier;
        }
    }
    
    if ($reachedMilestone === null || $prizeIdentifier === null) {
        return;
    }
    
    // Find the prize with this identifier
    $prize = Prize::where('reward_data->reward_id', $prizeIdentifier)->first();
    
    if (!$prize) {
        return;
    }
    
    // Check if user already has this prize
    $userPrize = UserPrize::where('telegram_user_id', $user->id)
        ->where('prize_id', $prize->id)
        ->first();
        
    if ($userPrize) {
        return;
    }
    
    // Award the prize to the user
    DB::transaction(function () use ($user, $prize) {
        UserPrize::create([
            'telegram_user_id' => $user->id,
            'prize_id' => $prize->id,
            'unlocked_at' => now()
        ]);
    });
    
    // Award achievement points for reaching the milestone
    $achievementPointService = new AchievementPointService();
    $achievementPointService->awardPoints(
        $user->id,
        2, // Award 2 points for reaching a game milestone
        'game_milestone',
        $gameId,
        "Reached {$gameId} milestone: {$reachedMilestone}"
    );
}
```

### 6.2 Game Cosmetic Rewards

Implement game-specific cosmetic rewards:

```php
// app/Http/Controllers/API/GameCosmeticController.php

/**
 * Get the user's game-specific cosmetic effects.
 */
public function getGameCosmetics(Request $request, $gameId)
{
    $user = $request->user();
    
    // Get user's equipped cosmetic prizes
    $equippedPrizes = $user->prizes()
        ->wherePivot('is_equipped', true)
        ->whereHas('prize', function ($query) use ($gameId) {
            $query->where('reward_type', 'cosmetic')
                ->whereJsonContains('reward_data->game_id', $gameId);
        })
        ->get();
    
    // Format the cosmetics for the game
    $gameCosmetics = [];
    
    foreach ($equippedPrizes as $userPrize) {
        $prize = $userPrize->prize;
        $rewardDetails = $prize->getRewardDetails();
        
        $gameCosmetics[] = [
            'id' => $prize->id,
            'name' => $prize->name,
            'type' => $rewardDetails['type'] ?? 'unknown',
            'visual_data' => $rewardDetails['visual_data'] ?? []
        ];
    }
    
    return response()->json($gameCosmetics);
}
```
