import { GameInstance } from '../../games/registry';
import { resourceManager } from '../../games/resource-manager';
import { loadGameScripts, getLoadingProgress } from './loader';

/**
 * Initialize and return a Rabbit Game instance
 * @param options Game options from GameWrapper
 * @returns GameInstance compatible with the registry
 */
export const rabbitGame = (options: any): GameInstance => {
  // Game instance reference
  let engine: any = null;
  let assetLoader: any = null;
  let canvasElement: HTMLCanvasElement | null = null;
  let gameInitialized = false;

  // Create GameInstance wrapper
  const gameInstance: GameInstance = {
    // Load assets
    load: (onReady, onProgress) => {
      // First load all required scripts
      loadGameScripts()
        .then(() => {
          try {
            // Get the canvas element
            canvasElement = document.getElementById(options.canvasId) as HTMLCanvasElement;
            if (!canvasElement) {
              console.error(`Canvas element with ID ${options.canvasId} not found`);
              onProgress({
                success: 0,
                total: 1,
                failed: 1
              });
              return;
            }

            // Check if required classes are available
            console.log('Window object keys:', Object.keys(window));

            if (!(window as any).Engine) {
              console.error('Engine class not found in window object');
            }

            if (!(window as any).AssetLoader) {
              console.error('AssetLoader class not found in window object');
            }

            if (!(window as any).Engine || !(window as any).AssetLoader) {
              console.error('Required game classes not found. Engine or AssetLoader missing.');
              onProgress({
                success: 0,
                total: 1,
                failed: 1
              });
              return;
            }

            // Create engine instance
            engine = new (window as any).Engine(options.canvasId, {
              width: options.width,
              height: options.height,
              debug: options.debug || false
            });

            // Create asset loader
            assetLoader = new (window as any).AssetLoader(engine, {
              usePathGenerator: true,
              pathGenerator: (path: string) => {
                // Remove 'assets/' prefix if it exists in the path
                const cleanPath = path.replace(/^assets\//, '');
                return `/game/rabbit/${cleanPath}`;
              }
            });

            // Store integration callbacks in the engine for state access
            if (options.setGameScore) {
              engine.setVariable('setGameScore', options.setGameScore);
            }
            if (options.onGameOver) {
              engine.setVariable('onGameOver', options.onGameOver);
            }

            // Override the loadAll method to call onReady when assets are loaded
            const originalLoadAll = assetLoader.loadAll.bind(assetLoader);
            assetLoader.loadAll = (callback?: () => void) => {
              originalLoadAll(() => {
                // Create game states
                createGameStates();

                // Start with menu state
                engine.setState((window as any).CONSTANTS.STATES.MENU);

                // Start game loop
                engine.start();

                gameInitialized = true;
                onReady();

                if (callback) callback();
              });
            };

            // Show loading screen
            showLoadingScreen();

            // Start loading assets
            assetLoader.loadAll();
          } catch (error) {
            console.error('Error initializing game:', error);
            onProgress({
              success: 0,
              total: 1,
              failed: 1
            });
          }
        })
        .catch((error) => {
          console.error('Failed to load Rabbit Game scripts:', error);
          // Report loading progress with error
          onProgress({
            success: 0,
            total: 1,
            failed: 1
          });
        });

      // Report script loading progress
      const updateProgress = () => {
        const progress = getLoadingProgress();
        onProgress({
          success: progress.loaded,
          total: progress.total,
          failed: 0
        });

        if (progress.loaded < progress.total) {
          requestAnimationFrame(updateProgress);
        }
      };

      updateProgress();
    },

    // Start game
    start: () => {
      // Game is already started in load
      if (engine && gameInitialized) {
        // Ensure game is in the correct state
        engine.setState((window as any).CONSTANTS.STATES.PLAYING);
      }
    },

    // Destroy game
    destroy: () => {
      if (!engine) return;

      try {
        // Stop all game processes
        engine.setVariable('gameStartNow', false);

        // Stop the game loop
        engine.stop();

        // Explicitly stop all audio instances
        // Background music
        const bgmInstance = engine.getVariable('bgmInstance');
        if (bgmInstance) {
          engine.stopSound(bgmInstance);
        }

        // Menu music
        const menuMusicInstance = engine.getVariable('menuMusicInstance');
        if (menuMusicInstance) {
          engine.stopSound(menuMusicInstance);
        }

        // Game over sound
        const gameOverInstance = engine.getVariable('gameOverInstance');
        if (gameOverInstance) {
          engine.stopSound(gameOverInstance);
        }

        // Stop all other sounds by iterating through the sounds Map
        if (engine.sounds && typeof engine.sounds.forEach === 'function') {
          engine.sounds.forEach((sound: HTMLAudioElement, id: string) => {
            try {
              // Create a clone to stop it
              const tempInstance = sound.cloneNode() as HTMLAudioElement;
              tempInstance.pause();
              tempInstance.currentTime = 0;

              // Remove references
              if (tempInstance.parentNode) {
                tempInstance.parentNode.removeChild(tempInstance);
              }
            } catch (soundError) {
              console.warn(`Error stopping sound ${id}:`, soundError);
            }
          });
        }

        // Clean up any audio elements in the DOM
        const gameAudioElements = document.querySelectorAll('audio[src*="/game/rabbit/"]');
        gameAudioElements.forEach((audio) => {
          try {
            const audioElement = audio as HTMLAudioElement;
            audioElement.pause();
            audioElement.currentTime = 0;
            // Remove the element to prevent any queued sounds
            if (audioElement.parentNode) {
              audioElement.parentNode.removeChild(audioElement);
            }
          } catch (audioError) {
            console.warn('Error cleaning up audio element:', audioError);
          }
        });

        // Clear any audio container
        const audioContainer = document.querySelector('#game-audio-container');
        if (audioContainer) {
          audioContainer.innerHTML = '';
        }

        // Destroy engine (this will handle event listeners and basic cleanup)
        engine.destroy();

        // Release resources through the resource manager
        resourceManager.releaseResources('rabbit');
      } catch (error) {
        console.error('Error destroying game:', error);
      }

      // Clear canvas
      if (canvasElement) {
        const ctx = canvasElement.getContext('2d');
        if (ctx) {
          ctx.clearRect(0, 0, canvasElement.width, canvasElement.height);
        }
      }

      // Clear references
      engine = null;
      assetLoader = null;
      gameInitialized = false;
    },

    // Play background music
    playBgm: () => {
      if (!engine) return;

      try {
        const bgmInstance = engine.playSound('gameMusic', { loop: true, volume: 0.5 });
        if (bgmInstance) {
          engine.setVariable('bgmInstance', bgmInstance);
        }
      } catch (error) {
        console.error('Error playing background music:', error);
      }
    },

    // Pause background music
    pauseBgm: () => {
      if (!engine) return;

      try {
        const bgmInstance = engine.getVariable('bgmInstance');
        if (bgmInstance) {
          engine.stopSound(bgmInstance);
        }
      } catch (error) {
        console.error('Error pausing background music:', error);
      }
    },

    // Reset game state
    resetGameState: () => {
      if (!engine) return;

      try {
        // Reset any game-specific state variables
        engine.setVariable('gameStartNow', false);

        // Clear any active instances
        engine.stop();
      } catch (error) {
        console.error('Error resetting game state:', error);
      }
    },

    // Clear event listeners
    clearEventListeners: () => {
      if (!engine) return;

      try {
        // Remove window event listeners for keyboard events
        window.removeEventListener('keydown', (engine as any).keyDownHandler);
        window.removeEventListener('keyup', (engine as any).keyUpHandler);

        // Remove canvas event listeners
        if (canvasElement) {
          canvasElement.removeEventListener('touchstart', (engine as any).touchStartHandler);
          canvasElement.removeEventListener('touchend', (engine as any).touchEndHandler);
          canvasElement.removeEventListener('mousedown', (engine as any).mouseDownHandler);
          canvasElement.removeEventListener('mouseup', (engine as any).mouseUpHandler);
        }

        // Clear any other event listeners
        if (typeof engine.removeEventListeners === 'function') {
          engine.removeEventListeners();
        }
      } catch (error) {
        console.error('Error clearing event listeners:', error);
      }
    }
  };

  /**
   * Show loading screen
   */
  const showLoadingScreen = () => {
    if (!engine) return;

    const ctx = engine.ctx;
    const width = engine.width;
    const height = engine.height;

    // Animation frame for loading screen
    const updateLoading = () => {
      // Clear canvas
      ctx.fillStyle = '#000000';
      ctx.fillRect(0, 0, width, height);

      // Update progress
      const loadingProgress = assetLoader ? assetLoader.getProgress() : 0;

      // Draw loading text
      ctx.fillStyle = '#FFFFFF';
      ctx.font = 'bold 30px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('LOADING...', width / 2, height / 2 - 50);

      // Draw progress bar
      const barWidth = width * 0.7;
      const barHeight = 20;
      const barX = (width - barWidth) / 2;
      const barY = height / 2;

      // Draw bar background
      ctx.fillStyle = '#333333';
      ctx.fillRect(barX, barY, barWidth, barHeight);

      // Draw progress
      ctx.fillStyle = '#00FF00';
      ctx.fillRect(barX, barY, barWidth * loadingProgress, barHeight);

      // Draw percentage
      ctx.fillStyle = '#FFFFFF';
      ctx.font = 'bold 16px Arial';
      ctx.fillText(`${Math.round(loadingProgress * 100)}%`, width / 2, barY + barHeight + 20);

      // Continue animation if not done loading
      if (loadingProgress < 1) {
        requestAnimationFrame(updateLoading);
      }
    };

    // Start loading animation
    updateLoading();
  };

  /**
   * Create game states
   */
  const createGameStates = () => {
    if (!engine) return;

    try {
      // Create states
      const menuState = new (window as any).MenuState(engine);
      const playState = new (window as any).PlayState(engine);
      const gameOverState = new (window as any).GameOverState(engine);

      // Add states to engine
      engine.addState((window as any).CONSTANTS.STATES.MENU, menuState);
      engine.addState((window as any).CONSTANTS.STATES.PLAYING, playState);
      engine.addState((window as any).CONSTANTS.STATES.GAME_OVER, gameOverState);
    } catch (error) {
      console.error('Error creating game states:', error);
    }
  };

  return gameInstance;
};