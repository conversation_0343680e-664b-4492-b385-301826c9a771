import { useState, useEffect, useRef } from 'react';

// Custom SecurityError for safe error handling
class SecurityError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'SecurityError';
  }
}

// Whitelist of allowed public assets with enhanced validation
const assetUrls = Object.freeze([
  // Core UI assets
  '/images/logo.png',
  '/images/splash-screen/bg.png',
  '/images/loader.png',
  '/images/blur.png',
  
  // Game assets (excluding game-specific assets that have their own loading)
  '/game/back.jpg',
  '/game/background.png',
  '/game/block.png',
  '/game/block-perfect.png',
  '/game/block-rope.png',
  '/game/c1.png',
  '/game/c2.png',
  
  // Fonts
  '/fonts/battlx.woff'
].map(url => {
  // Enhanced URL validation at initialization
  if (!/^\/(?:[a-zA-Z0-9\-_]+\/)*[a-zA-Z0-9\-_]+\.(png|jpg|woff)$/i.test(url)) {
    throw new SecurityError('Invalid asset URL format');
  }
  if (url.includes('..') || url.startsWith('http')) {
    throw new SecurityError('Invalid asset URL');
  }
  return url;
}));

interface AssetPreloaderHook {
  assetsLoaded: boolean;
  progress: number;
}

interface RateLimiterWindow {
  count: number;
  timestamp: number;
}

class SlidingWindowRateLimiter {
  private windows: Map<string, RateLimiterWindow[]> = new Map();
  private readonly windowMs: number;
  private readonly maxRequests: number;

  constructor(windowMs: number = 1000, maxRequests: number = 5) {
    this.windowMs = windowMs;
    this.maxRequests = maxRequests;
  }

  checkLimit(key: string): boolean {
    const now = Date.now();
    const windows = this.windows.get(key) || [];
    
    // Remove expired windows
    const validWindows = windows.filter(w => now - w.timestamp < this.windowMs);
    
    // Count total requests in valid windows
    const totalRequests = validWindows.reduce((sum, w) => sum + w.count, 0);
    
    if (totalRequests >= this.maxRequests) {
      return false;
    }

    // Add new request
    validWindows.push({ count: 1, timestamp: now });
    this.windows.set(key, validWindows);
    
    return true;
  }

  clear(key: string) {
    this.windows.delete(key);
  }

  clearAll() {
    this.windows.clear();
  }
}

export const useAssetPreloader = (): AssetPreloaderHook => {
  const [assetsLoaded, setAssetsLoaded] = useState(false);
  const [progress, setProgress] = useState(0);
  const loadingImagesRef = useRef<HTMLImageElement[]>([]);
  const timeoutsRef = useRef<Map<string, number>>(new Map());
  const rateLimiterRef = useRef<SlidingWindowRateLimiter>(new SlidingWindowRateLimiter());

  useEffect(() => {
    let isMounted = true;
    const loadTimeout = 10000; // 10 seconds timeout

    const loadAsset = async (url: string): Promise<void> => {
      // Security: Enhanced URL validation at runtime
      if (!assetUrls.includes(url)) {
        throw new SecurityError('Asset not in whitelist');
      }

      if (!rateLimiterRef.current.checkLimit(url)) {
        throw new SecurityError('Rate limit exceeded');
      }

      return new Promise((resolve, reject) => {
        const timeoutId = window.setTimeout(() => {
          reject(new SecurityError('Loading timeout'));
        }, loadTimeout);

        timeoutsRef.current.set(url, timeoutId);

        if (url.endsWith('.woff')) {
          const font = new FontFace('battlx', `url(${url})`);
          font.load()
            .then(() => {
              if (isMounted) {
                document.fonts.add(font);
                clearTimeout(timeoutId);
                timeoutsRef.current.delete(url);
                resolve();
              }
            })
            .catch(() => {
              clearTimeout(timeoutId);
              timeoutsRef.current.delete(url);
              reject(new SecurityError('Font loading failed'));
            });
        } else {
          const img = new Image();
          loadingImagesRef.current.push(img);

          img.onload = () => {
            clearTimeout(timeoutId);
            timeoutsRef.current.delete(url);
            loadingImagesRef.current = loadingImagesRef.current.filter(i => i !== img);
            resolve();
          };

          img.onerror = () => {
            clearTimeout(timeoutId);
            timeoutsRef.current.delete(url);
            loadingImagesRef.current = loadingImagesRef.current.filter(i => i !== img);
            reject(new SecurityError('Image loading failed'));
          };

          img.src = url;
        }
      });
    };

    const preloadAssets = async () => {
      let loaded = 0;
      const total = assetUrls.length;

      try {
        await Promise.all(
          assetUrls.map(async (url) => {
            try {
              await loadAsset(url);
              if (isMounted) {
                loaded++;
                setProgress((loaded / total) * 100);
              }
            } catch (error) {
              // Safe error logging without exposing internals
              console.error(error instanceof SecurityError ? error.message : 'Asset loading error');
            }
          })
        );

        if (isMounted) {
          setAssetsLoaded(true);
        }
      } catch (error) {
        console.error('Asset preloading failed');
        if (isMounted) {
          setAssetsLoaded(true);
        }
      }
    };

    preloadAssets();

    // Enhanced cleanup
    return () => {
      isMounted = false;
      
      // Clear all timeouts
      timeoutsRef.current.forEach(timeoutId => clearTimeout(timeoutId));
      timeoutsRef.current.clear();
      
      // Clear rate limiter
      rateLimiterRef.current.clearAll();
      
      // Clear loading images
      loadingImagesRef.current.forEach(img => {
        img.src = ''; // Cancel any pending loads
        img.onload = null;
        img.onerror = null;
      });
      loadingImagesRef.current = [];
      
      // Reset states
      setProgress(0);
      setAssetsLoaded(false);
    };
  }, []);

  return { assetsLoaded, progress };
};