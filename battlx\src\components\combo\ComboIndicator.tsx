import React from 'react';
import { useSpring, animated } from 'react-spring';
import { useComboStore, COMBO_ENERGY_COST_MULTIPLIER } from '@/store/combo-store';

export const ComboIndicator: React.FC = () => {
  const { comboMultiplier, comboActive, comboLevel } = useComboStore();
  
  // Dynamic styles based on combo level
  const getComboColor = () => {
    switch (comboLevel) {
      case 'legendary': return '#FFD700'; // Gold
      case 'extreme': return '#FF4500'; // Red-Orange
      case 'high': return '#FF1493'; // Deep Pink
      case 'medium': return '#9370DB'; // Medium Purple
      case 'low': 
      default: return '#4169E1'; // Royal Blue
    }
  };
  
  // Scale animation based on multiplier
  const scaleValue = Math.min(2, 1 + (comboMultiplier / 100));
  
  // React Spring animations - always define the hook regardless of comboActive state
  const springProps = useSpring({
    scale: scaleValue,
    opacity: 1,
    from: { scale: 1, opacity: 0 },
    config: { tension: 300, friction: 10 }
  });
  
  // Skip rendering if combo not active
  if (!comboActive) return null;
  
  return (
    <animated.div
      className="combo-indicator"
      style={{
        position: 'absolute',
        top: '20%',
        left: '50%',
        transform: springProps.scale.to(s => `translate(-50%, -50%) scale(${s})`),
        color: getComboColor(),
        opacity: springProps.opacity,
        textShadow: `0 0 10px ${getComboColor()}`,
        fontWeight: 'bold',
        fontSize: '2rem',
        zIndex: 100,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }}
    >
      <div>{comboMultiplier}x</div>
      <div style={{
        fontSize: '0.8rem',
        marginTop: '5px',
        backgroundColor: 'rgba(255, 0, 0, 0.6)',
        padding: '2px 6px',
        borderRadius: '4px',
        color: 'white'
      }}>
        Energy: {COMBO_ENERGY_COST_MULTIPLIER}x
      </div>
    </animated.div>
  );
};

export default ComboIndicator;