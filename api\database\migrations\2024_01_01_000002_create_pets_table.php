<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->foreignId('pet_template_id')->constrained('pet_templates')->onDelete('cascade');
            
            // Pet state
            $table->integer('level')->default(1);
            $table->integer('experience')->default(0);
            $table->integer('happiness')->default(50);
            
            // Interaction tracking
            $table->timestamp('last_fed')->nullable();
            $table->timestamp('last_played')->nullable();
            $table->timestamp('last_petted')->nullable();
            $table->integer('daily_interaction_count')->default(0);
            $table->date('daily_interaction_reset_date')->default(DB::raw('CURRENT_DATE'));
            
            // Status flags
            $table->boolean('is_featured')->default(false); // For home screen display
            $table->boolean('is_favorite')->default(false);
            $table->string('nickname', 50)->nullable();
            
            // Evolution tracking
            $table->integer('evolution_stage')->default(0); // 0=base, 1=first, 2=second, etc.
            $table->timestamp('last_evolution')->nullable();
            
            $table->timestamps();
            
            $table->index(['telegram_user_id', 'is_featured']);
            $table->index(['telegram_user_id', 'pet_template_id']);
            $table->unique(['telegram_user_id', 'pet_template_id']); // One pet per template per user
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pets');
    }
};
