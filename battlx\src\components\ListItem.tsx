import { cn } from "@/lib/utils";

type Props = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  icon?: JSX.Element;
  image?: string;
  title: string | JSX.Element;
  subtitle?: string | JSX.Element;
  action?: string | JSX.Element | false;
  variant?: 'default' | 'square';
};

export default function ListItem({
  icon,
  image,
  title,
  subtitle,
  className,
  action,
  variant = 'default',
  ...props
}: Props) {
  const baseStyles = "bg-[#1A1617] border border-[#B3B3B3]/20 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)] hover:bg-[#4A0E0E]/30 transition-all duration-300";

  if (variant === 'square') {
    return (
      <button
        className={cn(
          baseStyles,
          "group w-32 h-32 p-4 flex flex-col items-center justify-center rounded-2xl",
          className
        )}
        type="button"
        {...props}
      >
        {icon ? (
          <div className="w-16 h-16 text-[#9B8B6C] opacity-80 mb-2">
            {icon}
          </div>
        ) : image && (
          <div className="w-16 h-16 relative mb-2">
            <img
              src={image}
              alt={typeof title === 'string' ? title : 'thumbnail'}
              className="w-full h-full object-contain opacity-80 [filter:sepia(50%)_saturate(50%)_brightness(80%)_contrast(120%)]"
            />
          </div>
        )}
        <div className="text-sm font-medium text-center">
          <p className="text-[#9B8B6C] truncate max-w-[100px]">
            {title}
          </p>
          {subtitle && (
            <p className="text-xs text-[#B3B3B3]/80 truncate max-w-[100px]">
              {subtitle}
            </p>
          )}
        </div>
        {action && (
          <div className="absolute top-2 right-2">
            {action}
          </div>
        )}
      </button>
    );
  }

  return (
    <button
      className={cn(
        baseStyles,
        "group flex items-center w-full gap-4 px-4 py-2 rounded-xl",
        className
      )}
      type="button"
      {...props}
    >
      {icon ? (
        <div className="w-9 h-9 text-[#9B8B6C] opacity-80">
          {icon}
        </div>
      ) : image && (
        <div className="w-9 h-9 relative">
          <img
            src={image}
            alt={typeof title === 'string' ? title : 'thumbnail'}
            className="w-full h-full object-cover opacity-80 [filter:sepia(50%)_saturate(50%)_brightness(80%)_contrast(120%)]"
          />
        </div>
      )}
      <div className="text-sm font-medium text-left text-[#B3B3B3]">
        <p className="text-[#B3B3B3]">{title}</p>
        {subtitle}
      </div>
      <div className="ml-auto">{action}</div>
    </button>
  );
}
