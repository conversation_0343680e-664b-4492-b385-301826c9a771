/**
 * Game Engine
 * Handles the core functionality of the game including:
 * - Canvas setup
 * - Game loop
 * - Asset management
 * - State management
 */
class Engine {
    /**
     * @param {string} canvasId - The ID of the canvas element
     * @param {Object} options - Engine options
     * @param {number} options.width - Canvas width
     * @param {number} options.height - Canvas height
     * @param {boolean} options.debug - Enable debug mode
     */
    constructor(canvasId, options = {}) {
        // Canvas setup
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.width = options.width || 540;
        this.height = options.height || 960;
        this.canvas.width = this.width;
        this.canvas.height = this.height;

        // Game state
        this.currentState = null;
        this.states = {};
        this.debug = options.debug || false;
        this.paused = false;

        // Animation frame
        this.animationFrameId = null;
        this.lastTime = 0;
        this.deltaTime = 0;

        // Assets
        this.images = new Map();
        this.sounds = new Map();
        this.sprites = new Map();

        // Game variables
        this.variables = new Map();

        // Event listeners
        this.setupEventListeners();
    }

    /**
     * Start the game loop
     */
    start() {
        if (this.currentState) {
            this.lastTime = performance.now();
            this.loop(this.lastTime);
        } else {
            console.error('No game state set. Use setState() before starting the game.');
        }
    }

    /**
     * Main game loop
     * @param {number} timestamp - Current timestamp
     */
    loop(timestamp) {
        // Calculate delta time
        this.deltaTime = (timestamp - this.lastTime) / 1000;
        this.lastTime = timestamp;

        // Clear the canvas
        this.ctx.clearRect(0, 0, this.width, this.height);

        // Update and render current state
        if (!this.paused && this.currentState) {
            this.currentState.update(this.deltaTime);
            this.currentState.render(this.ctx);
        }

        // Debug info
        if (this.debug) {
            this.drawDebugInfo();
        }

        // Continue the loop
        this.animationFrameId = requestAnimationFrame(this.loop.bind(this));
    }

    /**
     * Stop the game loop
     */
    stop() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    }

    /**
     * Toggle pause state
     */
    togglePause() {
        this.paused = !this.paused;
    }

    /**
     * Add a game state
     * @param {string} name - State name
     * @param {Object} state - State object
     */
    addState(name, state) {
        this.states[name] = state;
        state.init(this);
    }

    /**
     * Set the current game state
     * @param {string} name - State name
     */
    setState(name) {
        if (this.states[name]) {
            if (this.currentState && this.currentState.exit) {
                this.currentState.exit();
            }
            this.currentState = this.states[name];
            if (this.currentState.enter) {
                this.currentState.enter();
            }
        } else {
            console.error(`State "${name}" not found.`);
        }
    }

    /**
     * Load an image
     * @param {string} id - Image identifier
     * @param {string} path - Image path
     * @returns {Promise} - Promise that resolves when the image is loaded
     */
    loadImage(id, path) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.images.set(id, img);
                resolve(img);
            };
            img.onerror = () => {
                reject(new Error(`Failed to load image: ${path}`));
            };
            img.src = path;
        });
    }

    /**
     * Get a loaded image
     * @param {string} id - Image identifier
     * @returns {HTMLImageElement} - The image
     */
    getImage(id) {
        return this.images.get(id);
    }

    /**
     * Load a sound
     * @param {string} id - Sound identifier
     * @param {string} path - Sound path
     * @returns {Promise} - Promise that resolves when the sound is loaded
     */
    loadSound(id, path) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.oncanplaythrough = () => {
                this.sounds.set(id, audio);
                resolve(audio);
            };
            audio.onerror = () => {
                reject(new Error(`Failed to load sound: ${path}`));
            };
            audio.src = path;
            audio.load();
        });
    }

    /**
     * Play a sound
     * @param {string} id - Sound identifier
     * @param {Object} options - Sound options
     * @param {number} options.volume - Volume (0-1)
     * @param {boolean} options.loop - Loop the sound
     * @returns {HTMLAudioElement} - The audio element
     */
    playSound(id, options = {}) {
        const sound = this.sounds.get(id);
        if (sound) {
            const soundInstance = sound.cloneNode();
            soundInstance.volume = options.volume !== undefined ? options.volume : 1;
            soundInstance.loop = options.loop || false;
            soundInstance.play().catch(error => {
                console.warn('Audio playback prevented:', error);
            });
            return soundInstance;
        }
        return null;
    }

    /**
     * Stop a sound
     * @param {HTMLAudioElement} soundInstance - The sound instance to stop
     */
    stopSound(soundInstance) {
        if (soundInstance) {
            soundInstance.pause();
            soundInstance.currentTime = 0;
        }
    }

    /**
     * Load a sprite atlas
     * @param {string} id - Sprite identifier
     * @param {string} imagePath - Image path
     * @param {Object} config - Sprite configuration
     * @returns {Promise} - Promise that resolves when the sprite is loaded
     */
    loadSpriteAtlas(id, imagePath, config) {
        return this.loadImage(id, imagePath).then(image => {
            this.sprites.set(id, {
                image,
                config
            });
            return this.sprites.get(id);
        });
    }

    /**
     * Create a sprite animation
     * @param {string} id - Sprite identifier
     * @param {Object} options - Animation options
     * @param {string} options.animName - Optional specific animation name within the sprite atlas
     * @returns {Object} - Sprite animation object
     */
    createSprite(id, options = {}) {
        const spriteData = this.sprites.get(id);
        if (!spriteData) {
            console.error(`Sprite data not found for id: ${id}`);
            return null;
        }

        const { image, config } = spriteData;
        console.log(`Creating sprite for id: ${id}`, config);

        // If animName is provided, use that specific animation config
        const animConfig = options.animName && config[options.animName] ? config[options.animName] : config;
        const { x, y, width, height, sliceX, anims } = animConfig;

        if (!anims) {
            console.error(`No animations found for sprite: ${id}`, animConfig);
        }

        const frameWidth = width / sliceX;

        return {
            image,
            x,
            y,
            width: frameWidth,
            height,
            frameWidth,
            frameHeight: height,
            totalFrames: sliceX,
            currentFrame: 0,
            currentAnim: null,
            anims,
            animTimer: 0,

            /**
             * Play an animation
             * @param {string} animName - Animation name
             */
            play(animName) {
                if (this.anims && this.anims[animName]) {
                    this.currentAnim = animName;
                    this.currentFrame = this.anims[animName].from;
                }
            },

            /**
             * Update the sprite animation
             * @param {number} deltaTime - Time since last update
             */
            update(deltaTime) {
                if (this.currentAnim) {
                    const anim = this.anims[this.currentAnim];
                    this.animTimer += deltaTime;

                    if (this.animTimer >= 1 / anim.speed) {
                        this.currentFrame++;
                        this.animTimer = 0;

                        if (this.currentFrame > anim.to) {
                            if (anim.loop) {
                                this.currentFrame = anim.from;
                            } else {
                                this.currentFrame = anim.to;
                            }
                        }
                    }
                }
            },

            /**
             * Draw the sprite
             * @param {CanvasRenderingContext2D} ctx - Canvas context
             * @param {number} x - X position
             * @param {number} y - Y position
             * @param {number} scale - Scale factor
             */
            draw(ctx, x, y, scale = 1) {
                try {
                    const frameX = this.x + (this.currentFrame * this.frameWidth);

                    // Draw debug outline
                    if (window.DEBUG_SPRITES) {
                        ctx.strokeStyle = 'red';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(
                            x - (this.frameWidth * scale / 2),
                            y - (this.frameHeight * scale / 2),
                            this.frameWidth * scale,
                            this.frameHeight * scale
                        );
                    }

                    ctx.drawImage(
                        this.image,
                        frameX, this.y,
                        this.frameWidth, this.frameHeight,
                        x - (this.frameWidth * scale / 2), y - (this.frameHeight * scale / 2),
                        this.frameWidth * scale, this.frameHeight * scale
                    );
                } catch (error) {
                    console.error('Error drawing sprite:', error);
                    // Draw fallback rectangle
                    ctx.fillStyle = 'purple';
                    ctx.fillRect(
                        x - (this.frameWidth * scale / 2),
                        y - (this.frameHeight * scale / 2),
                        this.frameWidth * scale,
                        this.frameHeight * scale
                    );
                }
            }
        };
    }

    /**
     * Set a game variable
     * @param {string} name - Variable name
     * @param {*} value - Variable value
     */
    setVariable(name, value) {
        this.variables.set(name, value);
    }

    /**
     * Get a game variable
     * @param {string} name - Variable name
     * @param {*} defaultValue - Default value if variable doesn't exist
     * @returns {*} - Variable value
     */
    getVariable(name, defaultValue = null) {
        return this.variables.has(name) ? this.variables.get(name) : defaultValue;
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Keyboard events
        this.keys = new Set();
        window.addEventListener('keydown', e => {
            this.keys.add(e.key.toLowerCase());
            if (this.currentState && this.currentState.onKeyDown) {
                this.currentState.onKeyDown(e.key.toLowerCase());
            }
        });

        window.addEventListener('keyup', e => {
            this.keys.delete(e.key.toLowerCase());
            if (this.currentState && this.currentState.onKeyUp) {
                this.currentState.onKeyUp(e.key.toLowerCase());
            }
        });

        // Touch events
        this.touchActive = false;
        this.canvas.addEventListener('touchstart', e => {
            e.preventDefault();
            this.touchActive = true;
            if (this.currentState && this.currentState.onTouchStart) {
                this.currentState.onTouchStart(e.touches[0].clientX, e.touches[0].clientY);
            }
        });

        this.canvas.addEventListener('touchend', e => {
            e.preventDefault();
            this.touchActive = false;
            if (this.currentState && this.currentState.onTouchEnd) {
                this.currentState.onTouchEnd();
            }
        });

        // Mouse events
        this.mouseDown = false;
        this.canvas.addEventListener('mousedown', e => {
            this.mouseDown = true;
            if (this.currentState && this.currentState.onMouseDown) {
                this.currentState.onMouseDown(e.offsetX, e.offsetY);
            }
        });

        this.canvas.addEventListener('mouseup', e => {
            this.mouseDown = false;
            if (this.currentState && this.currentState.onMouseUp) {
                this.currentState.onMouseUp(e.offsetX, e.offsetY);
            }
        });
    }

    /**
     * Check if a key is pressed
     * @param {string} key - Key to check
     * @returns {boolean} - True if key is pressed
     */
    isKeyPressed(key) {
        return this.keys.has(key.toLowerCase());
    }

    /**
     * Check if any key in a list is pressed
     * @param {Array<string>} keys - Keys to check
     * @returns {boolean} - True if any key is pressed
     */
    isAnyKeyPressed(keys) {
        return keys.some(key => this.isKeyPressed(key));
    }

    /**
     * Draw debug information
     */
    drawDebugInfo() {
        this.ctx.fillStyle = 'white';
        this.ctx.font = '14px Arial';
        this.ctx.fillText(`FPS: ${Math.round(1 / this.deltaTime)}`, 10, 20);
    }

    /**
     * Clean up resources
     */
    destroy() {
        this.stop();

        // Remove event listeners
        window.removeEventListener('keydown', this.keyDownHandler);
        window.removeEventListener('keyup', this.keyUpHandler);
        this.canvas.removeEventListener('touchstart', this.touchStartHandler);
        this.canvas.removeEventListener('touchend', this.touchEndHandler);
        this.canvas.removeEventListener('mousedown', this.mouseDownHandler);
        this.canvas.removeEventListener('mouseup', this.mouseUpHandler);

        // Clear assets
        this.images.clear();
        this.sounds.clear();
        this.sprites.clear();

        // Clear variables
        this.variables.clear();
    }
}
