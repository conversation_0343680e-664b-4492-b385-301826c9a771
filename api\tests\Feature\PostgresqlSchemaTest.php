<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Foundation\Testing\RefreshDatabase;

class PostgresqlSchemaTest extends TestCase
{
    use RefreshDatabase;

    public function test_database_connection()
    {
        $this->assertTrue(
            DB::connection() instanceof \Illuminate\Database\PostgresqlConnection,
            'Database connection is not PostgreSQL'
        );
    }

    public function test_unsigned_columns_constraints()
    {
        $testCases = [
            [
                'table' => 'telegram_users',
                'columns' => [
                    'balance' => -1,
                    'level_id' => -1,
                    'referred_by' => -1
                ]
            ],
            [
                'table' => 'mission_levels',
                'columns' => [
                    'level' => -1,
                    'cost' => -1,
                    'production_per_hour' => -1
                ]
            ],
            [
                'table' => 'levels',
                'columns' => [
                    'from_balance' => -1,
                    'to_balance' => -1
                ]
            ]
        ];

        foreach ($testCases as $test) {
            foreach ($test['columns'] as $column => $invalidValue) {
                try {
                    DB::table($test['table'])->insert([
                        $column => $invalidValue
                    ]);
                    $this->fail("Expected check constraint violation for {$test['table']}.{$column}");
                } catch (\PDOException $e) {
                    $this->assertStringContainsString(
                        'check constraint',
                        strtolower($e->getMessage()),
                        "Check constraint not properly enforced for {$test['table']}.{$column}"
                    );
                }
            }
        }
    }

    public function test_foreign_key_constraints()
    {
        $relationships = [
            [
                'from' => 'telegram_users',
                'to' => 'levels',
                'foreign_key' => 'level_id',
                'references' => 'id'
            ],
            [
                'from' => 'mission_levels',
                'to' => 'missions',
                'foreign_key' => 'mission_id',
                'references' => 'id'
            ],
            [
                'from' => 'tasks',
                'to' => 'task_types',
                'foreign_key' => 'type_id',
                'references' => 'id'
            ]
        ];

        foreach ($relationships as $rel) {
            try {
                // Try to insert with invalid foreign key
                DB::table($rel['from'])->insert([
                    $rel['foreign_key'] => 999999999
                ]);
                $this->fail("Expected foreign key violation for {$rel['from']}->{$rel['to']}");
            } catch (\PDOException $e) {
                $this->assertStringContainsString(
                    'foreign key constraint',
                    strtolower($e->getMessage()),
                    "Foreign key not properly enforced for {$rel['from']}->{$rel['to']}"
                );
            }
        }
    }

    public function test_unique_constraints()
    {
        $uniqueColumns = [
            'task_types' => ['name'],
            'telegram_users' => ['telegram_id'],
            'cache' => ['key']
        ];

        foreach ($uniqueColumns as $table => $columns) {
            foreach ($columns as $column) {
                // Insert first record
                DB::table($table)->insert([
                    $column => 'test_unique_' . time()
                ]);

                try {
                    // Try to insert duplicate
                    DB::table($table)->insert([
                        $column => 'test_unique_' . time()
                    ]);
                    $this->fail("Expected unique constraint violation for {$table}.{$column}");
                } catch (\PDOException $e) {
                    $this->assertStringContainsString(
                        'unique constraint',
                        strtolower($e->getMessage()),
                        "Unique constraint not properly enforced for {$table}.{$column}"
                    );
                }
            }
        }
    }

    public function test_cascade_deletes()
    {
        // Test level deletion cascading
        $levelId = DB::table('levels')->insertGetId([
            'level' => 1,
            'name' => 'Test Level',
            'from_balance' => 0,
            'to_balance' => 100
        ]);

        DB::table('telegram_users')->insert([
            'telegram_id' => time(),
            'first_name' => 'Test',
            'level_id' => $levelId,
            'balance' => 0,
            'earn_per_tap' => 1,
            'available_energy' => 500,
            'max_energy' => 500,
            'multi_tap_level' => 1,
            'energy_limit_level' => 1
        ]);

        $userCount = DB::table('telegram_users')
            ->where('level_id', $levelId)
            ->count();
        $this->assertGreaterThan(0, $userCount, 'Test data not inserted properly');

        // Delete level
        DB::table('levels')->where('id', $levelId)->delete();

        $remainingUsers = DB::table('telegram_users')
            ->where('level_id', $levelId)
            ->count();
        $this->assertEquals(0, $remainingUsers, 'Cascade delete not working properly');
    }

    public function test_check_constraints_complex()
    {
        // Test levels balance relationship
        try {
            DB::table('levels')->insert([
                'level' => 1,
                'name' => 'Invalid Level',
                'from_balance' => 100,
                'to_balance' => 50  // Should fail as to_balance < from_balance
            ]);
            $this->fail('Expected check constraint violation for levels balance relationship');
        } catch (\PDOException $e) {
            $this->assertStringContainsString(
                'check constraint',
                strtolower($e->getMessage()),
                'Balance relationship check constraint not properly enforced'
            );
        }
    }

    public function test_timestamp_columns()
    {
        $tables = ['telegram_users', 'missions', 'tasks'];
        
        foreach ($tables as $table) {
            $columns = Schema::getColumnListing($table);
            $this->assertTrue(
                in_array('created_at', $columns) && in_array('updated_at', $columns),
                "Timestamp columns missing in {$table}"
            );
            
            // Test auto-update of updated_at
            $id = DB::table($table)->insertGetId([
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            sleep(1); // Wait to ensure timestamp difference
            
            DB::table($table)->where('id', $id)->update(['updated_at' => now()]);
            
            $record = DB::table($table)->find($id);
            $this->assertNotEquals(
                $record->created_at,
                $record->updated_at,
                "updated_at not auto-updating in {$table}"
            );
        }
    }
}