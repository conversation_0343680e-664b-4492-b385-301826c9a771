import React from 'react';
import ComboIndicator from './ComboIndicator';
import ComboActivator from './ComboActivator';
import ComboEffects from './ComboEffects';
import FrenzySystem from './FrenzySystem';
import { usePrizeFeatureStore } from '@/store/prize-feature-store';

/**
 * ComboSystem component that wraps all combo-related components
 * This can be imported into the main App component
 */
function ComboSystem() {
  const { comboEnabled, frenzyEnabled } = usePrizeFeatureStore();

  return (
    <React.Fragment>
      {comboEnabled && (
        <>
          <ComboIndicator />
          <ComboActivator />
          <ComboEffects />
        </>
      )}
      {frenzyEnabled && comboEnabled && (
        <FrenzySystem />
      )}
    </React.Fragment>
  );
}

export default ComboSystem;