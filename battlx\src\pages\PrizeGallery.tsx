
import UserPrizeGallery from '@/components/prizetree/UserPrizeGallery';
import UserGameDetails from '@/components/UserGameDetails';
import { Link } from 'react-router-dom';

export default function PrizeGallery() {
  return (
    <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1">
      <div className="flex flex-col flex-1 w-full h-full px-6 pb-20 mt-8 modal-body">
        <UserGameDetails className="mt-4" />

        <div className="flex items-center justify-between mt-6">
          <h1 className="text-xl font-bold text-[#9B8B6C]">My Prizes</h1>
          <Link
            to="/prizes"
            className="px-3 py-1 text-sm rounded-lg bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40"
          >
            Prize Tree
          </Link>
        </div>

        <div className="flex-1 mt-4 overflow-hidden border border-[#9B8B6C]/40 rounded-lg bg-[#120D0E]/80 p-4">
          <UserPrizeGallery />
        </div>
      </div>
    </div>
  );
}
