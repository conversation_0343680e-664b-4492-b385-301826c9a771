import { UserType } from "@/types/UserType";
import { create } from "zustand";
import { GAMES } from "../games/registry";
import { useComboStore, COMBO_ENERGY_COST_MULTIPLIER } from "./combo-store";

/**
 * Game status information
 */
interface GameStatus {
  unlocked: boolean;
  highScore: number;
}


/**
 * Extended UserStore with game-specific functionality
 */
type UserStore = UserType & {
  // Existing methods
  UserTap: () => boolean;
  incraseEnergy: (value: number) => void;

  // Game-related state
  rabbit_game_unlocked: boolean; // Added for Rabbit game
  slash_game_unlocked: boolean; // Added for Slash game

  // Achievement points
  achievement_points: number;

  // Combo-related state
  currentComboMultiplier: number;

  // Game-related methods
  updateGameScore: (score: number) => void;
  getGameStatus: (gameId: string) => GameStatus;
  updateTowerGamePlays: (plays: number, resetAt?: string | null) => void;

  // Achievement points methods
  updateAchievementPoints: (points: number) => void;

  // Combo-related methods
  setComboMultiplier: (multiplier: number) => void;

};

export const useUserStore = create<UserStore>((set, get) => ({
  // Existing state
  game_score: 0,
  telegram_id: 0,
  max_energy: 0,
  balance: 0,
  earn_per_tap: 0,
  available_energy: 0,
  energy_limit_level: 0,
  first_name: "",
  id: 0,
  last_login_date: "",
  last_name: "",
  level_id: 0,
  login_streak: 0,
  multi_tap_level: 0,
  production_per_hour: 0,
  updated_at: "",
  username: "",
  tower_game_unlocked: false,
  tower_game_plays: 15,
  tower_game_plays_reset_at: null,
  rabbit_game_unlocked: false, // Added for Rabbit game state
  slash_game_unlocked: false, // Added for Slash game state

  // Achievement points
  achievement_points: 0,

  // Combo-related state
  currentComboMultiplier: 1,

  /**
   * User tap action
   */
  UserTap() {
    // Check if combo is active to apply higher energy cost
    const isComboActive = useComboStore.getState().comboActive;

    // Apply a fixed multiplier to energy cost during combo
    const energyCostMultiplier = isComboActive ? COMBO_ENERGY_COST_MULTIPLIER : 1;
    const energyCost = get().earn_per_tap * energyCostMultiplier;

    // Check if user has enough energy for the potentially increased cost
    if (get().available_energy < energyCost) return false;

    // Get the current multiplier from combo store
    const totalMultiplier = useComboStore.getState().getCurrentMultiplier();

    // Calculate the actual points earned with the multiplier
    const pointsEarned = Math.floor(get().earn_per_tap * totalMultiplier);

    // Deduct energy (potentially increased) and add points with multiplier
    set((state) => ({
      available_energy: state.available_energy - energyCost,
      balance: state.balance + pointsEarned, // Apply the multiplier to points earned
    }));

    // Log for debugging
    console.log(`Tap: +${pointsEarned} points (base: ${get().earn_per_tap}, multiplier: ${totalMultiplier}x)`);

    return true;
  },

  /**
   * Increase energy
   */
  incraseEnergy: (value) => {
    set((state) => ({
      available_energy: Math.min(
        state.available_energy + value,
        state.max_energy
      ),
    }));
  },

  /**
   * Updates the game score by adding the new score to the existing total
   */
  updateGameScore: (score) => {
    set(state => ({
      game_score: state.game_score + score
    }));
  },

  /**
   * Gets the status of a game (unlocked and high score)
   */
  getGameStatus: (gameId) => {
    const state = get();
    if (gameId === 'tower') {
      return {
        unlocked: state.tower_game_unlocked,
        highScore: state.game_score // Using aggregated score
      };
    }
    // --- Added Rabbit Game Check ---
    if (gameId === 'rabbit') {
      return {
        unlocked: state.rabbit_game_unlocked,
        highScore: state.game_score // Using aggregated score
      };
    }
    // --- End Added Check ---

    // --- Added Slash Game Check ---
    if (gameId === 'slash') {
      return {
        unlocked: state.slash_game_unlocked,
        highScore: state.game_score // Using aggregated score
      };
    }
    // --- End Added Check ---

    // Fallback for potentially other games (though only tower/rabbit exist now)
    const game = GAMES[gameId];
    if (!game) {
      console.warn(`Unknown gameId in getGameStatus: ${gameId}`);
      return { unlocked: false, highScore: 0 };
    }

    // Generic unlock check (might not be accurate if backend is source of truth)
    // Preferring explicit checks for known games above.
    const isUnlockedGeneric = game.unlockRequirement.type === 'level'
        ? state.level_id >= game.unlockRequirement.value
        : state.balance >= game.unlockRequirement.value; // This might be misleading

    return {
      unlocked: isUnlockedGeneric, // Use with caution for future games
      highScore: state.game_score
    };
  },

  updateTowerGamePlays: (plays, resetAt) => {
    set(state => ({
      ...state,
      tower_game_plays: plays,
      tower_game_plays_reset_at: resetAt || state.tower_game_plays_reset_at
    }));
  },

  /**
   * Set the current combo multiplier
   */
  setComboMultiplier: (multiplier) => {
    set({ currentComboMultiplier: multiplier });
  },

  /**
   * Update achievement points
   */
  updateAchievementPoints: (points) => {
    set({
      achievement_points: points
    });
  }
}));
