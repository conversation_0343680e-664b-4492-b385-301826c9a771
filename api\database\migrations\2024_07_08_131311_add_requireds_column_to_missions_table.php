<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add columns without check constraints
        Schema::table('missions', function (Blueprint $table) {
            $table->integer('required_user_level')->default(0);
            $table->integer('required_friends_invitation')->default(0);
        });

        // Add CHECK constraints using raw SQL
        DB::statement('ALTER TABLE missions ADD CONSTRAINT chk_required_user_level_non_negative CHECK (required_user_level >= 0)');
        DB::statement('ALTER TABLE missions ADD CONSTRAINT chk_required_friends_invitation_non_negative CHECK (required_friends_invitation >= 0)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the CHECK constraints
        DB::statement('ALTER TABLE missions DROP CONSTRAINT IF EXISTS chk_required_user_level_non_negative');
        DB::statement('ALTER TABLE missions DROP CONSTRAINT IF EXISTS chk_required_friends_invitation_non_negative');

        // Drop the columns
        Schema::table('missions', function (Blueprint $table) {
            $table->dropColumn('required_user_level');
            $table->dropColumn('required_friends_invitation');
        });
    }
};