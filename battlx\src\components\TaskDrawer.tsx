import { TaskType } from "../types/TaskType";
import { <PERSON><PERSON> } from "./ui/button";
import Drawer, { DrawerProps } from "./ui/drawer";
import Price from "./Price";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { $http } from "@/lib/http";
import { toast } from "react-toastify";
import dayjs from "dayjs";
import { BattlxIcon } from "@/components/icons/BattlxIcon";
import { useUserStore } from "@/store/user-store";

export default function TaskDrawer({
  task,
  ...props
}: DrawerProps & {
  task: TaskType | null;
}) {
  const queryClient = useQueryClient();

  const submitMutation = useMutation({
    mutationFn: () =>
      $http.post<{ message: string }>(`/clicker/tasks/${task?.id}`),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSuccess: (response) => {
      toast.success(response?.data?.message || "Task submitted successfully");
      queryClient.invalidateQueries({ queryKey: ["tasks"] });
      task!.is_submitted = true;
      task!.submitted_at = new Date().toISOString();
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "An error occurred");
    },
  });

  const claimMutation = useMutation({
    mutationFn: () =>
      $http.post<{ message: string }>(`/clicker/tasks/${task?.id}/claim`),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onSuccess: (response) => {
      toast.success(response?.data?.message || "Task submitted successfully");
      queryClient.invalidateQueries({ queryKey: ["tasks"] });
      task!.is_rewarded = true;
      useUserStore.setState((state) => {
        state.balance += task!.reward_coins;
        return state;
      });
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "An error occurred");
    },
  });

  if (!task) return null;
  return (
    <Drawer {...props}>
      {task.type === "video" ? (
        <BattlxIcon icon="youtube" className="h-24 mx-auto opacity-80 text-[#9B8B6C]" />
      ) : task.image ? (
        <img
          src={task.image}
          alt={task.name}
          className="object-contain h-24 mx-auto opacity-80 [filter:sepia(50%)_saturate(50%)_brightness(80%)_contrast(120%)]"
        />
      ) : (
        <BattlxIcon icon="bounty" className="h-24 mx-auto opacity-80 text-[#9B8B6C]" />
      )}
      <h2 className="text-2xl font-medium text-center mt-9 text-[#9B8B6C]">{task.name}</h2>
      <div className="px-5 py-2 mx-auto mt-4 border-2 border-dashed rounded-full border-[#B3B3B3]/20 bg-[#1A1617] shadow-[0_4px_15px_rgba(74,14,14,0.3)] w-fit">
        <Price
          amount={task.reward_coins.toLocaleString()}
          className="justify-center text-xl"
        />
      </div>
      {task.is_submitted &&
        dayjs().isBefore(dayjs(task.submitted_at).add(60, "m")) && (
          <p className="mt-6 text-center text-[#B3B3B3]/60">
            Task submitted! Please Wait 1 hour for the moderation check to claim
            the prize.
          </p>
        )}
      <Button
        className="w-full mt-12 bg-[#1A1617] text-[#9B8B6C] border border-[#B3B3B3]/20 hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_15px_rgba(74,14,14,0.3)]"
        asChild
        onClick={() => submitMutation.mutate()}
      >
        <a href={task.link} target="_blank">
          {task.action_name}
        </a>
      </Button>

      {!task.is_rewarded && (
        <Button
          className="w-full mt-6 bg-[#1A1617] text-[#9B8B6C] border border-[#B3B3B3]/20 hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_15px_rgba(74,14,14,0.3)] disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={
            claimMutation.isPending ||
            !task.is_submitted ||
            dayjs().isBefore(dayjs(task.submitted_at).add(60, "m"))
          }
          onClick={() => claimMutation.mutate()}
        >
          {claimMutation.isPending && (
            <BattlxIcon icon="loading" className="w-6 h-6 mr-2 animate-spin" />
          )}
          Check
        </Button>
      )}
    </Drawer>
  );
}
