<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('collectibles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->string('collectible_id', 50); // References collectible_templates.collectible_id
            
            $table->enum('unlock_source', ['pet_purchase', 'mystery_box', 'prize_tree', 'collection_bonus']);
            $table->string('source_reference')->nullable(); // Pet ID, box opening ID, etc.
            $table->timestamp('obtained_at');
            
            $table->timestamps();
            
            $table->unique(['telegram_user_id', 'collectible_id']);
            $table->index(['telegram_user_id', 'obtained_at']);
            
            $table->foreign('collectible_id')
                  ->references('collectible_id')
                  ->on('collectible_templates')
                  ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('collectibles');
    }
};
