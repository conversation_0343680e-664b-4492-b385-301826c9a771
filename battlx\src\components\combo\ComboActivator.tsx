import React, { useEffect } from 'react';
import { useSpring, animated } from 'react-spring';
import { useComboStore } from '@/store/combo-store';

export const ComboActivator: React.FC = () => {
  const { comboReady, activateCombo } = useComboStore();
  
  // Pulsing animation - always define the hook regardless of comboReady state
  const springProps = useSpring({
    from: { scale: 1, opacity: 0.7 },
    to: async (next) => {
      // eslint-disable-next-line no-constant-condition
      while (true) {
        await next({ scale: 1.2, opacity: 1 });
        await next({ scale: 1, opacity: 0.7 });
      }
    },
    config: { tension: 300, friction: 10 }
  });
  
  // Play sound effect when combo becomes ready
  useEffect(() => {
    if (comboReady) {
      // Play "COMBO READY" sound
      try {
        const audio = new Audio();
        audio.src = "/sounds/combo-ready.mp3"; // We'll need to add this sound file
        audio.volume = 0.5;
        audio.play().catch(() => {
          // Silently fail if audio can't be played
          console.log("Could not play combo ready sound");
        });
      } catch (error) {
        console.error("Error playing combo ready sound:", error);
      }
    }
  }, [comboReady]);
  
  // Only render the component if combo is ready
  if (!comboReady) return null;
  
  return (
    <animated.div
      className="combo-activator"
      style={{
        position: 'absolute',
        top: '40%',
        left: '50%',
        transform: springProps.scale.to(s => `translate(-50%, -50%) scale(${s})`),
        opacity: springProps.opacity,
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        color: '#FFD700', // Gold
        padding: '10px 20px',
        borderRadius: '10px',
        fontWeight: 'bold',
        fontSize: '1.5rem',
        cursor: 'pointer',
        zIndex: 100,
        boxShadow: '0 0 15px #FFD700',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }}
      onClick={activateCombo}
    >
      <div>COMBO READY</div>
      <div style={{
        fontSize: '0.8rem',
        marginTop: '5px',
        color: 'rgba(255, 255, 255, 0.8)'
      }}>
        Warning: 3x Energy Cost
      </div>
    </animated.div>
  );
};

export default ComboActivator;