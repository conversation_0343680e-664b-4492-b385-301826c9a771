// Background Manager class for handling the game background
class BGManager {
    constructor(canvas) {
        this.canvas = canvas;
        this.timeOffset = 0;
        this.dayCycleDuration = 900; // 15 minutes
        this.dayColor = '#ffffff';
        this.nightColor = '#044456';
        this.runTimeHue = true;
        this.canScroll = true;
        this.bgImage = null;
        this.tileSize = 1024; // Increased tile size
        this.pattern = null; // For creating a repeating pattern
    }

    // Set the background image
    setBackground(image) {
        this.bgImage = image;

        // Create a pattern from the image if possible
        if (image && this.canvas) {
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = this.tileSize;
            tempCanvas.height = this.tileSize;
            const tempCtx = tempCanvas.getContext('2d');

            // Draw the image to the temp canvas, scaling it to fill the tile size
            tempCtx.drawImage(image, 0, 0, this.tileSize, this.tileSize);

            // Create a pattern from the temp canvas
            const ctx = this.canvas.getContext('2d');
            this.pattern = ctx.createPattern(tempCanvas, 'repeat');
        }
    }

    // Update the background
    update(deltaTime) {
        if (this.runTimeHue) {
            this.timeOffset += deltaTime / 1000;
            if (this.timeOffset > this.dayCycleDuration) {
                this.timeOffset = 0;
            }
        }
    }

    // Draw the background
    draw(ctx, camera) {
        if (!this.bgImage && !this.pattern) return;

        // Calculate the time of day (0-1)
        const timeOfDay = this.timeOffset / this.dayCycleDuration;

        // Calculate the color based on time of day
        const color = this.calculateDayNightColor(timeOfDay);

        // Method 1: Use pattern for full screen background
        if (this.pattern) {
            ctx.save();

            // Apply camera offset to make the background move with the camera
            const offsetX = -camera.x * 0.2; // Parallax effect (slower than camera)
            const offsetY = -camera.y * 0.2;

            // Set the pattern transform
            ctx.translate(offsetX % this.tileSize, offsetY % this.tileSize);

            // Fill the entire canvas with the pattern
            ctx.fillStyle = this.pattern;
            ctx.fillRect(-offsetX, -offsetY, this.canvas.width + this.tileSize, this.canvas.height + this.tileSize);

            ctx.restore();
        } else {
            // Method 2: Fallback to tiled drawing if pattern creation failed
            const tileWidth = this.tileSize;
            const tileHeight = this.tileSize;

            // Calculate the visible area with extra tiles for smooth scrolling
            const startX = Math.floor(camera.x / tileWidth) * tileWidth - tileWidth * 2;
            const startY = Math.floor(camera.y / tileHeight) * tileHeight - tileHeight * 2;
            const endX = startX + camera.width + tileWidth * 4;
            const endY = startY + camera.height + tileHeight * 4;

            // Draw the tiles
            for (let x = startX; x < endX; x += tileWidth) {
                for (let y = startY; y < endY; y += tileHeight) {
                    const screenX = x - camera.x + camera.width / 2;
                    const screenY = y - camera.y + camera.height / 2;

                    ctx.drawImage(
                        this.bgImage,
                        screenX,
                        screenY,
                        tileWidth,
                        tileHeight
                    );
                }
            }
        }

        // Apply day/night overlay
        ctx.save();
        ctx.fillStyle = color;
        ctx.globalCompositeOperation = 'multiply';
        ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        ctx.restore();
    }

    // Calculate the color for day/night cycle
    calculateDayNightColor(timeOfDay) {
        // Simple day/night cycle: brightest at noon, darkest at midnight
        const dayNightCycle = Math.sin(timeOfDay * Math.PI * 2 - Math.PI / 2) * 0.5 + 0.5;

        // Parse the day and night colors
        const dayR = parseInt(this.dayColor.slice(1, 3), 16);
        const dayG = parseInt(this.dayColor.slice(3, 5), 16);
        const dayB = parseInt(this.dayColor.slice(5, 7), 16);

        const nightR = parseInt(this.nightColor.slice(1, 3), 16);
        const nightG = parseInt(this.nightColor.slice(3, 5), 16);
        const nightB = parseInt(this.nightColor.slice(5, 7), 16);

        // Interpolate between day and night colors
        const r = Math.floor(lerp(nightR, dayR, dayNightCycle));
        const g = Math.floor(lerp(nightG, dayG, dayNightCycle));
        const b = Math.floor(lerp(nightB, dayB, dayNightCycle));

        // Convert back to hex
        return `rgba(${r}, ${g}, ${b}, 0.3)`;
    }
}
