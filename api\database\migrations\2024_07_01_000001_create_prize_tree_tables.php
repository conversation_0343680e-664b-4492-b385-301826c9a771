<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create prize_trees table
        Schema::create('prize_trees', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('icon')->nullable();
            $table->string('theme_color')->nullable();
            $table->integer('display_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_seasonal')->default(false);
            $table->timestamp('available_until')->nullable();
            $table->timestamps();
        });

        // Create prizes table
        Schema::create('prizes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('prize_tree_id')->constrained('prize_trees')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('icon')->nullable();
            $table->integer('tier')->default(1);
            $table->integer('position')->default(0);
            $table->string('category')->nullable();
            $table->integer('cost')->default(1);
            $table->boolean('is_root')->default(false);
            $table->string('reward_type'); // cosmetic, title, card, balance, booster, etc.
            $table->json('reward_data')->nullable();
            $table->timestamps();
        });
        
        // Create prize prerequisites table (many-to-many)
        Schema::create('prize_prerequisites', function (Blueprint $table) {
            $table->id();
            $table->foreignId('prize_id')->constrained('prizes')->onDelete('cascade');
            $table->foreignId('prerequisite_id')->constrained('prizes')->onDelete('cascade');
            $table->timestamps();
            
            // Each prize can have a prerequisite only once
            $table->unique(['prize_id', 'prerequisite_id']);
        });

        // Create user_prizes table
        Schema::create('user_prizes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->foreignId('prize_id')->constrained('prizes')->onDelete('cascade');
            $table->timestamp('unlocked_at');
            $table->boolean('is_equipped')->default(false);
            $table->timestamps();
            
            // Each user can unlock a prize only once
            $table->unique(['telegram_user_id', 'prize_id']);
        });

        // Create user_achievement_points table
        Schema::create('user_achievement_points', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->integer('total_earned')->default(0);
            $table->integer('total_spent')->default(0);
            $table->timestamps();
            
            // Each user should have only one record
            $table->unique(['telegram_user_id']);
        });

        // Create achievement_point_transactions table
        Schema::create('achievement_point_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->integer('amount');
            $table->string('type'); // earn, spend, refund
            $table->string('source'); // mission_complete, tap_milestone, prize_unlock, etc.
            $table->integer('source_id')->nullable();
            $table->string('description')->nullable();
            $table->timestamps();
        });

        // Create tap_stats table
        Schema::create('tap_stats', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->bigInteger('total_taps')->default(0);
            $table->timestamps();
            
            // Each user should have only one record
            $table->unique(['telegram_user_id']);
        });

        // Create game_stats table
        Schema::create('game_stats', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->string('game_id'); // tower, rabbit, slash, etc.
            $table->integer('high_score')->default(0);
            $table->bigInteger('total_score')->default(0);
            $table->integer('plays')->default(0);
            $table->timestamps();
            
            // Each user should have only one record per game
            $table->unique(['telegram_user_id', 'game_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('game_stats');
        Schema::dropIfExists('tap_stats');
        Schema::dropIfExists('achievement_point_transactions');
        Schema::dropIfExists('user_achievement_points');
        Schema::dropIfExists('user_prizes');
        Schema::dropIfExists('prize_prerequisites');
        Schema::dropIfExists('prizes');
        Schema::dropIfExists('prize_trees');
    }
};
