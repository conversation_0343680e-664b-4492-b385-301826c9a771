# Phase 1: Real-Time Battle System Implementation

## Overview
Transform the tap-to-earn mechanics into an engaging real-time PvP battle system with matchmaking, skill-based combat, and dynamic arenas.

## Backend Implementation

### 1. Database Schema

#### New Migration Files
```php
// 2024_XX_XX_create_battle_system_tables.php

Schema::create('arenas', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('environment_type'); // desert, forest, urban, space
    $table->json('special_effects'); // environmental bonuses/penalties
    $table->string('image_url');
    $table->boolean('is_active')->default(true);
    $table->timestamps();
});

Schema::create('battles', function (Blueprint $table) {
    $table->id();
    $table->enum('type', ['1v1', '2v2', 'battle_royale']);
    $table->enum('status', ['waiting', 'in_progress', 'completed', 'cancelled']);
    $table->foreignId('arena_id')->constrained();
    $table->integer('max_participants');
    $table->integer('duration_seconds')->default(180); // 3 minutes
    $table->json('battle_data')->nullable(); // store battle events
    $table->timestamp('started_at')->nullable();
    $table->timestamp('ended_at')->nullable();
    $table->timestamps();
});

Schema::create('battle_participants', function (Blueprint $table) {
    $table->id();
    $table->foreignId('battle_id')->constrained()->onDelete('cascade');
    $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
    $table->integer('position'); // starting position in arena
    $table->integer('final_score')->default(0);
    $table->enum('result', ['win', 'loss', 'draw'])->nullable();
    $table->integer('damage_dealt')->default(0);
    $table->integer('damage_taken')->default(0);
    $table->integer('energy_used')->default(0);
    $table->json('actions_log')->nullable(); // detailed action history
    $table->timestamps();
});

Schema::create('battle_actions', function (Blueprint $table) {
    $table->id();
    $table->foreignId('battle_id')->constrained()->onDelete('cascade');
    $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
    $table->enum('action_type', ['attack', 'defend', 'special', 'move']);
    $table->json('action_data'); // coordinates, power, target, etc.
    $table->integer('energy_cost');
    $table->timestamp('executed_at');
    $table->index(['battle_id', 'executed_at']);
});

Schema::create('player_stats', function (Blueprint $table) {
    $table->id();
    $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
    $table->integer('elo_rating')->default(1000);
    $table->integer('total_battles')->default(0);
    $table->integer('wins')->default(0);
    $table->integer('losses')->default(0);
    $table->integer('draws')->default(0);
    $table->integer('total_damage_dealt')->default(0);
    $table->integer('total_damage_taken')->default(0);
    $table->integer('longest_win_streak')->default(0);
    $table->integer('current_win_streak')->default(0);
    $table->timestamps();
});
```

### 2. Models

#### Arena Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Arena extends Model
{
    protected $fillable = [
        'name', 'environment_type', 'special_effects', 'image_url', 'is_active'
    ];

    protected $casts = [
        'special_effects' => 'array',
        'is_active' => 'boolean'
    ];

    public function battles()
    {
        return $this->hasMany(Battle::class);
    }

    public function getSpecialEffectBonus($effectType)
    {
        return $this->special_effects[$effectType] ?? 0;
    }
}
```

#### Battle Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Battle extends Model
{
    protected $fillable = [
        'type', 'status', 'arena_id', 'max_participants', 
        'duration_seconds', 'battle_data', 'started_at', 'ended_at'
    ];

    protected $casts = [
        'battle_data' => 'array',
        'started_at' => 'datetime',
        'ended_at' => 'datetime'
    ];

    public function arena()
    {
        return $this->belongsTo(Arena::class);
    }

    public function participants()
    {
        return $this->hasMany(BattleParticipant::class);
    }

    public function actions()
    {
        return $this->hasMany(BattleAction::class);
    }

    public function isActive()
    {
        return $this->status === 'in_progress';
    }

    public function canJoin()
    {
        return $this->status === 'waiting' && 
               $this->participants()->count() < $this->max_participants;
    }

    public function getRemainingTime()
    {
        if (!$this->started_at) return $this->duration_seconds;
        
        $elapsed = now()->diffInSeconds($this->started_at);
        return max(0, $this->duration_seconds - $elapsed);
    }
}
```

### 3. Controllers

#### BattleController
```php
<?php

namespace App\Http\Controllers;

use App\Models\Battle;
use App\Models\Arena;
use App\Models\BattleParticipant;
use App\Services\MatchmakingService;
use App\Services\BattleEngineService;
use Illuminate\Http\Request;

class BattleController extends Controller
{
    protected $matchmakingService;
    protected $battleEngineService;

    public function __construct(
        MatchmakingService $matchmakingService,
        BattleEngineService $battleEngineService
    ) {
        $this->matchmakingService = $matchmakingService;
        $this->battleEngineService = $battleEngineService;
    }

    public function joinQueue(Request $request)
    {
        $request->validate([
            'battle_type' => 'required|in:1v1,2v2,battle_royale',
            'arena_preference' => 'nullable|exists:arenas,id'
        ]);

        $user = $request->user();
        
        // Check if user has enough energy
        if ($user->available_energy < 50) {
            return response()->json([
                'error' => 'Insufficient energy for battle'
            ], 400);
        }

        $battle = $this->matchmakingService->findOrCreateBattle(
            $user,
            $request->battle_type,
            $request->arena_preference
        );

        return response()->json([
            'battle_id' => $battle->id,
            'status' => $battle->status,
            'participants_count' => $battle->participants()->count(),
            'max_participants' => $battle->max_participants,
            'estimated_wait_time' => $this->matchmakingService->getEstimatedWaitTime($request->battle_type)
        ]);
    }

    public function getBattleStatus(Request $request, Battle $battle)
    {
        $user = $request->user();
        
        // Verify user is participant
        $participant = $battle->participants()
            ->where('telegram_user_id', $user->id)
            ->first();

        if (!$participant) {
            return response()->json(['error' => 'Not a participant'], 403);
        }

        return response()->json([
            'battle' => $battle->load(['arena', 'participants.user']),
            'remaining_time' => $battle->getRemainingTime(),
            'your_stats' => [
                'score' => $participant->final_score,
                'damage_dealt' => $participant->damage_dealt,
                'damage_taken' => $participant->damage_taken,
                'energy_used' => $participant->energy_used
            ]
        ]);
    }

    public function submitAction(Request $request, Battle $battle)
    {
        $request->validate([
            'action_type' => 'required|in:attack,defend,special,move',
            'target_x' => 'required|numeric|min:0|max:100',
            'target_y' => 'required|numeric|min:0|max:100',
            'power' => 'required|integer|min:1|max:10'
        ]);

        $user = $request->user();

        if (!$battle->isActive()) {
            return response()->json(['error' => 'Battle not active'], 400);
        }

        $result = $this->battleEngineService->processAction(
            $battle,
            $user,
            $request->only(['action_type', 'target_x', 'target_y', 'power'])
        );

        return response()->json($result);
    }

    public function leaveBattle(Request $request, Battle $battle)
    {
        $user = $request->user();
        
        $participant = $battle->participants()
            ->where('telegram_user_id', $user->id)
            ->first();

        if (!$participant) {
            return response()->json(['error' => 'Not a participant'], 403);
        }

        if ($battle->status === 'in_progress') {
            // Penalty for leaving active battle
            $participant->update(['result' => 'loss']);
            $this->battleEngineService->handlePlayerLeave($battle, $user);
        } else {
            // Remove from waiting battle
            $participant->delete();
        }

        return response()->json(['message' => 'Left battle successfully']);
    }

    public function getLeaderboard(Request $request)
    {
        $type = $request->get('type', 'elo'); // elo, wins, damage
        
        $query = \App\Models\PlayerStats::with('user')
            ->where('total_battles', '>', 0);

        switch ($type) {
            case 'wins':
                $query->orderBy('wins', 'desc');
                break;
            case 'damage':
                $query->orderBy('total_damage_dealt', 'desc');
                break;
            default:
                $query->orderBy('elo_rating', 'desc');
        }

        $leaderboard = $query->take(100)->get();

        return response()->json($leaderboard);
    }
}
```

### 4. Services

#### MatchmakingService
```php
<?php

namespace App\Services;

use App\Models\Battle;
use App\Models\Arena;
use App\Models\BattleParticipant;
use App\Models\PlayerStats;
use App\Models\TelegramUser;

class MatchmakingService
{
    public function findOrCreateBattle(TelegramUser $user, string $battleType, ?int $arenaPreference = null)
    {
        // Get user's ELO rating
        $playerStats = PlayerStats::firstOrCreate(
            ['telegram_user_id' => $user->id],
            ['elo_rating' => 1000]
        );

        // Find existing battle within ELO range
        $eloRange = 100; // ±100 ELO points
        $battle = $this->findSuitableBattle($battleType, $playerStats->elo_rating, $eloRange, $arenaPreference);

        if (!$battle) {
            $battle = $this->createNewBattle($battleType, $arenaPreference);
        }

        // Add user to battle
        BattleParticipant::create([
            'battle_id' => $battle->id,
            'telegram_user_id' => $user->id,
            'position' => $this->getNextPosition($battle)
        ]);

        // Start battle if full
        if ($battle->participants()->count() >= $battle->max_participants) {
            $this->startBattle($battle);
        }

        return $battle;
    }

    protected function findSuitableBattle(string $battleType, int $userElo, int $eloRange, ?int $arenaPreference)
    {
        $query = Battle::where('type', $battleType)
            ->where('status', 'waiting')
            ->whereHas('participants.user.playerStats', function ($q) use ($userElo, $eloRange) {
                $q->whereBetween('elo_rating', [$userElo - $eloRange, $userElo + $eloRange]);
            });

        if ($arenaPreference) {
            $query->where('arena_id', $arenaPreference);
        }

        return $query->first();
    }

    protected function createNewBattle(string $battleType, ?int $arenaPreference)
    {
        $maxParticipants = match ($battleType) {
            '1v1' => 2,
            '2v2' => 4,
            'battle_royale' => 8,
            default => 2
        };

        $arena = $arenaPreference 
            ? Arena::find($arenaPreference)
            : Arena::where('is_active', true)->inRandomOrder()->first();

        return Battle::create([
            'type' => $battleType,
            'status' => 'waiting',
            'arena_id' => $arena->id,
            'max_participants' => $maxParticipants,
            'duration_seconds' => $this->getBattleDuration($battleType)
        ]);
    }

    protected function getBattleDuration(string $battleType): int
    {
        return match ($battleType) {
            '1v1' => 120, // 2 minutes
            '2v2' => 180, // 3 minutes
            'battle_royale' => 300, // 5 minutes
            default => 180
        };
    }

    protected function getNextPosition(Battle $battle): int
    {
        return $battle->participants()->count() + 1;
    }

    protected function startBattle(Battle $battle)
    {
        $battle->update([
            'status' => 'in_progress',
            'started_at' => now()
        ]);

        // Deduct energy from all participants
        foreach ($battle->participants as $participant) {
            $participant->user->decrement('available_energy', 50);
        }

        // Broadcast battle start event
        broadcast(new \App\Events\BattleStarted($battle));
    }

    public function getEstimatedWaitTime(string $battleType): int
    {
        $waitingBattles = Battle::where('type', $battleType)
            ->where('status', 'waiting')
            ->count();

        // Estimate based on current queue
        return max(10, $waitingBattles * 30); // seconds
    }
}
```

## Frontend Implementation

### 1. Battle Queue Component

```typescript
// src/components/battle/BattleQueue.tsx
import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { $http } from '@/lib/http';
import { useUserStore } from '@/store/user-store';
import { useBattleStore } from '@/store/battle-store';

interface BattleQueueProps {
  onBattleFound: (battleId: number) => void;
}

export const BattleQueue: React.FC<BattleQueueProps> = ({ onBattleFound }) => {
  const [selectedType, setSelectedType] = useState<'1v1' | '2v2' | 'battle_royale'>('1v1');
  const [isQueuing, setIsQueuing] = useState(false);
  const user = useUserStore();
  const { setCurrentBattle } = useBattleStore();

  const joinQueueMutation = useMutation({
    mutationFn: (battleType: string) =>
      $http.post('/api/battles/queue', { battle_type: battleType }),
    onSuccess: (response) => {
      setCurrentBattle(response.data);
      setIsQueuing(true);
      
      if (response.data.status === 'in_progress') {
        onBattleFound(response.data.battle_id);
      }
    }
  });

  const { data: arenas } = useQuery({
    queryKey: ['arenas'],
    queryFn: () => $http.get('/api/arenas').then(res => res.data)
  });

  const handleJoinQueue = () => {
    if (user.available_energy < 50) {
      alert('Insufficient energy for battle!');
      return;
    }
    
    joinQueueMutation.mutate(selectedType);
  };

  return (
    <div className="battle-queue-container">
      <div className="queue-header">
        <h2>Battle Arena</h2>
        <div className="energy-display">
          Energy: {user.available_energy}/{user.max_energy}
        </div>
      </div>

      <div className="battle-types">
        {['1v1', '2v2', 'battle_royale'].map((type) => (
          <button
            key={type}
            className={`battle-type-btn ${selectedType === type ? 'active' : ''}`}
            onClick={() => setSelectedType(type as any)}
          >
            {type.toUpperCase()}
          </button>
        ))}
      </div>

      <div className="arena-selection">
        <h3>Select Arena</h3>
        <div className="arena-grid">
          {arenas?.map((arena: any) => (
            <div key={arena.id} className="arena-card">
              <img src={arena.image_url} alt={arena.name} />
              <span>{arena.name}</span>
            </div>
          ))}
        </div>
      </div>

      <button
        className="join-queue-btn"
        onClick={handleJoinQueue}
        disabled={isQueuing || joinQueueMutation.isPending}
      >
        {isQueuing ? 'Searching for Battle...' : 'Join Battle'}
      </button>
    </div>
  );
};
```

### 2. Battle Store

```typescript
// src/store/battle-store.ts
import { create } from 'zustand';

interface BattleState {
  currentBattle: any | null;
  battleStatus: 'idle' | 'queuing' | 'in_battle' | 'completed';
  participants: any[];
  battleActions: any[];
  remainingTime: number;
}

interface BattleActions {
  setCurrentBattle: (battle: any) => void;
  setBattleStatus: (status: BattleState['battleStatus']) => void;
  addBattleAction: (action: any) => void;
  updateRemainingTime: (time: number) => void;
  resetBattle: () => void;
}

export const useBattleStore = create<BattleState & BattleActions>((set) => ({
  currentBattle: null,
  battleStatus: 'idle',
  participants: [],
  battleActions: [],
  remainingTime: 0,

  setCurrentBattle: (battle) => set({ currentBattle: battle }),
  setBattleStatus: (status) => set({ battleStatus: status }),
  addBattleAction: (action) => set((state) => ({
    battleActions: [...state.battleActions, action]
  })),
  updateRemainingTime: (time) => set({ remainingTime: time }),
  resetBattle: () => set({
    currentBattle: null,
    battleStatus: 'idle',
    participants: [],
    battleActions: [],
    remainingTime: 0
  })
}));
```

## Next Steps

1. **WebSocket Integration**: Implement real-time battle updates
2. **Battle Arena UI**: Create the interactive battle interface
3. **Testing Framework**: Set up automated testing for battle mechanics
4. **Performance Optimization**: Ensure smooth real-time performance
5. **Balance Tuning**: Adjust battle mechanics based on testing

This implementation provides the foundation for engaging real-time PvP battles that will transform the user experience from passive tapping to active strategic combat.
