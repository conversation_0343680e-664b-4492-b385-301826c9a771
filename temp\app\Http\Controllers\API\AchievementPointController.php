<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\UserAchievementPoint;
use App\Models\AchievementPointTransaction;
use App\Services\AchievementPointService;
use Illuminate\Http\Request;

class AchievementPointController extends Controller
{
    protected $achievementPointService;
    
    public function __construct(AchievementPointService $achievementPointService)
    {
        $this->achievementPointService = $achievementPointService;
    }
    
    /**
     * Award achievement points to a user.
     */
    public function award(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:telegram_users,id',
            'amount' => 'required|integer|min:1',
            'source' => 'required|string',
            'source_id' => 'nullable|integer',
            'description' => 'nullable|string'
        ]);
        
        $userId = $request->user_id;
        $amount = $request->amount;
        
        // Use the service to award points
        $result = $this->achievementPointService->awardPoints(
            $userId,
            $amount,
            $request->source,
            $request->source_id,
            $request->description
        );
        
        if (!$result['success']) {
            return response()->json([
                'message' => 'Failed to award achievement points',
                'error' => $result['message']
            ], 500);
        }
        
        return response()->json([
            'message' => 'Achievement points awarded successfully',
            'total_points' => $result['total_points']
        ]);
    }

    /**
     * Get the user's achievement point transactions.
     */
    public function transactions(Request $request)
    {
        $user = $request->user();
        
        $transactions = AchievementPointTransaction::where('telegram_user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);
            
        return response()->json($transactions);
    }
}
