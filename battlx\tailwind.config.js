/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "1rem",
      screens: {
        "sm": "360px",
        "md": "414px",
        "lg": "768px",
        "xl": "1024px",
        "2xl": "1400px",
      },
    },
    screens: {
      // Mobile-first responsive breakpoints for consistent display
      'xs': '320px',   // Small mobile devices
      'sm': '360px',   // Standard mobile devices (Samsung Galaxy S8+)
      'md': '414px',   // Large mobile devices (iPhone Plus)
      'lg': '768px',   // Small tablets
      'xl': '1024px',  // Large tablets
      '2xl': '1280px', // Desktop (not used in Telegram Web App)
    },
    extend: {
      fontFamily: {
        'gothic': ['Olnova-HeavyCond', 'sans-serif'],
      },
      colors: {
        primary: "#fff",
        'ds-gold': '#CFB53B',
        'ds-purple': '#4A154B',
        'ds-dark-purple': '#2D0C2D',
      },
      spacing: {
        // Consistent spacing for mobile devices
        'safe-top': 'env(safe-area-inset-top)',
        'safe-bottom': 'env(safe-area-inset-bottom)',
        'safe-left': 'env(safe-area-inset-left)',
        'safe-right': 'env(safe-area-inset-right)',
      },
      height: {
        // Dynamic viewport heights for consistent display
        'screen-dynamic': '100dvh',
        'screen-small': '100svh',
        'screen-large': '100lvh',
        'tg-viewport': 'var(--tg-viewport-height, 100vh)',
      },
      maxWidth: {
        // Consistent max widths for mobile layouts
        'mobile': '414px',
        'container': 'min(100vw, 414px)',
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
};
