<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\PrizeTree;
use App\Services\PrizeService;
use Illuminate\Http\Request;

class PrizeTreeController extends Controller
{
    protected $prizeService;
    
    public function __construct(PrizeService $prizeService)
    {
        $this->prizeService = $prizeService;
    }
    
    /**
     * Get all active prize trees.
     */
    public function index()
    {
        $prizeTrees = $this->prizeService->getAvailablePrizeTrees();
        return response()->json($prizeTrees);
    }

    /**
     * Get a specific prize tree with its prizes.
     */
    public function show($id)
    {
        $prizeTree = $this->prizeService->getPrizeTree($id);
        return response()->json($prizeTree);
    }
}
