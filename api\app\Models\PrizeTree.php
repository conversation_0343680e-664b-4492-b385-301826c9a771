<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PrizeTree extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'icon',
        'theme_color',
        'display_order',
        'is_active',
        'is_seasonal',
        'available_until'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_seasonal' => 'boolean',
        'available_until' => 'datetime'
    ];

    /**
     * Get the prizes that belong to this prize tree.
     */
    public function prizes()
    {
        return $this->hasMany(Prize::class);
    }

    /**
     * Get the root prizes for this tree.
     */
    public function rootPrizes()
    {
        return $this->prizes()->where('is_root', true);
    }
}
