<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the 'referral_tasks' table
        Schema::create('referral_tasks', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->smallInteger('number_of_referrals')->unsigned(); // SMALLINT with unsigned constraint
            $table->bigInteger('reward')->unsigned(); // BIGINT with unsigned constraint
            $table->timestamps();
        });

        // Add CHECK constraints using raw SQL
        DB::statement('ALTER TABLE referral_tasks ADD CONSTRAINT chk_number_of_referrals_non_negative CHECK (number_of_referrals >= 0)');
        DB::statement('ALTER TABLE referral_tasks ADD CONSTRAINT chk_reward_non_negative CHECK (reward >= 0)');

        // Create the 'telegram_user_referral_task' table
        Schema::create('telegram_user_referral_task', function (Blueprint $table) {
            $table->id();
            $table->foreignId('referral_task_id')->constrained()->onDelete('cascade');
            $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
            $table->boolean('is_completed')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the CHECK constraints
        DB::statement('ALTER TABLE referral_tasks DROP CONSTRAINT IF EXISTS chk_number_of_referrals_non_negative');
        DB::statement('ALTER TABLE referral_tasks DROP CONSTRAINT IF EXISTS chk_reward_non_negative');

        // Drop the tables
        Schema::dropIfExists('referral_tasks');
        Schema::dropIfExists('telegram_user_referral_task');
    }
};