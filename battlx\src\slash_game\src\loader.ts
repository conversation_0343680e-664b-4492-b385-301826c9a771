/**
 * Slash Game Script Loader
 *
 * This module handles loading all the necessary JavaScript files for the Slash Game
 * before the game is initialized. Uses the exact same loading order as the working index.html
 */

// List of scripts to load in order (following the exact order from index.html)
const scripts = [
  // Enums first
  '/game/slash/js/enums/characterType.js',
  '/game/slash/js/enums/destructibleType.js',
  '/game/slash/js/enums/enemyType.js',
  '/game/slash/js/enums/pickupType.js',
  '/game/slash/js/enums/sceneType.js',
  '/game/slash/js/enums/treasureType.js',
  '/game/slash/js/enums/weaponType.js',
  '/game/slash/js/enums/npcType.js',
  '/game/slash/js/enums/fixedTreasures.js',

  // Constants
  '/game/slash/js/consts/characters.js',
  '/game/slash/js/consts/weapons.js',
  '/game/slash/js/consts/pickups.js',
  '/game/slash/js/consts/enemies.js',
  '/game/slash/js/consts/destructibles.js',
  '/game/slash/js/consts/enemySpawnConfig.js',
  '/game/slash/js/consts/stages.js',

  // Components (in dependency order)
  '/game/slash/js/src/utils.js',
  '/game/slash/js/src/vector2.js',
  '/game/slash/js/src/containmentRect.js',
  '/game/slash/js/src/bgManager.js',
  '/game/slash/js/src/player.js',
  '/game/slash/js/src/enemy.js',
  '/game/slash/js/src/enemyProjectile.js',
  '/game/slash/js/src/weapon.js',
  '/game/slash/js/src/pickup.js',
  '/game/slash/js/src/destructible.js',
  '/game/slash/js/src/stage.js',
  '/game/slash/js/src/ui.js',
  '/game/slash/js/src/sceneManager.js',
  '/game/slash/js/src/gameCore.js',
  '/game/slash/js/src/game.js'
];

// Track loading status
let loadedScripts = 0;
let totalScripts = scripts.length;
let loadingPromise: Promise<void> | null = null;

/**
 * Load all scripts required for the Slash Game
 * @returns Promise that resolves when all scripts are loaded
 */
export const loadGameScripts = (): Promise<void> => {
  // Return existing promise if already loading
  if (loadingPromise) {
    return loadingPromise;
  }

  // Create new loading promise
  loadingPromise = new Promise((resolve, reject) => {
    // Skip loading if scripts are already loaded
    // Check if Game and GameCore are available
    if ((window as any).Game && (window as any).GameCore) {
      console.log('Slash Game components already loaded');
      console.log('Game:', (window as any).Game);
      console.log('GameCore:', (window as any).GameCore);
      resolve();
      return;
    } else {
      console.log('Game or GameCore not found in window object');
      console.log('Window keys:', Object.keys(window));
    }

    console.log('Loading Slash Game scripts...');

    // Load scripts in sequence
    const loadNextScript = (index: number) => {
      if (index >= scripts.length) {
        console.log('All Slash Game scripts loaded successfully');
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = scripts[index];
      script.async = false; // Load in order
      script.type = 'text/javascript'; // Ensure correct type

      console.log(`Loading script ${index + 1}/${scripts.length}: ${scripts[index]}`);

      script.onload = () => {
        loadedScripts++;
        console.log(`Script loaded successfully: ${scripts[index]}`);

        // Check if Game or GameCore was loaded in this script
        if (scripts[index].includes('game.js')) {
          console.log('After loading game.js, Game class available:', !!(window as any).Game);
          console.log('Window keys after game.js:', Object.keys(window).filter(key => key.includes('Game') || key.includes('Core')));
        }

        if (scripts[index].includes('gameCore.js')) {
          console.log('After loading gameCore.js, GameCore class available:', !!(window as any).GameCore);
          console.log('Window keys after gameCore.js:', Object.keys(window).filter(key => key.includes('Game') || key.includes('Core')));
        }

        loadNextScript(index + 1);
      };

      script.onerror = (error) => {
        console.error(`Failed to load script: ${scripts[index]}`, error);
        reject(new Error(`Failed to load script: ${scripts[index]}`));
      };

      // Append to document.head instead of document.body for better script loading
      document.head.appendChild(script);
    };

    // Start loading scripts
    loadNextScript(0);
  });

  return loadingPromise;
};

/**
 * Get the current loading progress
 * @returns Object with loading status
 */
export const getLoadingProgress = () => {
  return {
    loaded: loadedScripts,
    total: totalScripts,
    progress: loadedScripts / totalScripts
  };
};
