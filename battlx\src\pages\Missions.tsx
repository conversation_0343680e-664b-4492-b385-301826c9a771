/* eslint-disable @typescript-eslint/no-explicit-any */
import MissionDrawer from "@/components/MissionDrawer";
import Price from "@/components/Price";
import UserGameDetails from "@/components/UserGameDetails";
import { $http } from "@/lib/http";
import { cn, compactNumber } from "@/lib/utils";
import { uesStore } from "@/store";
import { useUserStore } from "@/store/user-store";
import { Mission } from "@/types/MissionType";
import { useQuery } from "@tanstack/react-query";
import { BattlxIcon } from "@/components/icons/BattlxIcon";
import { useState } from "react";
import { Swiper, SwiperSlide } from 'swiper/react';
import { EffectCoverflow, Pagination } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/effect-coverflow';
import 'swiper/css/pagination';
import styles from './Missions.module.css';

export default function Missions() {
  const user = useUserStore();
  const { missionTypes, totalReferals } = uesStore();
  const [activeType, setActiveType] = useState(missionTypes?.[0]);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [selectedMission, setSelectedMission] = useState<Mission | null>(null);
  const [activeSection, setActiveSection] = useState<'eternal' | 'grave' | 'dark' | 'necrotic'>('eternal');

  const missions = useQuery({
    queryKey: ["/clicker/missions", activeType?.id],
    queryFn: () =>
      $http.$get<Mission[]>(`/clicker/missions`, {
        params: { type: activeType?.id },
      }),
    staleTime: 1000 * 60,
    enabled: !!activeType?.id,
  });

  return (
    <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1">
      <div className="flex flex-col w-full h-screen px-4 modal-body" style={{ minHeight: '100vh', maxHeight: '100vh' }}>
        <UserGameDetails className="mt-4" />
        <div className="flex items-center justify-center space-x-1 text-gradient" style={{ marginTop: '30px' }}>
          <BattlxIcon
            icon="coins"
            className="text-3xl w-9 h-10 text-[#9B8B6C]"
          />
          <span className="text-3xl font-bold font-gothic text-[#9B8B6C]">
            {Math.floor(user.balance)?.toLocaleString()}
          </span>
        </div>
        <div style={{ marginTop: '20px' }}>
          <div className="flex gap-2 justify-center" style={{ marginTop: '15px' }}>
            {[
              { id: 'eternal', name: 'Eternal Harvest', typeId: 1 },
              { id: 'grave', name: 'Grave Yield', typeId: 2 },
              { id: 'necrotic', name: 'Necrotic Flow', typeId: 3 },
              { id: 'dark', name: 'Dark Tithes', typeId: 4 },
            ].map((section) => (
              <button
                key={section.id}
                onClick={() => {
                  setActiveSection(section.id as typeof activeSection);
                  setActiveType(missionTypes.find(t => t.id === section.typeId)!);
                }}
                className={cn(
                  "text-xs font-medium uppercase px-3 py-2 transition-all duration-300 rounded-lg border border-[#B3B3B3]/20",
                  activeSection === section.id
                    ? "bg-[#1A1617] text-[#9B8B6C] shadow-[0_4px_15px_rgba(74,14,14,0.3)]"
                    : "text-[#B3B3B3]/60 hover:bg-[#1A1617]/50"
                )}
              >
                {section.name}
              </button>
            ))}
          </div>
          {(activeSection === 'eternal' || activeSection === 'grave' || activeSection === 'necrotic' || activeSection === 'dark') && (
            <div style={{ marginTop: '55px' }}>
              {missions.isLoading ? (
                <div className="flex items-center justify-center h-full mt-6">
                  <BattlxIcon icon="loading" className="w-12 h-12 animate-spin text-primary" />
                </div>
              ) : (
                <Swiper
                  effect={'coverflow'}
                  grabCursor={true}
                  centeredSlides={true}
                  slidesPerView={'auto'}
                  modules={[EffectCoverflow, Pagination]}
                  className={styles.missionCardsSwiper}
                  coverflowEffect={{
                    rotate: 50,
                    stretch: 0,
                    depth: 100,
                    modifier: 1,
                    slideShadows: true,
                  }}
                  pagination={{
                    clickable: true,
                    dynamicBullets: true,
                  }}
                  spaceBetween={30}
                  initialSlide={0}
                >
                  {missions.data?.map((mission, key) => (
                    <SwiperSlide key={key}>
                      <div
                        className={cn(
                          styles.gameCard,
                          {
                            "opacity-40": !mission.is_unlocked || (
                              (mission?.required_user_level && mission.required_user_level > user.level!.level) ||
                              (mission.required_friends_invitation && mission.required_friends_invitation > totalReferals)
                            ),
                          }
                        )}
                        onClick={() => {
                          if (
                            !mission.next_level ||
                            !mission.is_unlocked ||
                            (mission?.required_user_level && mission.required_user_level > user.level!.level) ||
                            (mission.required_friends_invitation && mission.required_friends_invitation > totalReferals)
                          )
                            return;
                          setSelectedMission(mission);
                          setOpenDrawer(true);
                        }}
                      >
                        <div className={styles.cardHeader}>
                          <h3 className="text-lg font-bold text-[#9B8B6C] truncate">{mission.name}</h3>
                        </div>

                        <div className={styles.cardImage}>
                          <img
                            src={mission.image}
                            alt={mission.name}
                          />
                        </div>

                        <div className={styles.cardContent}>
                          <p className="text-sm font-medium text-[#B3B3B3]/80">
                            {
                              activeSection === 'eternal' ? 'Profit per hour' :
                              activeSection === 'grave' ? 'One-time reward' :
                              activeSection === 'dark' ? 'Skin' :
                              'Referral reward'
                            }
                          </p>
                          <Price
                            amount={
                              mission.production_per_hour ||
                              `+${mission.next_level?.production_per_hour || 0}`
                            }
                            className="mt-1 text-lg"
                          />
                        </div>

                        {activeSection === 'eternal' && mission.next_level && (
                          <div className={styles.cardFooter}>
                            <div className="flex items-center justify-between">
                              <p className="text-sm font-medium text-[#9B8B6C]">
                                LVL {mission.next_level?.level}
                              </p>
                              {mission.required_user_level &&
                              mission.required_user_level > user.level!.level ? (
                                <div className="flex items-center gap-2 text-xs text-[#B3B3B3]/60">
                                  <BattlxIcon
                                    icon="lock"
                                    className="w-4 h-4 opacity-80 text-[#9B8B6C]"
                                  />
                                  <span>
                                    Required lvl{" "}
                                    <span className="text-[#9B8B6C]">{mission.required_user_level}</span>
                                  </span>
                                </div>
                              ) : mission.required_mission_id && !mission.is_unlocked ? (
                                <div className="flex items-center gap-2 text-xs text-[#B3B3B3]/60">
                                  <BattlxIcon
                                    icon="lock"
                                    className="w-4 h-4 opacity-80 text-[#9B8B6C]"
                                  />
                                  <span>
                                    Requires{" "}
                                    <span className="text-[#9B8B6C]">
                                      {mission.required_mission?.name}
                                    </span>
                                  </span>
                                </div>
                              ) : mission.required_friends_invitation &&
                                mission.required_friends_invitation > totalReferals ? (
                                <div className="flex items-center gap-2 text-xs text-[#B3B3B3]/60">
                                  <BattlxIcon
                                    icon="lock"
                                    className="w-4 h-4 opacity-80 text-[#9B8B6C]"
                                  />
                                  <span>
                                    Required friends{" "}
                                    <span className="text-[#9B8B6C]">{mission.required_friends_invitation}</span>
                                  </span>
                                </div>
                              ) : (
                                mission.next_level?.cost && (
                                  <Price
                                    amount={compactNumber(mission.next_level?.cost)}
                                    className="text-sm"
                                  />
                                )
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>
              )}
            </div>
          )}
        </div>
      </div>
      <MissionDrawer
        open={openDrawer}
        onOpenChange={setOpenDrawer}
        mission={selectedMission}
      />
    </div>
  );
}
