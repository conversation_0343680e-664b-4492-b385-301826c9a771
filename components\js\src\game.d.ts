declare class Game {
    constructor(canvas: HTMLCanvasElement);
    canvas: HTMLCanvasElement;
    ctx: CanvasRenderingContext2D | null;
    lastTime: number;
    accumulator: number;
    timeStep: number;
    running: boolean;
    static core: any; // Assuming GameCore is not typed, or needs a separate d.ts
    static deltaTime: number;

    resizeCanvas(): void;
    handleInput(e: MouseEvent | TouchEvent): void;
    start(): Promise<void>;
    stop(): void;
    gameLoop(currentTime: number): void;
    cleanUp(): void;
}

// If GameCore is also used, you might need a declaration for it as well.
// declare class GameCore { ... }

export { Game };