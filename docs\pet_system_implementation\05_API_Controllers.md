# API Controllers Implementation

## Overview
This document covers the creation of API controllers for the Pet System, including pet management, interactions, purchases, and collection endpoints.

## Implementation Time: 3-4 days
## Complexity: High
## Dependencies: Eloquent models completed

## Pet Management Controller

### PetController
```php
<?php
// File: api/app/Http/Controllers/PetController.php

namespace App\Http\Controllers;

use App\Models\Pet;
use App\Models\PetTemplate;
use App\Models\TelegramUser;
use App\Services\PetService;
use App\Services\AchievementPointService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class PetController extends Controller
{
    protected PetService $petService;
    protected AchievementPointService $achievementPointService;

    public function __construct(PetService $petService, AchievementPointService $achievementPointService)
    {
        $this->petService = $petService;
        $this->achievementPointService = $achievementPointService;
    }

    /**
     * Get user's pet collection
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $pets = $user->pets()
                    ->with(['template', 'interactions' => function($q) {
                        $q->whereDate('interaction_time', today())->latest();
                    }])
                    ->orderBy('created_at', 'desc')
                    ->get();

        $collectionProgress = $user->getCollectionProgress();
        
        return response()->json([
            'success' => true,
            'pets' => $pets->map(function($pet) {
                return [
                    'id' => $pet->id,
                    'template_id' => $pet->pet_template_id,
                    'name' => $pet->display_name,
                    'category' => $pet->template->category,
                    'rarity' => $pet->template->rarity,
                    'level' => $pet->level,
                    'experience' => $pet->experience,
                    'experience_to_next_level' => $pet->experience_to_next_level,
                    'happiness' => $pet->happiness,
                    'happiness_percentage' => $pet->happiness_percentage,
                    'current_image' => $pet->current_image,
                    'can_evolve' => $pet->can_evolve,
                    'evolution_stage' => $pet->evolution_stage,
                    'is_featured' => $pet->is_featured,
                    'is_favorite' => $pet->is_favorite,
                    'nickname' => $pet->nickname,
                    'interactions_today' => $pet->interactions->count(),
                    'last_interaction' => $pet->interactions->first()?->interaction_time,
                    'can_feed' => $pet->canInteract('feed'),
                    'can_play' => $pet->canInteract('play'),
                    'can_pet' => $pet->canInteract('pet'),
                    'mystery_box_unlocks' => $pet->template->mystery_box_unlocks,
                    'collectible_reward' => $pet->template->collectible_reward_id,
                ];
            }),
            'collection_progress' => $collectionProgress,
            'pets_needing_attention' => $user->getPetsNeedingAttention()->count()
        ]);
    }

    /**
     * Get featured pet for home screen widget
     */
    public function getFeaturedPet(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $featuredPet = $user->featuredPet()->with('template')->first();
        
        if (!$featuredPet) {
            // Auto-select first pet as featured if none set
            $firstPet = $user->pets()->with('template')->first();
            if ($firstPet) {
                $firstPet->update(['is_featured' => true]);
                $featuredPet = $firstPet;
            }
        }
        
        if (!$featuredPet) {
            return response()->json([
                'success' => true,
                'featured_pet' => null
            ]);
        }
        
        return response()->json([
            'success' => true,
            'featured_pet' => [
                'id' => $featuredPet->id,
                'name' => $featuredPet->display_name,
                'category' => $featuredPet->template->category,
                'rarity' => $featuredPet->template->rarity,
                'level' => $featuredPet->level,
                'happiness' => $featuredPet->happiness,
                'happiness_percentage' => $featuredPet->happiness_percentage,
                'current_image' => $featuredPet->current_image,
                'can_feed' => $featuredPet->canInteract('feed'),
                'can_play' => $featuredPet->canInteract('play'),
                'can_pet' => $featuredPet->canInteract('pet'),
                'interactions_today' => $featuredPet->interactions()
                    ->whereDate('interaction_time', today())
                    ->count(),
                'needs_attention' => $featuredPet->happiness < 30
            ]
        ]);
    }

    /**
     * Set featured pet
     */
    public function setFeaturedPet(Request $request, Pet $pet): JsonResponse
    {
        $user = $request->user();
        
        if ($pet->telegram_user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Pet not found'
            ], 404);
        }
        
        // Remove featured status from all pets
        $user->pets()->update(['is_featured' => false]);
        
        // Set new featured pet
        $pet->update(['is_featured' => true]);
        
        return response()->json([
            'success' => true,
            'message' => 'Featured pet updated successfully'
        ]);
    }

    /**
     * Purchase a pet
     */
    public function purchase(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'pet_template_id' => 'required|exists:pet_templates,id',
            'purchase_method' => 'required|in:coins,gems',
            'nickname' => 'nullable|string|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $petTemplate = PetTemplate::findOrFail($request->pet_template_id);
        $purchaseMethod = $request->purchase_method;
        $nickname = $request->nickname;

        try {
            $result = $this->petService->purchasePet($user, $petTemplate, $purchaseMethod, $nickname);
            
            return response()->json([
                'success' => true,
                'message' => 'Pet purchased successfully!',
                'pet' => $result['pet'],
                'collectible_unlocked' => $result['collectible_unlocked'],
                'mystery_boxes_unlocked' => $result['mystery_boxes_unlocked'],
                'user_balance' => [
                    'balance' => $user->fresh()->balance,
                    'gems' => $user->fresh()->gems ?? 0
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Interact with pet (feed, play, pet)
     */
    public function interact(Request $request, Pet $pet): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'interaction_type' => 'required|in:feed,play,pet'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid interaction type'
            ], 422);
        }

        $user = $request->user();
        $interactionType = $request->interaction_type;

        if ($pet->telegram_user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Pet not found'
            ], 404);
        }

        try {
            $rewards = $pet->interact($interactionType, $user);
            
            // Award achievement points for interactions
            $this->achievementPointService->awardPoints(
                $user->id,
                1,
                'pet_interaction',
                $pet->id,
                "Interacted with {$pet->display_name} ({$interactionType})"
            );
            
            return response()->json([
                'success' => true,
                'message' => ucfirst($interactionType) . ' interaction completed!',
                'rewards' => $rewards,
                'pet' => [
                    'id' => $pet->id,
                    'happiness' => $pet->fresh()->happiness,
                    'happiness_percentage' => $pet->fresh()->happiness_percentage,
                    'level' => $pet->fresh()->level,
                    'experience' => $pet->fresh()->experience,
                    'experience_to_next_level' => $pet->fresh()->experience_to_next_level,
                    'can_evolve' => $pet->fresh()->can_evolve,
                    'evolution_stage' => $pet->fresh()->evolution_stage,
                    'current_image' => $pet->fresh()->current_image,
                    'can_feed' => $pet->fresh()->canInteract('feed'),
                    'can_play' => $pet->fresh()->canInteract('play'),
                    'can_pet' => $pet->fresh()->canInteract('pet')
                ],
                'user_balance' => [
                    'balance' => $user->fresh()->balance,
                    'available_energy' => $user->fresh()->available_energy
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Update pet nickname
     */
    public function updateNickname(Request $request, Pet $pet): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'nickname' => 'nullable|string|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid nickname',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if ($pet->telegram_user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Pet not found'
            ], 404);
        }

        $pet->update(['nickname' => $request->nickname]);

        return response()->json([
            'success' => true,
            'message' => 'Pet nickname updated successfully',
            'pet' => [
                'id' => $pet->id,
                'nickname' => $pet->nickname,
                'display_name' => $pet->display_name
            ]
        ]);
    }

    /**
     * Toggle pet favorite status
     */
    public function toggleFavorite(Request $request, Pet $pet): JsonResponse
    {
        $user = $request->user();

        if ($pet->telegram_user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Pet not found'
            ], 404);
        }

        $pet->update(['is_favorite' => !$pet->is_favorite]);

        return response()->json([
            'success' => true,
            'message' => $pet->is_favorite ? 'Pet added to favorites' : 'Pet removed from favorites',
            'is_favorite' => $pet->is_favorite
        ]);
    }

    /**
     * Get pet interaction history
     */
    public function getInteractionHistory(Request $request, Pet $pet): JsonResponse
    {
        $user = $request->user();

        if ($pet->telegram_user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Pet not found'
            ], 404);
        }

        $interactions = $pet->interactions()
                           ->orderBy('interaction_time', 'desc')
                           ->paginate(20);

        return response()->json([
            'success' => true,
            'interactions' => $interactions->items(),
            'pagination' => [
                'current_page' => $interactions->currentPage(),
                'last_page' => $interactions->lastPage(),
                'per_page' => $interactions->perPage(),
                'total' => $interactions->total()
            ]
        ]);
    }

    /**
     * Get pets needing attention
     */
    public function getPetsNeedingAttention(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $petsNeedingAttention = $user->getPetsNeedingAttention();
        
        return response()->json([
            'success' => true,
            'pets_needing_attention' => $petsNeedingAttention->map(function($pet) {
                return [
                    'id' => $pet->id,
                    'name' => $pet->display_name,
                    'happiness' => $pet->happiness,
                    'happiness_percentage' => $pet->happiness_percentage,
                    'current_image' => $pet->current_image,
                    'last_fed' => $pet->last_fed,
                    'last_played' => $pet->last_played,
                    'last_petted' => $pet->last_petted
                ];
            }),
            'count' => $petsNeedingAttention->count()
        ]);
    }
}
```

## Pet Shop Controller

### PetShopController
```php
<?php
// File: api/app/Http/Controllers/PetShopController.php

namespace App\Http\Controllers;

use App\Models\PetTemplate;
use App\Models\TelegramUser;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PetShopController extends Controller
{
    /**
     * Get available pets for purchase
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $category = $request->query('category');
        $rarity = $request->query('rarity');
        
        $query = PetTemplate::active()->availableForPurchase();
        
        if ($category) {
            $query->byCategory($category);
        }
        
        if ($rarity) {
            $query->byRarity($rarity);
        }
        
        $petTemplates = $query->orderBy('sort_order')
                             ->orderBy('rarity')
                             ->orderBy('name')
                             ->get();
        
        $availablePets = $petTemplates->map(function($template) use ($user) {
            $isOwned = $user->pets()->where('pet_template_id', $template->id)->exists();
            $canPurchase = !$isOwned && $template->canBePurchasedBy($user);
            
            return [
                'id' => $template->id,
                'name' => $template->name,
                'category' => $template->category,
                'rarity' => $template->rarity,
                'rarity_color' => $template->rarity_color,
                'description' => $template->description,
                'image_url' => $template->image_url,
                'animation_url' => $template->animation_url,
                'coin_cost' => $template->coin_cost,
                'gem_cost' => $template->gem_cost,
                'prize_tree_unlock_level' => $template->prize_tree_unlock_level,
                'is_premium_only' => $template->is_premium_only,
                'is_owned' => $isOwned,
                'can_purchase' => $canPurchase,
                'mystery_box_unlocks' => $template->mystery_box_unlocks,
                'collectible_reward_id' => $template->collectible_reward_id,
                'unlock_reason' => $this->getUnlockReason($template, $user)
            ];
        });
        
        return response()->json([
            'success' => true,
            'pets' => $availablePets,
            'categories' => ['shadow', 'undead', 'demon', 'spirit', 'beast'],
            'rarities' => ['common', 'rare', 'epic', 'legendary', 'mythic'],
            'user_balance' => [
                'balance' => $user->balance,
                'gems' => $user->gems ?? 0
            ]
        ]);
    }

    /**
     * Get featured pets (rotating selection)
     */
    public function getFeaturedPets(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Get 3 random pets that user doesn't own
        $ownedPetTemplateIds = $user->pets()->pluck('pet_template_id');
        
        $featuredPets = PetTemplate::active()
                                  ->availableForPurchase()
                                  ->whereNotIn('id', $ownedPetTemplateIds)
                                  ->inRandomOrder()
                                  ->limit(3)
                                  ->get();
        
        return response()->json([
            'success' => true,
            'featured_pets' => $featuredPets->map(function($template) use ($user) {
                return [
                    'id' => $template->id,
                    'name' => $template->name,
                    'category' => $template->category,
                    'rarity' => $template->rarity,
                    'rarity_color' => $template->rarity_color,
                    'image_url' => $template->image_url,
                    'coin_cost' => $template->coin_cost,
                    'gem_cost' => $template->gem_cost,
                    'can_purchase' => $template->canBePurchasedBy($user)
                ];
            })
        ]);
    }

    private function getUnlockReason(PetTemplate $template, TelegramUser $user): ?string
    {
        if ($template->canBePurchasedBy($user)) {
            return null;
        }
        
        if ($template->prize_tree_unlock_level) {
            $userProgress = $user->prizeTreeProgress();
            $currentLevel = $userProgress ? $userProgress->current_level : 0;
            
            if ($currentLevel < $template->prize_tree_unlock_level) {
                return "Requires Prize Tree level {$template->prize_tree_unlock_level} (current: {$currentLevel})";
            }
        }
        
        return 'Requirements not met';
    }
}
```

## Acceptance Criteria
- [x] Pet collection endpoint returns complete pet data
- [x] Featured pet endpoint works for home screen widget
- [x] Pet purchase system functional with validation
- [x] Pet interaction system working with rewards
- [x] Pet shop displays available pets correctly
- [x] Proper error handling and validation
- [x] Collectible management endpoints implemented
- [x] Mystery box system endpoints implemented
- [x] API routes configuration complete

## Next Steps
1. Create Pet Services for business logic
2. Implement Mystery Box controllers
3. Create Collection system controllers
4. Add API routes configuration

## Troubleshooting
- Ensure proper authentication middleware
- Validate user ownership of pets
- Check energy requirements for interactions
- Verify achievement point integration
