{/* Gothic Frame Border with Diagonal Line Pattern */}
<div className="absolute inset-0 overflow-hidden pointer-events-none">
  {/* Background pattern for the entire frame */}
  <div className="absolute inset-0 before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]"></div>
  
  {/* Top Border with Gothic Arches and Flourishes */}
  <div className="absolute top-0 left-0 right-0 h-16 flex">
    <div className="w-16 h-16 bg-[#1A1617] border border-[#9B8B6C]/20 shadow-[0_4px_15px_rgba(74,14,14,0.3)]">
      <svg viewBox="0 0 100 100" className="w-full h-full">
        <path d="M0,0 L100,0 L100,100 L75,75 Q50,100 25,75 L0,100 Z" fill="currentColor" className="text-[#4A0E0E]" />
        <path d="M25,25 Q50,0 75,25" stroke="currentColor" strokeWidth="4" fill="none" className="text-[#9B8B6C]" />
        <circle cx="50" cy="15" r="5" fill="currentColor" className="text-[#9B8B6C]" />
      </svg>
    </div>
    <div className="flex-1 h-16 flex items-center justify-center overflow-hidden">
      <div className="flex space-x-1">
        {[...Array(15)].map((_, i) => (
          <div key={i} className="w-12 h-16 relative">
            <div className="absolute bottom-0 w-full h-10 bg-[#1A1617] rounded-t-2xl border border-[#9B8B6C]/20 before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]" />
            <div className="absolute bottom-8 w-full">
              <div className="mx-auto w-8 h-8 bg-[#4A0E0E] rotate-45 transform origin-bottom shadow-[0_4px_15px_rgba(74,14,14,0.3)] border border-[#9B8B6C]/20 before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]" />
            </div>
          </div>
        ))}
      </div>
    </div>
    <div className="w-16 h-16 bg-[#1A1617] border border-[#9B8B6C]/20 shadow-[0_4px_15px_rgba(74,14,14,0.3)]">
      <svg viewBox="0 0 100 100" className="w-full h-full">
        <path d="M0,0 L100,0 L100,100 L75,75 Q50,100 25,75 L0,100 Z" fill="currentColor" className="text-[#4A0E0E]" />
        <path d="M25,25 Q50,0 75,25" stroke="currentColor" strokeWidth="4" fill="none" className="text-[#9B8B6C]" />
        <circle cx="50" cy="15" r="5" fill="currentColor" className="text-[#9B8B6C]" />
      </svg>
    </div>
  </div>
  
  {/* Left Border with Vertical Gothic Elements and gradient highlight */}
  <div className="absolute top-16 left-0 bottom-16 w-8 bg-[#1A1617] flex flex-col border-r border-[#9B8B6C]/20 shadow-[4px_0_15px_rgba(74,14,14,0.3)] before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]">
    {[...Array(8)].map((_, i) => (
      <div key={i} className="flex-1 border-t border-[#9B8B6C]/20 relative">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-4 h-4 bg-[#4A0E0E] rotate-45 shadow-[0_4px_15px_rgba(74,14,14,0.3)] border border-[#9B8B6C]/20" />
        </div>
      </div>
    ))}
    {/* Gradient highlight */}
    <div className="absolute top-1/2 right-0 bg-gradient-to-r from-[#9B8B6C] via-[#B3B3B3] to-[#9B8B6C] rounded-sm shadow-[0_0_6px_rgba(155,139,108,0.3)] h-[2px] w-full"></div>
  </div>
  
  {/* Right Border with Vertical Gothic Elements and gradient highlight */}
  <div className="absolute top-16 right-0 bottom-16 w-8 bg-[#1A1617] flex flex-col border-l border-[#4A0E0E] shadow-[-4px_0_15px_rgba(74,14,14,0.3)] before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]">
    {[...Array(8)].map((_, i) => (
      <div key={i} className="flex-1 border-t border-[#4A0E0E] relative">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-4 h-4 bg-[#4A0E0E] rotate-45 shadow-[0_4px_15px_rgba(74,14,14,0.3)] border border-[#9B8B6C]/20" />
        </div>
      </div>
    ))}
    {/* Gradient highlight */}
    <div className="absolute top-1/2 left-0 bg-gradient-to-r from-[#9B8B6C] via-[#B3B3B3] to-[#9B8B6C] rounded-sm shadow-[0_0_6px_rgba(155,139,108,0.3)] h-[2px] w-full"></div>
  </div>
  
  {/* Bottom Border with Inverted Gothic Arches */}
  <div className="absolute bottom-0 left-0 right-0 h-16 flex">
    <div className="w-16 h-16 bg-[#1A1617] border border-[#9B8B6C]/20 shadow-[0_4px_15px_rgba(74,14,14,0.3)] before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]">
      <svg viewBox="0 0 100 100" className="w-full h-full">
        <path d="M0,0 L100,0 L100,100 L0,100 L0,0 Z" fill="currentColor" className="text-[#4A0E0E]" />
        <path d="M25,75 Q50,100 75,75" stroke="currentColor" strokeWidth="4" fill="none" className="text-[#9B8B6C]" />
        <circle cx="50" cy="85" r="5" fill="currentColor" className="text-[#9B8B6C]" />
      </svg>
    </div>
    <div className="flex-1 h-16 flex items-center justify-center overflow-hidden">
      <div className="flex space-x-1">
        {[...Array(15)].map((_, i) => (
          <div key={i} className="w-12 h-16 relative">
            <div className="absolute top-0 w-full h-10 bg-[#1A1617] rounded-b-2xl border border-[#9B8B6C]/20 before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]" />
            <div className="absolute top-8 w-full">
              <div className="mx-auto w-8 h-8 bg-[#4A0E0E] rotate-45 transform origin-top shadow-[0_4px_15px_rgba(74,14,14,0.3)] border border-[#9B8B6C]/20 before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]" />
            </div>
          </div>
        ))}
      </div>
    </div>
    <div className="w-16 h-16 bg-[#1A1617] border border-[#9B8B6C]/20 shadow-[0_4px_15px_rgba(74,14,14,0.3)] before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)]">
      <svg viewBox="0 0 100 100" className="w-full h-full">
        <path d="M0,0 L100,0 L100,100 L0,100 L0,0 Z" fill="currentColor" className="text-[#4A0E0E]" />
        <path d="M25,75 Q50,100 75,75" stroke="currentColor" strokeWidth="4" fill="none" className="text-[#9B8B6C]" />
        <circle cx="50" cy="85" r="5" fill="currentColor" className="text-[#9B8B6C]" />
      </svg>
    </div>
  </div>
  
  {/* Horizontal gradient lines */}
  <div className="absolute top-16 left-8 right-8 bg-gradient-to-r from-[#9B8B6C] via-[#B3B3B3] to-[#9B8B6C] rounded-sm shadow-[0_0_6px_rgba(155,139,108,0.3)] h-[2px]"></div>
  <div className="absolute bottom-16 left-8 right-8 bg-gradient-to-r from-[#9B8B6C] via-[#B3B3B3] to-[#9B8B6C] rounded-sm shadow-[0_0_6px_rgba(155,139,108,0.3)] h-[2px]"></div>
</div>