# Slash Game Integration Guide: 09 - Testing and Validation Requirements

This document outlines the testing strategy and specific test cases for the Slash game integration to ensure its functionality, performance, and correct interaction with the frontend and backend.

## 1. Testing Strategy

A comprehensive testing strategy should include a combination of:

*   **Unit Tests:** Focus on testing individual components and functions within the Slash game's core logic (`js/src/components/`). This includes testing game mechanics, object behaviors, calculations (e.g., damage, experience, coin collection), and state transitions.
*   **Integration Tests:** Verify the interaction between different parts of the system. This includes:
    *   Testing the integration between the Slash game module (`main.ts`) and `GameWrapper.tsx`, ensuring the `GameInstance` interface is correctly implemented and callbacks (`onGameOver`) are triggered with the correct data (final coin amount).
    *   Testing the interaction between `GameWrapper.tsx` and the backend API endpoints (`/api/game/check-play-availability`, `/api/game/update-score`).
*   **End-to-End Tests:** Simulate the full user flow, from navigating to the game screen, unlocking the game (if necessary), playing a game session, reaching game over, and verifying that the score is correctly saved and reflected in the user's total game score.
*   **Performance Testing:** Assess the game's performance, especially with many on-screen entities, to identify bottlenecks and ensure a smooth gameplay experience.
*   **Manual Testing:** Essential for gameplay feel, visual correctness, and overall user experience that automated tests might not fully capture.

## 2. Specific Test Cases

The following specific test cases should be covered:

*   **Game Access:**
    *   Verify that the Slash game appears in the list of available games.
    *   Test navigation to the Slash game screen.
    *   If the game is locked, verify that the "unlock game" option is presented.
    *   Test the game unlocking process, ensuring the correct price is deducted and the game becomes unlocked.
    *   Verify that a user with an unlocked game can access and start playing.
    *   Verify that a user with a locked game cannot bypass the unlock process.
*   **Gameplay:**
    *   Verify that the game loads and initializes correctly.
    *   Test core game mechanics (player movement, weapon firing, enemy spawning and movement, pickup collection).
    *   **Verify that collecting coins correctly increases the player's internal coin count.**
    *   Test player leveling and its effects (if any).
    *   Verify that the game over condition is correctly detected (e.g., player health reaching zero).
    *   Verify that the game transitions to the game over state.
*   **Scoring and API Integration:**
    *   **Verify that upon game over, the final collected coin amount is correctly passed to the `onGameOver` callback in `main.ts`.**
    *   **Verify that `GameWrapper.tsx` receives the correct final coin amount from the `onGameOver` callback.**
    *   **Verify that `GameWrapper.tsx` correctly calls the `/api/game/update-score` endpoint with the final coin amount and the 'slash' game ID.**
    *   **On the backend, verify that the `updateScore` endpoint correctly adds the submitted coin amount to the user's total `game_score` in the `telegram_users` table.**
    *   Verify that the updated total game score is reflected in the frontend (e.g., on the user's profile or a leaderboard).
*   **Unlimited Plays:**
    *   Verify that after unlocking the Slash game, the user can play it multiple times without any play attempt limitations (unlike the Tower game).
*   **Error Handling:**
    *   Test how the frontend handles errors during API calls (e.g., network issues, backend errors during play availability check or score submission).
    *   Test how the game handles asset loading failures.

## 3. Validation Requirements

*   **Input Validation:** Ensure that any data sent from the frontend to the backend API is properly validated on the backend to prevent malicious or incorrect data from affecting the system (e.g., validating the submitted score).
*   **Data Integrity:** Verify that the user's `game_score` on the backend accurately reflects the sum of the final coin amounts from their Slash game sessions.
*   **Game State Consistency:** Ensure that the game state is consistent between the game module and the `GameWrapper` (e.g., the `isGameOver` state).

By thoroughly testing and validating the Slash game integration against these requirements, we can ensure a robust and correct implementation.