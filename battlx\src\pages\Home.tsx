import UserTap from "../components/UserTap";
import { useUserStore } from "../store/user-store";
import { Link } from "react-router-dom";
import UserGameDetails from "@/components/UserGameDetails";
import levelConfig from "@/config/level-config";
import { BattlxIcon } from "@/components/icons/BattlxIcon";
import { uesStore } from "@/store";
import { useState } from "react";

export default function Home() {
  const user = useUserStore();
  const [showStats, setShowStats] = useState(false);
  const { maxLevel } = uesStore();
  return (
    <div
      className="flex-1 px-5 pb-20 bg-center bg-cover"
      style={{
        backgroundImage: `url(${levelConfig.bg[user?.level?.level || 1]})`,
      }}
    >
      <div className="mt-4">

        <header className="relative rounded-lg">
          <div className="absolute inset-0 bg-[#3D0000]/10 border border-[#B3B3B3]/10 backdrop-blur-sm before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(255,0,0,0.02)_0px,rgba(255,0,0,0.02)_1px,transparent_1px,transparent_10px)] shadow-[0_2px_10px_rgba(74,14,14,0.15)]"></div>
          <div className="flex items-center gap-3 px-3 py-1.5 relative z-[2]">
            <div className="flex items-center gap-1.5 px-2.5 py-1.5 border-2 rounded-full bg-black/20 border-white/10">
              <BattlxIcon
                icon="avatar"
                className="w-6 h-6 text-[#9B8B6C]"
              />
              <p className="text-xs font-medium uppercase">
                {user?.first_name} {user?.last_name}
              </p>
            </div>

            <div className="flex flex-col gap-1 flex-1 mx-4">
              <div className="flex items-center justify-between">
                <div className="text-xs text-[#9B8B6C]">{user.level?.name}</div>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-[#9B8B6C]">Level</span>
                  <span className="font-gothic text-[#9B8B6C]">
                    {user.level?.level}/{maxLevel}
                  </span>
                </div>
              </div>
              <div className="w-full bg-[#5A0000] border overflow-hidden border-[#FF0000]/10 rounded-lg h-4 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(255,0,0,0.03)_0px,rgba(255,0,0,0.03)_1px,transparent_1px,transparent_10px)] shadow-[0_0_15px_rgba(255,0,0,0.15)]">
                <div
                  className="bg-gradient-to-l from-[#000000] via-[#000000] to-[#000000] h-full ml-auto transition-all duration-300 shadow-[inset_0_0_20px_rgba(255,0,0,0.4),0_0_10px_rgba(255,0,0,0.3)]"
                  style={{
                    width: `${((user.balance! - user.level!.from_balance) / (user.level!.to_balance - user.level!.from_balance)) * 100}%`,
                  }}
                ></div>
              </div>
            </div>
          </div>
        </header>



        {/* Balance and Sample Sections - Top Left */}
        <div className="mt-4 flex justify-start gap-3">
          {/* Balance Section */}
          <div className="bg-[#1A1617] rounded-lg border border-[#B3B3B3]/20 backdrop-blur-3xl relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)] px-2 py-1">
            <div className="flex space-x-1 justify-center items-center select-none relative z-[2]">
              <BattlxIcon
                icon="coin4"
                size="md"
                className="w-4 h-4 text-lg font-black text-[#9B8B6C] leading-tight tracking-wide [text-shadow:1px_1px_2px_rgba(0,0,0,0.6)]"
              />
              <span className="text-lg font-black text-[#9B8B6C] leading-tight tracking-wide [text-shadow:2px_2px_4px_rgba(0,0,0,0.6)]">
                {Math.floor(user.balance)?.toLocaleString()}
              </span>
            </div>
          </div>

          {/* Sample Section with 0000 */}
          <div className="bg-[#1A1617] rounded-lg border border-[#B3B3B3]/20 backdrop-blur-3xl relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)] px-2 py-1">
            <div className="flex space-x-1 justify-center items-center select-none relative z-[2]">
              <BattlxIcon
                icon="coin4"
                size="md"
                className="w-4 h-4 text-lg font-black text-[#9B8B6C] leading-tight tracking-wide [text-shadow:1px_1px_2px_rgba(0,0,0,0.6)]"
              />
              <span className="text-lg font-black text-[#9B8B6C] leading-tight tracking-wide [text-shadow:2px_2px_4px_rgba(0,0,0,0.6)]">
                0000
              </span>
            </div>
          </div>
        </div>

        {/* Mini App Bar - Clean without balance */}
        <div className="mt-4 flex justify-center">
          <div className="flex items-center px-2 py-0.5 gap-1 bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 backdrop-blur-3xl relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)]">

            {/* Leaderboard Button */}
            <Link
              to="/leaderboard"
              className="relative flex items-center rounded-md flex-col justify-center font-medium text-[7px] px-3 py-0.5 gap-0 select-none text-[#B3B3B3]/60 border border-[#9B8B6C]/20 bg-[#1A1617]/50 hover:bg-[#4A0E0E]/20 transition-all duration-300 w-12"
            >
              <BattlxIcon
                icon="leaderboard"
                size="lg"
                className="opacity-50 transition-all duration-300 text-[#9B8B6C] scale-125"
              />
              <span>Leaderboard</span>
            </Link>

            {/* Energy Display */}
            <div className="relative flex items-center rounded-md flex-col justify-center font-medium text-[7px] px-3 py-0.5 gap-0 select-none text-[#B3B3B3]/60 border border-[#9B8B6C]/20 bg-[#1A1617]/50 w-12">
              <BattlxIcon
                icon="bolt"
                size="lg"
                className="opacity-50 transition-all duration-300 text-[#9B8B6C] scale-125"
              />
              <span className="text-[#9B8B6C]">{user.available_energy}/{user.max_energy}</span>
            </div>

            {/* Boost Button */}
            <Link
              to="/boost"
              className="relative flex items-center rounded-md flex-col justify-center font-medium text-[7px] px-3 py-0.5 gap-0 select-none text-[#B3B3B3]/60 border border-[#9B8B6C]/20 bg-[#1A1617]/50 hover:bg-[#4A0E0E]/20 transition-all duration-300 w-12"
            >
              <BattlxIcon
                icon="boost"
                size="lg"
                className="opacity-50 transition-all duration-300 text-[#9B8B6C] scale-125"
              />
              <span>Boost</span>
            </Link>
          </div>
        </div>

        {showStats && (
          <>
            <div
              className="fixed inset-0 bg-black/50 z-10"
              onClick={() => setShowStats(false)}
            />
            <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20 w-[90%] max-w-md">
              <UserGameDetails />
            </div>
          </>
        )}
        <UserTap />
      </div>
    </div>
  );
}