/**
 * Game Engine
 * Handles the core functionality of the game including:
 * - Canvas setup
 * - Game loop
 * - Asset management
 * - State management
 */
class Engine {
    /**
     * @param {string} canvasId - The ID of the canvas element
     * @param {Object} options - Engine options
     * @param {number} options.width - Canvas width
     * @param {number} options.height - Canvas height
     * @param {boolean} options.debug - Enable debug mode
     */
    constructor(canvasId, options = {}) {
        // Store reference to this engine instance in window for global access
        window.currentEngine = this;

        // Canvas setup
        this.canvas = document.getElementById(canvasId);
        this.ctx = this.canvas.getContext('2d');
        this.width = options.width || 540;
        this.height = options.height || 960;
        this.canvas.width = this.width;
        this.canvas.height = this.height;

        // Game state
        this.currentState = null;
        this.states = {};
        this.debug = options.debug || false;
        this.paused = false;

        // Animation frame
        this.animationFrameId = null;
        this.lastTime = 0;
        this.deltaTime = 0;

        // Assets
        this.images = new Map();
        this.sounds = new Map();
        this.sprites = new Map();

        // Track active sound instances
        this.activeSoundInstances = new Set();

        // Game variables
        this.variables = new Map();

        // Event listeners
        this.setupEventListeners();
    }

    /**
     * Start the game loop
     */
    start() {
        if (this.currentState) {
            this.lastTime = performance.now();
            this.loop(this.lastTime);
        } else {
            console.error('No game state set. Use setState() before starting the game.');
        }
    }

    /**
     * Main game loop
     * @param {number} timestamp - Current timestamp
     */
    loop(timestamp) {
        // Calculate delta time
        this.deltaTime = (timestamp - this.lastTime) / 1000;
        this.lastTime = timestamp;

        // Clear the canvas
        this.ctx.clearRect(0, 0, this.width, this.height);

        // Update and render current state
        if (!this.paused && this.currentState) {
            this.currentState.update(this.deltaTime);
            this.currentState.render(this.ctx);
        }

        // Debug info
        if (this.debug) {
            this.drawDebugInfo();
        }

        // Continue the loop
        this.animationFrameId = requestAnimationFrame(this.loop.bind(this));
    }

    /**
     * Stop the game loop
     */
    stop() {
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    }

    /**
     * Toggle pause state
     */
    togglePause() {
        this.paused = !this.paused;
    }

    /**
     * Add a game state
     * @param {string} name - State name
     * @param {Object} state - State object
     */
    addState(name, state) {
        this.states[name] = state;
        state.init(this);
    }

    /**
     * Set the current game state
     * @param {string} name - State name
     */
    setState(name) {
        if (this.states[name]) {
            if (this.currentState && this.currentState.exit) {
                this.currentState.exit();
            }
            this.currentState = this.states[name];
            if (this.currentState.enter) {
                this.currentState.enter();
            }
        } else {
            console.error(`State "${name}" not found.`);
        }
    }

    /**
     * Load an image
     * @param {string} id - Image identifier
     * @param {string} path - Image path
     * @returns {Promise} - Promise that resolves when the image is loaded
     */
    loadImage(id, path) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => {
                this.images.set(id, img);
                resolve(img);
            };
            img.onerror = () => {
                reject(new Error(`Failed to load image: ${path}`));
            };
            img.src = path;
        });
    }

    /**
     * Get a loaded image
     * @param {string} id - Image identifier
     * @returns {HTMLImageElement} - The image
     */
    getImage(id) {
        return this.images.get(id);
    }

    /**
     * Load a sound
     * @param {string} id - Sound identifier
     * @param {string} path - Sound path
     * @returns {Promise} - Promise that resolves when the sound is loaded
     */
    loadSound(id, path) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.oncanplaythrough = () => {
                this.sounds.set(id, audio);
                resolve(audio);
            };
            audio.onerror = () => {
                reject(new Error(`Failed to load sound: ${path}`));
            };
            audio.src = path;
            audio.load();
        });
    }

    /**
     * Play a sound
     * @param {string} id - Sound identifier
     * @param {Object} options - Sound options
     * @param {number} options.volume - Volume (0-1)
     * @param {boolean} options.loop - Loop the sound
     * @returns {HTMLAudioElement} - The audio element
     */
    playSound(id, options = {}) {
        console.log(`[RabbitGame] Playing sound: ${id}`, options);
        const sound = this.sounds.get(id);
        if (sound) {
            try {
                const soundInstance = sound.cloneNode();
                soundInstance.volume = options.volume !== undefined ? options.volume : 1;
                soundInstance.loop = options.loop || false;

                // Add custom attribute to identify the sound
                soundInstance.setAttribute('data-sound-id', id);

                // Track this instance
                this.activeSoundInstances.add(soundInstance);

                // Add event listener to remove from tracking when it ends
                soundInstance.addEventListener('ended', () => {
                    this.activeSoundInstances.delete(soundInstance);
                    console.log(`[RabbitGame] Sound ended and removed from tracking: ${id}`);
                });

                // Play the sound
                const playPromise = soundInstance.play();
                if (playPromise) {
                    playPromise.catch(error => {
                        console.warn(`[RabbitGame] Audio playback prevented for ${id}:`, error);
                        this.activeSoundInstances.delete(soundInstance);
                    });
                }

                return soundInstance;
            } catch (error) {
                console.error(`[RabbitGame] Error playing sound ${id}:`, error);
                return null;
            }
        } else {
            console.warn(`[RabbitGame] Sound not found: ${id}`);
            return null;
        }
    }

    /**
     * Stop a sound
     * @param {HTMLAudioElement} soundInstance - The sound instance to stop
     */
    stopSound(soundInstance) {
        if (!soundInstance) return;

        try {
            // Get the sound ID for logging
            const soundId = soundInstance.getAttribute('data-sound-id') || 'unknown';
            console.log(`[RabbitGame] Stopping sound: ${soundId}`);

            // Stop the sound
            soundInstance.pause();
            soundInstance.currentTime = 0;

            // Remove from tracking
            this.activeSoundInstances.delete(soundInstance);

            // Remove from DOM if it has a parent
            if (soundInstance.parentNode) {
                soundInstance.parentNode.removeChild(soundInstance);
            }
        } catch (error) {
            console.error('[RabbitGame] Error stopping sound:', error);
        }
    }

    /**
     * Load a sprite atlas
     * @param {string} id - Sprite identifier
     * @param {string} imagePath - Image path
     * @param {Object} config - Sprite configuration
     * @returns {Promise} - Promise that resolves when the sprite is loaded
     */
    loadSpriteAtlas(id, imagePath, config) {
        return this.loadImage(id, imagePath).then(image => {
            this.sprites.set(id, {
                image,
                config
            });
            return this.sprites.get(id);
        });
    }

    /**
     * Create a sprite animation
     * @param {string} id - Sprite identifier
     * @param {Object} options - Animation options
     * @param {string} options.animName - Optional specific animation name within the sprite atlas
     * @returns {Object} - Sprite animation object
     */
    createSprite(id, options = {}) {
        const spriteData = this.sprites.get(id);
        if (!spriteData) {
            console.error(`Sprite data not found for id: ${id}`);
            return null;
        }

        const { image, config } = spriteData;
        console.log(`Creating sprite for id: ${id}`, config);

        // If animName is provided, use that specific animation config
        const animConfig = options.animName && config[options.animName] ? config[options.animName] : config;
        const { x, y, width, height, sliceX, anims } = animConfig;

        if (!anims) {
            console.error(`No animations found for sprite: ${id}`, animConfig);
        }

        const frameWidth = width / sliceX;

        return {
            image,
            x,
            y,
            width: frameWidth,
            height,
            frameWidth,
            frameHeight: height,
            totalFrames: sliceX,
            currentFrame: 0,
            currentAnim: null,
            anims,
            animTimer: 0,

            /**
             * Play an animation
             * @param {string} animName - Animation name
             */
            play(animName) {
                if (this.anims && this.anims[animName]) {
                    this.currentAnim = animName;
                    this.currentFrame = this.anims[animName].from;
                }
            },

            /**
             * Update the sprite animation
             * @param {number} deltaTime - Time since last update
             */
            update(deltaTime) {
                if (this.currentAnim) {
                    const anim = this.anims[this.currentAnim];
                    this.animTimer += deltaTime;

                    if (this.animTimer >= 1 / anim.speed) {
                        this.currentFrame++;
                        this.animTimer = 0;

                        if (this.currentFrame > anim.to) {
                            if (anim.loop) {
                                this.currentFrame = anim.from;
                            } else {
                                this.currentFrame = anim.to;
                            }
                        }
                    }
                }
            },

            /**
             * Draw the sprite
             * @param {CanvasRenderingContext2D} ctx - Canvas context
             * @param {number} x - X position
             * @param {number} y - Y position
             * @param {number} scale - Scale factor
             */
            draw(ctx, x, y, scale = 1) {
                try {
                    const frameX = this.x + (this.currentFrame * this.frameWidth);

                    // Draw debug outline
                    if (window.DEBUG_SPRITES) {
                        ctx.strokeStyle = 'red';
                        ctx.lineWidth = 2;
                        ctx.strokeRect(
                            x - (this.frameWidth * scale / 2),
                            y - (this.frameHeight * scale / 2),
                            this.frameWidth * scale,
                            this.frameHeight * scale
                        );
                    }

                    ctx.drawImage(
                        this.image,
                        frameX, this.y,
                        this.frameWidth, this.frameHeight,
                        x - (this.frameWidth * scale / 2), y - (this.frameHeight * scale / 2),
                        this.frameWidth * scale, this.frameHeight * scale
                    );
                } catch (error) {
                    console.error('Error drawing sprite:', error);
                    // Draw fallback rectangle
                    ctx.fillStyle = 'purple';
                    ctx.fillRect(
                        x - (this.frameWidth * scale / 2),
                        y - (this.frameHeight * scale / 2),
                        this.frameWidth * scale,
                        this.frameHeight * scale
                    );
                }
            }
        };
    }

    /**
     * Set a game variable
     * @param {string} name - Variable name
     * @param {*} value - Variable value
     */
    setVariable(name, value) {
        this.variables.set(name, value);
    }

    /**
     * Get a game variable
     * @param {string} name - Variable name
     * @param {*} defaultValue - Default value if variable doesn't exist
     * @returns {*} - Variable value
     */
    getVariable(name, defaultValue = null) {
        return this.variables.has(name) ? this.variables.get(name) : defaultValue;
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Keyboard events
        this.keys = new Set();

        // Store handler references for later removal
        this.keyDownHandler = (e) => {
            this.keys.add(e.key.toLowerCase());
            if (this.currentState && this.currentState.onKeyDown) {
                this.currentState.onKeyDown(e.key.toLowerCase());
            }
        };

        this.keyUpHandler = (e) => {
            this.keys.delete(e.key.toLowerCase());
            if (this.currentState && this.currentState.onKeyUp) {
                this.currentState.onKeyUp(e.key.toLowerCase());
            }
        };

        // Add keyboard event listeners
        window.addEventListener('keydown', this.keyDownHandler);
        window.addEventListener('keyup', this.keyUpHandler);

        // Touch events
        this.touchActive = false;

        this.touchStartHandler = (e) => {
            e.preventDefault();
            this.touchActive = true;
            if (this.currentState && this.currentState.onTouchStart) {
                this.currentState.onTouchStart(e.touches[0].clientX, e.touches[0].clientY);
            }
        };

        this.touchEndHandler = (e) => {
            e.preventDefault();
            this.touchActive = false;
            if (this.currentState && this.currentState.onTouchEnd) {
                this.currentState.onTouchEnd();
            }
        };

        // Add touch event listeners
        if (this.canvas) {
            this.canvas.addEventListener('touchstart', this.touchStartHandler);
            this.canvas.addEventListener('touchend', this.touchEndHandler);
        }

        // Mouse events
        this.mouseDown = false;

        this.mouseDownHandler = (e) => {
            this.mouseDown = true;
            if (this.currentState && this.currentState.onMouseDown) {
                this.currentState.onMouseDown(e.offsetX, e.offsetY);
            }
        };

        this.mouseUpHandler = (e) => {
            this.mouseDown = false;
            if (this.currentState && this.currentState.onMouseUp) {
                this.currentState.onMouseUp(e.offsetX, e.offsetY);
            }
        };

        // Add mouse event listeners
        if (this.canvas) {
            this.canvas.addEventListener('mousedown', this.mouseDownHandler);
            this.canvas.addEventListener('mouseup', this.mouseUpHandler);
        }
    }

    /**
     * Check if a key is pressed
     * @param {string} key - Key to check
     * @returns {boolean} - True if key is pressed
     */
    isKeyPressed(key) {
        return this.keys.has(key.toLowerCase());
    }

    /**
     * Check if any key in a list is pressed
     * @param {Array<string>} keys - Keys to check
     * @returns {boolean} - True if any key is pressed
     */
    isAnyKeyPressed(keys) {
        return keys.some(key => this.isKeyPressed(key));
    }

    /**
     * Draw debug information
     */
    drawDebugInfo() {
        this.ctx.fillStyle = 'white';
        this.ctx.font = '14px Arial';
        this.ctx.fillText(`FPS: ${Math.round(1 / this.deltaTime)}`, 10, 20);
    }

    /**
     * Clean up resources
     */
    destroy() {
        try {
            // Stop the game loop
            this.stop();

            // Remove event listeners
            this.removeEventListeners();

            // Stop all active sounds
            this.stopAllSounds();

            // Clear assets
            this.images.clear();
            this.sounds.clear();
            this.sprites.clear();

            // Clear variables
            this.variables.clear();

            // Clear canvas
            if (this.canvas && this.ctx) {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
            }
        } catch (error) {
            console.error('Error during engine cleanup:', error);
        }
    }

    /**
     * Remove all event listeners
     */
    removeEventListeners() {
        try {
            // Remove window event listeners
            window.removeEventListener('keydown', this.keyDownHandler);
            window.removeEventListener('keyup', this.keyUpHandler);

            // Remove canvas event listeners if canvas exists
            if (this.canvas) {
                this.canvas.removeEventListener('touchstart', this.touchStartHandler);
                this.canvas.removeEventListener('touchend', this.touchEndHandler);
                this.canvas.removeEventListener('mousedown', this.mouseDownHandler);
                this.canvas.removeEventListener('mouseup', this.mouseUpHandler);
            }

            // Reset event handler references
            this.keyDownHandler = null;
            this.keyUpHandler = null;
            this.touchStartHandler = null;
            this.touchEndHandler = null;
            this.mouseDownHandler = null;
            this.mouseUpHandler = null;
        } catch (error) {
            console.error('Error removing event listeners:', error);
        }
    }

    /**
     * Stop all active sounds
     */
    stopAllSounds() {
        console.log('[RabbitGame] Stopping all sounds, active instances:', this.activeSoundInstances.size);
        try {
            // First, stop all tracked active instances
            if (this.activeSoundInstances && this.activeSoundInstances.size > 0) {
                console.log(`[RabbitGame] Stopping ${this.activeSoundInstances.size} tracked sound instances`);

                // Create a copy of the set to avoid modification during iteration
                const instances = Array.from(this.activeSoundInstances);
                instances.forEach(audio => {
                    try {
                        const soundId = audio.getAttribute('data-sound-id') || 'unknown';
                        console.log(`[RabbitGame] Stopping tracked sound: ${soundId}`);
                        audio.pause();
                        audio.currentTime = 0;

                        // Remove from DOM if it has a parent
                        if (audio.parentNode) {
                            audio.parentNode.removeChild(audio);
                        }
                    } catch (audioError) {
                        console.warn('[RabbitGame] Error stopping tracked audio:', audioError);
                    }
                });

                // Clear the tracking set
                this.activeSoundInstances.clear();
            }

            // As a backup, find all audio elements related to this game in the DOM
            const rabbitAudioElements = document.querySelectorAll('audio[src*="/game/rabbit/"]');
            if (rabbitAudioElements.length > 0) {
                console.log(`[RabbitGame] Found ${rabbitAudioElements.length} rabbit game audio elements in DOM`);
                rabbitAudioElements.forEach(audio => {
                    try {
                        console.log(`[RabbitGame] Stopping DOM audio: ${audio.src}`);
                        audio.pause();
                        audio.currentTime = 0;

                        // Remove from DOM
                        if (audio.parentNode) {
                            audio.parentNode.removeChild(audio);
                        }
                    } catch (audioError) {
                        console.warn('[RabbitGame] Error stopping DOM audio:', audioError);
                    }
                });
            }

            // As a last resort, stop all audio elements
            const allAudioElements = document.querySelectorAll('audio');
            if (allAudioElements.length > 0) {
                console.log(`[RabbitGame] Found ${allAudioElements.length} total audio elements in DOM`);
                allAudioElements.forEach(audio => {
                    // Only stop if it's not already handled and likely belongs to this game
                    if (audio.src && (audio.src.includes('/game/rabbit/') || !audio.src.includes('/game/'))) {
                        try {
                            console.log(`[RabbitGame] Stopping general audio: ${audio.src}`);
                            audio.pause();
                            audio.currentTime = 0;
                        } catch (audioError) {
                            console.warn('[RabbitGame] Error stopping general audio:', audioError);
                        }
                    }
                });
            }

            // Clear the audio container if it exists
            const audioContainer = document.querySelector('#game-audio-container');
            if (audioContainer) {
                console.log('[RabbitGame] Clearing audio container');
                audioContainer.innerHTML = '';
            }

            // Don't clear the sounds map as it contains the original audio templates
            // that might be needed if the game continues
        } catch (error) {
            console.error('[RabbitGame] Error in stopAllSounds:', error);
        }
    }
}

// Explicitly add Engine to the window object
window.Engine = Engine;
