<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('telegram_users', function (Blueprint $table) {
            // Add columns for play tracking with default values
            $table->integer('tower_game_plays')->default(15)->after('tower_game_unlocked');
            $table->timestamp('tower_game_plays_reset_at')
                ->nullable()
                ->default(DB::raw("CURRENT_TIMESTAMP + INTERVAL '1 day'"))
                ->after('tower_game_plays');
            
            // Add index for efficient reset checks
            $table->index('tower_game_plays_reset_at', 'idx_tower_reset');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('telegram_users', function (Blueprint $table) {
            // Remove index first
            $table->dropIndex('idx_tower_reset');
            
            // Remove columns
            $table->dropColumn(['tower_game_plays', 'tower_game_plays_reset_at'
            ]);
        });
    }
};
