import { createBrowserRouter } from "react-router-dom";
import Layout from "./components/partials/Layout";
import Home from "./pages/Home";
import Boost from "./pages/Boost";
import Leaderboard from "./pages/Leaderboard";
import Earn from "./pages/Earn";
import Friends from "./pages/Friends";
import Missions from "./pages/Missions";
import Wallet from "./pages/Wallet";
import Game from "./pages/Game";
import { GameHub } from "./pages/GameHub";
import { GamesList } from "./pages/GamesList";
import PrizeTree from "./pages/PrizeTree";
import PrizeGallery from "./pages/PrizeGallery";

const router = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,

    children: [
      {
        path: "",
        element: <Home />,
      },
      {
        path: "boost",
        element: <Boost />,
      },
      {
        path: "leaderboard",
        element: <Leaderboard />,
      },
      {
        path: "friends",
        element: <Friends />,
      },
      {
        path: "earn",
        element: <Earn />,
      },
      {
        path: "missions",
        element: <Missions />,
      },
      {
        path: "wallet",
        element: <Wallet />,
      },
      {
        path: "game",
        element: <Game />, // Keep for backward compatibility
      },
      {
        path: "games",
        element: <GamesList />, // List of all available games
      },
      {
        path: "games/:gameId",
        element: <GameHub />, // GameHub with specific game
      },
      {
        path: "prizes",
        element: <PrizeTree />, // Prize Tree page
      },
      {
        path: "prizes/gallery",
        element: <PrizeGallery />, // User's prize gallery
      },
    ],
  },
]);

export default router;
