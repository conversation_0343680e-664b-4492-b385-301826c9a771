<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Collectible extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id', 'collectible_id', 'unlock_source',
        'source_reference', 'obtained_at'
    ];

    protected $casts = [
        'obtained_at' => 'datetime',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(CollectibleTemplate::class, 'collectible_id', 'collectible_id');
    }

    // Scopes
    public function scopeBySource($query, $source)
    {
        return $query->where('unlock_source', $source);
    }

    public function scopeRecentlyObtained($query, $days = 7)
    {
        return $query->where('obtained_at', '>=', now()->subDays($days));
    }

    public function scopeByCategory($query, $category)
    {
        return $query->whereHas('template', function($q) use ($category) {
            $q->where('category', $category);
        });
    }

    public function scopeByRarity($query, $rarity)
    {
        return $query->whereHas('template', function($q) use ($rarity) {
            $q->where('rarity', $rarity);
        });
    }

    // Accessors
    public function getSourceDisplayAttribute(): string
    {
        return match($this->unlock_source) {
            'pet_purchase' => 'Pet Purchase',
            'mystery_box' => 'Mystery Box',
            'prize_tree' => 'Prize Tree',
            'collection_bonus' => 'Collection Bonus',
            default => ucfirst(str_replace('_', ' ', $this->unlock_source))
        };
    }

    public function getDaysOwnedAttribute(): int
    {
        return $this->obtained_at->diffInDays(now());
    }

    // Business Logic Methods
    public function getCollectionProgress(): array
    {
        $collectionSet = $this->template->collectionSet;
        if (!$collectionSet) {
            return [];
        }

        $totalInSet = $collectionSet->collectibleTemplates()->count();
        $ownedInSet = $this->user->collectibles()
            ->whereHas('template', function($q) use ($collectionSet) {
                $q->where('collection_set_id', $collectionSet->set_id);
            })
            ->count();

        return [
            'set_name' => $collectionSet->name,
            'owned' => $ownedInSet,
            'total' => $totalInSet,
            'percentage' => $totalInSet > 0 ? round(($ownedInSet / $totalInSet) * 100, 1) : 0
        ];
    }
}
