
import { motion } from 'framer-motion';
import { BattlxIcon } from '@/components/icons/BattlxIcon';
import { PrizeTree } from '@/types/PrizeTypes';

interface PrizeTreeSelectorProps {
  trees: PrizeTree[];
  selectedTree: PrizeTree | null;
  onSelectTree: (tree: PrizeTree) => void;
}

export default function PrizeTreeSelector({ trees, selectedTree, onSelectTree }: PrizeTreeSelectorProps) {
  return (
    <div className="flex items-center justify-center mt-4 space-x-2 overflow-x-auto pb-2">
      {trees.map(tree => (
        <motion.button
          key={tree.id}
          className={`flex items-center px-4 py-2 text-sm font-medium rounded-lg ${
            selectedTree?.id === tree.id
              ? 'bg-[#9B8B6C] text-[#120D0E]'
              : 'bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40'
          }`}
          onClick={() => onSelectTree(tree)}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <BattlxIcon icon={tree.icon as any || 'skill'} className="w-4 h-4 mr-2" />
          {tree.name}
        </motion.button>
      ))}
    </div>
  );
}
