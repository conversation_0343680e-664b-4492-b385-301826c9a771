{"main": "index.ts", "scripts": {"start": "node dist/index.js", "dev": "nodemon index.ts", "build": "rimraf ./dist && tsc"}, "dependencies": {"dotenv": "^16.4.5", "express": "^4.19.2", "input": "^1.0.1", "telegraf": "^4.16.3", "telegram": "^2.22.2"}, "devDependencies": {"@types/express": "^4.17.21", "nodemon": "^3.1.3", "rimraf": "^3.0.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.0.0", "typescript": "^5.5.4"}}