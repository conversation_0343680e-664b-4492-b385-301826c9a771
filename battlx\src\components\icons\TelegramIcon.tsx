import React from "react";

export default function TelegramIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="18"
      height="16"
      viewBox="0 0 18 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.1355 0.416692C16.3568 0.323544 16.5991 0.291418 16.837 0.323657C17.0749 0.355896 17.2999 0.451322 17.4884 0.600003C17.677 0.748684 17.8222 0.945184 17.9091 1.16905C17.9959 1.39292 18.0212 1.63597 17.9822 1.8729L15.951 14.1933C15.754 15.3818 14.45 16.0633 13.3601 15.4713C12.4484 14.9761 11.0943 14.2131 9.87632 13.4169C9.26733 13.0184 7.40184 11.7422 7.63111 10.834C7.82814 10.0576 10.9627 7.13979 12.7538 5.40505C13.4568 4.72352 13.1362 4.33036 12.306 4.95727C10.2444 6.51378 6.93435 8.88079 5.83996 9.5471C4.87452 10.1346 4.37121 10.2349 3.76938 10.1346C2.6714 9.9519 1.65313 9.6689 0.822039 9.3241C-0.301014 8.8584 -0.246384 7.31442 0.821144 6.86484L16.1355 0.416692Z"
        fill="currentColor"
      />
    </svg>
  );
}
