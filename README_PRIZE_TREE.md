# Prize Tree System Implementation

This document provides instructions for implementing the Prize Tree system in the Battlx application.

## Overview

The Prize Tree system allows users to earn achievement points through various activities and spend them to unlock prizes organized in themed trees. The system includes:

- Achievement points earned through gameplay, missions, tasks, etc.
- Prize trees with hierarchical prize structures
- Prize unlocking with achievement points
- Prize gallery for viewing and equipping unlocked prizes

## Implementation Steps

### 1. Database Setup

Run the migration to create the necessary tables:

```bash
php artisan migrate
```

This will create the following tables:
- `prize_trees`: Themed collections of prizes
- `prizes`: Individual prizes within trees
- `prize_prerequisites`: Prerequisites for unlocking prizes
- `user_prizes`: Tracks which prizes users have unlocked
- `user_achievement_points`: Tracks users' achievement point balances
- `achievement_point_transactions`: Records all achievement point transactions
- `tap_stats`: Tracks users' tapping statistics
- `game_stats`: Tracks users' game statistics

### 2. Seed Initial Data

Run the seeder to populate the database with initial prize trees and prizes:

```bash
php artisan db:seed --class=PrizeTreeSeeder
```

### 3. Backend Components

The following backend components have been implemented:

- **Models**:
  - `PrizeTree`: Represents a themed collection of prizes
  - `Prize`: Represents a prize in the prize tree
  - `UserPrize`: Represents a prize unlocked by a user
  - `UserAchievementPoint`: Tracks a user's achievement point balance
  - `AchievementPointTransaction`: Records achievement point transactions
  - `TapStat`: Tracks a user's tapping statistics
  - `GameStat`: Tracks a user's game statistics

- **Controllers**:
  - `PrizeTreeController`: Handles prize tree-related requests
  - `UserPrizeController`: Handles user prize-related requests
  - `AchievementPointController`: Handles achievement point-related requests

- **Services**:
  - `AchievementPointService`: Handles achievement point transactions
  - `PrizeService`: Handles prize-related operations

- **Middleware**:
  - `AdminMiddleware`: Protects admin-only endpoints

### 4. Frontend Components

The following frontend components have been implemented:

- **Pages**:
  - `PrizeTree.tsx`: Main page for viewing and interacting with prize trees
  - `PrizeGallery.tsx`: Page for viewing and equipping unlocked prizes

- **Components**:
  - `PrizeTreeCanvas.tsx`: Renders the prize tree visualization
  - `PrizeNodeDetails.tsx`: Displays details about a selected prize
  - `PrizeTreeSelector.tsx`: Allows switching between different prize trees
  - `UserPrizeGallery.tsx`: Displays a user's unlocked prizes

- **Types**:
  - `PrizeTypes.ts`: TypeScript interfaces for the Prize Tree system

### 5. API Endpoints

The following API endpoints have been implemented:

- `GET /prizes/trees`: Get all prize trees
- `GET /prizes/trees/{id}`: Get a specific prize tree with its prizes
- `GET /prizes/user`: Get user's prizes and achievement points
- `POST /prizes/unlock`: Unlock a prize
- `POST /prizes/equip`: Equip a prize
- `POST /prizes/unequip`: Unequip a prize
- `GET /prizes/transactions`: Get achievement point transactions
- `POST /achievement-points/award`: Award achievement points (admin only)

### 6. Achievement Point Sources

Achievement points are awarded for the following activities:

- **Tapping Milestones**: Points for reaching certain numbers of total taps
- **Daily Tasks**: Points for completing daily login tasks
- **Regular Tasks**: Points for completing bounty tasks
- **Referral Tasks**: Points for completing referral-based tasks
- **Mission Completion**: Points for completing missions
- **Game Milestones**: Points for unlocking games and reaching score milestones

## Testing

To test the Prize Tree system:

1. Run the migrations and seeders
2. Navigate to the "Prizes" tab in the application
3. Verify that prize trees are displayed correctly
4. Verify that achievement points are displayed correctly
5. Try unlocking a prize and verify that achievement points are deducted
6. Navigate to the Prize Gallery and verify that unlocked prizes are displayed
7. Try equipping a prize and verify that it works correctly

## Troubleshooting

If you encounter issues:

1. Check the Laravel logs for backend errors
2. Check the browser console for frontend errors
3. Verify that the database tables were created correctly
4. Verify that the seeders ran correctly
5. Verify that the API endpoints are working correctly using a tool like Postman

## Future Enhancements

Potential future enhancements include:

1. Seasonal prize trees with exclusive prizes
2. Achievement badges for milestone achievements
3. Prize sharing functionality
4. Prize crafting system
5. More interactive prize effects
