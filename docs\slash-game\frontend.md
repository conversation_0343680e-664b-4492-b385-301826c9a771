# Skill Trees System - Frontend Implementation

## Overview

The frontend implementation of the Skill Trees system will focus on creating an intuitive, visually appealing interface that aligns with BattlX's gothic theme while providing clear progression paths for players.

## UI Components

### 1. Skill Tree Main View

#### Component Structure
```jsx
// src/pages/SkillTree.tsx
import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { $http } from '@/lib/http';
import { useUserStore } from '@/store/user-store';
import SkillTreeCanvas from '@/components/skilltree/SkillTreeCanvas';
import SkillNodeDetails from '@/components/skilltree/SkillNodeDetails';
import SkillTreeSelector from '@/components/skilltree/SkillTreeSelector';
import UserGameDetails from '@/components/UserGameDetails';
import { BattlxIcon } from '@/components/icons/BattlxIcon';

export default function SkillTree() {
  const user = useUserStore();
  const [selectedTree, setSelectedTree] = useState(null);
  const [selectedNode, setSelectedNode] = useState(null);
  
  // Fetch available skill trees
  const skillTrees = useQuery({
    queryKey: ['/skill-trees'],
    queryFn: () => $http.$get('/skill-trees'),
  });
  
  // Fetch user's skill points and unlocked skills
  const userSkills = useQuery({
    queryKey: ['/user-skills'],
    queryFn: () => $http.$get('/user-skills'),
  });
  
  // Mutation for unlocking skills
  const unlockSkill = useMutation({
    mutationFn: (skillId) => $http.post('/unlock-skill', { skill_id: skillId }),
    onSuccess: () => {
      // Refetch user skills
      userSkills.refetch();
      // Update user store if needed
      // useUserStore.setState(...);
    },
  });
  
  return (
    <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1">
      <div className="flex flex-col flex-1 w-full h-full px-6 pb-20 mt-8 modal-body">
        <UserGameDetails className="mt-4" />
        
        {/* Skill Points Display */}
        <div className="flex items-center justify-center mt-6 space-x-2">
          <BattlxIcon icon="skill" className="w-6 h-6 text-[#9B8B6C]" />
          <span className="text-xl font-bold text-[#9B8B6C]">
            {userSkills.data?.skill_points || 0} Skill Points
          </span>
        </div>
        
        {/* Skill Tree Selector */}
        <SkillTreeSelector 
          trees={skillTrees.data || []} 
          selectedTree={selectedTree}
          onSelectTree={setSelectedTree}
        />
        
        {/* Main Skill Tree Canvas */}
        {selectedTree && (
          <SkillTreeCanvas
            tree={selectedTree}
            userSkills={userSkills.data?.unlocked_skills || []}
            onNodeSelect={setSelectedNode}
          />
        )}
        
        {/* Skill Node Details Panel */}
        {selectedNode && (
          <SkillNodeDetails
            node={selectedNode}
            isUnlocked={userSkills.data?.unlocked_skills?.includes(selectedNode.id)}
            canUnlock={userSkills.data?.skill_points >= selectedNode.cost}
            onUnlock={() => unlockSkill.mutate(selectedNode.id)}
          />
        )}
      </div>
    </div>
  );
}
```

### 2. Skill Tree Canvas Component

This component will render the actual skill tree visualization using SVG or Canvas.

```jsx
// src/components/skilltree/SkillTreeCanvas.tsx
import React, { useRef, useEffect } from 'react';
import { SkillNode, SkillTree, UserSkill } from '@/types/SkillTreeTypes';

interface SkillTreeCanvasProps {
  tree: SkillTree;
  userSkills: number[]; // IDs of unlocked skills
  onNodeSelect: (node: SkillNode) => void;
}

const SkillTreeCanvas: React.FC<SkillTreeCanvasProps> = ({ 
  tree, 
  userSkills, 
  onNodeSelect 
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  
  // Calculate positions for each node based on its tier and position
  const calculateNodePositions = () => {
    // Logic to position nodes in a tree-like structure
    // This will depend on the specific visual design of your skill trees
  };
  
  // Draw connections between nodes
  const drawConnections = () => {
    // Logic to draw SVG paths between connected nodes
  };
  
  useEffect(() => {
    if (canvasRef.current && tree) {
      calculateNodePositions();
      drawConnections();
    }
  }, [tree, userSkills]);
  
  return (
    <div className="relative w-full h-[60vh] mt-4 bg-black/30 rounded-xl overflow-hidden border border-[#9B8B6C]/20" ref={canvasRef}>
      <svg className="absolute inset-0 w-full h-full pointer-events-none">
        {/* Connection lines between nodes */}
        {tree.connections.map((connection, index) => (
          <path
            key={`connection-${index}`}
            d={`M ${connection.startX} ${connection.startY} L ${connection.endX} ${connection.endY}`}
            stroke={userSkills.includes(connection.startNodeId) && userSkills.includes(connection.endNodeId) 
              ? "#9B8B6C" 
              : "#9B8B6C40"}
            strokeWidth="2"
            strokeDasharray={userSkills.includes(connection.startNodeId) ? "none" : "5,5"}
          />
        ))}
      </svg>
      
      <div className="relative z-10">
        {tree.nodes.map((node) => (
          <div
            key={`node-${node.id}`}
            className={`absolute cursor-pointer transition-all duration-300 ${
              userSkills.includes(node.id) 
                ? "bg-[#9B8B6C] text-black" 
                : "bg-[#1A1617] text-[#9B8B6C] border border-[#9B8B6C]/40"
            } ${
              node.isRoot ? "w-16 h-16" : "w-12 h-12"
            } rounded-full flex items-center justify-center transform hover:scale-110`}
            style={{
              left: `${node.x}px`,
              top: `${node.y}px`,
              boxShadow: userSkills.includes(node.id) 
                ? "0 0 15px rgba(155, 139, 108, 0.6)" 
                : "none"
            }}
            onClick={() => onNodeSelect(node)}
          >
            <BattlxIcon icon={node.icon} className="w-6 h-6" />
          </div>
        ))}
      </div>
    </div>
  );
};

export default SkillTreeCanvas;
```

### 3. Skill Node Details Component

This component will display details about a selected skill node and allow the player to unlock it.

```jsx
// src/components/skilltree/SkillNodeDetails.tsx
import React from 'react';
import { SkillNode } from '@/types/SkillTreeTypes';
import { Button } from '@/components/ui/button';
import { BattlxIcon } from '@/components/icons/BattlxIcon';

interface SkillNodeDetailsProps {
  node: SkillNode;
  isUnlocked: boolean;
  canUnlock: boolean;
  onUnlock: () => void;
}

const SkillNodeDetails: React.FC<SkillNodeDetailsProps> = ({
  node,
  isUnlocked,
  canUnlock,
  onUnlock
}) => {
  return (
    <div className="mt-4 p-4 bg-[#120D0E] rounded-xl border border-[#B3B3B3]/20 relative overflow-hidden before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.07)_0px,rgba(155,139,108,0.07)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_20px_rgba(74,14,14,0.4)]">
      <h3 className="text-xl font-bold text-[#9B8B6C] mb-2">{node.name}</h3>
      
      <div className="flex items-center gap-2 mb-2">
        <span className="text-sm text-[#B3B3B3]/70">Tier {node.tier}</span>
        <span className="text-sm text-[#B3B3B3]/70">•</span>
        <span className="text-sm text-[#B3B3B3]/70">{node.category}</span>
      </div>
      
      <p className="text-[#B3B3B3] mb-4">{node.description}</p>
      
      {node.effects.map((effect, index) => (
        <div key={`effect-${index}`} className="flex items-center gap-2 mb-2">
          <BattlxIcon icon={effect.icon} className="w-5 h-5 text-[#9B8B6C]" />
          <span className="text-[#B3B3B3]">{effect.description}</span>
        </div>
      ))}
      
      {!isUnlocked && (
        <div className="mt-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-[#B3B3B3]">Cost:</span>
            <span className="text-[#9B8B6C] font-bold">{node.cost} Skill Points</span>
          </div>
          
          <Button
            className="w-full bg-[#120D0E] text-[#9B8B6C] border border-[#B3B3B3]/20 hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_20px_rgba(74,14,14,0.4)] px-5 py-3 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={onUnlock}
            disabled={!canUnlock}
          >
            {canUnlock ? "Unlock Skill" : "Not Enough Skill Points"}
          </Button>
        </div>
      )}
      
      {isUnlocked && (
        <div className="mt-4 p-2 bg-[#9B8B6C]/20 rounded-lg text-center">
          <span className="text-[#9B8B6C] font-bold">Skill Unlocked</span>
        </div>
      )}
    </div>
  );
};

export default SkillNodeDetails;
```

### 4. Skill Tree Selector Component

This component will allow players to switch between different skill trees.

```jsx
// src/components/skilltree/SkillTreeSelector.tsx
import React from 'react';
import { SkillTree } from '@/types/SkillTreeTypes';

interface SkillTreeSelectorProps {
  trees: SkillTree[];
  selectedTree: SkillTree | null;
  onSelectTree: (tree: SkillTree) => void;
}

const SkillTreeSelector: React.FC<SkillTreeSelectorProps> = ({
  trees,
  selectedTree,
  onSelectTree
}) => {
  return (
    <div className="flex justify-center gap-4 mt-6">
      {trees.map((tree) => (
        <button
          key={tree.id}
          className={`px-4 py-2 rounded-lg transition-all duration-300 ${
            selectedTree?.id === tree.id
              ? "bg-[#9B8B6C] text-black"
              : "bg-[#120D0E] text-[#9B8B6C] border border-[#B3B3B3]/20"
          }`}
          onClick={() => onSelectTree(tree)}
        >
          <div className="flex items-center gap-2">
            <BattlxIcon icon={tree.icon} className="w-5 h-5" />
            <span>{tree.name}</span>
          </div>
        </button>
      ))}
    </div>
  );
};

export default SkillTreeSelector;
```

## TypeScript Types

```typescript
// src/types/SkillTreeTypes.ts
export interface SkillNode {
  id: number;
  name: string;
  description: string;
  icon: string;
  tier: number;
  position: number;
  category: string;
  cost: number;
  prerequisites: number[]; // IDs of prerequisite skills
  effects: SkillEffect[];
  isRoot: boolean;
  x?: number; // For positioning in the UI
  y?: number; // For positioning in the UI
}

export interface SkillEffect {
  icon: string;
  description: string;
  type: 'passive' | 'active' | 'stat';
  value: number;
  target: string; // What this effect modifies (e.g., 'combo_multiplier', 'slash_damage')
}

export interface SkillConnection {
  startNodeId: number;
  endNodeId: number;
  startX?: number;
  startY?: number;
  endX?: number;
  endY?: number;
}

export interface SkillTree {
  id: number;
  name: string;
  description: string;
  icon: string;
  nodes: SkillNode[];
  connections: SkillConnection[];
}

export interface UserSkill {
  skill_id: number;
  unlocked_at: string;
}

export interface UserSkillsData {
  skill_points: number;
  unlocked_skills: number[]; // IDs of unlocked skills
  user_skills: UserSkill[];
}
```

## Navigation Integration

Add the Skill Tree to the main navigation in the app:

```jsx
// src/components/partials/AppBar.tsx
// Add to the existing navigation items

const navItems = [
  // ... existing items
  {
    icon: "skill",
    label: "Skills",
    href: "/skills",
    active: pathname === "/skills",
  },
];
```

## Router Integration

Add the Skill Tree page to the router:

```jsx
// src/router.tsx
import SkillTree from "./pages/SkillTree";

// Add to the existing routes
const router = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,
    children: [
      // ... existing routes
      {
        path: "/skills",
        element: <SkillTree />,
      },
    ],
  },
]);
```

## Animation and Visual Effects

To enhance the visual appeal of the skill tree, implement the following effects:

1. **Node Unlock Animation**:
   - Create a pulsing effect when a node is unlocked
   - Add particle effects emanating from the node
   - Animate the connection lines filling in when a new connection is established

2. **Tree Navigation**:
   - Implement smooth panning and zooming for larger skill trees
   - Add transition animations when switching between different trees

3. **Skill Point Acquisition Animation**:
   - Create a floating animation when skill points are earned
   - Add a notification effect when new skill points are available

## Mobile Responsiveness

Ensure the skill tree is fully responsive on mobile devices:

1. **Touch Controls**:
   - Implement pinch-to-zoom for the skill tree canvas
   - Add touch-friendly node selection
   - Create swipe gestures for navigating between trees

2. **Layout Adjustments**:
   - Adjust the layout for smaller screens
   - Create a collapsible details panel for mobile view
   - Optimize the node size and spacing for touch interaction

## Performance Considerations

1. **Rendering Optimization**:
   - Use virtualization for large skill trees to only render visible nodes
   - Implement canvas-based rendering for complex trees with many connections
   - Optimize SVG rendering by using groups and clipping

2. **State Management**:
   - Cache skill tree data to reduce API calls
   - Implement efficient state updates when unlocking skills
   - Use memoization for expensive calculations

## User Experience Enhancements

1. **Tooltips and Guides**:
   - Add tooltips for skill nodes showing basic information on hover
   - Implement a guided tour for first-time users
   - Show recommended skills based on the player's playstyle

2. **Visual Indicators**:
   - Highlight available paths based on the player's current skill points
   - Show skill prerequisites clearly
   - Indicate which skills are most popular among other players

3. **Skill Search and Filtering**:
   - Add a search function to find specific skills
   - Implement filters for different skill categories
   - Allow sorting by cost, tier, or effect type

## Integration with Existing UI

Ensure the skill tree UI follows the existing gothic theme:

1. **Consistent Styling**:
   - Use the same color palette as other pages
   - Maintain the diagonal pattern backgrounds
   - Apply consistent shadow effects

2. **Transition Effects**:
   - Create smooth transitions between the skill tree and other pages
   - Maintain state when navigating away and back to the skill tree

3. **Shared Components**:
   - Reuse existing UI components like buttons and icons
   - Maintain consistent header and footer elements
