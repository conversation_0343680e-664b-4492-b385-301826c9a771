import { useEffect, useState } from "react";
import TelegramIcon from "../icons/TelegramIcon";
import TwitterIcon from "../icons/TwitterIcon";
import YoutubeIcon from "../icons/YoutubeIcon";
import { useAssetPreloader } from "../../hooks/useAssetPreloader";

interface SplashScreenProps {
  onLoadComplete?: () => void;
  authLoaded: boolean;
}

const socialLinks = [
  {
    title: "Telegram",
    url: "#",
    icon: TelegramIcon,
  },
  {
    title: "Youtube",
    url: "#",
    icon: YoutubeIcon,
  },
  {
    title: "Twitter",
    url: "#",
    icon: TwitterIcon,
  },
];

const splashScreenImages = [
  "/images/splash-screen/bg.png",
];

export default function SplashScreen({ onLoadComplete, authLoaded }: SplashScreenProps) {
  const { assetsLoaded, progress } = useAssetPreloader();
  const [loadingText, setLoadingText] = useState("Loading assets...");
  const progressPercentage = Math.floor(progress);
  const randomImage = splashScreenImages[Math.floor(Math.random() * splashScreenImages.length)];

  useEffect(() => {
    const updateLoadingText = () => {
      if (!authLoaded) {
        setLoadingText("Authenticating...");
      } else if (!assetsLoaded) {
        setLoadingText("Loading game assets");
      } else {
        setLoadingText("Starting game...");
        setTimeout(() => onLoadComplete?.(), 500); // Small delay for smooth transition
      }
    };

    updateLoadingText();
  }, [authLoaded, assetsLoaded, onLoadComplete]);

  return (
    <div
      className="flex flex-col items-center justify-between pt-16 bg-cover bg-center w-full max-w-lg h-[--tg-viewport-height] mx-auto"
      style={{ backgroundImage: `url('${randomImage}')` }}
    >
      <img src="/images/logo.png" alt="logo" className="h-48 max-w-full" />
      <div className="flex flex-col items-center w-full">
        <div className="flex flex-col items-center w-full pb-6 bg-[url('/images/blur.png')] bg-cover bg-center">
          <h1 className="text-6xl font-medium text-center uppercase text-shadow">
            BATTLX
          </h1>
          <img
            src="/images/loader.png"
            alt="loader"
            className="mt-3 animate-spin"
          />
          <p className="mt-3 text-sm font-bold uppercase text-primary">
            Stay tuned
          </p>
          <p className="mt-1 font-medium">
            {loadingText} ({progressPercentage}%)
          </p>
          {/* Progress bar */}
          <div className="w-64 h-2 mt-4 bg-white/10 rounded-full overflow-hidden">
            <div
              className="h-full bg-primary transition-all duration-300 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
          <div className="flex items-center gap-4 mt-4">
            {socialLinks.map((link) => (
              <a
                key={link.title}
                href={link.url}
                target="_blank"
                rel="noreferrer"
                className="flex items-center justify-center w-12 h-12 border-2 rounded-full text-primary border-primary/10 bg-white/5"
              >
                <link.icon className="w-6 h-6" />
              </a>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
