<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('pets', function (Blueprint $table) {
            $table->index(['happiness', 'last_fed']); // For finding pets needing attention
            $table->index(['level', 'experience']); // For evolution calculations
        });
        
        Schema::table('pet_interactions', function (Blueprint $table) {
            $table->index(['telegram_user_id', 'interaction_time', 'interaction_type']);
        });
        
        Schema::table('collectibles', function (Blueprint $table) {
            $table->index(['telegram_user_id', 'unlock_source']);
        });
        
        Schema::table('mystery_box_openings', function (Blueprint $table) {
            $table->index(['telegram_user_id', 'purchase_method', 'opened_at']);
        });
    }

    public function down(): void
    {
        Schema::table('pets', function (Blueprint $table) {
            $table->dropIndex(['happiness', 'last_fed']);
            $table->dropIndex(['level', 'experience']);
        });
        
        Schema::table('pet_interactions', function (Blueprint $table) {
            $table->dropIndex(['telegram_user_id', 'interaction_time', 'interaction_type']);
        });
        
        Schema::table('collectibles', function (Blueprint $table) {
            $table->dropIndex(['telegram_user_id', 'unlock_source']);
        });
        
        Schema::table('mystery_box_openings', function (Blueprint $table) {
            $table->dropIndex(['telegram_user_id', 'purchase_method', 'opened_at']);
        });
    }
};
