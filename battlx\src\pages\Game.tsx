import { useEffect, useRef, useState } from 'react';
import { $http } from '../lib/http';
import { Loader2Icon } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

type GameInstance = {
    init(): void;
    start(): void;
    load(onReady: () => void, onProgress?: (status: { success: number; total: number; failed: number }) => void): void;
    playBgm(): void;
    pauseBgm(): void;
    destroy(): void;
    setVariable(name: string, value: any): void;
};

// Import TowerGame dynamically to avoid TypeScript errors
const importGame = async () => {
    const module = await import('../tower_game/src/index');
    return module.TowerGame;
};

export default function Game() {
    const navigate = useNavigate();
    const gameContainer = useRef<HTMLDivElement>(null);
    const gameInstance = useRef<GameInstance | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [loadingProgress, setLoadingProgress] = useState(0);
    const [error, setError] = useState<string | null>(null);
    const [isGameOver, setIsGameOver] = useState(false);
    const [finalScore, setFinalScore] = useState(0);
    const [isGameActive, setIsGameActive] = useState(false);
    const [hasQuit, setHasQuit] = useState(false);

    // Function to stop all game sounds and prevent future sounds
    const stopAllGameSounds = () => {
        if (!gameInstance.current) return;

        // Stop background music
        gameInstance.current.pauseBgm();
        
        // Stop all existing audio elements
        const gameAudioElements = document.querySelectorAll('audio[src*="/game/"]');
        gameAudioElements.forEach((audio) => {
            const audioElement = audio as HTMLAudioElement;
            audioElement.pause();
            audioElement.currentTime = 0;
            // Remove the element to prevent any queued sounds
            audioElement.remove();
        });

        // Clear any audio elements in the DOM
        const audioContainer = document.querySelector('#game-audio-container');
        if (audioContainer) {
            audioContainer.innerHTML = '';
        }
    };

    // Function to clean up game resources
    const cleanupGame = () => {
        stopAllGameSounds();
        if (gameInstance.current) {
            gameInstance.current.destroy?.();
            gameInstance.current = null;
        }
        setIsGameActive(false);
    };

    useEffect(() => {
        let mounted = true;

        // Create audio container if it doesn't exist
        let audioContainer = document.querySelector('#game-audio-container');
        if (!audioContainer) {
            audioContainer = document.createElement('div');
            audioContainer.id = 'game-audio-container';
            document.body.appendChild(audioContainer);
        }

        // Handle visibility change
        const handleVisibilityChange = () => {
            if (!gameInstance.current || hasQuit) return;
            
            if (document.hidden) {
                stopAllGameSounds();
            } else if (isGameActive && !isGameOver && !hasQuit) {
                gameInstance.current.playBgm();
            }
        };

        // Handle window blur/focus
        const handleBlur = () => {
            if (gameInstance.current) {
                stopAllGameSounds();
            }
        };

        const handleFocus = () => {
            if (gameInstance.current && !document.hidden && isGameActive && !isGameOver && !hasQuit) {
                gameInstance.current.playBgm();
            }
        };

        const initGame = async () => {
            try {
                if (!gameContainer.current) return;

                const TowerGame = await importGame();
                const container = gameContainer.current;
                const rect = container.getBoundingClientRect();
                const ratio = 1.5;

                let gameWidth = rect.width;
                let gameHeight = rect.height;
                
                if (rect.height / ratio < rect.width) {
                    gameWidth = Math.ceil(rect.height / ratio);
                }
                if (rect.width * ratio < rect.height) {
                    gameHeight = Math.ceil(rect.width * ratio);
                }

                const options = {
                    width: gameWidth,
                    height: gameHeight,
                    canvasId: 'tower-game-canvas',
                    soundOn: true,
                    setGameScore: async (score: number) => {
                        try {
                            await $http.post('/game/score', { score });
                            setFinalScore(score);
                        } catch (error) {
                            console.error('Failed to save score:', error);
                        }
                    },
                    onGameOver: (score: number) => {
                        if (gameInstance.current) {
                            stopAllGameSounds();
                        }
                        setIsGameOver(true);
                        setIsGameActive(false);
                        setFinalScore(score);
                    }
                };

                // Initialize game
                const game = TowerGame(options);
                gameInstance.current = game;

                // Load game assets and start
                game.load(() => {
                    if (!mounted) return;
                    game.init();
                    setIsLoading(false);
                    setIsGameActive(true);
                    game.start();
                    game.playBgm();
                }, (status: { success: number; total: number; failed: number }) => {
                    if (!mounted) return;
                    if (status.failed > 0) {
                        setError('Failed to load some game assets. Please refresh the page.');
                        return;
                    }
                    const progress = Math.round((status.success / status.total) * 100);
                    setLoadingProgress(progress);
                });

                const handleResize = () => {
                    if (!container || !game) return;

                    const rect = container.getBoundingClientRect();
                    const ratio = 1.5;

                    let width = rect.width;
                    let height = rect.height;
                    
                    if (height / ratio < width) {
                        width = Math.ceil(height / ratio);
                    }
                    if (width * ratio < height) {
                        height = Math.ceil(width * ratio);
                    }

                    const canvas = document.getElementById('tower-game-canvas') as HTMLCanvasElement | null;
                    if (canvas) {
                        canvas.style.width = `${width}px`;
                        canvas.style.height = `${height}px`;
                        game.setVariable('width', width);
                        game.setVariable('height', height);
                    }
                };

                window.addEventListener('resize', handleResize);
                handleResize();

                // Add event listeners for visibility and focus
                document.addEventListener('visibilitychange', handleVisibilityChange);
                window.addEventListener('blur', handleBlur);
                window.addEventListener('focus', handleFocus);

                return () => {
                    window.removeEventListener('resize', handleResize);
                };
            } catch (err) {
                if (mounted) {
                    setError('Failed to initialize game. Please refresh the page.');
                    console.error('Game initialization error:', err);
                }
            }
        };

        initGame();

        return () => {
            mounted = false;
            cleanupGame();
            // Clean up event listeners
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            window.removeEventListener('blur', handleBlur);
            window.removeEventListener('focus', handleFocus);

            // Remove audio container
            const audioContainer = document.querySelector('#game-audio-container');
            if (audioContainer) {
                audioContainer.remove();
            }
        };
    }, [hasQuit, isGameOver]);

    const handlePlayAgain = () => {
        if (gameInstance.current) {
            setIsGameOver(false);
            setHasQuit(false);
            setIsGameActive(true);
            gameInstance.current.init();
            gameInstance.current.start();
            gameInstance.current.playBgm();
        }
    };

    const handleGoHome = () => {
        setHasQuit(true);
        cleanupGame();
        navigate('/');
    };

    return (
        <div ref={gameContainer} className="fixed inset-0 bg-black flex items-center justify-center">
            {isLoading && !error && (
                <div className="text-center text-white">
                    <Loader2Icon className="w-12 h-12 animate-spin text-primary mx-auto mb-4" />
                    <div>Loading game... {loadingProgress}%</div>
                </div>
            )}
            {error && (
                <div className="text-center text-red-500 p-4">
                    {error}
                </div>
            )}
            <canvas 
                id="tower-game-canvas" 
                className={isLoading || error ? 'hidden' : 'block'} 
                style={{ 
                    maxWidth: '100vw',
                    maxHeight: '100vh'
                }} 
            />
            {isGameOver && (
                <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50">
                    <div className="relative flex flex-col items-center bg-contain bg-center bg-no-repeat p-8"
                         style={{ backgroundImage: "url('/game/main-modal-bg.png')" }}>
                        <img 
                            src="/game/main-modal-over.png" 
                            alt="Game Over" 
                            className="w-64 mb-4"
                        />
                        <div className="text-white text-2xl mb-6">
                            Score: {finalScore}
                        </div>
                        <div className="flex gap-4">
                            <button
                                onClick={handlePlayAgain}
                                className="hover:opacity-80 transition-opacity"
                            >
                                <img 
                                    src="/game/main-modal-again-b.png" 
                                    alt="Play Again" 
                                    className="w-32"
                                />
                            </button>
                            <button
                                onClick={handleGoHome}
                                className="hover:opacity-80 transition-opacity"
                            >
                                <img 
                                    src="/game/main-share-icon.png" 
                                    alt="Home" 
                                    className="w-32"
                                />
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}