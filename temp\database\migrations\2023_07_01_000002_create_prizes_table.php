<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('prizes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('prize_tree_id')->constrained('prize_trees')->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('icon')->nullable();
            $table->integer('tier')->default(1);
            $table->integer('position')->default(0);
            $table->string('category')->nullable();
            $table->integer('cost')->default(1);
            $table->boolean('is_root')->default(false);
            $table->string('reward_type'); // cosmetic, title, card, balance, booster, etc.
            $table->json('reward_data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('prizes');
    }
};
