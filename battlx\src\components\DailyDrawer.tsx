import { cn, compactNumber } from "@/lib/utils";
import { <PERSON><PERSON> } from "./ui/button";
import Drawer, { DrawerProps } from "./ui/drawer";
import { useMutation, useQuery } from "@tanstack/react-query";
import { $http } from "@/lib/http";
import { toast } from "react-toastify";
import { BattlxIcon } from "./icons/BattlxIcon";
import { DailyTaskType } from "@/types/TaskType";
import { useUserStore } from "@/store/user-store";
import { useNavigate } from "react-router-dom";

export default function DailyDrawer({ ...props }: DrawerProps) {
  const navigate = useNavigate();
  const dailyTasks = useQuery({
    queryKey: ["daily-tasks"],
    queryFn: () => $http.$get<DailyTaskType[]>("/clicker/daily-tasks"),
    staleTime: Infinity,
  });

  const claimTaskMutation = useMutation({
    mutationFn: () =>
      $http.post<{ message: string; balance: number }>(
        `/clicker/claim-daily-task`
      ),
    onSuccess: (response) => {
      toast.success(response.data.message);
      dailyTasks.refetch();
      useUserStore.setState({
        balance: response.data.balance,
      });
      navigate("/");
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Something went wrong");
    },
  });
  return (
    <Drawer {...props}>
      <BattlxIcon icon="coins" className="mx-auto h-28 opacity-80 text-[#9B8B6C]" />
      <h2 className="mt-6 text-2xl font-bold text-center text-[#9B8B6C]">Daily Reward</h2>
      <p className="mt-2.5 text-center font-medium text-[#B3B3B3]/80">
        Acquire coins for logging into the game daily without skipping
      </p>
      <div className="grid grid-cols-4 gap-3 mt-10 overflow-y-auto max-h-64">
        {dailyTasks.data?.map((item, key) => (
          <div
            key={key}
            className={cn(
              "flex flex-col border-2 border-transparent items-center bg-[#1A1617] rounded-lg opacity-40 py-2.5 px-4 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)]",
              item.completed && "opacity-100 border-[#9B8B6C] bg-[#4A0E0E]/30",
              item.available && !item.completed && "opacity-100 border-[#B3B3B3]/20"
            )}
          >
            <p className="text-sm font-medium text-[#B3B3B3]">{item.name}</p>
            <BattlxIcon icon="coin" className="w-5 h-5 opacity-80 text-[#9B8B6C]" />
            <p
              className={cn(
                "font-medium text-[#9B8B6C]",
                item.completed && "text-[#9B8B6C]"
              )}
            >
              {compactNumber(item.reward_coins)}
            </p>
          </div>
        ))}
      </div>
      <Button
        className="w-full mt-6 bg-[#1A1617] text-[#9B8B6C] border border-[#B3B3B3]/20 hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_15px_rgba(74,14,14,0.3)] disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={
          !dailyTasks.data?.some((item) => item.available && !item.completed) ||
          claimTaskMutation.isPending ||
          dailyTasks.isLoading
        }
        onClick={() => claimTaskMutation.mutate()}
      >
        {claimTaskMutation.isPending && (
          <BattlxIcon icon="loading" className="w-6 h-6 mr-2 animate-spin" />
        )}
        Claim
      </Button>
    </Drawer>
  );
}
