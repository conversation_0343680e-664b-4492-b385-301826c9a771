export type TaskType = {
  id: string;
  name: string;           // mapped from title in API
  title?: string;         // original field
  description?: string;
  type: string;           // mapped from type.name in API
  type_id?: number;       // original field
  link?: string;          // mapped from url in API
  url?: string;           // original field
  image?: string;
  reward_coins: number;   // mapped from reward in API
  reward?: number;        // original field
  action_name: string;    // kept for backward compatibility
  is_submitted: boolean;
  is_rewarded: boolean;
  submitted_at: string | null;
};

export type DailyTaskType = {
  id: number;
  name: string;
  description: string;
  reward_coins: number;
  required_login_streak: number;
  available: false;
  completed: false;
  status: string;
};

export type ReferralTaskType = {
  id: number;
  title: string;
  number_of_referrals: number;
  reward: number;
  is_completed?: boolean | null;
};
