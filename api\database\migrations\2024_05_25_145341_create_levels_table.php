<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create the table
        Schema::create('levels', function (Blueprint $table) {
            $table->id();
            $table->integer('level');
            $table->string('name');
            $table->integer('from_balance');
            $table->integer('to_balance');
            $table->timestamps();
        });

        // Add CHECK constraints using raw SQL
        DB::statement('ALTER TABLE levels ADD CONSTRAINT chk_level_positive CHECK (level >= 1)');
        DB::statement('ALTER TABLE levels ADD CONSTRAINT chk_from_balance_non_negative CHECK (from_balance >= 0)');
        DB::statement('ALTER TABLE levels ADD CONSTRAINT chk_to_balance_non_negative CHECK (to_balance >= 0)');
        DB::statement('ALTER TABLE levels ADD CONSTRAINT chk_to_balance_greater_than_from_balance CHECK (to_balance > from_balance)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the CHECK constraints
        DB::statement('ALTER TABLE levels DROP CONSTRAINT IF EXISTS chk_level_positive');
        DB::statement('ALTER TABLE levels DROP CONSTRAINT IF EXISTS chk_from_balance_non_negative');
        DB::statement('ALTER TABLE levels DROP CONSTRAINT IF EXISTS chk_to_balance_non_negative');
        DB::statement('ALTER TABLE levels DROP CONSTRAINT IF EXISTS chk_to_balance_greater_than_from_balance');

        // Drop the table
        Schema::dropIfExists('levels');
    }
};