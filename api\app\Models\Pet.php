<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Pet extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id', 'pet_template_id', 'level', 'experience', 'happiness',
        'last_fed', 'last_played', 'last_petted', 'daily_interaction_count',
        'daily_interaction_reset_date', 'is_featured', 'is_favorite', 'nickname',
        'evolution_stage', 'last_evolution'
    ];

    protected $casts = [
        'last_fed' => 'datetime',
        'last_played' => 'datetime',
        'last_petted' => 'datetime',
        'last_evolution' => 'datetime',
        'daily_interaction_reset_date' => 'date',
        'is_featured' => 'boolean',
        'is_favorite' => 'boolean',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(PetTemplate::class, 'pet_template_id');
    }

    public function interactions(): HasMany
    {
        return $this->hasMany(PetInteraction::class);
    }

    public function happinessLogs(): HasMany
    {
        return $this->hasMany(PetHappinessLog::class);
    }

    // Scopes
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeNeedingAttention($query)
    {
        return $query->where('happiness', '<', 30);
    }

    public function scopeReadyForEvolution($query)
    {
        return $query->whereRaw('level >= (
            SELECT JSON_EXTRACT(evolution_levels, "$[" || pets.evolution_stage || "]")
            FROM pet_templates 
            WHERE pet_templates.id = pets.pet_template_id
            AND JSON_ARRAY_LENGTH(evolution_levels) > pets.evolution_stage
        )');
    }

    // Accessors
    public function getDisplayNameAttribute(): string
    {
        return $this->nickname ?: $this->template->name;
    }

    public function getCurrentImageAttribute(): string
    {
        return $this->template->getEvolutionImageForLevel($this->level);
    }

    public function getHappinessPercentageAttribute(): float
    {
        return round(($this->happiness / $this->template->max_happiness) * 100, 1);
    }

    public function getExperienceToNextLevelAttribute(): int
    {
        return $this->calculateExperienceRequired($this->level + 1) - $this->experience;
    }

    public function getCanEvolveAttribute(): bool
    {
        $nextEvolutionLevel = $this->template->getNextEvolutionLevel($this->level);
        return $nextEvolutionLevel && $this->level >= $nextEvolutionLevel;
    }

    // Interaction Methods
    public function canInteract(string $interactionType): bool
    {
        $this->resetDailyInteractionsIfNeeded();
        
        $limits = [
            'feed' => 5,
            'play' => 3,
            'pet' => 10,
        ];
        
        $cooldowns = [
            'feed' => 60,    // minutes
            'play' => 120,   // minutes
            'pet' => 30,     // minutes
        ];
        
        // Check daily limit
        $todayInteractions = $this->interactions()
            ->where('interaction_type', $interactionType)
            ->whereDate('interaction_time', today())
            ->count();
            
        if ($todayInteractions >= ($limits[$interactionType] ?? 0)) {
            return false;
        }
        
        // Check cooldown
        $lastInteraction = $this->interactions()
            ->where('interaction_type', $interactionType)
            ->latest('interaction_time')
            ->first();
            
        if ($lastInteraction) {
            $cooldownMinutes = $cooldowns[$interactionType] ?? 0;
            $cooldownExpires = $lastInteraction->interaction_time->addMinutes($cooldownMinutes);
            
            if (now() < $cooldownExpires) {
                return false;
            }
        }
        
        return true;
    }

    public function interact(string $interactionType, TelegramUser $user): array
    {
        if (!$this->canInteract($interactionType)) {
            throw new \Exception("Cannot perform {$interactionType} interaction at this time");
        }
        
        $interactionConfig = $this->getInteractionConfig($interactionType);
        
        // Check if user has enough energy
        if ($user->available_energy < $interactionConfig['energy_cost']) {
            throw new \Exception("Not enough energy for this interaction");
        }
        
        // Calculate rewards
        $happinessBonus = $this->happiness >= 80 ? 2.0 : 1.0; // Happy pet bonus
        $rewards = [
            'coins' => (int)($interactionConfig['coins'] * $happinessBonus),
            'experience' => $interactionConfig['experience'],
            'happiness' => $interactionConfig['happiness'],
            'materials' => 0,
            'collectible' => null
        ];
        
        // Random bonus rewards
        if (rand(1, 100) <= $interactionConfig['material_chance']) {
            $rewards['materials'] = 1;
        }
        
        if (rand(1, 100) <= $interactionConfig['collectible_chance']) {
            $rewards['collectible'] = $this->getRandomCollectible();
        }
        
        // Apply changes
        $this->updateFromInteraction($interactionType, $rewards);
        $user->decrement('available_energy', $interactionConfig['energy_cost']);
        $user->increment('balance', $rewards['coins']);
        
        // Log interaction
        $this->interactions()->create([
            'telegram_user_id' => $user->id,
            'interaction_type' => $interactionType,
            'energy_cost' => $interactionConfig['energy_cost'],
            'happiness_gained' => $rewards['happiness'],
            'experience_gained' => $rewards['experience'],
            'coins_rewarded' => $rewards['coins'],
            'materials_rewarded' => $rewards['materials'],
            'collectible_rewarded' => $rewards['collectible'],
            'bonus_applied' => $happinessBonus > 1,
            'interaction_time' => now()
        ]);
        
        // Log happiness change
        $this->logHappinessChange($rewards['happiness'], "interaction_{$interactionType}");
        
        return $rewards;
    }

    private function getInteractionConfig(string $type): array
    {
        return match($type) {
            'feed' => [
                'energy_cost' => 5,
                'happiness' => 20,
                'experience' => 10,
                'coins' => 50,
                'material_chance' => 30,
                'collectible_chance' => 0
            ],
            'play' => [
                'energy_cost' => 10,
                'happiness' => 30,
                'experience' => 15,
                'coins' => 100,
                'material_chance' => 50,
                'collectible_chance' => 10
            ],
            'pet' => [
                'energy_cost' => 2,
                'happiness' => 10,
                'experience' => 5,
                'coins' => 25,
                'material_chance' => 0,
                'collectible_chance' => 0
            ],
            default => throw new \Exception("Unknown interaction type: {$type}")
        };
    }

    private function updateFromInteraction(string $interactionType, array $rewards): void
    {
        $oldHappiness = $this->happiness;
        
        $this->happiness = min(
            $this->template->max_happiness,
            $this->happiness + $rewards['happiness']
        );
        
        $this->experience += $rewards['experience'];
        
        // Check for level up
        while ($this->canLevelUp()) {
            $this->levelUp();
        }
        
        // Update interaction timestamps
        $now = now();
        match($interactionType) {
            'feed' => $this->last_fed = $now,
            'play' => $this->last_played = $now,
            'pet' => $this->last_petted = $now,
            default => null
        };
        
        $this->save();
    }

    private function canLevelUp(): bool
    {
        $requiredExp = $this->calculateExperienceRequired($this->level + 1);
        return $this->experience >= $requiredExp && $this->level < $this->template->max_level;
    }

    private function levelUp(): void
    {
        $this->level++;
        
        // Check for evolution
        if ($this->can_evolve) {
            $this->evolve();
        }
    }

    private function evolve(): void
    {
        $this->evolution_stage++;
        $this->last_evolution = now();
        $this->happiness = min($this->template->max_happiness, $this->happiness + 20); // Evolution happiness bonus
        
        $this->logHappinessChange(20, 'evolution_bonus');
    }

    private function calculateExperienceRequired(int $level): int
    {
        return (int)(100 * pow(1.2, $level - 1));
    }

    private function resetDailyInteractionsIfNeeded(): void
    {
        if ($this->daily_interaction_reset_date < today()) {
            $this->daily_interaction_count = 0;
            $this->daily_interaction_reset_date = today();
            $this->save();
        }
    }

    private function logHappinessChange(int $change, string $reason): void
    {
        $oldHappiness = $this->happiness - $change;
        
        $this->happinessLogs()->create([
            'happiness_before' => $oldHappiness,
            'happiness_after' => $this->happiness,
            'happiness_change' => $change,
            'change_reason' => $reason,
            'logged_at' => now()
        ]);
    }

    // Daily happiness decay (called by scheduled job)
    public function applyDailyHappinessDecay(): void
    {
        $decayAmount = $this->template->happiness_decay_rate;
        $oldHappiness = $this->happiness;
        
        $this->happiness = max(0, $this->happiness - $decayAmount);
        $this->save();
        
        if ($decayAmount > 0) {
            $this->logHappinessChange(-$decayAmount, 'daily_decay');
        }
    }

    private function getRandomCollectible(): ?string
    {
        // This would be implemented to return a random collectible based on pet category
        return null;
    }
}
