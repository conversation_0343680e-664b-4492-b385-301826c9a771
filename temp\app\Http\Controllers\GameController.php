<?php

namespace App\Http\Controllers;

use App\Models\TelegramUser;
use App\Services\AchievementPointService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class GameController extends Controller
{
    protected $achievementPointService;
    
    public function __construct(AchievementPointService $achievementPointService)
    {
        $this->achievementPointService = $achievementPointService;
    }
    
    /**
     * Update the user's game score
     */
    public function updateScore(Request $request)
    {
        $request->validate([
            'score' => 'required|integer|min:1',
            'game_id' => 'required|string|in:tower,rabbit,slash'
        ]);
        
        $user = $request->user();
        $score = $request->score;
        $gameId = $request->game_id;
        
        // Update the user's game score
        $user->game_score += $score;
        $user->save();
        
        // Check for score milestones and award achievement points
        $this->checkScoreMilestones($user, $score, $gameId);
        
        return response()->json([
            'success' => true,
            'message' => 'Score updated successfully',
            'total_score' => $user->game_score
        ]);
    }
    
    /**
     * Check for score milestones and award achievement points
     */
    private function checkScoreMilestones($user, $score, $gameId)
    {
        // Get or create game stats
        $gameStats = \App\Models\GameStat::firstOrCreate(
            ['telegram_user_id' => $user->id, 'game_id' => $gameId],
            ['high_score' => 0, 'total_score' => 0, 'plays' => 0]
        );
        
        // Update stats
        $oldHighScore = $gameStats->high_score;
        $oldTotalScore = $gameStats->total_score;
        
        if ($score > $gameStats->high_score) {
            $gameStats->high_score = $score;
        }
        
        $gameStats->total_score += $score;
        $gameStats->plays++;
        $gameStats->save();
        
        // Define score milestones
        $scoreMilestones = [1000, 5000, 10000, 50000, 100000];
        
        // Check if any total score milestones were crossed
        foreach ($scoreMilestones as $milestone) {
            if ($oldTotalScore < $milestone && $gameStats->total_score >= $milestone) {
                // Award achievement points
                $points = 1;
                if ($milestone >= 10000) $points = 2;
                if ($milestone >= 100000) $points = 3;
                
                $this->achievementPointService->awardPoints(
                    $user->id,
                    $points,
                    'game_score_milestone',
                    $milestone,
                    "Reached {$milestone} total score in {$gameId} game"
                );
            }
        }
        
        // Define high score milestones (different for each game)
        $highScoreMilestones = [
            'tower' => [100, 500, 1000, 2000, 5000],
            'rabbit' => [200, 1000, 2000, 5000, 10000],
            'slash' => [500, 2000, 5000, 10000, 20000]
        ];
        
        // Check if any high score milestones were crossed
        if (isset($highScoreMilestones[$gameId])) {
            foreach ($highScoreMilestones[$gameId] as $milestone) {
                if ($oldHighScore < $milestone && $gameStats->high_score >= $milestone) {
                    // Award achievement points
                    $points = 1;
                    if ($milestone >= $highScoreMilestones[$gameId][2]) $points = 2; // Medium milestone
                    if ($milestone >= $highScoreMilestones[$gameId][4]) $points = 3; // Highest milestone
                    
                    $this->achievementPointService->awardPoints(
                        $user->id,
                        $points,
                        'game_highscore_milestone',
                        $milestone,
                        "Reached {$milestone} high score in {$gameId} game"
                    );
                }
            }
        }
    }
    
    /**
     * Get the game leaderboard
     */
    public function leaderboard(Request $request)
    {
        $request->validate([
            'game_id' => 'required|string|in:tower,rabbit,slash'
        ]);
        
        $gameId = $request->game_id;
        
        $leaderboard = \App\Models\GameStat::where('game_id', $gameId)
            ->orderBy('high_score', 'desc')
            ->with('user:id,telegram_id,first_name,last_name,username')
            ->limit(100)
            ->get()
            ->map(function ($stat) {
                return [
                    'user_id' => $stat->user->id,
                    'telegram_id' => $stat->user->telegram_id,
                    'name' => $stat->user->first_name . ' ' . $stat->user->last_name,
                    'username' => $stat->user->username,
                    'high_score' => $stat->high_score,
                    'plays' => $stat->plays
                ];
            });
        
        return response()->json($leaderboard);
    }
    
    /**
     * Check if the user can play a game
     */
    public function checkPlayAvailability(Request $request)
    {
        $request->validate([
            'game_id' => 'required|string|in:tower,rabbit,slash'
        ]);
        
        $user = $request->user();
        $gameId = $request->game_id;
        
        switch ($gameId) {
            case 'tower':
                $result = $user->canPlayTowerGame();
                break;
            case 'rabbit':
                $result = $user->canPlayRabbitGame();
                break;
            case 'slash':
                $result = $user->canPlaySlashGame();
                break;
            default:
                $result = ['allowed' => false, 'reason' => 'invalid_game'];
        }
        
        return response()->json($result);
    }
    
    /**
     * Use a play for a game
     */
    public function usePlay(Request $request)
    {
        $request->validate([
            'game_id' => 'required|string|in:tower,rabbit,slash',
            'paid' => 'boolean'
        ]);
        
        $user = $request->user();
        $gameId = $request->game_id;
        $paid = $request->paid ?? false;
        
        if ($gameId === 'tower') {
            $result = $user->useTowerGamePlay($paid);
            return response()->json($result);
        }
        
        // Rabbit and Slash games have unlimited plays once unlocked
        return response()->json([
            'success' => true,
            'message' => 'Game play started'
        ]);
    }
    
    /**
     * Unlock a game
     */
    public function unlockGame(Request $request)
    {
        $request->validate([
            'game_id' => 'required|string|in:tower,rabbit,slash'
        ]);
        
        $user = $request->user();
        $gameId = $request->game_id;
        
        // Check if user has enough balance
        $unlockCost = 5000; // 5000 coins to unlock a game
        
        if ($user->balance < $unlockCost) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient balance',
                'required' => $unlockCost,
                'balance' => $user->balance
            ], 400);
        }
        
        DB::beginTransaction();
        
        try {
            // Deduct balance
            $user->balance -= $unlockCost;
            
            // Unlock the game
            switch ($gameId) {
                case 'tower':
                    $user->tower_game_unlocked = true;
                    $user->tower_game_plays = 15;
                    $user->tower_game_plays_reset_at = now();
                    break;
                case 'rabbit':
                    $user->rabbit_game_unlocked = true;
                    break;
                case 'slash':
                    $user->slash_game_unlocked = true;
                    break;
            }
            
            $user->save();
            
            // Award achievement points for unlocking a game
            $this->achievementPointService->awardPoints(
                $user->id,
                2, // Award 2 points for unlocking a game
                'game_unlock',
                $gameId,
                "Unlocked {$gameId} game"
            );
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Game unlocked successfully',
                'balance' => $user->balance
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to unlock game: ' . $e->getMessage()
            ], 500);
        }
    }
}
