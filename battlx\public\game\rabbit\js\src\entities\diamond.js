/**
 * Diamond Entity
 * Collectible that gives points
 */
class Diamond {
    /**
     * @param {Engine} engine - Game engine
     * @param {number} x - X position
     * @param {number} y - Y position
     * @param {number} speed - Movement speed
     */
    constructor(engine, x, y, speed) {
        this.engine = engine;
        this.x = x;
        this.y = y;
        this.speed = speed;
        this.width = 52 * 1.2; // Sprite width * scale (52 = 416/8)
        this.height = 52 * 1.2; // Sprite height * scale
        this.scale = 1.2;

        // Animation
        this.sprite = engine.createSprite('diamond');

        // Only play animation if sprite is available
        if (this.sprite) {
            this.sprite.play('spin');
        } else {
            console.error('Failed to create diamond sprite');
            // Try to create a fallback sprite
            const img = engine.getImage('diamond-img');
            if (img) {
                console.log('Using fallback diamond sprite');
                this.fallbackImg = img;
            }
        }

        // State
        this.active = true;
    }

    /**
     * Update the diamond
     * @param {number} deltaTime - Time since last update
     */
    update(deltaTime) {
        // Move from right to left
        this.x -= this.speed * deltaTime;

        // Update sprite animation
        if (this.sprite) {
            this.sprite.update(deltaTime);
        }

        // Deactivate if off screen
        if (this.x < -this.width) {
            this.active = false;
        }
    }

    /**
     * Get the diamond's collision box
     * @returns {Object} - Collision box
     */
    getCollisionBox() {
        return {
            x: this.x - this.width / 2 + 8,
            y: this.y - this.height / 2 + 8,
            width: this.width - 16,
            height: this.height - 16
        };
    }

    /**
     * Check if the diamond is active
     * @returns {boolean} - True if active
     */
    isActive() {
        return this.active;
    }

    /**
     * Deactivate the diamond
     */
    deactivate() {
        this.active = false;
    }

    /**
     * Render the diamond
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     */
    render(ctx) {
        // Draw the diamond sprite
        if (this.sprite) {
            this.sprite.draw(ctx, this.x, this.y, this.scale);
        } else if (this.fallbackImg) {
            // Draw fallback image
            const frameWidth = 52; // 416 / 8
            const frameHeight = 52;
            try {
                ctx.drawImage(
                    this.fallbackImg,
                    0, 0, // Source x, y (first frame)
                    frameWidth, frameHeight, // Source width, height
                    this.x - this.width/2, this.y - this.height/2, // Destination x, y
                    this.width, this.height // Destination width, height
                );
            } catch (error) {
                console.error('Error drawing diamond fallback:', error);
                // Draw colored rectangle as last resort
                ctx.fillStyle = 'cyan';
                ctx.fillRect(this.x - this.width/2, this.y - this.height/2, this.width, this.height);
            }
        } else {
            // Fallback rendering if sprite is not available
            ctx.fillStyle = 'yellow';
            ctx.fillRect(this.x - this.width/2, this.y - this.height/2, this.width, this.height);
        }

        // Draw collision box in debug mode
        if (this.engine.debug) {
            const box = this.getCollisionBox();
            ctx.strokeStyle = 'green';
            ctx.lineWidth = 2;
            ctx.strokeRect(box.x, box.y, box.width, box.height);
        }
    }
}
