# PvP Technical Implementation: Step-by-Step Guide

## Phase 1: Core Battle System (Week 1-2)

### 1.1 Database Setup

**Create the battle system migrations:**

```bash
# In your Laravel API directory
php artisan make:migration create_battle_system_tables
```

```php
// api/database/migrations/2024_XX_XX_create_battle_system_tables.php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Characters table
        Schema::create('characters', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['striker', 'guardian', 'mystic', 'assassin', 'engineer']);
            $table->json('base_stats'); // health, attack, defense, speed, energy
            $table->json('abilities'); // Array of ability IDs
            $table->string('sprite_path');
            $table->boolean('is_unlocked_by_default')->default(false);
            $table->timestamps();
        });

        // User Characters (owned characters with progression)
        Schema::create('user_characters', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
            $table->foreignId('character_id')->constrained()->onDelete('cascade');
            $table->integer('level')->default(1);
            $table->integer('experience')->default(0);
            $table->json('current_stats'); // Calculated stats with level bonuses
            $table->json('equipped_abilities'); // 3 selected abilities for battle
            $table->json('cosmetics')->nullable(); // Skins, effects, etc.
            $table->boolean('is_active')->default(false); // Currently selected character
            $table->timestamps();
            
            $table->unique(['telegram_user_id', 'character_id']);
        });

        // Battles table (from previous plan)
        Schema::create('battles', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['1v1', '2v2', 'battle_royale']);
            $table->enum('status', ['waiting', 'preparation', 'in_progress', 'completed', 'cancelled']);
            $table->foreignId('arena_id')->constrained();
            $table->integer('max_participants');
            $table->integer('current_turn')->default(0);
            $table->foreignId('current_turn_player_id')->nullable()->constrained('telegram_users');
            $table->json('battle_state'); // Complete game state
            $table->timestamp('turn_deadline')->nullable();
            $table->timestamp('started_at')->nullable();
            $table->timestamp('ended_at')->nullable();
            $table->timestamps();
        });

        // Battle Participants
        Schema::create('battle_participants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('battle_id')->constrained()->onDelete('cascade');
            $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_character_id')->constrained()->onDelete('cascade');
            $table->integer('position_x')->default(0);
            $table->integer('position_y')->default(0);
            $table->integer('current_health');
            $table->integer('current_energy');
            $table->json('status_effects')->nullable();
            $table->enum('result', ['win', 'loss', 'draw'])->nullable();
            $table->integer('damage_dealt')->default(0);
            $table->integer('damage_taken')->default(0);
            $table->integer('elo_change')->default(0);
            $table->timestamps();
        });

        // Battle Actions Log
        Schema::create('battle_actions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('battle_id')->constrained()->onDelete('cascade');
            $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
            $table->integer('turn_number');
            $table->enum('action_type', ['move', 'attack', 'defend', 'special', 'wait']);
            $table->json('action_data'); // target position, ability used, etc.
            $table->json('result_data'); // damage dealt, effects applied, etc.
            $table->timestamp('executed_at');
            $table->timestamps();
        });

        // Arenas
        Schema::create('arenas', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('theme'); // desert, forest, urban, space
            $table->integer('width')->default(5);
            $table->integer('height')->default(5);
            $table->json('special_tiles')->nullable(); // Hazards, power-ups
            $table->string('background_image');
            $table->json('environmental_effects')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('battle_actions');
        Schema::dropIfExists('battle_participants');
        Schema::dropIfExists('battles');
        Schema::dropIfExists('user_characters');
        Schema::dropIfExists('characters');
        Schema::dropIfExists('arenas');
    }
};
```

### 1.2 Seed Default Data

```php
// api/database/seeders/BattleSystemSeeder.php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Character;
use App\Models\Arena;

class BattleSystemSeeder extends Seeder
{
    public function run()
    {
        // Create default characters
        $characters = [
            [
                'name' => 'Blaze Striker',
                'type' => 'striker',
                'base_stats' => [
                    'health' => 80,
                    'attack' => 25,
                    'defense' => 10,
                    'speed' => 20,
                    'energy' => 100
                ],
                'abilities' => ['quick_strike', 'flame_dash', 'berserker_rage'],
                'sprite_path' => 'characters/striker/base.png',
                'is_unlocked_by_default' => true
            ],
            [
                'name' => 'Iron Guardian',
                'type' => 'guardian',
                'base_stats' => [
                    'health' => 120,
                    'attack' => 15,
                    'defense' => 25,
                    'speed' => 10,
                    'energy' => 100
                ],
                'abilities' => ['shield_wall', 'taunt', 'fortress_stance'],
                'sprite_path' => 'characters/guardian/base.png',
                'is_unlocked_by_default' => true
            ],
            [
                'name' => 'Void Mystic',
                'type' => 'mystic',
                'base_stats' => [
                    'health' => 90,
                    'attack' => 20,
                    'defense' => 15,
                    'speed' => 15,
                    'energy' => 120
                ],
                'abilities' => ['arcane_bolt', 'teleport', 'mana_shield'],
                'sprite_path' => 'characters/mystic/base.png',
                'is_unlocked_by_default' => false
            ]
        ];

        foreach ($characters as $character) {
            Character::create($character);
        }

        // Create default arenas
        $arenas = [
            [
                'name' => 'Desert Colosseum',
                'theme' => 'desert',
                'width' => 5,
                'height' => 5,
                'background_image' => 'arenas/desert/background.jpg',
                'environmental_effects' => [
                    'sandstorm' => ['turn_interval' => 5, 'damage' => 5]
                ]
            ],
            [
                'name' => 'Mystic Forest',
                'theme' => 'forest',
                'width' => 5,
                'height' => 5,
                'background_image' => 'arenas/forest/background.jpg',
                'environmental_effects' => [
                    'healing_springs' => ['positions' => [[2,2]], 'heal_amount' => 10]
                ]
            ]
        ];

        foreach ($arenas as $arena) {
            Arena::create($arena);
        }
    }
}
```

### 1.3 Core Models

```php
// api/app/Models/Character.php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Character extends Model
{
    protected $fillable = [
        'name', 'type', 'base_stats', 'abilities', 'sprite_path', 'is_unlocked_by_default'
    ];

    protected $casts = [
        'base_stats' => 'array',
        'abilities' => 'array',
        'is_unlocked_by_default' => 'boolean'
    ];

    public function userCharacters()
    {
        return $this->hasMany(UserCharacter::class);
    }

    public function getStatAtLevel(string $stat, int $level): int
    {
        $baseStat = $this->base_stats[$stat] ?? 0;
        $growthRate = match($stat) {
            'health' => 5,
            'attack' => 2,
            'defense' => 2,
            'speed' => 1,
            'energy' => 3,
            default => 1
        };
        
        return $baseStat + (($level - 1) * $growthRate);
    }
}
```

```php
// api/app/Models/UserCharacter.php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserCharacter extends Model
{
    protected $fillable = [
        'telegram_user_id', 'character_id', 'level', 'experience',
        'current_stats', 'equipped_abilities', 'cosmetics', 'is_active'
    ];

    protected $casts = [
        'current_stats' => 'array',
        'equipped_abilities' => 'array',
        'cosmetics' => 'array',
        'is_active' => 'boolean'
    ];

    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function character()
    {
        return $this->belongsTo(Character::class);
    }

    public function calculateCurrentStats(): array
    {
        $character = $this->character;
        $stats = [];
        
        foreach ($character->base_stats as $stat => $baseValue) {
            $stats[$stat] = $character->getStatAtLevel($stat, $this->level);
        }
        
        return $stats;
    }

    public function addExperience(int $amount): bool
    {
        $this->experience += $amount;
        $requiredExp = $this->level * 100; // 100 XP per level
        
        $leveledUp = false;
        while ($this->experience >= $requiredExp) {
            $this->level++;
            $this->experience -= $requiredExp;
            $requiredExp = $this->level * 100;
            $leveledUp = true;
        }
        
        if ($leveledUp) {
            $this->current_stats = $this->calculateCurrentStats();
        }
        
        $this->save();
        return $leveledUp;
    }
}
```

### 1.4 Battle Engine Service

```php
// api/app/Services/BattleEngineService.php
<?php

namespace App\Services;

use App\Models\Battle;
use App\Models\BattleParticipant;
use App\Models\BattleAction;
use App\Models\TelegramUser;

class BattleEngineService
{
    public function processAction(Battle $battle, TelegramUser $user, array $actionData)
    {
        // Validate it's the user's turn
        if ($battle->current_turn_player_id !== $user->id) {
            throw new \Exception('Not your turn');
        }

        // Validate action legality
        $participant = $battle->participants()
            ->where('telegram_user_id', $user->id)
            ->first();

        if (!$this->isActionLegal($actionData, $participant, $battle)) {
            throw new \Exception('Illegal action');
        }

        // Process the action
        $result = $this->executeAction($actionData, $participant, $battle);

        // Log the action
        BattleAction::create([
            'battle_id' => $battle->id,
            'telegram_user_id' => $user->id,
            'turn_number' => $battle->current_turn,
            'action_type' => $actionData['action_type'],
            'action_data' => $actionData,
            'result_data' => $result,
            'executed_at' => now()
        ]);

        // Update battle state
        $this->updateBattleState($battle, $result);

        // Check for battle end conditions
        $this->checkBattleEndConditions($battle);

        // Advance turn
        $this->advanceTurn($battle);

        return $result;
    }

    protected function executeAction(array $actionData, BattleParticipant $participant, Battle $battle): array
    {
        $result = ['success' => true, 'effects' => []];

        switch ($actionData['action_type']) {
            case 'move':
                $result = $this->executeMove($actionData, $participant, $battle);
                break;
            case 'attack':
                $result = $this->executeAttack($actionData, $participant, $battle);
                break;
            case 'defend':
                $result = $this->executeDefend($actionData, $participant, $battle);
                break;
            case 'special':
                $result = $this->executeSpecial($actionData, $participant, $battle);
                break;
        }

        return $result;
    }

    protected function executeAttack(array $actionData, BattleParticipant $attacker, Battle $battle): array
    {
        $targetX = $actionData['target_x'];
        $targetY = $actionData['target_y'];

        // Find target at position
        $target = $battle->participants()
            ->where('position_x', $targetX)
            ->where('position_y', $targetY)
            ->where('id', '!=', $attacker->id)
            ->first();

        if (!$target) {
            return ['success' => false, 'message' => 'No target at position'];
        }

        // Calculate damage
        $attackerStats = $attacker->userCharacter->current_stats;
        $targetStats = $target->userCharacter->current_stats;

        $baseDamage = $attackerStats['attack'];
        $defense = $targetStats['defense'];
        $finalDamage = max(1, $baseDamage - $defense);

        // Apply damage
        $target->current_health = max(0, $target->current_health - $finalDamage);
        $target->damage_taken += $finalDamage;
        $target->save();

        $attacker->damage_dealt += $finalDamage;
        $attacker->save();

        return [
            'success' => true,
            'damage_dealt' => $finalDamage,
            'target_health' => $target->current_health,
            'target_defeated' => $target->current_health <= 0
        ];
    }

    protected function checkBattleEndConditions(Battle $battle): void
    {
        $alivePlayers = $battle->participants()
            ->where('current_health', '>', 0)
            ->count();

        if ($alivePlayers <= 1) {
            $this->endBattle($battle);
        }
    }

    protected function endBattle(Battle $battle): void
    {
        $battle->update([
            'status' => 'completed',
            'ended_at' => now()
        ]);

        // Determine winners and losers
        $winner = $battle->participants()
            ->where('current_health', '>', 0)
            ->first();

        if ($winner) {
            $winner->update(['result' => 'win']);
            
            // Award rewards
            $this->awardBattleRewards($winner);
        }

        // Update ELO ratings
        $this->updateEloRatings($battle);

        // Broadcast battle end
        broadcast(new \App\Events\BattleEnded($battle));
    }

    protected function awardBattleRewards(BattleParticipant $winner): void
    {
        $user = $winner->user;
        $userCharacter = $winner->userCharacter;

        // Award coins
        $coinReward = 100 + ($winner->damage_dealt * 2);
        $user->increment('balance', $coinReward);

        // Award character XP
        $xpReward = 50 + ($winner->damage_dealt);
        $userCharacter->addExperience($xpReward);
    }
}
```

## Phase 2: Frontend Battle Interface (Week 3-4)

### 2.1 Battle Arena Component

```typescript
// battlx/src/components/battle/BattleArena.tsx
import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import { $http } from '@/lib/http';
import { BattleCanvas } from './BattleCanvas';
import { BattleHUD } from './BattleHUD';
import { ActionPanel } from './ActionPanel';

interface BattleState {
  id: number;
  status: string;
  current_turn: number;
  current_turn_player_id: number;
  participants: BattleParticipant[];
  arena: Arena;
  turn_deadline: string;
}

interface BattleParticipant {
  id: number;
  user: {
    id: number;
    first_name: string;
  };
  user_character: {
    character: {
      name: string;
      type: string;
      sprite_path: string;
    };
    level: number;
    current_stats: {
      health: number;
      attack: number;
      defense: number;
      speed: number;
      energy: number;
    };
  };
  position_x: number;
  position_y: number;
  current_health: number;
  current_energy: number;
  status_effects: any[];
}

export const BattleArena: React.FC = () => {
  const { battleId } = useParams<{ battleId: string }>();
  const [selectedAction, setSelectedAction] = useState<string | null>(null);
  const [selectedTarget, setSelectedTarget] = useState<{x: number, y: number} | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  const { data: battleState, refetch } = useQuery({
    queryKey: ['battle', battleId],
    queryFn: () => $http.get(`/api/battles/${battleId}`).then(res => res.data),
    refetchInterval: 1000 // Poll every second as fallback
  });

  const submitActionMutation = useMutation({
    mutationFn: (actionData: any) => 
      $http.post(`/api/battles/${battleId}/action`, actionData),
    onSuccess: () => {
      setSelectedAction(null);
      setSelectedTarget(null);
      refetch();
    }
  });

  // WebSocket connection for real-time updates
  useEffect(() => {
    if (!battleId) return;

    const wsUrl = `${process.env.REACT_APP_WS_URL}/battle/${battleId}`;
    wsRef.current = new WebSocket(wsUrl);

    wsRef.current.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      if (data.type === 'battle_state_update') {
        refetch();
      }
    };

    return () => {
      wsRef.current?.close();
    };
  }, [battleId, refetch]);

  const handleActionSubmit = () => {
    if (!selectedAction || !selectedTarget) return;

    const actionData = {
      action_type: selectedAction,
      target_x: selectedTarget.x,
      target_y: selectedTarget.y
    };

    submitActionMutation.mutate(actionData);
  };

  const handleTileClick = (x: number, y: number) => {
    if (selectedAction) {
      setSelectedTarget({ x, y });
    }
  };

  if (!battleState) {
    return <div className="loading">Loading battle...</div>;
  }

  const isMyTurn = battleState.current_turn_player_id === getCurrentUserId();
  const timeRemaining = new Date(battleState.turn_deadline).getTime() - Date.now();

  return (
    <div className="battle-arena">
      <BattleHUD 
        battleState={battleState}
        timeRemaining={timeRemaining}
        isMyTurn={isMyTurn}
      />
      
      <BattleCanvas
        battleState={battleState}
        onTileClick={handleTileClick}
        selectedTarget={selectedTarget}
      />
      
      <ActionPanel
        isMyTurn={isMyTurn}
        selectedAction={selectedAction}
        onActionSelect={setSelectedAction}
        onActionSubmit={handleActionSubmit}
        canSubmit={!!(selectedAction && selectedTarget)}
      />
    </div>
  );
};

function getCurrentUserId(): number {
  // Get from your user store
  return 1; // Placeholder
}
```

### 2.2 Battle Canvas Renderer

```typescript
// battlx/src/components/battle/BattleCanvas.tsx
import React, { useRef, useEffect, useCallback } from 'react';

interface BattleCanvasProps {
  battleState: BattleState;
  onTileClick: (x: number, y: number) => void;
  selectedTarget: {x: number, y: number} | null;
}

export const BattleCanvas: React.FC<BattleCanvasProps> = ({
  battleState,
  onTileClick,
  selectedTarget
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();

  const TILE_SIZE = 80;
  const GRID_WIDTH = 5;
  const GRID_HEIGHT = 5;

  const drawGrid = useCallback((ctx: CanvasRenderingContext2D) => {
    ctx.strokeStyle = '#444';
    ctx.lineWidth = 2;

    // Draw grid lines
    for (let x = 0; x <= GRID_WIDTH; x++) {
      ctx.beginPath();
      ctx.moveTo(x * TILE_SIZE, 0);
      ctx.lineTo(x * TILE_SIZE, GRID_HEIGHT * TILE_SIZE);
      ctx.stroke();
    }

    for (let y = 0; y <= GRID_HEIGHT; y++) {
      ctx.beginPath();
      ctx.moveTo(0, y * TILE_SIZE);
      ctx.lineTo(GRID_WIDTH * TILE_SIZE, y * TILE_SIZE);
      ctx.stroke();
    }

    // Highlight selected target
    if (selectedTarget) {
      ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
      ctx.fillRect(
        selectedTarget.x * TILE_SIZE,
        selectedTarget.y * TILE_SIZE,
        TILE_SIZE,
        TILE_SIZE
      );
    }
  }, [selectedTarget]);

  const drawCharacters = useCallback((ctx: CanvasRenderingContext2D) => {
    battleState.participants.forEach(participant => {
      const x = participant.position_x * TILE_SIZE + TILE_SIZE / 2;
      const y = participant.position_y * TILE_SIZE + TILE_SIZE / 2;

      // Draw character circle (placeholder for sprite)
      ctx.fillStyle = participant.user.id === getCurrentUserId() ? '#00ff00' : '#ff0000';
      ctx.beginPath();
      ctx.arc(x, y, 25, 0, 2 * Math.PI);
      ctx.fill();

      // Draw health bar
      const healthPercentage = participant.current_health / participant.user_character.current_stats.health;
      const barWidth = 60;
      const barHeight = 8;
      
      ctx.fillStyle = '#333';
      ctx.fillRect(x - barWidth/2, y - 40, barWidth, barHeight);
      
      ctx.fillStyle = healthPercentage > 0.5 ? '#00ff00' : healthPercentage > 0.25 ? '#ffff00' : '#ff0000';
      ctx.fillRect(x - barWidth/2, y - 40, barWidth * healthPercentage, barHeight);

      // Draw character name
      ctx.fillStyle = '#fff';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(participant.user.first_name, x, y + 45);
    });
  }, [battleState.participants]);

  const render = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background
    ctx.fillStyle = '#2a2a2a';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw grid
    drawGrid(ctx);

    // Draw characters
    drawCharacters(ctx);

    animationFrameRef.current = requestAnimationFrame(render);
  }, [drawGrid, drawCharacters]);

  useEffect(() => {
    render();
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [render]);

  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    const gridX = Math.floor(x / TILE_SIZE);
    const gridY = Math.floor(y / TILE_SIZE);

    if (gridX >= 0 && gridX < GRID_WIDTH && gridY >= 0 && gridY < GRID_HEIGHT) {
      onTileClick(gridX, gridY);
    }
  };

  return (
    <canvas
      ref={canvasRef}
      width={GRID_WIDTH * TILE_SIZE}
      height={GRID_HEIGHT * TILE_SIZE}
      onClick={handleCanvasClick}
      className="battle-canvas border-2 border-gray-600 cursor-pointer"
    />
  );
};
```

This implementation provides:

1. **Complete database structure** for characters, battles, and progression
2. **Server-side battle engine** with turn-based mechanics
3. **Real-time frontend interface** with canvas rendering
4. **WebSocket integration** for live updates
5. **Mobile-optimized controls** with touch-friendly interface

The system is designed to be:
- **Scalable**: Can handle multiple concurrent battles
- **Secure**: All actions validated server-side
- **Performant**: Optimized for mobile devices
- **Extensible**: Easy to add new characters, abilities, and game modes

Next steps would be to implement the asset loading system, add character animations, and create the matchmaking interface.
