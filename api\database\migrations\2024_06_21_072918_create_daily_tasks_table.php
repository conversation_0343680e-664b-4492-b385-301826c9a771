<?php

use App\Models\TelegramUser;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateDailyTasksTable extends Migration
{
    public function up()
    {
        // Create the 'daily_tasks' table
        Schema::create('daily_tasks', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->integer('required_login_streak'); // Ensure non-negative via CHECK constraint
            $table->integer('reward_coins'); // Ensure non-negative via CHECK constraint
            $table->timestamps();
        });

        // Add CHECK constraints for 'required_login_streak' and 'reward_coins' columns using raw SQL
        DB::statement('ALTER TABLE daily_tasks ADD CONSTRAINT chk_required_login_streak_non_negative CHECK (required_login_streak >= 0)');
        DB::statement('ALTER TABLE daily_tasks ADD CONSTRAINT chk_reward_coins_non_negative CHECK (reward_coins >= 0)');

        // Create the 'telegram_user_daily_tasks' table
        Schema::create('telegram_user_daily_tasks', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(TelegramUser::class)->constrained()->onDelete('cascade');
            $table->foreignId('daily_task_id')->constrained()->onDelete('cascade');
            $table->boolean('completed')->default(false);
            $table->timestamps();
        });
    }

    public function down()
    {
        // Drop the CHECK constraints
        DB::statement('ALTER TABLE daily_tasks DROP CONSTRAINT IF EXISTS chk_required_login_streak_non_negative');
        DB::statement('ALTER TABLE daily_tasks DROP CONSTRAINT IF EXISTS chk_reward_coins_non_negative');

        // Drop the tables
        Schema::dropIfExists('telegram_user_daily_tasks');
        Schema::dropIfExists('daily_tasks');
    }
}