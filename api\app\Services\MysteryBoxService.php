<?php

namespace App\Services;

use App\Models\MysteryBoxType;
use App\Models\MysteryBoxUnlock;
use App\Models\MysteryBoxOpening;
use App\Models\TelegramUser;
use App\Models\CollectibleTemplate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MysteryBoxService
{
    protected CollectibleService $collectibleService;

    public function __construct(CollectibleService $collectibleService)
    {
        $this->collectibleService = $collectibleService;
    }

    /**
     * Unlock a mystery box type for a user
     */
    public function unlockBoxType(
        TelegramUser $user,
        string $boxType,
        string $unlockSource,
        ?string $sourceReference = null
    ): ?array {
        $mysteryBoxType = MysteryBoxType::where('box_type', $boxType)->first();
        
        if (!$mysteryBoxType) {
            Log::warning('Mystery box type not found', ['box_type' => $boxType]);
            return null;
        }

        // Check if already unlocked
        $existingUnlock = $user->mysteryBoxUnlocks()
            ->where('box_type', $boxType)
            ->first();

        if ($existingUnlock) {
            return null; // Already unlocked
        }

        try {
            $unlock = $user->mysteryBoxUnlocks()->create([
                'box_type' => $boxType,
                'unlock_source' => $unlockSource,
                'source_reference' => $sourceReference,
                'unlocked_at' => now()
            ]);

            Log::info('Mystery box unlocked', [
                'user_id' => $user->id,
                'box_type' => $boxType,
                'unlock_source' => $unlockSource
            ]);

            return [
                'box_type' => $boxType,
                'display_name' => $mysteryBoxType->display_name,
                'unlock_source' => $unlockSource,
                'unlocked_at' => $unlock->unlocked_at
            ];

        } catch (\Exception $e) {
            Log::error('Failed to unlock mystery box', [
                'user_id' => $user->id,
                'box_type' => $boxType,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Open a mystery box and generate rewards
     */
    public function openMysteryBox(
        TelegramUser $user,
        MysteryBoxType $mysteryBoxType,
        string $currency
    ): array {
        return $this->openBoxes($user, $mysteryBoxType->box_type, $currency, 1)[0];
    }

    /**
     * Open mystery boxes and generate rewards (supports multiple boxes)
     */
    public function openBoxes(
        TelegramUser $user,
        string $boxType,
        string $purchaseMethod,
        int $quantity = 1
    ): array {
        $mysteryBoxType = MysteryBoxType::where('box_type', $boxType)
                                      ->where('is_active', true)
                                      ->firstOrFail();

        if (!$mysteryBoxType->canBePurchasedBy($user)) {
            throw new \Exception('Cannot purchase this mystery box');
        }

        $cost = $mysteryBoxType->getCost($purchaseMethod);
        $totalCost = $cost * $quantity;

        if ($totalCost <= 0) {
            throw new \Exception('Invalid purchase method');
        }

        // Validate user balance
        $this->validateUserBalance($user, $purchaseMethod, $totalCost);

        DB::beginTransaction();

        try {
            // Deduct cost
            $this->deductCost($user, $purchaseMethod, $totalCost);

            $results = [];

            for ($i = 0; $i < $quantity; $i++) {
                $rewards = $this->generateBoxRewards($mysteryBoxType);
                $opening = $this->recordBoxOpening($user, $mysteryBoxType, $purchaseMethod, $cost, $rewards);

                // Grant rewards to user
                $grantedRewards = $this->grantRewards($user, $rewards);

                $results[] = [
                    'opening_id' => $opening->id,
                    'rewards' => $grantedRewards,
                    'contained_rare_item' => $opening->contained_rare_item
                ];
            }

            DB::commit();

            Log::info('Mystery boxes opened', [
                'user_id' => $user->id,
                'box_type' => $boxType,
                'quantity' => $quantity,
                'total_cost' => $totalCost,
                'purchase_method' => $purchaseMethod
            ]);

            return $results;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Mystery box opening failed', [
                'user_id' => $user->id,
                'box_type' => $boxType,
                'quantity' => $quantity,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get user's mystery box statistics
     */
    public function getUserMysteryBoxStats(TelegramUser $user): array
    {
        $totalOpenings = $user->mysteryBoxOpenings()->count();
        $totalSpent = $user->mysteryBoxOpenings()->sum('cost_paid');
        $totalValue = $user->mysteryBoxOpenings()->sum('total_value');
        $rareItemsFound = $user->mysteryBoxOpenings()->withRareItems()->count();

        $byMethod = $user->mysteryBoxOpenings()
            ->selectRaw('purchase_method, COUNT(*) as count, SUM(cost_paid) as total_spent')
            ->groupBy('purchase_method')
            ->get()
            ->map(function($item) {
                return [
                    'method' => $item->purchase_method,
                    'count' => $item->count,
                    'total_spent' => $item->total_spent
                ];
            });

        $byBoxType = $user->mysteryBoxOpenings()
            ->with('mysteryBoxType')
            ->get()
            ->groupBy('box_type')
            ->map(function($openings, $boxType) {
                return [
                    'box_type' => $boxType,
                    'display_name' => $openings->first()->mysteryBoxType->display_name,
                    'count' => $openings->count(),
                    'total_spent' => $openings->sum('cost_paid'),
                    'total_value' => $openings->sum('total_value'),
                    'rare_items' => $openings->where('contained_rare_item', true)->count()
                ];
            });

        $recentOpenings = $user->mysteryBoxOpenings()
            ->where('opened_at', '>=', now()->subWeek())
            ->count();

        return [
            'total_openings' => $totalOpenings,
            'total_spent' => $totalSpent,
            'total_value_received' => $totalValue,
            'net_profit_loss' => $totalValue - $totalSpent,
            'rare_items_found' => $rareItemsFound,
            'success_rate' => $totalOpenings > 0 ? round(($totalValue >= $totalSpent ? 1 : 0) * 100, 1) : 0,
            'recent_openings_this_week' => $recentOpenings,
            'by_purchase_method' => $byMethod,
            'by_box_type' => $byBoxType->values(),
            'average_value_per_opening' => $totalOpenings > 0 ? round($totalValue / $totalOpenings, 2) : 0
        ];
    }

    /**
     * Get available mystery boxes for user
     */
    public function getAvailableMysteryBoxes(TelegramUser $user): array
    {
        $mysteryBoxTypes = MysteryBoxType::active()->purchasable()->ordered()->get();

        return $mysteryBoxTypes->map(function($boxType) use ($user) {
            $isUnlocked = $boxType->isUnlockedBy($user);
            
            return [
                'id' => $boxType->id,
                'box_type' => $boxType->box_type,
                'display_name' => $boxType->display_name,
                'category' => $boxType->category,
                'rarity' => $boxType->rarity,
                'description' => $boxType->description,
                'image_url' => $boxType->image_url,
                'coin_cost' => $boxType->coin_cost,
                'gem_cost' => $boxType->gem_cost,
                'achievement_points_cost' => $boxType->achievement_points_cost,
                'is_unlocked' => $isUnlocked,
                'can_purchase_coins' => $isUnlocked && $boxType->canBePurchasedBy($user, 'coins'),
                'can_purchase_gems' => $isUnlocked && $boxType->canBePurchasedBy($user, 'gems'),
                'can_purchase_achievement_points' => $isUnlocked && $boxType->canBePurchasedBy($user, 'achievement_points'),
                'meets_requirements' => $boxType->meetsUnlockRequirements($user)
            ];
        })->toArray();
    }

    /**
     * Get user's unlocked mystery boxes
     */
    public function getUserUnlockedBoxes(TelegramUser $user): array
    {
        $unlockedBoxes = $user->mysteryBoxUnlocks()
            ->with('mysteryBoxType')
            ->orderBy('unlocked_at', 'desc')
            ->get();

        return $unlockedBoxes->map(function($unlock) {
            return [
                'box_type' => $unlock->box_type,
                'display_name' => $unlock->mysteryBoxType->display_name,
                'category' => $unlock->mysteryBoxType->category,
                'rarity' => $unlock->mysteryBoxType->rarity,
                'image_url' => $unlock->mysteryBoxType->image_url,
                'unlock_source' => $unlock->unlock_source,
                'unlocked_at' => $unlock->unlocked_at,
                'days_unlocked' => $unlock->days_unlocked
            ];
        })->toArray();
    }

    /**
     * Get possible rewards for a mystery box type
     */
    public function getPossibleRewards(MysteryBoxType $mysteryBoxType): array
    {
        $possibleRewards = $mysteryBoxType->possible_rewards ?? [];
        $weights = $mysteryBoxType->reward_weights ?? [];

        $rewardDetails = [];

        foreach ($possibleRewards as $index => $collectibleId) {
            $collectibleTemplate = CollectibleTemplate::where('collectible_id', $collectibleId)->first();

            if ($collectibleTemplate) {
                $weight = $weights[$index] ?? 0;
                $totalWeight = array_sum($weights);
                $probability = $totalWeight > 0 ? round(($weight / $totalWeight) * 100, 1) : 0;

                $rewardDetails[] = [
                    'collectible_id' => $collectibleId,
                    'name' => $collectibleTemplate->name,
                    'type' => $collectibleTemplate->type,
                    'category' => $collectibleTemplate->category,
                    'rarity' => $collectibleTemplate->rarity,
                    'image_url' => $collectibleTemplate->image_url,
                    'estimated_value' => $collectibleTemplate->estimated_value,
                    'probability_percentage' => $probability,
                    'weight' => $weight
                ];
            }
        }

        // Sort by rarity and probability
        usort($rewardDetails, function($a, $b) {
            $rarityOrder = ['common' => 1, 'rare' => 2, 'epic' => 3, 'legendary' => 4, 'mythic' => 5];
            $aRarity = $rarityOrder[$a['rarity']] ?? 0;
            $bRarity = $rarityOrder[$b['rarity']] ?? 0;

            if ($aRarity === $bRarity) {
                return $b['probability_percentage'] <=> $a['probability_percentage'];
            }

            return $aRarity <=> $bRarity;
        });

        return $rewardDetails;
    }

    // Private helper methods

    private function validateUserBalance(TelegramUser $user, string $currency, int $cost): void
    {
        switch ($currency) {
            case 'coins':
                if ($user->balance < $cost) {
                    throw new \Exception('Insufficient coins');
                }
                break;
            case 'gems':
                if (($user->gems ?? 0) < $cost) {
                    throw new \Exception('Insufficient gems');
                }
                break;
            case 'achievement_points':
                if (($user->achievement_points ?? 0) < $cost) {
                    throw new \Exception('Insufficient achievement points');
                }
                break;
            default:
                throw new \Exception('Invalid currency');
        }
    }

    private function deductCost(TelegramUser $user, string $currency, int $cost): void
    {
        switch ($currency) {
            case 'coins':
                $user->decrement('balance', $cost);
                break;
            case 'gems':
                $user->decrement('gems', $cost);
                break;
            case 'achievement_points':
                $user->decrement('achievement_points', $cost);
                break;
            default:
                throw new \Exception('Invalid currency');
        }
    }

    private function generateBoxRewards(MysteryBoxType $mysteryBoxType): array
    {
        $possibleRewards = $mysteryBoxType->possible_rewards ?? [];
        $weights = $mysteryBoxType->reward_weights ?? [];

        if (empty($possibleRewards) || empty($weights)) {
            return [];
        }

        // Generate 1-3 rewards based on rarity
        $numRewards = match($mysteryBoxType->rarity) {
            'common' => 1,
            'rare' => rand(1, 2),
            'epic' => rand(2, 3),
            'legendary' => rand(2, 3),
            'mythic' => 3,
            default => 1
        };

        $rewards = [];
        for ($i = 0; $i < $numRewards; $i++) {
            $selectedReward = $this->selectWeightedReward($possibleRewards, $weights);
            if ($selectedReward && !in_array($selectedReward, $rewards)) {
                $rewards[] = $selectedReward;
            }
        }

        return $rewards;
    }

    private function recordBoxOpening(
        TelegramUser $user,
        MysteryBoxType $mysteryBoxType,
        string $purchaseMethod,
        int $cost,
        array $rewards
    ): MysteryBoxOpening {
        $totalValue = 0;
        $containsRare = false;

        foreach ($rewards as $collectibleId) {
            $collectibleTemplate = CollectibleTemplate::where('collectible_id', $collectibleId)->first();
            if ($collectibleTemplate) {
                $totalValue += $collectibleTemplate->estimated_value;
                if (in_array($collectibleTemplate->rarity, ['epic', 'legendary', 'mythic'])) {
                    $containsRare = true;
                }
            }
        }

        return $mysteryBoxType->openings()->create([
            'telegram_user_id' => $user->id,
            'purchase_method' => $purchaseMethod,
            'cost_paid' => $cost,
            'currency_used' => $purchaseMethod,
            'rewards_received' => $rewards,
            'total_value' => $totalValue,
            'contained_rare_item' => $containsRare,
            'opened_at' => now()
        ]);
    }

    private function grantRewards(TelegramUser $user, array $rewards): array
    {
        $grantedRewards = [];

        foreach ($rewards as $collectibleId) {
            $result = $this->collectibleService->unlockCollectible(
                $user,
                $collectibleId,
                'mystery_box',
                null
            );

            if ($result) {
                $grantedRewards[] = [
                    'collectible_id' => $collectibleId,
                    'name' => $result['template']->name,
                    'type' => $result['template']->type,
                    'rarity' => $result['template']->rarity,
                    'image_url' => $result['template']->image_url,
                    'estimated_value' => $result['template']->estimated_value,
                    'was_new' => true
                ];
            } else {
                // User already owns this collectible, give alternative reward
                $collectibleTemplate = CollectibleTemplate::where('collectible_id', $collectibleId)->first();
                if ($collectibleTemplate) {
                    // Give coins as compensation
                    $compensationCoins = $collectibleTemplate->estimated_value;
                    $user->increment('balance', $compensationCoins);

                    $grantedRewards[] = [
                        'collectible_id' => $collectibleId,
                        'name' => $collectibleTemplate->name,
                        'type' => 'duplicate_compensation',
                        'rarity' => $collectibleTemplate->rarity,
                        'image_url' => $collectibleTemplate->image_url,
                        'estimated_value' => $compensationCoins,
                        'was_new' => false,
                        'compensation_coins' => $compensationCoins
                    ];
                }
            }
        }

        return $grantedRewards;
    }

    private function generateRewards(MysteryBoxType $mysteryBoxType): array
    {
        return $this->generateBoxRewards($mysteryBoxType);
    }

    private function selectWeightedReward(array $rewards, array $weights): ?string
    {
        $totalWeight = array_sum($weights);
        if ($totalWeight <= 0) {
            return $rewards[0] ?? null;
        }
        
        $random = rand(1, $totalWeight);
        
        $currentWeight = 0;
        foreach ($rewards as $index => $reward) {
            $currentWeight += $weights[$index] ?? 0;
            if ($random <= $currentWeight) {
                return $reward;
            }
        }

        return $rewards[0] ?? null;
    }
}
