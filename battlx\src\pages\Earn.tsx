import { ReferralTaskType, TaskType } from "@/types/TaskType";
import { BattlxIcon } from "@/components/icons/BattlxIcon";
import { useMemo, useState } from "react";
import TaskDrawer from "@/components/TaskDrawer";
import ListItem from "@/components/ListItem";
import Price from "@/components/Price";
import DailyDrawer from "@/components/DailyDrawer";
import CheckIcon from "@/components/icons/CheckIcon";
import { useQuery } from "@tanstack/react-query";
import { $http } from "@/lib/http";
import { cn } from "@/lib/utils";
import { uesStore } from "@/store";
import LoadingPage from "@/components/LoadingPage";
import ReferralTaskDrawer from "@/components/ReferralTaskDrawer";

export default function Earn() {
  const { totalDailyRewards } = uesStore();
  const [activeTask, setActiveTask] = useState<TaskType | null>(null);
  const [isTaskDrawerOpen, setIsTaskDrawerOpen] = useState(false);
  const [isDailyDrawerOpen, setIsDailyDrawerOpen] = useState(false);
  const [isReferralTaskDrawerOpen, setIsReferralTaskDrawerOpen] = useState(false);
  const [activeReferralTask, setActiveReferralTask] = useState<ReferralTaskType | null>(null);
  const [activeSection, setActiveSection] = useState<'battlx' | 'partners'>('battlx');

  const { data, isLoading } = useQuery({
    queryKey: ["tasks"],
    queryFn: () => $http.$get<TaskType[]>("/clicker/tasks"),
  });

  const referralTasks = useQuery({
    queryKey: ["referral-tasks"],
    queryFn: () => $http.$get<ReferralTaskType[]>("/clicker/referral-tasks"),
  });

  const videoTasks = useMemo(
    () => data?.filter((task) => task.type === "video") || [],
    [data]
  );

  const otherTasks = useMemo(
    () => data?.filter((task) => task.type === "other") || [],
    [data]
  );

  if (isLoading) return <LoadingPage />;

  return (
    <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1">
      <div className="flex flex-col flex-1 w-full px-6 py-8 pb-24 mt-2 modal-body">
          <div className="flex items-center justify-center">
            <BattlxIcon
              icon="coins"
              className="text-3xl w-9 h-10 text-[#9B8B6C]"
            />
            <span className="text-3xl font-bold font-gothic text-[#9B8B6C] ml-2">
              EARN MORE COINS
            </span>
          </div>

          {/* Main Section Buttons */}
          <div className="flex gap-4 justify-center mt-8">
            <button
              onClick={() => setActiveSection('battlx')}
              className={cn(
                "flex-1 max-w-[160px] text-sm font-medium py-3 transition-all duration-300 rounded-lg border border-[#9B8B6C]/20 flex items-center justify-center gap-2",
                activeSection === 'battlx'
                  ? "bg-[#1A1617] text-[#9B8B6C] shadow-[0_4px_15px_rgba(74,14,14,0.3)] border-[#4A0E0E]"
                  : "text-[#B3B3B3]/60 hover:bg-[#1A1617]/50 hover:border-[#9B8B6C]/50"
              )}
            >
              <BattlxIcon icon="bounty" className="w-5 h-5" />
              BattlX
            </button>
            <button
              onClick={() => setActiveSection('partners')}
              className={cn(
                "flex-1 max-w-[160px] text-sm font-medium py-3 transition-all duration-300 rounded-lg border border-[#9B8B6C]/20 flex items-center justify-center gap-2",
                activeSection === 'partners'
                  ? "bg-[#1A1617] text-[#9B8B6C] shadow-[0_4px_15px_rgba(74,14,14,0.3)] border-[#4A0E0E]"
                  : "text-[#B3B3B3]/60 hover:bg-[#1A1617]/50 hover:border-[#9B8B6C]/50"
              )}
            >
              <BattlxIcon icon="coins" className="w-5 h-5" />
              Partners
            </button>
          </div>

          <div className="mt-6">
            {activeSection === 'battlx' && (
              <div className="space-y-6">
                {/* Daily Tasks */}
                <div>
                  <p className="text-sm font-medium text-center text-[#B3B3B3] mb-3">Daily Tasks</p>
                  <ListItem
                    title="Daily reward"
                    subtitle={
                      <Price
                        amount={`+${Number(totalDailyRewards).toLocaleString()}`}
                      />
                    }
                    icon={<BattlxIcon icon="dailytasks" className="w-full h-full" />}
                    onClick={() => setIsDailyDrawerOpen(true)}
                    className="bg-[#1A1617]/80"
                  />
                </div>

                {/* YouTube Tasks */}
                {videoTasks.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-center text-[#B3B3B3] mb-3">YouTube Tasks</p>
                    <div className="space-y-2">
                      {videoTasks.map((item) => (
                        <ListItem
                          key={item.id}
                          title={item.name}
                          subtitle={
                            <Price amount={`+${item.reward_coins.toLocaleString()}`} />
                          }
                          icon={item.image ? undefined : <BattlxIcon icon="youtube" className="w-full h-full" />}
                          image={item.image}
                          onClick={() => {
                            setActiveTask(item);
                            setIsTaskDrawerOpen(true);
                          }}
                          action={
                            item.is_rewarded ? (
                              <CheckIcon className="w-6 h-6 text-[#9B8B6C]" />
                            ) : undefined
                          }
                          disabled={item.is_rewarded}
                          className="bg-[#1A1617]/80"
                        />
                      ))}
                    </div>
                  </div>
                )}

                {/* BattlX Tasks */}
                {otherTasks.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-center text-[#B3B3B3] mb-3">BattlX Tasks</p>
                    <div className="space-y-2">
                      {otherTasks.map((item) => (
                        <ListItem
                          key={item.id}
                          title={item.name}
                          subtitle={
                            <Price amount={`+${item.reward_coins.toLocaleString()}`} />
                          }
                          icon={item.image ? undefined : <BattlxIcon icon="bounty" className="w-full h-full" />}
                          image={item.image}
                          className={cn(
                            "disabled:opacity-50 disabled:mix-blend-luminosity bg-[#1A1617]/80"
                          )}
                          disabled={item.is_rewarded}
                          action={
                            item.is_rewarded ? (
                              <CheckIcon className="w-6 h-6 text-[#9B8B6C]" />
                            ) : undefined
                          }
                          onClick={() => {
                            setActiveTask(item);
                            setIsTaskDrawerOpen(true);
                          }}
                        />
                      ))}
                    </div>
                  </div>
                )}

                {/* Referral Tasks */}
                {referralTasks.data && referralTasks.data.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-center text-[#B3B3B3] mb-3">Referral Tasks</p>
                    <div className="space-y-2">
                      {referralTasks.data.map((item) => (
                        <ListItem
                          key={item.id}
                          title={item.title}
                          subtitle={
                            <Price amount={`+${item.reward.toLocaleString()}`} />
                          }
                          icon={<BattlxIcon icon="bounty" className="w-full h-full" />}
                          className={cn(
                            "disabled:opacity-50 disabled:mix-blend-luminosity bg-[#1A1617]/80"
                          )}
                          disabled={!!item.is_completed}
                          action={
                            item.is_completed ? (
                              <CheckIcon className="w-6 h-6 text-[#9B8B6C]" />
                            ) : undefined
                          }
                          onClick={() => {
                            setActiveReferralTask(item);
                            setIsReferralTaskDrawerOpen(true);
                          }}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeSection === 'partners' && (
              <div className="flex flex-col items-center justify-center h-[300px]">
                <BattlxIcon
                  icon="coins"
                  className="w-16 h-16 text-[#9B8B6C] opacity-50 mb-4"
                />
                <p className="text-lg font-gothic text-[#9B8B6C] opacity-50">
                  Coming Soon
                </p>
              </div>
            )}
          </div>
      </div>

      <DailyDrawer
        open={isDailyDrawerOpen}
        onOpenChange={setIsDailyDrawerOpen}
      />
      <TaskDrawer
        task={activeTask}
        open={isTaskDrawerOpen}
        onOpenChange={setIsTaskDrawerOpen}
      />
      <ReferralTaskDrawer
        task={activeReferralTask}
        open={isReferralTaskDrawerOpen}
        onOpenChange={setIsReferralTaskDrawerOpen}
      />
    </div>
  );
}
