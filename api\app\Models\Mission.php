<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Mission extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'mission_type_id',
        'image',
        'required_user_level',
        'required_friends_invitation',
        'required_mission_id'
    ];

    public function getImageAttribute($value)
    {
        return $value ? env("APP_STORAGE_URL", "/") . '/storage' . $value : null;
    }

    public function levels()
    {
        return $this->hasMany(MissionLevel::class);
    }

    public function nextLevel()
    {
        return $this->hasOne(MissionLevel::class)
            ->orderBy('level');
    }

    public function type()
    {
        return $this->belongsTo(MissionType::class);
    }

    public function completions()
    {
        return $this->hasMany(MissionCompletion::class);
    }

    public function isCompletedBy($userId)
    {
        return $this->completions()
            ->where('telegram_user_id', $userId)
            ->exists();
    }

    public function requiredMission()
    {
        return $this->belongsTo(Mission::class, 'required_mission_id');
    }
}
