# Collectible Page Components Implementation

## Overview
This document covers the implementation of collectible page components, including collection displays, set management, search functionality, and collection progress tracking.

## Implementation Time: 3-4 days
## Complexity: Medium-High
## Dependencies: Collection system, search functionality

## Main Collection Components

### CollectionPage Component
```tsx
// File: battlx/src/pages/Collection.tsx

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCollectionStore } from '../stores/collectionStore';
import { useUserStore } from '../stores/userStore';
import CollectionGrid from '../components/collection/CollectionGrid';
import CollectionSets from '../components/collection/CollectionSets';
import CollectionSearch from '../components/collection/CollectionSearch';
import CollectionStats from '../components/collection/CollectionStats';
import CollectionFilters from '../components/collection/CollectionFilters';
import LoadingSpinner from '../components/common/LoadingSpinner';

const CollectionPage: React.FC = () => {
  const {
    collectibles,
    collectionSets,
    collectionProgress,
    loading,
    error,
    fetchCollection,
    searchCollectibles
  } = useCollectionStore();

  const { user } = useUserStore();
  
  const [activeTab, setActiveTab] = useState<'grid' | 'sets' | 'stats'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    category: '',
    rarity: '',
    type: '',
    owned: 'all'
  });

  useEffect(() => {
    if (user) {
      fetchCollection();
    }
  }, [user, fetchCollection]);

  useEffect(() => {
    if (searchQuery.length >= 2) {
      searchCollectibles(searchQuery);
    }
  }, [searchQuery, searchCollectibles]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleFiltersChange = (newFilters: any) => {
    setFilters(newFilters);
  };

  if (loading && collectibles.length === 0) {
    return <LoadingSpinner message="Loading your collection..." />;
  }

  return (
    <div className="collection-page">
      {/* Header */}
      <div className="collection-header">
        <div className="header-content">
          <h1>My Collection</h1>
          <p>Discover and manage your collectibles</p>
          
          {/* Quick Stats */}
          <div className="quick-stats">
            <div className="stat-item">
              <span className="stat-value">{collectionProgress?.collectibles?.owned || 0}</span>
              <span className="stat-label">Collectibles</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{collectionProgress?.overall_percentage?.toFixed(1) || 0}%</span>
              <span className="stat-label">Complete</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{collectionSets?.filter(set => set.is_completed)?.length || 0}</span>
              <span className="stat-label">Sets Complete</span>
            </div>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="collection-search-section">
        <CollectionSearch
          onSearch={handleSearch}
          placeholder="Search collectibles by name or description..."
        />
      </div>

      {/* Tab Navigation */}
      <div className="collection-tabs">
        <button
          className={`tab ${activeTab === 'grid' ? 'active' : ''}`}
          onClick={() => setActiveTab('grid')}
        >
          <span className="tab-icon">🎯</span>
          Collection Grid
        </button>
        
        <button
          className={`tab ${activeTab === 'sets' ? 'active' : ''}`}
          onClick={() => setActiveTab('sets')}
        >
          <span className="tab-icon">📚</span>
          Collection Sets
        </button>
        
        <button
          className={`tab ${activeTab === 'stats' ? 'active' : ''}`}
          onClick={() => setActiveTab('stats')}
        >
          <span className="tab-icon">📊</span>
          Statistics
        </button>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        <AnimatePresence mode="wait">
          {activeTab === 'grid' && (
            <motion.div
              key="grid"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <CollectionFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
              />
              
              <CollectionGrid
                collectibles={collectibles}
                filters={filters}
                searchQuery={searchQuery}
              />
            </motion.div>
          )}

          {activeTab === 'sets' && (
            <motion.div
              key="sets"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <CollectionSets sets={collectionSets} />
            </motion.div>
          )}

          {activeTab === 'stats' && (
            <motion.div
              key="stats"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <CollectionStats 
                progress={collectionProgress}
                collectibles={collectibles}
                sets={collectionSets}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default CollectionPage;
```

### CollectionGrid Component
```tsx
// File: battlx/src/components/collection/CollectionGrid.tsx

import React, { useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import CollectibleCard from './CollectibleCard';

interface CollectionGridProps {
  collectibles: any[];
  filters: {
    category: string;
    rarity: string;
    type: string;
    owned: string;
  };
  searchQuery: string;
}

const CollectionGrid: React.FC<CollectionGridProps> = ({
  collectibles,
  filters,
  searchQuery
}) => {
  const filteredCollectibles = useMemo(() => {
    return collectibles.filter(collectible => {
      // Search filter
      if (searchQuery && !collectible.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !collectible.description.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // Category filter
      if (filters.category && collectible.category !== filters.category) {
        return false;
      }

      // Rarity filter
      if (filters.rarity && collectible.rarity !== filters.rarity) {
        return false;
      }

      // Type filter
      if (filters.type && collectible.type !== filters.type) {
        return false;
      }

      // Owned filter
      if (filters.owned === 'owned' && !collectible.is_owned) {
        return false;
      }
      if (filters.owned === 'missing' && collectible.is_owned) {
        return false;
      }

      return true;
    });
  }, [collectibles, filters, searchQuery]);

  const sortedCollectibles = useMemo(() => {
    return [...filteredCollectibles].sort((a, b) => {
      // Sort by collection set first
      if (a.collection_set_id !== b.collection_set_id) {
        return a.collection_set_id.localeCompare(b.collection_set_id);
      }
      
      // Then by position within set
      if (a.set_position !== b.set_position) {
        return a.set_position - b.set_position;
      }
      
      // Finally by name
      return a.name.localeCompare(b.name);
    });
  }, [filteredCollectibles]);

  return (
    <div className="collection-grid">
      {/* Results Summary */}
      <div className="grid-summary">
        <span className="results-count">
          {filteredCollectibles.length} collectible{filteredCollectibles.length !== 1 ? 's' : ''}
        </span>
        
        {searchQuery && (
          <span className="search-info">
            for "{searchQuery}"
          </span>
        )}
        
        <div className="owned-summary">
          {filteredCollectibles.filter(c => c.is_owned).length} owned
        </div>
      </div>

      {/* Grid */}
      <div className="collectibles-grid">
        <AnimatePresence mode="popLayout">
          {sortedCollectibles.map((collectible, index) => (
            <motion.div
              key={collectible.id}
              layout
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ 
                duration: 0.3,
                delay: index * 0.02
              }}
            >
              <CollectibleCard collectible={collectible} />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {filteredCollectibles.length === 0 && (
        <div className="empty-grid">
          <div className="empty-icon">🔍</div>
          <h3>No collectibles found</h3>
          <p>Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  );
};

export default CollectionGrid;
```

### CollectibleCard Component
```tsx
// File: battlx/src/components/collection/CollectibleCard.tsx

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { getRarityColor, getRarityGradient } from '../../utils/rarityUtils';
import CollectibleModal from './CollectibleModal';

interface CollectibleCardProps {
  collectible: {
    id: string;
    name: string;
    type: string;
    rarity: string;
    category: string;
    description: string;
    image_url: string;
    collection_set_id: string;
    set_position: number;
    is_owned: boolean;
    unlock_source: string;
    unlock_requirement?: string;
    obtained_at?: string;
  };
}

const CollectibleCard: React.FC<CollectibleCardProps> = ({ collectible }) => {
  const [showModal, setShowModal] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const rarityColor = getRarityColor(collectible.rarity);
  const rarityGradient = getRarityGradient(collectible.rarity);

  const handleCardClick = () => {
    setShowModal(true);
  };

  return (
    <>
      <motion.div
        className={`collectible-card ${collectible.is_owned ? 'owned' : 'not-owned'}`}
        onClick={handleCardClick}
        whileHover={{ scale: 1.02, y: -2 }}
        whileTap={{ scale: 0.98 }}
        style={{
          background: collectible.is_owned ? rarityGradient : 'rgba(0,0,0,0.3)',
          borderColor: rarityColor,
        }}
      >
        {/* Rarity Border */}
        <div 
          className="rarity-border"
          style={{ borderColor: rarityColor }}
        />

        {/* Collectible Image */}
        <div className="collectible-image-container">
          <motion.img
            src={collectible.image_url}
            alt={collectible.name}
            className={`collectible-image ${imageLoaded ? 'loaded' : ''}`}
            onLoad={() => setImageLoaded(true)}
            initial={{ opacity: 0 }}
            animate={{ opacity: imageLoaded ? 1 : 0 }}
            transition={{ duration: 0.3 }}
          />
          
          {!imageLoaded && (
            <div className="image-placeholder">
              <div className="loading-spinner" />
            </div>
          )}

          {/* Owned Badge */}
          {collectible.is_owned && (
            <div className="owned-badge">
              <span>✓</span>
            </div>
          )}

          {/* Set Position */}
          <div className="set-position">
            #{collectible.set_position}
          </div>
        </div>

        {/* Collectible Info */}
        <div className="collectible-info">
          <h3 className="collectible-name">{collectible.name}</h3>
          
          <div className="collectible-meta">
            <span className="collectible-type">{collectible.type}</span>
            <span className="collectible-category">{collectible.category}</span>
          </div>
          
          <div className="rarity-indicator">
            <span 
              className={`rarity-text rarity-${collectible.rarity}`}
              style={{ color: rarityColor }}
            >
              {collectible.rarity.toUpperCase()}
            </span>
          </div>

          {/* Status */}
          <div className="collectible-status">
            {collectible.is_owned ? (
              <div className="status-owned">
                <span className="status-icon">✅</span>
                <span className="status-text">Owned</span>
                {collectible.obtained_at && (
                  <div className="obtained-date">
                    {new Date(collectible.obtained_at).toLocaleDateString()}
                  </div>
                )}
              </div>
            ) : (
              <div className="status-missing">
                <span className="status-icon">🔒</span>
                <span className="status-text">Not Owned</span>
                {collectible.unlock_requirement && (
                  <div className="unlock-hint">
                    {collectible.unlock_requirement}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Unlock Source */}
          <div className="unlock-source">
            <span className="source-label">Source:</span>
            <span className="source-value">
              {collectible.unlock_source.replace('_', ' ').toUpperCase()}
            </span>
          </div>
        </div>

        {/* Hover Effect */}
        <motion.div
          className="hover-effect"
          initial={{ opacity: 0 }}
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        >
          <span>Click for details</span>
        </motion.div>
      </motion.div>

      {/* Collectible Modal */}
      {showModal && (
        <CollectibleModal
          collectible={collectible}
          isOpen={showModal}
          onClose={() => setShowModal(false)}
        />
      )}
    </>
  );
};

export default CollectibleCard;
```

### CollectionSets Component
```tsx
// File: battlx/src/components/collection/CollectionSets.tsx

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import CollectionSetCard from './CollectionSetCard';
import CollectionSetModal from './CollectionSetModal';

interface CollectionSetsProps {
  sets: any[];
}

const CollectionSets: React.FC<CollectionSetsProps> = ({ sets }) => {
  const [selectedSet, setSelectedSet] = useState<any>(null);
  const [showModal, setShowModal] = useState(false);
  const [filter, setFilter] = useState<'all' | 'completed' | 'incomplete'>('all');

  const filteredSets = sets.filter(set => {
    switch (filter) {
      case 'completed':
        return set.is_completed;
      case 'incomplete':
        return !set.is_completed;
      default:
        return true;
    }
  });

  const handleSetClick = (set: any) => {
    setSelectedSet(set);
    setShowModal(true);
  };

  return (
    <div className="collection-sets">
      {/* Sets Header */}
      <div className="sets-header">
        <h2>Collection Sets</h2>
        <p>Complete sets to earn special rewards!</p>
        
        {/* Filter Buttons */}
        <div className="sets-filters">
          <button
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            All Sets ({sets.length})
          </button>
          <button
            className={`filter-btn ${filter === 'completed' ? 'active' : ''}`}
            onClick={() => setFilter('completed')}
          >
            Completed ({sets.filter(s => s.is_completed).length})
          </button>
          <button
            className={`filter-btn ${filter === 'incomplete' ? 'active' : ''}`}
            onClick={() => setFilter('incomplete')}
          >
            In Progress ({sets.filter(s => !s.is_completed).length})
          </button>
        </div>
      </div>

      {/* Sets Grid */}
      <div className="sets-grid">
        <AnimatePresence mode="popLayout">
          {filteredSets.map((set, index) => (
            <motion.div
              key={set.set_id}
              layout
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <CollectionSetCard
                set={set}
                onClick={() => handleSetClick(set)}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Empty State */}
      {filteredSets.length === 0 && (
        <div className="empty-sets">
          <div className="empty-icon">📚</div>
          <h3>No sets found</h3>
          <p>Try adjusting your filter</p>
        </div>
      )}

      {/* Set Modal */}
      <AnimatePresence>
        {showModal && selectedSet && (
          <CollectionSetModal
            set={selectedSet}
            isOpen={showModal}
            onClose={() => setShowModal(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default CollectionSets;
```

### CollectionSetCard Component
```tsx
// File: battlx/src/components/collection/CollectionSetCard.tsx

import React from 'react';
import { motion } from 'framer-motion';

interface CollectionSetCardProps {
  set: {
    set_id: string;
    name: string;
    category: string;
    description: string;
    icon_url: string;
    total_collectibles: number;
    owned_collectibles: number;
    completion_percentage: number;
    is_completed: boolean;
    rewards_claimed: boolean;
    completion_rewards?: any[];
  };
  onClick: () => void;
}

const CollectionSetCard: React.FC<CollectionSetCardProps> = ({ set, onClick }) => {
  return (
    <motion.div
      className={`collection-set-card ${set.is_completed ? 'completed' : 'incomplete'}`}
      onClick={onClick}
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
    >
      {/* Completion Badge */}
      {set.is_completed && (
        <div className="completion-badge">
          {set.rewards_claimed ? '🏆' : '⭐'}
        </div>
      )}

      {/* Set Icon */}
      <div className="set-icon-container">
        <img 
          src={set.icon_url} 
          alt={set.name}
          className="set-icon"
        />
      </div>

      {/* Set Info */}
      <div className="set-info">
        <h3 className="set-name">{set.name}</h3>
        <div className="set-category">{set.category}</div>
        <p className="set-description">{set.description}</p>

        {/* Progress */}
        <div className="set-progress">
          <div className="progress-text">
            {set.owned_collectibles}/{set.total_collectibles} collected
          </div>
          
          <div className="progress-bar">
            <motion.div
              className="progress-fill"
              initial={{ width: 0 }}
              animate={{ width: `${set.completion_percentage}%` }}
              transition={{ duration: 0.8, delay: 0.2 }}
              style={{
                backgroundColor: set.is_completed ? '#4ade80' : '#3b82f6'
              }}
            />
            <span className="progress-percentage">
              {set.completion_percentage.toFixed(1)}%
            </span>
          </div>
        </div>

        {/* Status */}
        <div className="set-status">
          {set.is_completed ? (
            set.rewards_claimed ? (
              <span className="status-claimed">✅ Rewards Claimed</span>
            ) : (
              <span className="status-ready">🎁 Ready to Claim!</span>
            )
          ) : (
            <span className="status-progress">
              📈 {set.total_collectibles - set.owned_collectibles} remaining
            </span>
          )}
        </div>

        {/* Rewards Preview */}
        {set.completion_rewards && set.completion_rewards.length > 0 && (
          <div className="rewards-preview">
            <span className="rewards-label">Rewards:</span>
            <div className="rewards-list">
              {set.completion_rewards.slice(0, 3).map((reward, index) => (
                <span key={index} className="reward-item">
                  {reward.type === 'coins' ? '🪙' : 
                   reward.type === 'gems' ? '💎' : '🎁'} {reward.amount || reward.name}
                </span>
              ))}
              {set.completion_rewards.length > 3 && (
                <span className="more-rewards">+{set.completion_rewards.length - 3} more</span>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Hover Effect */}
      <motion.div
        className="hover-effect"
        initial={{ opacity: 0 }}
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.2 }}
      >
        <span>View Details</span>
      </motion.div>
    </motion.div>
  );
};

export default CollectionSetCard;
```

### CollectionSearch Component
```tsx
// File: battlx/src/components/collection/CollectionSearch.tsx

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface CollectionSearchProps {
  onSearch: (query: string) => void;
  placeholder?: string;
}

const CollectionSearch: React.FC<CollectionSearchProps> = ({
  onSearch,
  placeholder = "Search collectibles..."
}) => {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      onSearch(query);
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [query, onSearch]);

  const handleClear = () => {
    setQuery('');
    inputRef.current?.focus();
  };

  return (
    <div className="collection-search">
      <motion.div
        className={`search-container ${isFocused ? 'focused' : ''}`}
        animate={{
          scale: isFocused ? 1.02 : 1,
          borderColor: isFocused ? '#3b82f6' : '#444'
        }}
        transition={{ duration: 0.2 }}
      >
        <div className="search-icon">🔍</div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          className="search-input"
        />

        <AnimatePresence>
          {query && (
            <motion.button
              className="clear-button"
              onClick={handleClear}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{ duration: 0.2 }}
            >
              ✕
            </motion.button>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Search Suggestions */}
      <AnimatePresence>
        {isFocused && query.length === 0 && (
          <motion.div
            className="search-suggestions"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            <div className="suggestion-item" onClick={() => setQuery('legendary')}>
              🔍 legendary
            </div>
            <div className="suggestion-item" onClick={() => setQuery('shadow')}>
              🔍 shadow
            </div>
            <div className="suggestion-item" onClick={() => setQuery('artifact')}>
              🔍 artifact
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CollectionSearch;
```

## Acceptance Criteria
- [ ] Collection page displays all collectibles correctly
- [ ] Search functionality works with real-time filtering
- [ ] Collection sets show proper completion status
- [ ] Collectible cards display accurate information
- [ ] Modal views provide detailed collectible information
- [ ] Filtering system functional across all categories
- [ ] Mobile-responsive design implemented

## Next Steps
1. Create state management integration
2. Implement comprehensive testing
3. Add performance optimizations
4. Create deployment documentation

## Troubleshooting
- Test search performance with large collections
- Verify collection progress calculations
- Check image loading and fallbacks
- Test modal interactions on mobile
- Ensure proper data synchronization with backend
