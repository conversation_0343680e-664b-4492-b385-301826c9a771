<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('collection_sets', function (Blueprint $table) {
            $table->id();
            $table->string('set_id', 50)->unique(); // 'shadow_collection', 'undead_collection'
            $table->string('name', 100);
            $table->enum('category', ['shadow', 'undead', 'demon', 'spirit', 'beast']);
            $table->text('description');
            $table->string('icon_url');
            
            // Set completion rewards
            $table->json('completion_rewards'); // Coins, gems, exclusive collectibles
            $table->string('bonus_mystery_box_type')->nullable(); // Exclusive box unlocked
            
            // Set configuration
            $table->integer('total_collectibles'); // How many items in this set
            $table->integer('required_for_completion'); // May be less than total for partial sets
            
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index('category');
            $table->index('is_active');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('collection_sets');
    }
};
