import React, { useEffect, useRef } from 'react';
import { useSpring, animated } from 'react-spring';
import { useComboStore } from '@/store/combo-store';

export const ComboEffects: React.FC = () => {
  const { comboMultiplier, comboLevel, comboActive } = useComboStore();
  const containerRef = useRef<HTMLDivElement>(null);
  const lastMultiplierRef = useRef<number>(1);
  
  // Screen shake effect - always define the hook regardless of comboActive state
  const [shakeProps, shakeApi] = useSpring(() => ({
    x: 0,
    y: 0,
    config: { tension: 300, friction: 10 }
  }));
  
  // Trigger screen shake on multiplier change
  useEffect(() => {
    if (comboActive && comboMultiplier > 1 && comboMultiplier !== lastMultiplierRef.current) {
      lastMultiplierRef.current = comboMultiplier;
      
      const intensity = Math.min(10, comboMultiplier / 10);
      
      // Random shake direction
      const shakeX = (Math.random() - 0.5) * intensity;
      const shakeY = (Math.random() - 0.5) * intensity;
      
      shakeApi.start({
        from: { x: 0, y: 0 },
        to: [
          { x: shakeX, y: shakeY },
          { x: -shakeX/2, y: -shakeY/2 },
          { x: 0, y: 0 }
        ]
      });
      
      // Create particles on multiplier change
      createParticles();
    }
  }, [comboMultiplier, comboActive, shakeApi]);
  
  // Encouragement text based on combo level
  const getEncouragementText = () => {
    switch (comboLevel) {
      case 'legendary': return 'LEGENDARY!';
      case 'extreme': return 'UNSTOPPABLE!';
      case 'high': return 'AMAZING!';
      case 'medium': return 'GREAT!';
      case 'low': 
      default: return 'COMBO!';
    }
  };
  
  // Create particles
  const createParticles = () => {
    if (!containerRef.current) return;
    
    const particleCount = Math.min(20, comboMultiplier);
    const container = containerRef.current;
    
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'combo-particle';
      
      // Random position
      const angle = Math.random() * Math.PI * 2;
      const distance = 50 + Math.random() * 100;
      const x = Math.cos(angle) * distance;
      const y = Math.sin(angle) * distance;
      
      // Random size
      const size = 5 + Math.random() * 10;
      
      // Style
      particle.style.position = 'absolute';
      particle.style.left = '50%';
      particle.style.top = '50%';
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      particle.style.borderRadius = '50%';
      particle.style.backgroundColor = getComboColor();
      particle.style.transform = `translate(${x}px, ${y}px)`;
      particle.style.opacity = '1';
      particle.style.transition = 'all 1s ease-out';
      
      container.appendChild(particle);
      
      // Animate and remove
      setTimeout(() => {
        particle.style.transform = `translate(${x*2}px, ${y*2}px)`;
        particle.style.opacity = '0';
      }, 10);
      
      setTimeout(() => {
        if (container.contains(particle)) {
          container.removeChild(particle);
        }
      }, 1000);
    }
  };
  
  // Get color based on combo level
  const getComboColor = () => {
    switch (comboLevel) {
      case 'legendary': return '#FFD700'; // Gold
      case 'extreme': return '#FF4500'; // Red-Orange
      case 'high': return '#FF1493'; // Deep Pink
      case 'medium': return '#9370DB'; // Medium Purple
      case 'low': 
      default: return '#4169E1'; // Royal Blue
    }
  };
  
  // Skip rendering if combo not active
  if (!comboActive) return null;
  
  return (
    <animated.div
      ref={containerRef}
      className="combo-effects-container"
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 90,
        transform: shakeProps.x.to((x, y) => `translate(${x}px, ${y}px)`)
      }}
    >
      {/* Encouragement Text */}
      <animated.div
        className="combo-encouragement"
        style={{
          position: 'absolute',
          top: '30%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: getComboColor(),
          textShadow: `0 0 10px ${getComboColor()}`,
          fontWeight: 'bold',
          fontSize: '3rem',
          opacity: 1,
          transition: 'opacity 0.5s ease-out'
        }}
      >
        {getEncouragementText()}
      </animated.div>
    </animated.div>
  );
};

export default ComboEffects;