<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PrizeTree extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'icon',
        'theme_color',
        'display_order',
        'is_active',
        'is_seasonal',
        'available_until'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_seasonal' => 'boolean',
        'available_until' => 'datetime'
    ];

    /**
     * Get the prizes for this tree.
     */
    public function prizes()
    {
        return $this->hasMany(Prize::class);
    }

    /**
     * Get the root prizes for this tree.
     */
    public function rootPrizes()
    {
        return $this->hasMany(Prize::class)->where('is_root', true);
    }
    
    /**
     * Check if the tree is currently available.
     */
    public function isAvailable()
    {
        if (!$this->is_active) {
            return false;
        }
        
        if ($this->available_until && now()->gt($this->available_until)) {
            return false;
        }
        
        return true;
    }
}
