# Background Tasks Implementation

## Overview
This document covers the implementation of background task processing for the Pet System, including queue management, task monitoring, and performance optimization.

## Implementation Time: 2-3 days
## Complexity: Medium-High
## Dependencies: Queue system, scheduled jobs

## Queue Configuration

### Queue Database Migration
```php
<?php
// File: api/database/migrations/2024_01_01_000020_create_jobs_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('jobs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('queue')->index();
            $table->longText('payload');
            $table->unsignedTinyInteger('attempts');
            $table->unsignedInteger('reserved_at')->nullable();
            $table->unsignedInteger('available_at');
            $table->unsignedInteger('created_at');
        });

        Schema::create('failed_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->unique();
            $table->text('connection');
            $table->text('queue');
            $table->longText('payload');
            $table->longText('exception');
            $table->timestamp('failed_at')->useCurrent();
        });

        Schema::create('job_batches', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name');
            $table->integer('total_jobs');
            $table->integer('pending_jobs');
            $table->integer('failed_jobs');
            $table->longText('failed_job_ids');
            $table->mediumText('options')->nullable();
            $table->integer('cancelled_at')->nullable();
            $table->integer('created_at');
            $table->integer('finished_at')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('job_batches');
        Schema::dropIfExists('failed_jobs');
        Schema::dropIfExists('jobs');
    }
};
```

## Pet System Background Jobs

### ProcessPetInteractionRewards Job
```php
<?php
// File: api/app/Jobs/ProcessPetInteractionRewards.php

namespace App\Jobs;

use App\Models\Pet;
use App\Models\TelegramUser;
use App\Services\PetService;
use App\Services\CollectibleService;
use App\Events\PetInteracted;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessPetInteractionRewards implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 120;
    public $tries = 3;
    public $backoff = [10, 30, 60];

    protected Pet $pet;
    protected TelegramUser $user;
    protected string $interactionType;
    protected array $baseRewards;

    public function __construct(Pet $pet, TelegramUser $user, string $interactionType, array $baseRewards)
    {
        $this->pet = $pet;
        $this->user = $user;
        $this->interactionType = $interactionType;
        $this->baseRewards = $baseRewards;
        
        $this->onQueue('pet-interactions');
    }

    public function handle(PetService $petService, CollectibleService $collectibleService): void
    {
        try {
            Log::info('Processing pet interaction rewards', [
                'pet_id' => $this->pet->id,
                'user_id' => $this->user->id,
                'interaction_type' => $this->interactionType
            ]);

            // Calculate enhanced rewards with bonuses
            $enhancedRewards = $petService->calculateInteractionRewards(
                $this->pet,
                $this->interactionType
            );

            // Process collectible rewards if any
            if (!empty($enhancedRewards['collectible'])) {
                $collectibleResult = $collectibleService->unlockCollectible(
                    $this->user,
                    $enhancedRewards['collectible'],
                    'pet_interaction',
                    $this->pet->id
                );

                if ($collectibleResult) {
                    $enhancedRewards['collectible_unlocked'] = $collectibleResult;
                }
            }

            // Check for level up and evolution
            $leveledUp = $this->checkLevelUp();
            $evolved = $this->checkEvolution();

            // Create interaction record
            $interaction = $this->pet->interactions()->create([
                'telegram_user_id' => $this->user->id,
                'interaction_type' => $this->interactionType,
                'energy_cost' => $this->baseRewards['energy_cost'],
                'happiness_gained' => $enhancedRewards['happiness'],
                'experience_gained' => $enhancedRewards['experience'],
                'coins_rewarded' => $enhancedRewards['coins'],
                'materials_rewarded' => $enhancedRewards['materials'],
                'collectible_rewarded' => $enhancedRewards['collectible'] ?? null,
                'bonus_applied' => $enhancedRewards['multiplier_applied'] > 1,
                'interaction_time' => now()
            ]);

            // Fire event for listeners
            event(new PetInteracted(
                $this->pet,
                $this->user,
                $interaction,
                $enhancedRewards,
                $leveledUp,
                $evolved
            ));

            Log::info('Pet interaction rewards processed successfully', [
                'pet_id' => $this->pet->id,
                'interaction_id' => $interaction->id,
                'rewards' => $enhancedRewards
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process pet interaction rewards', [
                'pet_id' => $this->pet->id,
                'user_id' => $this->user->id,
                'interaction_type' => $this->interactionType,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    private function checkLevelUp(): bool
    {
        $oldLevel = $this->pet->level;
        
        // Refresh pet to get latest data
        $this->pet->refresh();
        
        return $this->pet->level > $oldLevel;
    }

    private function checkEvolution(): bool
    {
        $oldStage = $this->pet->evolution_stage;
        
        // Refresh pet to get latest data
        $this->pet->refresh();
        
        return $this->pet->evolution_stage > $oldStage;
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Pet interaction rewards job failed permanently', [
            'pet_id' => $this->pet->id,
            'user_id' => $this->user->id,
            'interaction_type' => $this->interactionType,
            'error' => $exception->getMessage()
        ]);
    }
}
```

### ProcessMysteryBoxRewards Job
```php
<?php
// File: api/app/Jobs/ProcessMysteryBoxRewards.php

namespace App\Jobs;

use App\Models\MysteryBoxType;
use App\Models\TelegramUser;
use App\Services\MysteryBoxService;
use App\Services\CollectibleService;
use App\Events\MysteryBoxOpened;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessMysteryBoxRewards implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 180;
    public $tries = 3;
    public $backoff = [15, 45, 90];

    protected MysteryBoxType $mysteryBoxType;
    protected TelegramUser $user;
    protected string $purchaseMethod;
    protected int $cost;
    protected int $quantity;

    public function __construct(
        MysteryBoxType $mysteryBoxType,
        TelegramUser $user,
        string $purchaseMethod,
        int $cost,
        int $quantity = 1
    ) {
        $this->mysteryBoxType = $mysteryBoxType;
        $this->user = $user;
        $this->purchaseMethod = $purchaseMethod;
        $this->cost = $cost;
        $this->quantity = $quantity;
        
        $this->onQueue('mystery-boxes');
    }

    public function handle(MysteryBoxService $mysteryBoxService, CollectibleService $collectibleService): void
    {
        try {
            Log::info('Processing mystery box rewards', [
                'box_type' => $this->mysteryBoxType->box_type,
                'user_id' => $this->user->id,
                'quantity' => $this->quantity
            ]);

            $results = [];

            for ($i = 0; $i < $this->quantity; $i++) {
                // Generate rewards for this box
                $rewards = $this->mysteryBoxType->generateRewards();
                
                // Record the opening
                $opening = $this->mysteryBoxService->recordBoxOpening(
                    $this->user,
                    $this->mysteryBoxType,
                    $this->purchaseMethod,
                    $this->cost,
                    $rewards
                );

                // Grant rewards to user
                $grantedRewards = [];
                foreach ($rewards as $rewardId) {
                    $result = $collectibleService->unlockCollectible(
                        $this->user,
                        $rewardId,
                        'mystery_box',
                        $opening->id
                    );

                    if ($result) {
                        $grantedRewards[] = $result;
                    }
                }

                $results[] = [
                    'opening_id' => $opening->id,
                    'rewards' => $grantedRewards,
                    'contained_rare_item' => $opening->contained_rare_item
                ];

                // Fire event for this opening
                event(new MysteryBoxOpened(
                    $opening,
                    $this->mysteryBoxType,
                    $this->user,
                    $grantedRewards,
                    $opening->contained_rare_item
                ));
            }

            Log::info('Mystery box rewards processed successfully', [
                'box_type' => $this->mysteryBoxType->box_type,
                'user_id' => $this->user->id,
                'quantity' => $this->quantity,
                'results_count' => count($results)
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process mystery box rewards', [
                'box_type' => $this->mysteryBoxType->box_type,
                'user_id' => $this->user->id,
                'quantity' => $this->quantity,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Mystery box rewards job failed permanently', [
            'box_type' => $this->mysteryBoxType->box_type,
            'user_id' => $this->user->id,
            'quantity' => $this->quantity,
            'error' => $exception->getMessage()
        ]);
    }
}
```

### UpdateCollectionProgressBatch Job
```php
<?php
// File: api/app/Jobs/UpdateCollectionProgressBatch.php

namespace App\Jobs;

use App\Models\TelegramUser;
use App\Services\CollectibleService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateCollectionProgressBatch implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300;
    public $tries = 2;

    protected array $userIds;

    public function __construct(array $userIds)
    {
        $this->userIds = $userIds;
        $this->onQueue('collection-updates');
    }

    public function handle(CollectibleService $collectibleService): void
    {
        if ($this->batch()->cancelled()) {
            return;
        }

        try {
            Log::info('Processing collection progress batch', [
                'user_count' => count($this->userIds),
                'batch_id' => $this->batch()->id
            ]);

            $processed = 0;
            $errors = 0;

            foreach ($this->userIds as $userId) {
                try {
                    $user = TelegramUser::find($userId);
                    
                    if ($user) {
                        $collectibleService->updateAllCollectionProgress($user);
                        $processed++;
                    }

                } catch (\Exception $e) {
                    $errors++;
                    Log::error('Failed to update collection progress for user', [
                        'user_id' => $userId,
                        'error' => $e->getMessage()
                    ]);
                }

                // Check if batch was cancelled
                if ($this->batch()->cancelled()) {
                    break;
                }
            }

            Log::info('Collection progress batch completed', [
                'processed' => $processed,
                'errors' => $errors,
                'batch_id' => $this->batch()->id
            ]);

        } catch (\Exception $e) {
            Log::error('Collection progress batch failed', [
                'user_count' => count($this->userIds),
                'batch_id' => $this->batch()->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }
}
```

## Queue Management Service

### QueueManagementService
```php
<?php
// File: api/app/Services/QueueManagementService.php

namespace App\Services;

use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class QueueManagementService
{
    /**
     * Get queue statistics
     */
    public function getQueueStatistics(): array
    {
        $stats = [
            'pending_jobs' => $this->getPendingJobsCount(),
            'failed_jobs' => $this->getFailedJobsCount(),
            'jobs_by_queue' => $this->getJobsByQueue(),
            'recent_failures' => $this->getRecentFailures(),
            'queue_health' => $this->getQueueHealth(),
            'worker_status' => $this->getWorkerStatus()
        ];

        return $stats;
    }

    /**
     * Get pending jobs count
     */
    public function getPendingJobsCount(): int
    {
        return DB::table('jobs')->count();
    }

    /**
     * Get failed jobs count
     */
    public function getFailedJobsCount(): int
    {
        return DB::table('failed_jobs')->count();
    }

    /**
     * Get jobs grouped by queue
     */
    public function getJobsByQueue(): array
    {
        return DB::table('jobs')
                ->selectRaw('queue, COUNT(*) as count')
                ->groupBy('queue')
                ->pluck('count', 'queue')
                ->toArray();
    }

    /**
     * Get recent job failures
     */
    public function getRecentFailures(int $limit = 10): array
    {
        return DB::table('failed_jobs')
                ->orderBy('failed_at', 'desc')
                ->limit($limit)
                ->get()
                ->map(function($failure) {
                    $payload = json_decode($failure->payload, true);
                    
                    return [
                        'id' => $failure->id,
                        'uuid' => $failure->uuid,
                        'queue' => $failure->queue,
                        'job_class' => $payload['displayName'] ?? 'Unknown',
                        'failed_at' => $failure->failed_at,
                        'exception' => substr($failure->exception, 0, 500) . '...'
                    ];
                })
                ->toArray();
    }

    /**
     * Get queue health status
     */
    public function getQueueHealth(): array
    {
        $pendingJobs = $this->getPendingJobsCount();
        $failedJobs = $this->getFailedJobsCount();
        
        $health = 'healthy';
        $issues = [];

        // Check for high pending job count
        if ($pendingJobs > 1000) {
            $health = 'warning';
            $issues[] = "High pending job count: {$pendingJobs}";
        }

        if ($pendingJobs > 5000) {
            $health = 'critical';
        }

        // Check for high failure rate
        if ($failedJobs > 100) {
            $health = $health === 'critical' ? 'critical' : 'warning';
            $issues[] = "High failed job count: {$failedJobs}";
        }

        // Check for stuck jobs (jobs older than 1 hour)
        $stuckJobs = DB::table('jobs')
                      ->where('created_at', '<', now()->subHour()->timestamp)
                      ->count();

        if ($stuckJobs > 0) {
            $health = $health === 'critical' ? 'critical' : 'warning';
            $issues[] = "Stuck jobs detected: {$stuckJobs}";
        }

        return [
            'status' => $health,
            'issues' => $issues,
            'pending_jobs' => $pendingJobs,
            'failed_jobs' => $failedJobs,
            'stuck_jobs' => $stuckJobs
        ];
    }

    /**
     * Get worker status
     */
    public function getWorkerStatus(): array
    {
        // This would typically check if queue workers are running
        // Implementation depends on your deployment setup
        
        $workers = [
            'pet-interactions' => $this->checkWorkerQueue('pet-interactions'),
            'mystery-boxes' => $this->checkWorkerQueue('mystery-boxes'),
            'collection-updates' => $this->checkWorkerQueue('collection-updates'),
            'notifications' => $this->checkWorkerQueue('notifications'),
            'default' => $this->checkWorkerQueue('default')
        ];

        return $workers;
    }

    /**
     * Retry failed jobs
     */
    public function retryFailedJobs(array $jobIds = []): int
    {
        if (empty($jobIds)) {
            // Retry all failed jobs
            $retried = DB::table('failed_jobs')->count();
            Queue::retry('all');
        } else {
            // Retry specific jobs
            $retried = count($jobIds);
            foreach ($jobIds as $jobId) {
                Queue::retry($jobId);
            }
        }

        Log::info('Failed jobs retried', [
            'count' => $retried,
            'job_ids' => $jobIds
        ]);

        return $retried;
    }

    /**
     * Clear failed jobs
     */
    public function clearFailedJobs(array $jobIds = []): int
    {
        if (empty($jobIds)) {
            // Clear all failed jobs
            $cleared = DB::table('failed_jobs')->count();
            DB::table('failed_jobs')->truncate();
        } else {
            // Clear specific jobs
            $cleared = DB::table('failed_jobs')
                        ->whereIn('id', $jobIds)
                        ->delete();
        }

        Log::info('Failed jobs cleared', [
            'count' => $cleared,
            'job_ids' => $jobIds
        ]);

        return $cleared;
    }

    /**
     * Purge old jobs
     */
    public function purgeOldJobs(int $daysOld = 7): int
    {
        $cutoffDate = now()->subDays($daysOld);
        
        $purged = DB::table('failed_jobs')
                   ->where('failed_at', '<', $cutoffDate)
                   ->delete();

        Log::info('Old failed jobs purged', [
            'count' => $purged,
            'cutoff_date' => $cutoffDate
        ]);

        return $purged;
    }

    /**
     * Monitor queue performance
     */
    public function monitorQueuePerformance(): array
    {
        $cacheKey = 'queue_performance_metrics';
        
        return Cache::remember($cacheKey, 300, function() {
            $metrics = [
                'average_job_duration' => $this->calculateAverageJobDuration(),
                'jobs_per_minute' => $this->calculateJobsPerMinute(),
                'failure_rate' => $this->calculateFailureRate(),
                'queue_throughput' => $this->calculateQueueThroughput()
            ];

            return $metrics;
        });
    }

    private function checkWorkerQueue(string $queueName): array
    {
        // Check if there are recent job completions for this queue
        $recentJobs = DB::table('jobs')
                       ->where('queue', $queueName)
                       ->where('created_at', '>', now()->subMinutes(5)->timestamp)
                       ->count();

        $pendingJobs = DB::table('jobs')
                        ->where('queue', $queueName)
                        ->count();

        $status = 'unknown';
        
        if ($pendingJobs === 0) {
            $status = 'idle';
        } elseif ($recentJobs > 0) {
            $status = 'active';
        } else {
            $status = 'stalled';
        }

        return [
            'status' => $status,
            'pending_jobs' => $pendingJobs,
            'recent_activity' => $recentJobs > 0
        ];
    }

    private function calculateAverageJobDuration(): float
    {
        // This would require additional logging/metrics
        // For now, return a placeholder
        return 0.0;
    }

    private function calculateJobsPerMinute(): float
    {
        // Calculate based on recent job completions
        return 0.0;
    }

    private function calculateFailureRate(): float
    {
        $totalJobs = DB::table('failed_jobs')->count() + 1000; // Approximate completed jobs
        $failedJobs = DB::table('failed_jobs')->count();
        
        return $totalJobs > 0 ? ($failedJobs / $totalJobs) * 100 : 0;
    }

    private function calculateQueueThroughput(): array
    {
        return [
            'jobs_per_hour' => 0,
            'peak_hour' => null,
            'average_wait_time' => 0
        ];
    }
}
```

## Queue Monitoring Command

### QueueMonitorCommand
```php
<?php
// File: api/app/Console/Commands/QueueMonitorCommand.php

namespace App\Console\Commands;

use App\Services\QueueManagementService;
use Illuminate\Console\Command;

class QueueMonitorCommand extends Command
{
    protected $signature = 'queue:monitor {--alert-threshold=1000}';
    protected $description = 'Monitor queue health and send alerts if needed';

    protected QueueManagementService $queueService;

    public function __construct(QueueManagementService $queueService)
    {
        parent::__construct();
        $this->queueService = $queueService;
    }

    public function handle(): int
    {
        $this->info('Monitoring queue health...');

        $health = $this->queueService->getQueueHealth();
        $stats = $this->queueService->getQueueStatistics();

        $this->displayQueueStatus($health, $stats);

        // Check for alerts
        $alertThreshold = $this->option('alert-threshold');
        
        if ($stats['pending_jobs'] > $alertThreshold) {
            $this->error("ALERT: High pending job count: {$stats['pending_jobs']}");
            // Send notification to administrators
        }

        if ($health['status'] === 'critical') {
            $this->error('ALERT: Queue health is CRITICAL');
            foreach ($health['issues'] as $issue) {
                $this->error("  - {$issue}");
            }
        }

        return 0;
    }

    private function displayQueueStatus(array $health, array $stats): void
    {
        $this->table(
            ['Metric', 'Value'],
            [
                ['Health Status', $health['status']],
                ['Pending Jobs', $stats['pending_jobs']],
                ['Failed Jobs', $stats['failed_jobs']],
                ['Recent Failures', count($stats['recent_failures'])]
            ]
        );

        if (!empty($stats['jobs_by_queue'])) {
            $this->info('Jobs by Queue:');
            foreach ($stats['jobs_by_queue'] as $queue => $count) {
                $this->line("  {$queue}: {$count}");
            }
        }

        if (!empty($health['issues'])) {
            $this->warn('Issues detected:');
            foreach ($health['issues'] as $issue) {
                $this->line("  - {$issue}");
            }
        }
    }
}
```

## Acceptance Criteria
- [ ] Queue system properly configured
- [ ] Background jobs processing correctly
- [ ] Job monitoring and statistics functional
- [ ] Failed job handling implemented
- [ ] Queue health monitoring operational
- [ ] Performance metrics tracking
- [ ] Alert system for queue issues

## Next Steps
1. Implement React components for frontend
2. Create home screen integration
3. Set up comprehensive testing
4. Deploy and monitor in production

## Troubleshooting
- Monitor queue worker processes regularly
- Check database connections for queue tables
- Verify job serialization and deserialization
- Monitor memory usage for large batch jobs
- Set up proper logging for job failures
