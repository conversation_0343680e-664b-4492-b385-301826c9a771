/**
 * Game State
 * Base class for all game states
 */
class GameState {
    /**
     * @param {Engine} engine - Game engine
     */
    constructor(engine) {
        this.engine = engine;
    }
    
    /**
     * Initialize the state
     * @param {Engine} engine - Game engine
     */
    init(engine) {
        this.engine = engine;
    }
    
    /**
     * Called when entering the state
     */
    enter() {
        // Override in subclasses
    }
    
    /**
     * Called when exiting the state
     */
    exit() {
        // Override in subclasses
    }
    
    /**
     * Update the state
     * @param {number} deltaTime - Time since last update
     */
    update(deltaTime) {
        // Override in subclasses
    }
    
    /**
     * Render the state
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     */
    render(ctx) {
        // Override in subclasses
    }
    
    /**
     * Handle key down events
     * @param {string} key - Key pressed
     */
    onKeyDown(key) {
        // Override in subclasses
    }
    
    /**
     * Handle key up events
     * @param {string} key - Key released
     */
    onKeyUp(key) {
        // Override in subclasses
    }
    
    /**
     * Handle touch start events
     * @param {number} x - Touch X position
     * @param {number} y - Touch Y position
     */
    onTouchStart(x, y) {
        // Override in subclasses
    }
    
    /**
     * Handle touch end events
     */
    onTouchEnd() {
        // Override in subclasses
    }
    
    /**
     * Handle mouse down events
     * @param {number} x - Mouse X position
     * @param {number} y - Mouse Y position
     */
    onMouseDown(x, y) {
        // Override in subclasses
    }
    
    /**
     * Handle mouse up events
     * @param {number} x - Mouse X position
     * @param {number} y - Mouse Y position
     */
    onMouseUp(x, y) {
        // Override in subclasses
    }
}
