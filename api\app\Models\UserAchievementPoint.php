<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserAchievementPoint extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id',
        'total_earned',
        'total_spent'
    ];

    /**
     * Get the user that owns these achievement points.
     */
    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    /**
     * Get the available achievement points.
     */
    public function getAvailablePointsAttribute()
    {
        return $this->total_earned - $this->total_spent;
    }

    /**
     * Get the achievement point transactions for this user.
     */
    public function transactions()
    {
        return $this->hasMany(AchievementPointTransaction::class, 'telegram_user_id', 'telegram_user_id');
    }
}
