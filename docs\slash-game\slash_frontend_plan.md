# Slash Game Frontend Implementation Plan

## Overview
This document outlines the frontend implementation plan for the Slash game in the Telegram web app. The Slash game will follow similar patterns to the existing Tower and Rabbit games, with appropriate modifications for the Fruit Ninja-style gameplay.

## Current Status Analysis
- The game registry in `battlx/src/games/registry.ts` already includes the Slash game configuration
- The registry is trying to import the Slash game module from `../slash_game/src/main.ts` but it's missing
- There are existing Slash game files in `battlx/src/slash_game/js` and `battlx/public/game/slash`
- The GameWrapper component is set up to handle different games, including Slash
- The SlashGameDrawer component exists for unlocking the game

## Frontend Implementation Tasks

### 1. Create the Main Entry Point for the Slash Game
Create the missing `main.ts` file in the `battlx/src/slash_game/src` directory, following the pattern of the Rabbit game.

```typescript
// battlx/src/slash_game/src/main.ts
import { GameInstance } from '../../games/registry';
import { loadGameScripts, getLoadingProgress } from './loader';

/**
 * Initialize and return a Slash Game instance
 * @param options Game options from GameWrapper
 * @returns GameInstance compatible with the registry
 */
export const slashGame = (options: any): GameInstance => {
  // Game instance reference
  let engine: any = null;
  let assetLoader: any = null;
  let canvasElement: HTMLCanvasElement | null = null;
  let gameInitialized = false;

  // Create GameInstance wrapper
  const gameInstance: GameInstance = {
    // Load assets
    load: (onReady, onProgress) => {
      // First load all required scripts
      loadGameScripts()
        .then(() => {
          try {
            // Get the canvas element
            canvasElement = document.getElementById(options.canvasId) as HTMLCanvasElement;
            if (!canvasElement) {
              console.error(`Canvas element with ID ${options.canvasId} not found`);
              onProgress({
                success: 0,
                total: 1,
                failed: 1
              });
              return;
            }

            // Check if required classes are available
            if (!(window as any).Game || !(window as any).GameCore) {
              console.error('Required game classes not found. Game or GameCore missing.');
              onProgress({
                success: 0,
                total: 1,
                failed: 1
              });
              return;
            }

            // Create game instance
            const game = new (window as any).Game(canvasElement);
            
            // Store reference to game and core
            engine = game;
            
            // Store integration callbacks in the game core
            if (options.setGameScore) {
              (window as any).Game.core.setGameScore = options.setGameScore;
            }
            if (options.onGameOver) {
              (window as any).Game.core.onGameOver = options.onGameOver;
            }

            // Initialize game
            game.init().then(() => {
              gameInitialized = true;
              onReady();
            }).catch((error: any) => {
              console.error('Error initializing game:', error);
              onProgress({
                success: 0,
                total: 1,
                failed: 1
              });
            });
          } catch (error) {
            console.error('Error initializing game:', error);
            onProgress({
              success: 0,
              total: 1,
              failed: 1
            });
          }
        })
        .catch((error) => {
          console.error('Failed to load Slash Game scripts:', error);
          // Report loading progress with error
          onProgress({
            success: 0,
            total: 1,
            failed: 1
          });
        });

      // Report script loading progress
      const updateProgress = () => {
        const progress = getLoadingProgress();
        onProgress({
          success: progress.loaded,
          total: progress.total,
          failed: 0
        });

        if (progress.loaded < progress.total) {
          requestAnimationFrame(updateProgress);
        }
      };

      updateProgress();
    },

    // Start game
    start: () => {
      if (engine && gameInitialized) {
        engine.start();
      }
    },

    // Destroy game
    destroy: () => {
      if (!engine) return;
      
      try {
        // Stop the game loop
        engine.stop();
        
        // Clean up event listeners
        if (canvasElement) {
          canvasElement.removeEventListener('mousedown', engine.handleInput);
          canvasElement.removeEventListener('touchstart', engine.handleInput);
          window.removeEventListener('resize', engine.resizeCanvas);
        }
        
        // Clean up game objects
        if ((window as any).Game.core) {
          (window as any).Game.core.cleanUp();
        }
        
        // Clear canvas
        if (canvasElement) {
          const ctx = canvasElement.getContext('2d');
          if (ctx) {
            ctx.clearRect(0, 0, canvasElement.width, canvasElement.height);
          }
        }
      } catch (error) {
        console.error('Error destroying game:', error);
      }
    },

    // Play background music
    playBgm: () => {
      if ((window as any).Game.core && (window as any).Game.core.playBgm) {
        (window as any).Game.core.playBgm();
      }
    },

    // Pause background music
    pauseBgm: () => {
      if ((window as any).Game.core && (window as any).Game.core.pauseBgm) {
        (window as any).Game.core.pauseBgm();
      }
    },

    // Clear event listeners
    clearEventListeners: () => {
      if (canvasElement) {
        canvasElement.removeEventListener('mousedown', engine.handleInput);
        canvasElement.removeEventListener('touchstart', engine.handleInput);
        window.removeEventListener('resize', engine.resizeCanvas);
      }
    }
  };

  return gameInstance;
};
```

### 2. Create the Script Loader for the Slash Game
Create a loader.ts file to load the required scripts for the Slash game.

```typescript
// battlx/src/slash_game/src/loader.ts
// Track loading progress
let loadingProgress = {
  loaded: 0,
  total: 0
};

// Track loading promise
let loadingPromise: Promise<void> | null = null;

// List of scripts to load in order
const scripts = [
  // Core Components
  '/game/slash/js/src/components/utils.js',
  '/game/slash/js/src/components/vector2.js',
  '/game/slash/js/src/components/containmentRect.js',
  '/game/slash/js/src/components/assetLoader.js',
  '/game/slash/js/src/components/inputHandler.js',
  '/game/slash/js/src/components/virtualJoystick.js',
  
  // Game Components
  '/game/slash/js/src/components/bgManager.js',
  '/game/slash/js/src/components/player.js',
  '/game/slash/js/src/components/enemy.js',
  '/game/slash/js/src/components/enemyProjectile.js',
  '/game/slash/js/src/components/weapon.js',
  '/game/slash/js/src/components/pickup.js',
  '/game/slash/js/src/components/destructible.js',
  '/game/slash/js/src/components/stage.js',
  '/game/slash/js/src/components/ui.js',
  '/game/slash/js/src/components/sceneManager.js',
  
  // Game Core
  '/game/slash/js/src/components/gameCore.js',
  '/game/slash/js/src/components/game.js',
  
  // Constants
  '/game/slash/js/consts/stages.js',
  '/game/slash/js/consts/enemies.js',
  '/game/slash/js/consts/weapons.js',
  '/game/slash/js/consts/treasures.js'
];

/**
 * Load all required scripts for the Slash game
 * @returns Promise that resolves when all scripts are loaded
 */
export const loadGameScripts = (): Promise<void> => {
  // Return existing promise if already loading
  if (loadingPromise) {
    return loadingPromise;
  }
  
  // Reset loading progress
  loadingProgress = {
    loaded: 0,
    total: scripts.length
  };
  
  // Create new loading promise
  loadingPromise = new Promise((resolve, reject) => {
    // Skip loading if scripts are already loaded
    // Check if Game and GameCore are available
    if ((window as any).Game && (window as any).GameCore) {
      console.log('Slash Game components already loaded');
      resolve();
      return;
    }
    
    console.log('Loading Slash Game scripts...');
    
    // Load scripts sequentially
    const loadScript = (index: number) => {
      if (index >= scripts.length) {
        resolve();
        return;
      }
      
      const script = document.createElement('script');
      script.src = scripts[index];
      script.async = false;
      
      script.onload = () => {
        loadingProgress.loaded++;
        loadScript(index + 1);
      };
      
      script.onerror = (error) => {
        console.error(`Failed to load script: ${scripts[index]}`, error);
        reject(new Error(`Failed to load script: ${scripts[index]}`));
      };
      
      document.body.appendChild(script);
    };
    
    // Start loading scripts
    loadScript(0);
  });
  
  return loadingPromise;
};

/**
 * Get the current loading progress
 * @returns Object with loaded and total counts
 */
export const getLoadingProgress = () => {
  return { ...loadingProgress };
};
```
