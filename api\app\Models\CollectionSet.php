<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CollectionSet extends Model
{
    use HasFactory;

    protected $fillable = [
        'set_id', 'name', 'category', 'description', 'icon_url',
        'completion_rewards', 'bonus_mystery_box_type', 'total_collectibles',
        'required_for_completion', 'is_active', 'sort_order'
    ];

    protected $casts = [
        'completion_rewards' => 'array',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function collectibleTemplates(): HasMany
    {
        return $this->hasMany(CollectibleTemplate::class, 'collection_set_id', 'set_id');
    }

    public function userProgress(): HasMany
    {
        return $this->hasMany(UserCollectionProgress::class, 'set_id', 'set_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    // Accessors
    public function getIconUrlAttribute($value)
    {
        return $value ? asset($value) : null;
    }

    public function getCompletionRewardsSummaryAttribute(): string
    {
        $rewards = $this->completion_rewards ?? [];
        $summary = [];

        foreach ($rewards as $reward) {
            $type = $reward['type'] ?? '';
            $amount = $reward['amount'] ?? 0;
            
            $summary[] = match($type) {
                'coins' => number_format($amount) . ' coins',
                'gems' => $amount . ' gems',
                'achievement_points' => $amount . ' AP',
                default => $amount . ' ' . $type
            };
        }

        return implode(', ', $summary);
    }

    // Business Logic Methods
    public function getProgressForUser(TelegramUser $user): array
    {
        $ownedCollectibles = $user->collectibles()
            ->whereHas('template', function($q) {
                $q->where('collection_set_id', $this->set_id);
            })
            ->with('template')
            ->get();

        $allCollectibles = $this->collectibleTemplates()
            ->orderBy('set_position')
            ->get();

        $owned = $ownedCollectibles->pluck('collectible_id')->toArray();
        $missing = $allCollectibles->whereNotIn('collectible_id', $owned);

        $completionPercentage = $this->total_collectibles > 0 
            ? round(($ownedCollectibles->count() / $this->total_collectibles) * 100, 1)
            : 0;

        $isCompleted = $ownedCollectibles->count() >= $this->required_for_completion;

        return [
            'set_id' => $this->set_id,
            'name' => $this->name,
            'category' => $this->category,
            'owned_count' => $ownedCollectibles->count(),
            'total_count' => $this->total_collectibles,
            'required_count' => $this->required_for_completion,
            'completion_percentage' => $completionPercentage,
            'is_completed' => $isCompleted,
            'owned_collectibles' => $ownedCollectibles->toArray(),
            'missing_collectibles' => $missing->toArray(),
            'completion_rewards' => $this->completion_rewards,
            'bonus_mystery_box' => $this->bonus_mystery_box_type
        ];
    }

    public function isCompletedBy(TelegramUser $user): bool
    {
        $ownedCount = $user->collectibles()
            ->whereHas('template', function($q) {
                $q->where('collection_set_id', $this->set_id);
            })
            ->count();

        return $ownedCount >= $this->required_for_completion;
    }

    public function awardCompletionRewards(TelegramUser $user): array
    {
        if (!$this->isCompletedBy($user)) {
            throw new \Exception('Collection not completed');
        }

        $rewards = $this->completion_rewards ?? [];
        $awarded = [];

        foreach ($rewards as $reward) {
            $type = $reward['type'] ?? '';
            $amount = $reward['amount'] ?? 0;

            switch ($type) {
                case 'coins':
                    $user->increment('balance', $amount);
                    $awarded[] = ['type' => 'coins', 'amount' => $amount];
                    break;
                case 'gems':
                    $user->increment('gems', $amount);
                    $awarded[] = ['type' => 'gems', 'amount' => $amount];
                    break;
                case 'achievement_points':
                    $user->increment('achievement_points', $amount);
                    $awarded[] = ['type' => 'achievement_points', 'amount' => $amount];
                    break;
            }
        }

        // Unlock bonus mystery box if available
        if ($this->bonus_mystery_box_type) {
            // This would unlock the mystery box for the user
            $awarded[] = ['type' => 'mystery_box_unlock', 'box_type' => $this->bonus_mystery_box_type];
        }

        return $awarded;
    }
}
