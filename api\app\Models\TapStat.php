<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TapStat extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id',
        'total_taps'
    ];

    /**
     * Get the user that owns these tap stats.
     */
    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }
}
