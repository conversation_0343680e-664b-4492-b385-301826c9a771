import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { $http } from '@/lib/http';
import { BattlxIcon } from '@/components/icons/BattlxIcon';
import { toast } from 'react-toastify';
import { UserPrize, UserPrizesResponse, EquipPrizeResponse, UnequipPrizeResponse } from '@/types/PrizeTypes';

type TabType = 'all' | 'cosmetic' | 'title' | 'card' | 'special_item';

export default function UserPrizeGallery() {
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState<TabType>('all');

  // Fetch user's prizes
  const { data, isLoading, isError } = useQuery<UserPrizesResponse>({
    queryKey: ['user-prizes'],
    queryFn: () => $http.$get('/prizes/user'),
  });

  // Mutation for equipping prizes
  const equipPrize = useMutation<EquipPrizeResponse, Error, number>({
    mutationFn: (prizeId: number) => $http.post('/prizes/equip', { prize_id: prizeId }),
    onSuccess: () => {
      toast.success('Prize equipped successfully!');
      queryClient.invalidateQueries({ queryKey: ['user-prizes'] });
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to equip prize');
    }
  });

  // Mutation for unequipping prizes
  const unequipPrize = useMutation<UnequipPrizeResponse, Error, number>({
    mutationFn: (prizeId: number) => $http.post('/prizes/unequip', { prize_id: prizeId }),
    onSuccess: () => {
      toast.success('Prize unequipped successfully!');
      queryClient.invalidateQueries({ queryKey: ['user-prizes'] });
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to unequip prize');
    }
  });

  // Filter prizes based on active tab
  const filterPrizes = (): UserPrize[] => {
    if (!data?.user_prizes) return [];

    if (activeTab === 'all') {
      return data.user_prizes;
    }

    return data.user_prizes.filter(prize => prize.prize.reward_type === activeTab);
  };

  // Handle equip/unequip
  const handleToggleEquip = (prize: UserPrize) => {
    if (prize.is_equipped) {
      unequipPrize.mutate(prize.prize_id);
    } else {
      equipPrize.mutate(prize.prize_id);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="w-8 h-8 border-4 border-[#9B8B6C] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <div className="text-red-500 mb-2">Error loading prizes</div>
        <button
          className="px-4 py-2 bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40 rounded-lg"
          onClick={() => queryClient.invalidateQueries({ queryKey: ['user-prizes'] })}
        >
          Retry
        </button>
      </div>
    );
  }

  // No prizes state
  if (!data?.user_prizes?.length) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <BattlxIcon icon="coin" className="w-12 h-12 text-[#9B8B6C]/40 mb-4" />
        <div className="text-[#9B8B6C]/60 text-center">
          <p className="mb-2">No prizes unlocked yet</p>
          <p className="text-sm">Complete missions and earn achievement points to unlock prizes</p>
        </div>
      </div>
    );
  }

  const filteredPrizes = filterPrizes();

  return (
    <div className="flex flex-col h-full">
      {/* Achievement Points Display */}
      <div className="flex items-center justify-center mb-4 space-x-2">
        <BattlxIcon icon="coin" className="w-5 h-5 text-[#9B8B6C]" />
        <span className="text-lg font-bold text-[#9B8B6C]">
          {data?.achievement_points || 0} Achievement Points
        </span>
      </div>

      {/* Filter Tabs */}
      <div className="flex items-center justify-center mb-4 space-x-2 overflow-x-auto pb-2">
        <button
          className={`px-3 py-1 text-sm rounded-lg ${
            activeTab === 'all'
              ? 'bg-[#9B8B6C] text-[#120D0E]'
              : 'bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40'
          }`}
          onClick={() => setActiveTab('all')}
        >
          All
        </button>
        <button
          className={`px-3 py-1 text-sm rounded-lg ${
            activeTab === 'cosmetic'
              ? 'bg-[#9B8B6C] text-[#120D0E]'
              : 'bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40'
          }`}
          onClick={() => setActiveTab('cosmetic')}
        >
          Cosmetics
        </button>
        <button
          className={`px-3 py-1 text-sm rounded-lg ${
            activeTab === 'title'
              ? 'bg-[#9B8B6C] text-[#120D0E]'
              : 'bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40'
          }`}
          onClick={() => setActiveTab('title')}
        >
          Titles
        </button>
        <button
          className={`px-3 py-1 text-sm rounded-lg ${
            activeTab === 'card'
              ? 'bg-[#9B8B6C] text-[#120D0E]'
              : 'bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40'
          }`}
          onClick={() => setActiveTab('card')}
        >
          Cards
        </button>
        <button
          className={`px-3 py-1 text-sm rounded-lg ${
            activeTab === 'special_item'
              ? 'bg-[#9B8B6C] text-[#120D0E]'
              : 'bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40'
          }`}
          onClick={() => setActiveTab('special_item')}
        >
          Items
        </button>
      </div>

      {/* Prize Grid */}
      <div className="grid grid-cols-2 gap-4 overflow-y-auto pb-4 sm:grid-cols-3">
        {filteredPrizes.map(prize => (
          <div
            key={prize.prize_id}
            className={`flex flex-col items-center p-3 rounded-lg ${
              prize.is_equipped
                ? 'bg-[#9B8B6C]/20 border-2 border-[#9B8B6C]'
                : 'bg-[#120D0E] border border-[#9B8B6C]/40'
            }`}
          >
            <div className="flex items-center justify-center w-12 h-12 mb-2 rounded-full bg-[#120D0E] border border-[#9B8B6C]/60">
              <BattlxIcon icon={prize.prize.icon as any || 'skill'} className="w-6 h-6 text-[#9B8B6C]" />
            </div>
            <h3 className="text-sm font-medium text-center text-[#9B8B6C] mb-1">{prize.prize.name}</h3>
            <div className="px-2 py-0.5 text-xs rounded-full bg-[#9B8B6C]/20 text-[#9B8B6C] mb-2">
              {prize.prize.reward_type.charAt(0).toUpperCase() + prize.prize.reward_type.slice(1)}
            </div>
            <p className="text-xs text-center text-[#B3B3B3]/70 mb-2 line-clamp-2">{prize.prize.description}</p>

            {/* Equip/Unequip button (only for equippable prizes) */}
            {['cosmetic', 'title', 'emote'].includes(prize.prize.reward_type) && (
              <button
                className={`w-full mt-auto px-2 py-1 text-xs font-medium rounded-lg ${
                  prize.is_equipped
                    ? 'bg-[#120D0E] text-[#9B8B6C] border border-[#9B8B6C]/40'
                    : 'bg-[#9B8B6C] text-[#120D0E]'
                }`}
                onClick={() => handleToggleEquip(prize)}
                disabled={equipPrize.isPending || unequipPrize.isPending}
              >
                {equipPrize.isPending || unequipPrize.isPending ? (
                  <div className="w-3 h-3 mx-auto border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  prize.is_equipped ? 'Unequip' : 'Equip'
                )}
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
