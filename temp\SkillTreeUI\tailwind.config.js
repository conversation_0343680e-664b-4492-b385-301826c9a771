/** @type {import('tailwindcss').Config} */
export default {
  content: ['./index.html', './src/**/*.{js,ts,jsx,tsx}'],
  theme: {
    extend: {
      colors: {
        // Primary dark, moody base colors
        abyss: {
          50: '#2A2426',
          100: '#231D1F',
          200: '#1C1617',
          300: '#120D0E', // Deep shadow black
          400: '#0D0909',
          500: '#080505',
          900: '#020101',
        },
        brass: {
          50: '#D4C6A6',
          100: '#C5B48E',
          200: '#B6A277',
          300: '#9B8B6C', // Aged brass
          400: '#8A7B5D',
          500: '#796C4F',
          900: '#453D2C',
        },
        stone: {
          50: '#E6E6E6',
          100: '#D9D9D9',
          200: '#CCCCCC',
          300: '#B3B3B3', // Ancient stone
          400: '#999999',
          500: '#808080',
          900: '#4D4D4D',
        },
        // Accent colors for dramatic effects
        blood: {
          50: '#FFE6E6',
          100: '#FFCCCC',
          200: '#FF9999',
          300: '#FF6666',
          400: '#FF3333',
          500: '#FF0000',
          600: '#CC0000',
          700: '#990000',
          800: '#660000',
          900: '#330000',
        },
        amethyst: {
          50: '#F3E6FF',
          100: '#E6CCFF',
          200: '#CC99FF',
          300: '#B366FF',
          400: '#9933FF',
          500: '#7F00FF',
          600: '#6600CC',
          700: '#4C0099',
          800: '#330066',
          900: '#190033',
        },
      },
      backgroundImage: {
        'gothic-gradient': 'linear-gradient(to bottom, var(--tw-gradient-stops))',
        'ornate-pattern': 'repeating-linear-gradient(45deg, rgba(155, 139, 108, 0.1) 0px, rgba(155, 139, 108, 0.1) 2px, transparent 2px, transparent 6px)',
      },
      boxShadow: {
        'inner-glow': 'inset 0 0 20px rgba(127, 0, 255, 0.2)',
        'outer-glow': '0 0 20px rgba(127, 0, 255, 0.3)',
        'blood-glow': '0 0 15px rgba(255, 0, 0, 0.3)',
      },
      borderWidth: {
        '3': '3px',
      },
    },
  },
  plugins: [],
};