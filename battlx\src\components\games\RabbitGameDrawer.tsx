// src/components/games/RabbitGameDrawer.tsx
import { useMemo } from 'react';
import { Button } from '../ui/button'; // Assuming path is correct
import Drawer from '../ui/drawer'; // Assuming path is correct
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { BattlxIcon } from '@/components/icons/BattlxIcon'; // Assuming path is correct
import { useUserStore } from '@/store/user-store'; // Assuming path is correct
import { gameApi } from '@/lib/game-api'; // Assuming path is correct

const UNLOCK_PRICE = 5000; // Make sure this matches backend RABBIT_GAME_UNLOCK_PRICE

type RabbitGameDrawerProps = { // Renamed from RabbitGameDrawerProps for convention
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export default function RabbitGameDrawer({ open, onOpenChange }: RabbitGameDrawerProps) { // Renamed component for convention
  // Ensure rabbit_game_unlocked exists in the user store state
  const { balance, rabbit_game_unlocked } = useUserStore(state => ({
      balance: state.balance,
      rabbit_game_unlocked: state.rabbit_game_unlocked 
  }));

  const insufficientBalance = useMemo(() => {
    return balance < UNLOCK_PRICE;
  }, [balance]);

  const unlockMutation = useMutation({
    // Use the generalized unlockGame function
    mutationFn: () => gameApi.unlockGame('rabbit'), 
    onSuccess: (response) => {
      if (response.success && response.user) {
        toast.success('Rabbit Game unlocked successfully');
        // Update user store with fresh data from response
        useUserStore.setState((state) => ({
          ...state,
          ...response.user // Spread the updated user object
        }));
        onOpenChange(false); // Close the drawer
      } else {
        toast.error(response.message || 'Failed to unlock game');
      }
    },
    onError: (error: any) => {
      // Attempt to parse backend error message
      toast.error(error?.response?.data?.message || 'An error occurred while unlocking the game');
    },
  });

  // Don't render the drawer if the game is already unlocked
  if (rabbit_game_unlocked) return null; 

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      {/* Using class names from the plan */}
      <div className="!rounded-none [&_[data-drawer-content]]:!rounded-none">
        <img
          src="/game/thumbnails/rabbit.png" // Ensure path is correct
          alt="Rabbit Game"
          className="object-contain h-32 mx-auto opacity-80 rounded-none [filter:sepia(50%)_saturate(50%)_brightness(80%)_contrast(120%)]"
        />
        <h2 className="mt-6 text-2xl font-medium text-center text-[#9B8B6C]">
          Rabbit Game
        </h2>
        <div className="flex flex-col mx-auto mt-4 w-fit">
          <p className="text-xs text-center text-[#B3B3B3]/80">Unlock Price</p>
        </div>

        <div className="flex items-center justify-center mx-auto mt-6 space-x-1 text-[#9B8B6C]">
          <BattlxIcon icon="coins" className="w-8 h-8 text-[#9B8B6C]" />
          <span className="font-bold">{UNLOCK_PRICE.toLocaleString()}</span>
        </div>

        {insufficientBalance && (
          <div className="flex items-center justify-center mt-4">
            <p className="text-sm text-[#B3B3B3]/80">
              Need {(UNLOCK_PRICE - balance).toLocaleString()} more coins
            </p>
          </div>
        )}

        <Button
          className="w-full mt-6 bg-[#1A1617] border border-[#B3B3B3]/20 text-[#9B8B6C] hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_15px_rgba(74,14,14,0.3)] disabled:opacity-50 disabled:cursor-not-allowed rounded-none"
          disabled={unlockMutation.isPending || insufficientBalance}
          onClick={() => unlockMutation.mutate()}
        >
          {unlockMutation.isPending && (
            <BattlxIcon icon="loading" className="w-6 h-6 mr-2 animate-spin" />
          )}
          {insufficientBalance ? "Insufficient Balance" : "Unlock Game"}
        </Button>
      </div>
    </Drawer>
  );
}