<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('mystery_box_types', function (Blueprint $table) {
            $table->id();
            $table->string('box_type', 50)->unique(); // 'common_shadow', 'rare_undead', etc.
            $table->string('display_name', 100);
            $table->enum('rarity', ['common', 'rare', 'epic', 'legendary', 'mythic']);
            $table->enum('category', ['shadow', 'undead', 'demon', 'spirit', 'beast', 'universal']);
            
            $table->text('description');
            $table->string('image_url');
            $table->string('animation_url')->nullable();
            
            // Purchase costs
            $table->integer('coin_cost')->default(0);
            $table->integer('gem_cost')->default(0);
            $table->integer('achievement_points_cost')->default(0);
            
            // Reward configuration
            $table->json('possible_rewards'); // Array of possible collectibles/items
            $table->json('reward_weights'); // Probability weights for each reward
            $table->integer('guaranteed_rarity_level')->default(1); // 1=common, 5=mythic
            
            // Unlock requirements
            $table->json('unlock_requirements'); // Pet IDs or prize tree levels required
            
            $table->boolean('is_purchasable')->default(true);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index(['category', 'rarity']);
            $table->index('is_active');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mystery_box_types');
    }
};
