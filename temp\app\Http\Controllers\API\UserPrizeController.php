<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Prize;
use App\Models\UserPrize;
use App\Services\PrizeService;
use Illuminate\Http\Request;

class UserPrizeController extends Controller
{
    protected $prizeService;
    
    public function __construct(PrizeService $prizeService)
    {
        $this->prizeService = $prizeService;
    }
    
    /**
     * Get the user's prizes and achievement points.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $result = $this->prizeService->getUserPrizes($user->id);
        
        if (!$result['success']) {
            return response()->json([
                'message' => $result['message']
            ], 400);
        }
        
        return response()->json($result);
    }

    /**
     * Unlock a prize for the user.
     */
    public function unlock(Request $request)
    {
        $request->validate([
            'prize_id' => 'required|exists:prizes,id'
        ]);
        
        $user = $request->user();
        $prizeId = $request->prize_id;
        
        $result = $this->prizeService->unlockPrize($user->id, $prizeId);
        
        if (!$result['success']) {
            return response()->json([
                'message' => $result['message'],
                'data' => $result
            ], 400);
        }
        
        return response()->json([
            'message' => 'Prize unlocked successfully',
            'prize' => $result['prize'],
            'remaining_points' => $result['remaining_points']
        ]);
    }
    
    /**
     * Equip a prize.
     */
    public function equip(Request $request)
    {
        $request->validate([
            'prize_id' => 'required|exists:prizes,id'
        ]);
        
        $user = $request->user();
        $prizeId = $request->prize_id;
        
        $result = $this->prizeService->equipPrize($user->id, $prizeId);
        
        if (!$result['success']) {
            return response()->json([
                'message' => $result['message']
            ], 400);
        }
        
        return response()->json([
            'message' => 'Prize equipped successfully',
            'prize_id' => $prizeId,
            'reward_type' => $result['reward_type'],
            'reward_details' => $result['reward_details']
        ]);
    }
    
    /**
     * Unequip a prize.
     */
    public function unequip(Request $request)
    {
        $request->validate([
            'prize_id' => 'required|exists:prizes,id'
        ]);
        
        $user = $request->user();
        $prizeId = $request->prize_id;
        
        $result = $this->prizeService->unequipPrize($user->id, $prizeId);
        
        if (!$result['success']) {
            return response()->json([
                'message' => $result['message']
            ], 400);
        }
        
        return response()->json([
            'message' => 'Prize unequipped successfully',
            'prize_id' => $prizeId
        ]);
    }
}
