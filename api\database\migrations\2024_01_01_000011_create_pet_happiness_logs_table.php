<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pet_happiness_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('pet_id')->constrained('pets')->onDelete('cascade');
            
            $table->integer('happiness_before');
            $table->integer('happiness_after');
            $table->integer('happiness_change');
            
            $table->enum('change_reason', [
                'interaction_feed',
                'interaction_play', 
                'interaction_pet',
                'daily_decay',
                'evolution_bonus',
                'admin_adjustment'
            ]);
            
            $table->string('interaction_id')->nullable(); // Reference to pet_interactions.id
            $table->text('notes')->nullable();
            
            $table->timestamp('logged_at');
            $table->timestamps();
            
            $table->index(['pet_id', 'logged_at']);
            $table->index('change_reason');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pet_happiness_logs');
    }
};
