/**
 * Input Handler
 * Manages keyboard and touch input
 */
class InputHandler {
    /**
     * @param {Engine} engine - Game engine
     */
    constructor(engine) {
        this.engine = engine;
        this.jumpPressed = false;
        this.jumpReleased = true;
    }

    /**
     * Update the input state
     */
    update() {
        // Check for jump input
        const jumpKeys = CONSTANTS.KEYS.JUMP;
        const jumpKeyPressed = this.engine.isAnyKeyPressed(jumpKeys);
        const touchPressed = this.engine.touchActive || this.engine.mouseDown;

        // Track jump button press and release
        if ((jumpKeyPressed || touchPressed) && this.jumpReleased) {
            this.jumpPressed = true;
            this.jumpReleased = false;
        } else {
            this.jumpPressed = false;
        }

        if (!jumpKeyPressed && !touchPressed) {
            this.jumpReleased = true;
        }
    }

    /**
     * Check if the jump button was just pressed
     * @returns {boolean} - True if jump was just pressed
     */
    isJumpPressed() {
        return this.jumpPressed;
    }

    /**
     * Check if any start button is pressed
     * @returns {boolean} - True if any start button is pressed
     */
    isStartPressed() {
        const startKeys = CONSTANTS.KEYS.START;
        return this.engine.isAnyKeyPressed(startKeys) || this.engine.touchActive || this.engine.mouseDown;
    }
}
