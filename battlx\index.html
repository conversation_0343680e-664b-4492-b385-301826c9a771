<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"
    />
    <title>BattlX</title>

    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <script>
      // Enhanced viewport height management for consistent display across all devices
      function setViewportHeight() {
        const vh = window.innerHeight * 0.01;
        const vw = window.innerWidth * 0.01;

        // Set CSS custom properties for consistent viewport handling
        document.documentElement.style.setProperty('--vh', `${vh}px`);
        document.documentElement.style.setProperty('--vw', `${vw}px`);
        document.documentElement.style.setProperty('--tg-viewport-height', `${window.innerHeight}px`);
        document.documentElement.style.setProperty('--tg-viewport-width', `${window.innerWidth}px`);

        // Ensure consistent container width (max 414px for mobile consistency)
        const containerWidth = Math.min(window.innerWidth, 414);
        document.documentElement.style.setProperty('--container-width', `${containerWidth}px`);
      }

      // Set on initial load
      setViewportHeight();

      // Update on resize and orientation change
      window.addEventListener('resize', setViewportHeight);
      window.addEventListener('orientationchange', () => {
        setTimeout(setViewportHeight, 100); // Delay to ensure proper orientation change
      });

      // Handle Telegram WebApp viewport changes
      if (window.Telegram && window.Telegram.WebApp) {
        window.Telegram.WebApp.onEvent('viewportChanged', setViewportHeight);
      }
    </script>
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-PKZG3L8R");
    </script>
    <!-- End Google Tag Manager -->
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-PKZG3L8R"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->
  </body>
</html>
