// GameCore class for handling core game logic
class GameCore {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');

        // Game state
        this.isPaused = false;
        this.isGameOver = false;
        this.isTimeStopped = false;

        // Game objects
        this.player = null;
        this.enemies = [];
        this.bullets = [];
        this.enemyProjectiles = [];
        this.pickups = [];
        this.destructibles = [];
        this.weapons = [];

        // Game settings
        this.maxGems = 400;
        this.currentTreasureLevel = 3;
        this.survivedSeconds = 0;
        this.maxDestructibles = 10;
        this.baseMarkup = 0.1;
        this.updateTick = 0;
        this.updateFreq = 4;

        // Static properties
        this.playerPxSpeed = 82.5;
        this.enemySpeed = 0.77 * 0.3 * 0.5; // Reduced enemy speed by 50%

        // Treasure types
        this.currentTreasureTypes = [
            TreasureType.EVOLUTION,
            TreasureType.POWERUP,
            TreasureType.POWERUP,
            TreasureType.POWERUP,
            TreasureType.POWERUP
        ];

        this.currentFixedTreasures = [
            FixedTreasures.HELLFIRE,
            FixedTreasures.SCYTHE,
            FixedTreasures.BONE,
            FixedTreasures.AMOUNT,
            FixedTreasures.AMOUNT
        ];

        // Game components
        this.playerOptions = new PlayerOptions();
        this.weaponFactory = new WeaponFactory();
        this.lootManager = new LootManager();
        this.sceneManager = new SceneManager();
        this.stage = null;
        this.bgManager = null;

        // UI components
        this.mainUI = null;
        this.playerUI = null;
        this.levelUpUI = null;

        // Camera
        this.camera = {
            x: 0,
            y: 0,
            width: canvas.width,
            height: canvas.height
        };

        // Input
        this.input = new InputHandler(canvas);
        this.joystick = new VirtualJoystick(canvas);

        // Containment rectangles
        this.containmentRect_Screen = null;
        this.containmentRect_ScreenPlus = null;

        // Timers
        this.gameTimer = null;
        this.timeStopConfig = {
            delay: 1000,
            callback: this.clearTimeStop.bind(this)
        };

        // Debug
        this.debug = true; // Enable debug mode by default

        // Asset loader
        this.assetLoader = new AssetLoader();
        this.sprites = {};

        // Pools
        this.pickupPool = new PickupGroup();
        this.gemsPool = new PickupGroup();
        this.destructiblesPool = new DestructibleGroup();

        // Damage numbers
        this.damageNumbers = [];

        // Input state tracking
        this.lastGKeyState = false;
        this.lastUKeyState = false;
    }

    // Initialize the game
    async init() {
        // Load player options from storage
        this.playerOptions.load();

        // Reset enemy counter to ensure it starts at 0
        this.playerOptions.runEnemies = 0;

        // Load assets
        await this.loadAssets();

        // Create player
        this.makePlayer(this.playerOptions.selectedCharacter);

        // Create UI
        this.mainUI = new MainUI(this.canvas);
        this.playerUI = new PlayerUI(this.canvas);
        this.levelUpUI = new LevelUpUI(this.canvas);

        // Initialize UI with zero kills
        if (this.mainUI) {
            this.mainUI.setKills(0);
        }

        // Create stage
        this.stage = new Stage(this.playerOptions.selectedStage);
        this.stage.init();

        // Create background
        this.bgManager = new BGManager(this.canvas);
        this.bgManager.setBackground(this.sprites['bgTile']);

        // Create containment rectangles
        this.containmentRect_Screen = new ContainmentRect(0.6);
        this.containmentRect_ScreenPlus = new ContainmentRect(0.8);

        // Add only the starting weapon
        this.addWeapon(this.player.startingWeapon);

        // Load previously unlocked weapons
        this.loadUnlockedWeapons();

        // Initialize loot manager
        this.lootManager.init();

        // Start game timer
        this.gameTimer = setInterval(() => {
            if (!this.isPaused && !this.isGameOver) {
                this.survivedSeconds++;
                this.mainUI.setSurvivedSeconds(this.survivedSeconds);

                // Check for stage updates
                this.checkStageUpdate();
            }
        }, 1000);

        // Update UI
        this.playerUI.updatePlayerLevel();
        this.playerUI.update();
    }

    // Load previously unlocked weapons
    loadUnlockedWeapons() {
        // Unlock weapons based on player level
        if (this.player) {
            for (let level = 2; level <= this.player.level; level++) {
                switch (level) {
                    case 2:
                        this.unlockWeapon(WeaponType.BONE);
                        break;
                    case 3:
                        this.unlockWeapon(WeaponType.FIST);
                        break;
                    case 4:
                        this.unlockWeapon(WeaponType.ROCK);
                        break;
                    case 5:
                        this.unlockWeapon(WeaponType.RADIOACTIVE);
                        break;
                }
            }
        }

        // Also unlock weapons from player options
        if (this.playerOptions && this.playerOptions.unlockedWeapons) {
            for (const weaponType of this.playerOptions.unlockedWeapons) {
                this.unlockWeapon(weaponType);
            }
        }
    }

    // Unlock a weapon
    unlockWeapon(weaponType) {
        if (!WEAPONS[weaponType] || !WEAPONS[weaponType][0]) return;

        // Set the weapon as unlocked
        WEAPONS[weaponType][0].isUnlocked = true;


    }

    // Unlock all weapons (for debugging)
    unlockAllWeapons() {
        for (const weaponType in WEAPONS) {
            if (WEAPONS.hasOwnProperty(weaponType)) {
                this.unlockWeapon(weaponType);
            }
        }

        // Show notification
        if (this.player) {
            this.showDamageAt(this.player.x, this.player.y - 40, 'All Weapons Unlocked!');
        }
    }

    // Load game assets
    async loadAssets() {
        // Define assets to load
        const assets = {
            images: {
                'bgTile': 'assets/img/bgTile.png',
                'woozy_face.png': 'assets/img/woozy_face.png',
                'ghost_enemy.png': 'assets/img/ghost_enemy.png',
                'ghost1_enemy.png': 'assets/img/ghost1_enemy.png',
                'ghost2_enemy.png': 'assets/img/ghost2_enemy.png',
                'bat_enemy.png': 'assets/img/bat_enemy.png',
                'ogre_enemy.png': 'assets/img/ogre_enemy.png',
                'horns_enemy.png': 'assets/img/horns_enemy.png',
                'ghost_boss1.png': 'assets/img/ghost_boss1.png',
                'teleporter_enemy.png': 'assets/img/teleporter_enemy.png',
                'leg_bullet.png': 'assets/img/leg_bullet.png',
                'bone_bullet.png': 'assets/img/bone_bullet.png',
                'fist_bullet.png': 'assets/img/fist_bullet.png',
                'rock_bullet.png': 'assets/img/rock_bullet.png',
                'radiation_bullet.png': 'assets/img/radiation_bullet.png',
                'gem_pickup.png': 'assets/img/gem_pickup.png',
                'coin_pickup.png': 'assets/img/coin_pickup.png',
                'roast_pickup.png': 'assets/img/roast_pickup.png',
                'vacum_pickup.png': 'assets/img/vacum_pickup.png',
                'gift_destructible.png': 'assets/img/gift_destructible.png',
                'box_destructible.png': 'assets/img/box_destructible.png',
                'amount_powerup.png': 'assets/img/amount_powerup.png',
                'area_powerup.png': 'assets/img/area_powerup.png',
                'cooldown_powerup.png': 'assets/img/cooldown_powerup.png',
                'speed_powerup.png': 'assets/img/speed_powerup.png',
                'duration_powerup.png': 'assets/img/duration_powerup.png',
                'armor_powerup.png': 'assets/img/armor_powerup.png',
                'maxhealth_powerup.png': 'assets/img/maxhealth_powerup.png',
                'growth_powerup.png': 'assets/img/growth_powerup.png',
                'movespeed_powerup.png': 'assets/img/movespeed_powerup.png',
                'luck_powerup.png': 'assets/img/luck_powerup.png'
            }
        };

        try {
            // Load assets
            const result = await this.assetLoader.loadAll(assets);
            this.sprites = result.images;
        } catch (error) {

        }
    }

    // Create the player
    makePlayer(character) {
        this.player = new Player(this.canvas.width / 2, this.canvas.height / 2, character);
        return this.player;
    }

    // Add a weapon
    addWeapon(bulletType) {
        // Check if weapon already exists
        const existingWeapon = this.weapons.find(w => w.bulletType === bulletType);
        if (existingWeapon) {
            // If it exists, level it up instead
            existingWeapon.levelUp();
            return existingWeapon;
        }

        // Create new weapon
        const weapon = this.getWeapon(bulletType);
        weapon.level = 1;
        weapon.setOwner(this.player);
        this.weapons.push(weapon);

        if (this.mainUI) {
            this.mainUI.addWeaponIcon(bulletType);
        }


        return weapon;
    }

    // Remove a weapon
    removeWeapon(bulletType) {
        const weapon = this.weapons.find(w => w.bulletType === bulletType);
        if (weapon) {
            weapon.cleanUp();
            this.weapons.splice(this.weapons.indexOf(weapon), 1);
        }
    }

    // Get a weapon
    getWeapon(bulletType) {
        return this.weaponFactory.getWeapon(bulletType);
    }

    // Make a gem pickup
    makeGem(x, y, value) {
        return this.gemsPool.spawnAt(x, y, PickupType.GEM, value);
    }

    // Make a pickup
    makePickup(x, y, type) {
        return this.pickupPool.spawnAt(x, y, type);
    }

    // Make a treasure
    makeTreasure() {
        // Not implemented in this version
    }

    // Make a destructible
    makeDestructible(type) {
        // Use the same approach as boss spawning which works well
        // Generate a random angle and distance
        const angle = Math.random() * Math.PI * 2;
        const distance = 256 * Math.random();

        // Calculate position relative to canvas size
        const canvas = this.canvas;
        let x, y;

        if (this.player) {
            // Use the exact same formula as boss spawning
            x = this.player.x + 0.9 * Math.cos(angle) * (canvas.width + distance);
            y = this.player.y + 0.9 * Math.sin(angle) * (canvas.height + distance);
        } else {
            x = Math.cos(angle) * 500;
            y = Math.sin(angle) * 500;
        }

        // Spawn the destructible
        const destructible = this.destructiblesPool.spawnAt(x, y, type);

        // Mark destructible as non-despawnable
        if (destructible) {
            destructible.isCullable = false;
        }

        // Limit the number of destructibles by removing the furthest ones
        while (this.destructibles.length > this.maxDestructibles) {
            const furthest = this.furthest(this.player, this.destructibles);
            if (furthest) {
                furthest.despawn();
            }
        }


    }

    // Get a position outside of sight
    getPositionOutOfSight(margin) {
        const pos = getRandomPositionOutsideScreen(this.canvas, margin);

        if (this.player) {
            pos.x += this.player.x;
            pos.y += this.player.y;
        }

        return pos;
    }

    // Find the furthest object from a reference
    furthest(reference, objects) {
        if (!objects || objects.length === 0) return null;

        let furthestObject = null;
        let furthestDistance = 0;

        for (const obj of objects) {
            const dist = distance(reference.x, reference.y, obj.x, obj.y);
            if (dist > furthestDistance) {
                furthestDistance = dist;
                furthestObject = obj;
            }
        }

        return furthestObject;
    }

    // Turn on vacuum effect for all gems
    turnOnVacuum() {
        // Find all pickups (not just gems)
        const pickups = this.pickups.filter(p => !p.isCollected);

        // Process pickups in batches to avoid performance issues
        const processBatch = (startIndex, batchSize) => {
            const endIndex = Math.min(startIndex + batchSize, pickups.length);

            for (let i = startIndex; i < endIndex; i++) {
                const pickup = pickups[i];
                if (pickup && !pickup.isCollected) {
                    // Use the vacuum method instead of directly setting goToPlayer
                    pickup.vacuum();
                }
            }

            // Process next batch if there are more pickups
            if (endIndex < pickups.length) {
                setTimeout(() => {
                    processBatch(endIndex, batchSize);
                }, 50); // Small delay between batches to avoid freezing
            }
        };

        // Start processing in batches of 20
        if (pickups && pickups.length > 0) {
            processBatch(0, 20);

        }
    }

    // Handle pickup collection
    getPickup(pickup) {
        if (!pickup) return;

        switch (pickup.itemType) {
            case PickupType.GEM:
                // Add experience
                if (this.player) {
                    if (this.player.godMode) {
                        // In god mode, one gem is enough to level up
                        const maxXp = this.calculateMaxXp(this.player.level);
                        this.player.addXp(maxXp); // Add enough XP to level up

                        // Show special effect for god mode level up
                        this.showDamageAt(this.player.x, this.player.y, "LEVEL UP!");
                    } else {
                        // Normal XP gain - use the pickup's value (without growth factor)
                        // The growth factor is meant to increase gem drop rates, not XP per gem
                        this.player.addXp(pickup.value);

                        // Show the point value for feedback
                        this.showDamageAt(pickup.x, pickup.y - 20, `+${pickup.value}`);
                    }
                }
                break;

            case PickupType.COIN:
                // Add coins
                this.playerOptions.coins += pickup.value;
                this.playerOptions.lifetimeCoins += pickup.value;

                if (this.mainUI) {
                    this.mainUI.updateCoins();
                }
                break;

            case PickupType.ROAST:
                // Heal player
                if (this.player) {
                    this.player.recoverHp(pickup.value);
                }
                break;

            case PickupType.VACUUM:
                // Activate vacuum effect
                this.turnOnVacuum();
                break;
        }
    }

    // Handle magnet overlapping pickup
    onMagnetOverlapsPickup(_, pickup) {
        pickup.vacuum();
    }

    // Reset weapon cooldowns
    resetWeaponCooldowns() {
        this.weapons.forEach(weapon => {
            weapon.resetFiringTimer();
        });
    }

    // Check for level up
    checkForLevelUp() {
        if (!this.player) return;

        const maxXp = this.calculateMaxXp(this.player.level);

        if (this.player.xp >= maxXp) {
            // Level up
            this.player.xp -= maxXp;
            this.player.levelUp();

            // Show level up UI
            this.sceneManager.enterLevelUp();
        }

        // Update UI
        if (this.playerUI) {
            this.playerUI.update();
        }
    }

    // Calculate maximum XP for a level
    calculateMaxXp(level) {
        // Starting at 5 points for level 1, doubling each level (5→10→20→40, etc.)
        const baseXp = 5;
        return baseXp * Math.pow(2, level - 1);
    }

    // Check for stage update
    checkStageUpdate() {
        // For testing purposes, make stages change faster (every 20 seconds instead of 60)
        const minute = Math.floor(this.survivedSeconds / 20);

        // Check if there's a stage update for this minute
        const stageData = STAGES[this.playerOptions.selectedStage].find(
            stage => stage.minute === minute
        );

        if (stageData) {
            this.stage.updateData(stageData);
        }
    }

    // Show damage number or text at position
    showDamageAt(x, y, amount, color) {
        this.damageNumbers.push({
            x,
            y,
            amount: typeof amount === 'number' ? Math.floor(amount) : amount,
            lifetime: 0,
            maxLifetime: 1000,
            velocity: new Vector2(
                (Math.random() - 0.5) * 2,
                -2 - Math.random() * 2
            ),
            isText: typeof amount === 'string',
            color: color || null // Use custom color if provided
        });
    }

    // Stop time
    stopTime(duration) {
        this.isTimeStopped = true;

        // Resume after duration
        setTimeout(() => {
            this.clearTimeStop();
        }, duration);
    }

    // Clear time stop
    clearTimeStop() {
        this.isTimeStopped = false;
    }

    // Game over
    gameOver() {
        this.isGameOver = true;

        // Automatically reload the page after 3 seconds
        setTimeout(() => {
            // Save any necessary game state to localStorage if needed
            if (this.playerOptions) {
                this.playerOptions.save();
            }

            // Reload the page instead of trying to restart
            window.location.reload();
        }, 3000);
    }

    // Draw game over screen
    drawGameOver() {
        // Show game over message
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        this.ctx.font = 'bold 48px Arial';
        this.ctx.fillStyle = 'white';
        this.ctx.textAlign = 'center';
        this.ctx.fillText(
            'GAME OVER',
            this.canvas.width / 2,
            this.canvas.height / 2 - 50
        );

        this.ctx.font = '24px Arial';
        this.ctx.fillText(
            `Survived: ${formatTime(this.survivedSeconds)}`,
            this.canvas.width / 2,
            this.canvas.height / 2
        );

        this.ctx.fillText(
            `Enemies Defeated: ${this.playerOptions.runEnemies}`,
            this.canvas.width / 2,
            this.canvas.height / 2 + 40
        );

        this.ctx.fillText(
            'Reloading in 3 seconds...',
            this.canvas.width / 2,
            this.canvas.height / 2 + 100
        );
    }

    // Restart the game
    restart() {
        // Clear game objects
        this.enemies = [];
        this.bullets = [];
        this.enemyProjectiles = [];
        this.pickups = [];
        this.destructibles = [];
        this.weapons = [];
        this.damageNumbers = [];

        // Reset enemy pools
        if (this.stage) {
            this.stage.resetEnemyPools();
        }

        // Reset pickup pools
        this.pickupPool = new PickupGroup();
        this.gemsPool = new PickupGroup();
        this.destructiblesPool = new DestructibleGroup();

        // Reset game state
        this.isPaused = false;
        this.isGameOver = false;
        this.isTimeStopped = false;
        this.survivedSeconds = 0;

        // Reset player options for this run
        this.playerOptions.runEnemies = 0;
        this.playerOptions.runCoins = 0;

        // Reinitialize
        this.init();


    }

    // Clean up
    cleanUp() {
        this.enemies = [];
        this.bullets = [];
        this.enemyProjectiles = [];
        this.pickups = [];
        this.destructibles = [];
        this.weapons = [];
        this.damageNumbers = [];
    }

    // Update the game
    update(deltaTime) {
        if (this.isPaused || this.isGameOver) return;

        // Update input
        this.joystick.update();

        // Check for god mode toggle (G key)
        if (this.input.isKeyPressed('g') && !this.lastGKeyState) {
            if (this.player) {
                this.player.godMode = !this.player.godMode;

            }
        }
        this.lastGKeyState = this.input.isKeyPressed('g');

        // Check for unlock all weapons (U key)
        if (this.input.isKeyPressed('u') && !this.lastUKeyState) {
            this.unlockAllWeapons();

        }
        this.lastUKeyState = this.input.isKeyPressed('u');

        // Update player
        if (this.player) {
            // Handle movement
            let dx = 0;
            let dy = 0;

            // Keyboard input
            if (this.input.isKeyPressed('ArrowUp') || this.input.isKeyPressed('w')) {
                dy -= 1;
            }
            if (this.input.isKeyPressed('ArrowDown') || this.input.isKeyPressed('s')) {
                dy += 1;
            }
            if (this.input.isKeyPressed('ArrowLeft') || this.input.isKeyPressed('a')) {
                dx -= 1;
            }
            if (this.input.isKeyPressed('ArrowRight') || this.input.isKeyPressed('d')) {
                dx += 1;
            }

            // Joystick input (overrides keyboard)
            if (this.joystick.active) {
                dx = this.joystick.vector.x;
                dy = this.joystick.vector.y;
            }

            // Move player
            this.player.move(dx, dy, deltaTime);
            this.player.update(deltaTime);
        }

        // Update camera
        if (this.player) {
            this.camera.x = this.player.x;
            this.camera.y = this.player.y;
        }

        // Update background
        if (this.bgManager) {
            this.bgManager.update(deltaTime);
        }

        // Update weapons
        this.weapons.forEach(weapon => {
            weapon.update(deltaTime);
        });

        // Update bullets
        for (let i = this.bullets.length - 1; i >= 0; i--) {
            this.bullets[i].update(deltaTime);
        }

        // Update enemy projectiles
        if (this.enemyProjectiles && this.enemyProjectiles.length > 0) {
            // Update enemy projectiles

            // First update all projectiles
            for (let i = 0; i < this.enemyProjectiles.length; i++) {
                if (this.enemyProjectiles[i]) {
                    try {
                        this.enemyProjectiles[i].update(deltaTime);
                    } catch (error) {

                        // Mark problematic projectile as dead
                        this.enemyProjectiles[i].isDead = true;
                    }
                }
            }

            // Then remove all dead projectiles in a separate pass
            for (let i = this.enemyProjectiles.length - 1; i >= 0; i--) {
                if (!this.enemyProjectiles[i] || (this.enemyProjectiles[i] && this.enemyProjectiles[i].isDead)) {
                    this.enemyProjectiles.splice(i, 1);
                }
            }
        }

        // Update enemies
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            this.enemies[i].update(deltaTime);

            // Check collision with player
            if (this.player && !this.player.isDead && !this.player.isInvul) {
                const dist = distance(
                    this.player.x, this.player.y,
                    this.enemies[i].x, this.enemies[i].y
                );

                if (dist < this.player.radius + this.enemies[i].radius) {
                    // Player takes damage
                    const damage = this.enemies[i].power;
                    this.player.takeDamage(damage);

                    // Show damage number
                    this.showDamageAt(this.player.x, this.player.y, damage);
                }
            }
        }

        // Update pickups
        for (let i = this.pickups.length - 1; i >= 0; i--) {
            this.pickups[i].update(deltaTime);

            // Check collision with player
            if (this.player && !this.player.isDead) {
                const dist = distance(
                    this.player.x, this.player.y,
                    this.pickups[i].x, this.pickups[i].y
                );

                if (dist < this.player.radius + this.pickups[i].radius) {
                    // Collect pickup
                    this.pickups[i].getPickedUp();
                }
            }
        }

        // Update destructibles
        for (let i = this.destructibles.length - 1; i >= 0; i--) {
            this.destructibles[i].update(deltaTime);
        }

        // Update damage numbers
        for (let i = this.damageNumbers.length - 1; i >= 0; i--) {
            const damageNumber = this.damageNumbers[i];

            // Update position
            damageNumber.x += damageNumber.velocity.x;
            damageNumber.y += damageNumber.velocity.y;

            // Update velocity (add gravity)
            damageNumber.velocity.y += 0.1;

            // Update lifetime
            damageNumber.lifetime += deltaTime;

            // Remove if expired
            if (damageNumber.lifetime >= damageNumber.maxLifetime) {
                this.damageNumbers.splice(i, 1);
            }
        }

        // Check containment rectangles
        if (this.containmentRect_Screen) {
            this.containmentRect_Screen.update();

            // Check bullets (can be aggressive with cleanup)
            for (let i = 0; i < Math.min(10, this.bullets.length); i++) {
                this.containmentRect_Screen.despawnIfOutside(this.bullets);
            }

            // Check pickups (can be aggressive with cleanup)
            for (let i = 0; i < Math.min(10, this.pickups.length); i++) {
                this.containmentRect_Screen.despawnIfOutside(this.pickups);
            }

            // Check enemies (be more conservative with cleanup)
            // Only check a few enemies each frame to avoid mass disappearance
            const maxEnemiesToCheck = 3;
            for (let i = 0; i < Math.min(maxEnemiesToCheck, this.enemies.length); i++) {
                // Mark all enemies as teleportable instead of despawning them
                const enemy = this.enemies[i];
                if (enemy && !enemy.isDead) {
                    // Always make enemies teleport instead of despawn
                    enemy.isTeleportOnCull = true;
                }

                this.containmentRect_Screen.despawnIfOutside(this.enemies);
            }
        }

        // Update stage and spawn new enemies if needed
        if (this.stage && !this.isTimeStopped && !this.isPaused && !this.isGameOver) {
            // Update stage (handles phase transitions and other time-based events)
            this.stage.update(deltaTime);

            // Check if we need to spawn more enemies to meet the minimum
            const currentEnemyCount = this.enemies.length;
            const minimumEnemies = this.stage.minimum;
            const maximumEnemies = this.stage.maximum || 50; // Default max if not specified

            // Only spawn if we're below minimum and not exceeding maximum
            if (currentEnemyCount < minimumEnemies && currentEnemyCount < maximumEnemies) {
                // Spawn a new enemy with a 5% chance per frame (to avoid sudden mass spawning)
                if (Math.random() < 0.05) {
                    this.stage.spawnEnemiesInOuterRect();
                }
            }

            // Display current phase in debug info
            if (this.debug && this.stage.currentPhase) {
                this.currentPhase = this.stage.currentPhase;
                this.gameTime = this.stage.gameTime;
            }
        }
    }

    // Draw the game
    draw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Draw background
        if (this.bgManager) {
            this.bgManager.draw(this.ctx, this.camera);
        }

        // Draw destructibles
        this.destructibles.forEach(destructible => {
            destructible.draw(this.ctx, this.camera, this.sprites);
        });

        // Draw pickups
        this.pickups.forEach(pickup => {
            pickup.draw(this.ctx, this.camera, this.sprites);
        });

        // Draw enemies
        this.enemies.forEach(enemy => {
            enemy.draw(this.ctx, this.camera, this.sprites);
        });

        // Draw player
        if (this.player) {
            this.player.draw(this.ctx, this.camera, this.sprites);
        }

        // Draw bullets
        this.bullets.forEach(bullet => {
            bullet.draw(this.ctx, this.camera, this.sprites);
        });

        // Draw enemy projectiles
        if (this.enemyProjectiles && this.enemyProjectiles.length > 0) {
            // Draw enemy projectiles
            this.enemyProjectiles.forEach(projectile => {
                if (projectile && typeof projectile.draw === 'function') {
                    try {
                        projectile.draw(this.ctx, this.camera, this.sprites);
                    } catch (error) {

                    }
                }
            });
        }

        // Draw damage numbers
        this.damageNumbers.forEach(damageNumber => {
            const screenX = damageNumber.x - this.camera.x + this.camera.width / 2;
            const screenY = damageNumber.y - this.camera.y + this.camera.height / 2;

            // Calculate alpha based on lifetime
            const alpha = 1 - damageNumber.lifetime / damageNumber.maxLifetime;

            if (damageNumber.isText) {
                // Draw text message (like "LEVEL UP!")
                this.ctx.font = 'bold 20px Arial';

                // Use custom color if provided, otherwise use gold
                const textColor = damageNumber.color || `rgba(255, 215, 0, ${alpha})`;
                this.ctx.fillStyle = textColor;
                this.ctx.strokeStyle = `rgba(0, 0, 0, ${alpha})`;
                this.ctx.lineWidth = 2;
                this.ctx.textAlign = 'center';

                // Draw text with outline
                this.ctx.strokeText(
                    damageNumber.amount,
                    screenX,
                    screenY
                );
                this.ctx.fillText(
                    damageNumber.amount,
                    screenX,
                    screenY
                );
            } else {
                // Draw regular damage number
                this.ctx.font = 'bold 16px Arial';
                this.ctx.fillStyle = `rgba(255, 0, 0, ${alpha})`;
                this.ctx.textAlign = 'center';
                this.ctx.fillText(
                    damageNumber.amount.toString(),
                    screenX,
                    screenY
                );
            }
        });

        // Draw UI
        if (this.playerUI) {
            this.playerUI.draw(this.sprites);
        }

        if (this.mainUI) {
            this.mainUI.draw(this.sprites);
        }

        // Draw joystick
        if (this.joystick.visible) {
            this.joystick.draw(this.ctx);
        }

        // Draw level up UI
        if (this.levelUpUI && this.levelUpUI.visible) {
            this.levelUpUI.draw(this.sprites);
        }

        // Draw game over screen if game is over
        if (this.isGameOver) {
            this.drawGameOver();
        }

        // Draw debug info
        if (this.debug) {
            this.drawDebugInfo();
        }
    }

    // Draw debug information
    drawDebugInfo() {
        this.ctx.font = '12px Arial';
        this.ctx.fillStyle = 'black';
        this.ctx.textAlign = 'left';

        // Game stats
        this.ctx.fillText(`FPS: ${Math.round(1000 / Game.deltaTime)}`, 10, this.canvas.height - 105);
        this.ctx.fillText(`Game Time: ${Math.floor(this.gameTime / 60)}:${Math.floor(this.gameTime % 60).toString().padStart(2, '0')}`, 10, this.canvas.height - 90);
        this.ctx.fillText(`Phase: ${this.currentPhase || 1}`, 10, this.canvas.height - 75);

        // Entity counts
        this.ctx.fillText(`Enemies: ${this.enemies.length}`, 10, this.canvas.height - 60);
        this.ctx.fillText(`Bullets: ${this.bullets.length}`, 10, this.canvas.height - 45);
        this.ctx.fillText(`Enemy Projectiles: ${this.enemyProjectiles.length}`, 10, this.canvas.height - 30);
        this.ctx.fillText(`Pickups: ${this.pickups.length}`, 10, this.canvas.height - 15);
    }
}

// PlayerOptions class for storing player options
class PlayerOptions {
    constructor() {
        this.runCoins = 0;
        this.runEnemies = 0; // Initialize to 0 for enemy counter
        this.selectedStage = StageType.FOREST;
        this.selectedCharacter = CharacterType.WOOZY;
        this.selectedHyper = false;
        this.lifetimeSurvived = 0;
        this.soundsEnabled = true;
        this.musicEnabled = true;
        this.flashingVFXEnabled = true;
        this.joystickVisible = true;
        this.damageNumbersEnabled = true;
        this.cheatCodeUsed = false;
        this.boughtCharacters = [];
        this.boughtPowerups = [];
        this.collectedWeapons = [];
        this.unlockedWeapons = [];
        this.unlockedCharacters = [];
        this.collectedItems = [];
        this.achievements = [];
        this.unlockedStages = [];
        this.unlockedHypers = [];
        this.killCount = {};
        this.pickupCount = {};
        this.destroyedCount = {};
        this.coins = 0;
        this.lifetimeCoins = 0;
    }

    // Load options from storage
    load() {
        const savedOptions = localStorage.getItem('vampireGameOptions');

        if (savedOptions) {
            try {
                const options = JSON.parse(savedOptions);
                Object.assign(this, options);
            } catch (error) {

            }
        }
    }

    // Save options to storage
    save() {
        try {
            localStorage.setItem('vampireGameOptions', JSON.stringify(this));
        } catch (error) {

        }
    }

    // Add coins
    addCoins(amount) {
        this.coins += amount;
        this.lifetimeCoins += amount;
        this.save();
    }
}

// LootManager class for managing loot drops
class LootManager {
    constructor() {
        this.lootTable = [];
    }

    // Initialize the loot manager
    init() {
        // Add pickups to loot table with weights
        this.lootTable = [
            { type: PickupType.COIN, weight: 50 },
            { type: PickupType.ROAST, weight: 20 },
            { type: PickupType.VACUUM, weight: 5 }
        ];
    }

    // Get a random weighted item
    getRandomWeightedItem() {
        // Calculate total weight
        const totalWeight = this.lootTable.reduce(
            (sum, item) => sum + item.weight,
            0
        );

        // Get a random value
        const random = Math.random() * totalWeight;

        // Find the item
        let weightSum = 0;
        for (const item of this.lootTable) {
            weightSum += item.weight;
            if (random < weightSum) {
                return item.type;
            }
        }

        // Default to coin
        return PickupType.COIN;
    }
}
