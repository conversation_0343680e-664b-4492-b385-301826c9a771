import { useMemo, useState } from 'react';
import { Button } from '../ui/button';
import Drawer from '../ui/drawer';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'react-toastify';
import { BattlxIcon } from '@/components/icons/BattlxIcon';
import { useUserStore } from '@/store/user-store';
import { gameApi } from '@/lib/game-api';

const PLAY_PRICE = 500;

type TowerPlayDrawerProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
};

export default function TowerPlayDrawer({ open, onOpenChange }: TowerPlayDrawerProps) {
  const { balance, tower_game_plays } = useUserStore();
  const [playsToAdd, setPlaysToAdd] = useState(1);

  const totalCost = useMemo(() => {
    return playsToAdd * PLAY_PRICE;
  }, [playsToAdd]);

  const insufficientBalance = useMemo(() => {
    return balance < totalCost;
  }, [balance, totalCost]);

  const purchasePlaysMutation = useMutation({
    mutationFn: async () => {
      const result = await gameApi.usePlay(true, playsToAdd);
      if (!result.success) {
        throw new Error(result.message || 'Failed to purchase plays');
      }
      return result;
    },
    onSuccess: (response) => {
      if (response.success && response.data) {
        const { plays_remaining, balance } = response.data;
        if (typeof plays_remaining !== 'number' || typeof balance !== 'number') {
          throw new Error('Invalid response data');
        }
        
        toast.success(`Successfully purchased ${playsToAdd} plays`);
        // Update user state with the new data
        useUserStore.setState((state) => ({
          ...state,
          tower_game_plays: plays_remaining,
          balance: balance
        }));
        onOpenChange(false);
      } else {
        throw new Error('Invalid response data');
      }
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Failed to purchase plays');
    },
  });

  return (
    <Drawer 
      open={open}
      onOpenChange={onOpenChange}
    >
      <div className="!rounded-none [&_[data-drawer-content]]:!rounded-none">
        <img
          src="/game/thumbnails/tower.png"
          alt="Tower Game"
          className="object-contain h-32 mx-auto opacity-80 rounded-none [filter:sepia(50%)_saturate(50%)_brightness(80%)_contrast(120%)]"
        />
        <h2 className="mt-6 text-2xl font-medium text-center text-[#9B8B6C]">Purchase Plays</h2>
        
        <div className="flex flex-col mx-auto mt-4">
          <p className="text-center text-[#B3B3B3]/80">
            Current Plays: {tower_game_plays}
          </p>
          
          <div className="flex items-center justify-center gap-4 mt-4">
            <button
              className="w-8 h-8 flex items-center justify-center text-[#9B8B6C] bg-[#1A1617] border border-[#B3B3B3]/20 rounded-lg disabled:opacity-50"
              onClick={() => setPlaysToAdd(Math.max(1, playsToAdd - 1))}
              disabled={playsToAdd <= 1}
            >
              -
            </button>
            <span className="text-xl text-[#9B8B6C]">{playsToAdd}</span>
            <button
              className="w-8 h-8 flex items-center justify-center text-[#9B8B6C] bg-[#1A1617] border border-[#B3B3B3]/20 rounded-lg disabled:opacity-50"
              onClick={() => setPlaysToAdd(Math.min(100, playsToAdd + 1))}
              disabled={playsToAdd >= 100}
            >
              +
            </button>
          </div>

          <div className="flex items-center justify-center mx-auto mt-6 space-x-1 text-[#9B8B6C]">
            <BattlxIcon
              icon="coins"
              className="w-8 h-8 text-[#9B8B6C]"
            />
            <span className="font-bold">
              {totalCost.toLocaleString()}
            </span>
          </div>

          {insufficientBalance && (
            <div className="flex items-center justify-center mt-4">
              <p className="text-sm text-[#B3B3B3]/80">
                Need {(totalCost - balance).toLocaleString()} more coins
              </p>
            </div>
          )}

          <Button
            className="w-full mt-6 bg-[#1A1617] border border-[#B3B3B3]/20 text-[#9B8B6C] hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_15px_rgba(74,14,14,0.3)] disabled:opacity-50 disabled:cursor-not-allowed rounded-none"
            disabled={purchasePlaysMutation.isPending || insufficientBalance}
            onClick={() => purchasePlaysMutation.mutate()}
          >
            {purchasePlaysMutation.isPending && (
              <BattlxIcon icon="loading" className="w-6 h-6 mr-2 animate-spin" />
            )}
            {insufficientBalance ? "Insufficient Balance" : "Purchase Plays"}
          </Button>
        </div>
      </div>
    </Drawer>
  );
}