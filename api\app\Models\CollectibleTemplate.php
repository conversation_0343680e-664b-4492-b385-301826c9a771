<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CollectibleTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'collectible_id', 'name', 'type', 'category', 'rarity', 'description',
        'image_url', 'collection_set_id', 'set_position', 'unlock_source',
        'unlock_requirement', 'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function collectibles(): Has<PERSON>any
    {
        return $this->hasMany(Collectible::class, 'collectible_id', 'collectible_id');
    }

    public function collectionSet(): BelongsTo
    {
        return $this->belongsTo(CollectionSet::class, 'collection_set_id', 'set_id');
    }

    public function petTemplates(): HasMany
    {
        return $this->hasMany(PetTemplate::class, 'collectible_reward_id', 'collectible_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByRarity($query, $rarity)
    {
        return $query->where('rarity', $rarity);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCollectionSet($query, $setId)
    {
        return $query->where('collection_set_id', $setId);
    }

    // Accessors
    public function getImageUrlAttribute($value)
    {
        return $value ? asset($value) : null;
    }

    public function getRarityColorAttribute()
    {
        return match($this->rarity) {
            'common' => '#9CA3AF',
            'rare' => '#3B82F6',
            'epic' => '#8B5CF6',
            'legendary' => '#F59E0B',
            'mythic' => '#EF4444',
            default => '#6B7280'
        };
    }

    public function getTypeIconAttribute()
    {
        return match($this->type) {
            'artifact' => '🏺',
            'trophy' => '🏆',
            'relic' => '⚱️',
            'essence' => '💎',
            'scroll' => '📜',
            default => '❓'
        };
    }

    // Business Logic Methods
    public function isOwnedBy(TelegramUser $user): bool
    {
        return $user->collectibles()
            ->where('collectible_id', $this->collectible_id)
            ->exists();
    }

    public function canBeUnlockedBy(TelegramUser $user): bool
    {
        if (!$this->is_active) {
            return false;
        }

        return match($this->unlock_source) {
            'pet_purchase' => $this->canBeUnlockedByPetPurchase($user),
            'mystery_box' => true, // Can always be unlocked from mystery boxes
            'prize_tree' => $this->canBeUnlockedByPrizeTree($user),
            'collection_bonus' => false, // Unlocked automatically when collection is completed
            default => false
        };
    }

    private function canBeUnlockedByPetPurchase(TelegramUser $user): bool
    {
        if (!$this->unlock_requirement) {
            return false;
        }

        // Check if user owns the required pet
        $petTemplate = PetTemplate::where('name', $this->unlock_requirement)->first();
        if (!$petTemplate) {
            return false;
        }

        return $user->pets()->where('pet_template_id', $petTemplate->id)->exists();
    }

    private function canBeUnlockedByPrizeTree(TelegramUser $user): bool
    {
        if (!$this->unlock_requirement) {
            return false;
        }

        $requiredLevel = (int) $this->unlock_requirement;
        $userProgress = $user->prizeTreeProgress();
        
        return $userProgress && $userProgress->current_level >= $requiredLevel;
    }

    public function getEstimatedValueAttribute(): int
    {
        return match($this->rarity) {
            'common' => 100,
            'rare' => 500,
            'epic' => 2000,
            'legendary' => 10000,
            'mythic' => 50000,
            default => 50
        };
    }
}
