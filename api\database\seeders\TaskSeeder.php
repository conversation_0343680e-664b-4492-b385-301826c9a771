<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Task;
use App\Models\TaskType;

class TaskSeeder extends Seeder
{
    public function run()
    {
        // Get the task types
        $videoType = TaskType::where('name', 'video')->firstOrFail();
        $otherType = TaskType::where('name', 'other')->firstOrFail();

        $tasks = [
            [
                'title' => 'Watch Tutorial Video',
                'description' => 'Watch our game tutorial video on YouTube.',
                'reward' => 100,
                'url' => 'https://youtube.com',
                'type_id' => $videoType->id,
            ],
            [
                'title' => 'Join Our Discord',
                'description' => 'Join our official Discord server and say hello in the #welcome channel.',
                'reward' => 100,
                'url' => 'https://discord.gg/yourgame',
                'type_id' => $otherType->id,
            ],
            [
                'title' => 'Follow on Twitter',
                'description' => 'Follow our official Twitter account and retweet our pinned tweet.',
                'reward' => 150,
                'url' => 'https://twitter.com/yourgame',
                'type_id' => $otherType->id,
            ],
            [
                'title' => 'Like Facebook Page',
                'description' => 'Like our Facebook page and leave a comment on our latest post.',
                'reward' => 100,
                'url' => 'https://facebook.com/yourgame',
                'type_id' => $otherType->id,
            ],
            [
                'title' => 'Follow on Instagram',
                'description' => 'Follow our Instagram account and like our most recent post.',
                'reward' => 125,
                'url' => 'https://instagram.com/yourgame',
                'type_id' => $otherType->id,
            ],
            [
                'title' => 'Join Telegram Group',
                'description' => 'Join our Telegram group and introduce yourself.',
                'reward' => 175,
                'url' => 'https://t.me/yourgame',
                'type_id' => $otherType->id,
            ],
            [
                'title' => 'Sign Up for Newsletter',
                'description' => 'Subscribe to our weekly newsletter for game updates and tips.',
                'reward' => 80,
                'url' => 'https://yourgame.com/newsletter',
                'type_id' => $otherType->id,
            ],
            [
                'title' => 'Review on App Store',
                'description' => 'Leave a review for our game on the App Store or Google Play Store.',
                'reward' => 300,
                'url' => 'https://yourgame.com/review',
                'type_id' => $otherType->id,
            ],
        ];

        foreach ($tasks as $task) {
            Task::create($task);
        }
    }
}
