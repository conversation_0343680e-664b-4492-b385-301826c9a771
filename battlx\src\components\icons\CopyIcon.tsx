export default function CopyIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.7143 2.14286H10C9.43168 2.14286 8.88663 2.36862 8.48477 2.77049C8.08291 3.17235 7.85714 3.71739 7.85714 4.28571V5.71429H10C11.1366 5.71429 12.2267 6.16582 13.0305 6.96954C13.8342 7.77327 14.2857 8.86336 14.2857 10V12.1429H15.7143C16.2826 12.1429 16.8277 11.9171 17.2295 11.5152C17.6314 11.1134 17.8571 10.5683 17.8571 10V4.28571C17.8571 3.71739 17.6314 3.17235 17.2295 2.77049C16.8277 2.36862 16.2826 2.14286 15.7143 2.14286ZM14.2857 14.2857H15.7143C16.8509 14.2857 17.941 13.8342 18.7447 13.0305C19.5485 12.2267 20 11.1366 20 10V4.28571C20 3.14907 19.5485 2.05898 18.7447 1.25526C17.941 0.451529 16.8509 0 15.7143 0H10C8.86336 0 7.77327 0.451529 6.96954 1.25526C6.16582 2.05898 5.71429 3.14907 5.71429 4.28571V5.71429H4.28571C3.14907 5.71429 2.05898 6.16582 1.25526 6.96954C0.451529 7.77327 0 8.86336 0 10V15.7143C0 16.8509 0.451529 17.941 1.25526 18.7447C2.05898 19.5485 3.14907 20 4.28571 20H10C11.1366 20 12.2267 19.5485 13.0305 18.7447C13.8342 17.941 14.2857 16.8509 14.2857 15.7143V14.2857ZM4.28571 7.85714H10C10.5683 7.85714 11.1134 8.08291 11.5152 8.48477C11.9171 8.88663 12.1429 9.43168 12.1429 10V15.7143C12.1429 16.2826 11.9171 16.8277 11.5152 17.2295C11.1134 17.6314 10.5683 17.8571 10 17.8571H4.28571C3.71739 17.8571 3.17235 17.6314 2.77049 17.2295C2.36862 16.8277 2.14286 16.2826 2.14286 15.7143V10C2.14286 9.43168 2.36862 8.88663 2.77049 8.48477C3.17235 8.08291 3.71739 7.85714 4.28571 7.85714Z"
        fill="black"
      />
    </svg>
  );
}
