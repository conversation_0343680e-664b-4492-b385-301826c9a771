interface GameInstance {
    init(): void;
    start(): void;
    load(onReady: () => void, onProgress?: (status: { success: number; total: number; failed: number }) => void): void;
    playBgm(): void;
    pauseBgm(): void;
    destroy(): void;
    setVariable(name: string, value: any): void;
}

interface TowerGameOptions {
    width: number;
    height: number;
    canvasId: string;
    soundOn: boolean;
    setGameScore: (score: number) => void;
}

export function TowerGame(options: TowerGameOptions): GameInstance;