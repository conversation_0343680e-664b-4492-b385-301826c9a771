# Phase 3: Dynamic Quest System Implementation

## Overview
Replace static missions with an adaptive, AI-driven quest system that creates personalized challenges, community events, and emergent gameplay opportunities.

## Backend Implementation

### 1. Database Schema

#### Dynamic Quest System Tables
```php
// 2024_XX_XX_create_dynamic_quest_system.php

Schema::create('quest_templates', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->text('description_template'); // With placeholders like {target_player}, {amount}
    $table->enum('category', ['combat', 'exploration', 'social', 'economic', 'survival']);
    $table->enum('difficulty', ['easy', 'medium', 'hard', 'legendary']);
    $table->json('requirements'); // Level, guild membership, etc.
    $table->json('objectives'); // Dynamic objective templates
    $table->json('reward_templates'); // Reward calculation formulas
    $table->integer('base_experience')->default(100);
    $table->integer('cooldown_hours')->default(24);
    $table->boolean('is_active')->default(true);
    $table->timestamps();
});

Schema::create('active_quests', function (Blueprint $table) {
    $table->id();
    $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
    $table->foreignId('quest_template_id')->constrained()->onDelete('cascade');
    $table->string('generated_title');
    $table->text('generated_description');
    $table->json('objectives'); // Specific objectives with current progress
    $table->json('rewards'); // Calculated rewards for this instance
    $table->json('context_data'); // Target players, locations, etc.
    $table->enum('status', ['active', 'completed', 'failed', 'expired']);
    $table->timestamp('expires_at');
    $table->timestamp('completed_at')->nullable();
    $table->integer('progress_percentage')->default(0);
    $table->timestamps();
});

Schema::create('quest_objectives', function (Blueprint $table) {
    $table->id();
    $table->foreignId('active_quest_id')->constrained()->onDelete('cascade');
    $table->string('objective_type'); // kill_players, win_battles, collect_resources
    $table->json('parameters'); // target_count, specific_targets, etc.
    $table->integer('current_progress')->default(0);
    $table->integer('target_progress');
    $table->boolean('is_completed')->default(false);
    $table->timestamps();
});

Schema::create('community_events', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->text('description');
    $table->enum('event_type', ['server_boss', 'resource_rush', 'guild_war', 'exploration']);
    $table->json('global_objectives'); // Server-wide goals
    $table->json('participation_rewards'); // Rewards for participation
    $table->json('milestone_rewards'); // Rewards for reaching milestones
    $table->integer('current_progress')->default(0);
    $table->integer('target_progress');
    $table->enum('status', ['upcoming', 'active', 'completed', 'failed']);
    $table->timestamp('starts_at');
    $table->timestamp('ends_at');
    $table->timestamps();
});

Schema::create('event_participants', function (Blueprint $table) {
    $table->id();
    $table->foreignId('community_event_id')->constrained()->onDelete('cascade');
    $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
    $table->integer('contribution_points')->default(0);
    $table->json('individual_progress')->nullable();
    $table->timestamp('joined_at');
    $table->timestamps();
    
    $table->unique(['community_event_id', 'telegram_user_id']);
});

Schema::create('bounty_targets', function (Blueprint $table) {
    $table->id();
    $table->foreignId('target_user_id')->constrained('telegram_users')->onDelete('cascade');
    $table->foreignId('posted_by_user_id')->constrained('telegram_users')->onDelete('cascade');
    $table->integer('reward_amount');
    $table->text('reason')->nullable();
    $table->enum('status', ['active', 'claimed', 'expired']);
    $table->foreignId('claimed_by_user_id')->nullable()->constrained('telegram_users');
    $table->timestamp('expires_at');
    $table->timestamp('claimed_at')->nullable();
    $table->timestamps();
});

Schema::create('exploration_zones', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->text('description');
    $table->integer('x_coordinate');
    $table->integer('y_coordinate');
    $table->integer('radius'); // Exploration area size
    $table->enum('zone_type', ['ancient_ruins', 'resource_cache', 'monster_den', 'mystery']);
    $table->json('discovery_rewards');
    $table->integer('required_exploration_level')->default(1);
    $table->boolean('is_discovered')->default(false);
    $table->foreignId('discovered_by_user_id')->nullable()->constrained('telegram_users');
    $table->timestamp('discovered_at')->nullable();
    $table->timestamps();
});
```

### 2. Models

#### QuestTemplate Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class QuestTemplate extends Model
{
    protected $fillable = [
        'name', 'description_template', 'category', 'difficulty',
        'requirements', 'objectives', 'reward_templates',
        'base_experience', 'cooldown_hours', 'is_active'
    ];

    protected $casts = [
        'requirements' => 'array',
        'objectives' => 'array',
        'reward_templates' => 'array',
        'is_active' => 'boolean'
    ];

    public function activeQuests()
    {
        return $this->hasMany(ActiveQuest::class);
    }

    public function generateQuestForUser(TelegramUser $user)
    {
        $questGenerator = new \App\Services\QuestGeneratorService();
        return $questGenerator->generateFromTemplate($this, $user);
    }

    public function meetsRequirements(TelegramUser $user): bool
    {
        foreach ($this->requirements as $requirement => $value) {
            switch ($requirement) {
                case 'min_level':
                    if ($user->level->level < $value) return false;
                    break;
                case 'guild_member':
                    if ($value && !$user->guild) return false;
                    break;
                case 'min_battles':
                    if ($user->playerStats->total_battles < $value) return false;
                    break;
            }
        }
        return true;
    }
}
```

#### ActiveQuest Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ActiveQuest extends Model
{
    protected $fillable = [
        'telegram_user_id', 'quest_template_id', 'generated_title',
        'generated_description', 'objectives', 'rewards', 'context_data',
        'status', 'expires_at', 'completed_at', 'progress_percentage'
    ];

    protected $casts = [
        'objectives' => 'array',
        'rewards' => 'array',
        'context_data' => 'array',
        'expires_at' => 'datetime',
        'completed_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function template()
    {
        return $this->belongsTo(QuestTemplate::class, 'quest_template_id');
    }

    public function questObjectives()
    {
        return $this->hasMany(QuestObjective::class);
    }

    public function updateProgress()
    {
        $totalObjectives = $this->questObjectives()->count();
        $completedObjectives = $this->questObjectives()->where('is_completed', true)->count();
        
        $this->progress_percentage = $totalObjectives > 0 
            ? ($completedObjectives / $totalObjectives) * 100 
            : 0;

        if ($this->progress_percentage >= 100) {
            $this->status = 'completed';
            $this->completed_at = now();
            $this->grantRewards();
        }

        $this->save();
    }

    public function grantRewards()
    {
        foreach ($this->rewards as $rewardType => $amount) {
            switch ($rewardType) {
                case 'coins':
                    $this->user->increment('balance', $amount);
                    break;
                case 'experience':
                    $this->user->addExperience($amount);
                    break;
                case 'energy':
                    $this->user->increment('available_energy', min($amount, $this->user->max_energy));
                    break;
            }
        }
    }

    public function isExpired(): bool
    {
        return now()->isAfter($this->expires_at);
    }
}
```

### 3. Services

#### QuestGeneratorService
```php
<?php

namespace App\Services;

use App\Models\QuestTemplate;
use App\Models\ActiveQuest;
use App\Models\QuestObjective;
use App\Models\TelegramUser;
use App\Models\BountyTarget;

class QuestGeneratorService
{
    public function generateDailyQuestsForUser(TelegramUser $user)
    {
        // Remove expired quests
        $user->activeQuests()
            ->where('expires_at', '<', now())
            ->update(['status' => 'expired']);

        // Check if user already has daily quests
        $activeQuestCount = $user->activeQuests()
            ->where('status', 'active')
            ->count();

        if ($activeQuestCount >= 3) {
            return; // User already has maximum active quests
        }

        // Generate new quests based on user's profile
        $availableTemplates = QuestTemplate::where('is_active', true)
            ->get()
            ->filter(function ($template) use ($user) {
                return $template->meetsRequirements($user);
            });

        $questsToGenerate = min(3 - $activeQuestCount, $availableTemplates->count());

        for ($i = 0; $i < $questsToGenerate; $i++) {
            $template = $availableTemplates->random();
            $this->generateFromTemplate($template, $user);
            $availableTemplates = $availableTemplates->reject(function ($t) use ($template) {
                return $t->id === $template->id;
            });
        }
    }

    public function generateFromTemplate(QuestTemplate $template, TelegramUser $user)
    {
        $contextData = $this->generateContextData($template, $user);
        $objectives = $this->generateObjectives($template, $user, $contextData);
        $rewards = $this->calculateRewards($template, $user);

        $quest = ActiveQuest::create([
            'telegram_user_id' => $user->id,
            'quest_template_id' => $template->id,
            'generated_title' => $this->generateTitle($template, $contextData),
            'generated_description' => $this->generateDescription($template, $contextData),
            'objectives' => $objectives,
            'rewards' => $rewards,
            'context_data' => $contextData,
            'status' => 'active',
            'expires_at' => now()->addHours(24)
        ]);

        // Create objective records
        foreach ($objectives as $objective) {
            QuestObjective::create([
                'active_quest_id' => $quest->id,
                'objective_type' => $objective['type'],
                'parameters' => $objective['parameters'],
                'target_progress' => $objective['target']
            ]);
        }

        return $quest;
    }

    protected function generateContextData(QuestTemplate $template, TelegramUser $user): array
    {
        $context = [];

        switch ($template->category) {
            case 'combat':
                // Find suitable PvP targets
                $context['target_players'] = $this->findPvPTargets($user);
                break;
            case 'social':
                // Find guild members or friends
                $context['social_targets'] = $this->findSocialTargets($user);
                break;
            case 'exploration':
                // Find unexplored zones
                $context['exploration_zones'] = $this->findExplorationZones($user);
                break;
            case 'economic':
                // Set resource targets
                $context['resource_targets'] = $this->generateResourceTargets($user);
                break;
        }

        return $context;
    }

    protected function generateObjectives(QuestTemplate $template, TelegramUser $user, array $context): array
    {
        $objectives = [];

        foreach ($template->objectives as $objectiveTemplate) {
            $objective = [
                'type' => $objectiveTemplate['type'],
                'target' => $this->scaleObjectiveTarget($objectiveTemplate['base_target'], $user),
                'parameters' => $this->fillObjectiveParameters($objectiveTemplate['parameters'], $context)
            ];
            $objectives[] = $objective;
        }

        return $objectives;
    }

    protected function calculateRewards(QuestTemplate $template, TelegramUser $user): array
    {
        $baseRewards = $template->reward_templates;
        $scalingFactor = 1 + ($user->level->level * 0.1); // 10% increase per level

        $rewards = [];
        foreach ($baseRewards as $rewardType => $baseAmount) {
            $rewards[$rewardType] = (int)($baseAmount * $scalingFactor);
        }

        return $rewards;
    }

    protected function findPvPTargets(TelegramUser $user): array
    {
        // Find players with similar ELO rating
        $userElo = $user->playerStats->elo_rating ?? 1000;
        
        return TelegramUser::whereHas('playerStats', function ($q) use ($userElo) {
            $q->whereBetween('elo_rating', [$userElo - 200, $userElo + 200]);
        })
        ->where('id', '!=', $user->id)
        ->inRandomOrder()
        ->take(5)
        ->pluck('id')
        ->toArray();
    }

    protected function generateTitle(QuestTemplate $template, array $context): string
    {
        $title = $template->name;
        
        // Replace placeholders with context data
        foreach ($context as $key => $value) {
            if (is_array($value) && !empty($value)) {
                $title = str_replace("{{$key}}", $value[0], $title);
            } else {
                $title = str_replace("{{$key}}", $value, $title);
            }
        }

        return $title;
    }

    protected function generateDescription(QuestTemplate $template, array $context): string
    {
        $description = $template->description_template;
        
        // Replace placeholders with context data
        foreach ($context as $key => $value) {
            if (is_array($value)) {
                $description = str_replace("{{$key}}", implode(', ', $value), $description);
            } else {
                $description = str_replace("{{$key}}", $value, $description);
            }
        }

        return $description;
    }
}
```

#### CommunityEventService
```php
<?php

namespace App\Services;

use App\Models\CommunityEvent;
use App\Models\EventParticipant;
use App\Models\TelegramUser;

class CommunityEventService
{
    public function createServerBossEvent()
    {
        $event = CommunityEvent::create([
            'name' => 'Ancient Dragon Awakening',
            'description' => 'A legendary dragon has awakened! All players must work together to defeat it.',
            'event_type' => 'server_boss',
            'global_objectives' => [
                'total_damage' => ['target' => 1000000, 'current' => 0],
                'participants' => ['target' => 100, 'current' => 0]
            ],
            'participation_rewards' => [
                'coins' => 1000,
                'experience' => 500
            ],
            'milestone_rewards' => [
                25 => ['coins' => 2000, 'rare_item' => 1],
                50 => ['coins' => 3000, 'legendary_item' => 1],
                75 => ['coins' => 5000, 'epic_item' => 1],
                100 => ['coins' => 10000, 'mythic_item' => 1]
            ],
            'target_progress' => 100,
            'status' => 'active',
            'starts_at' => now(),
            'ends_at' => now()->addDays(3)
        ]);

        // Notify all active users
        $this->notifyAllUsers('server_boss_started', $event);

        return $event;
    }

    public function joinEvent(CommunityEvent $event, TelegramUser $user)
    {
        $participant = EventParticipant::firstOrCreate([
            'community_event_id' => $event->id,
            'telegram_user_id' => $user->id
        ], [
            'joined_at' => now()
        ]);

        // Update participant count
        $objectives = $event->global_objectives;
        $objectives['participants']['current'] = $event->participants()->count();
        $event->update(['global_objectives' => $objectives]);

        return $participant;
    }

    public function updateEventProgress(CommunityEvent $event, TelegramUser $user, string $progressType, int $amount)
    {
        $participant = $event->participants()
            ->where('telegram_user_id', $user->id)
            ->first();

        if (!$participant) {
            $participant = $this->joinEvent($event, $user);
        }

        // Update individual progress
        $individualProgress = $participant->individual_progress ?? [];
        $individualProgress[$progressType] = ($individualProgress[$progressType] ?? 0) + $amount;
        $participant->update(['individual_progress' => $individualProgress]);

        // Update global progress
        $objectives = $event->global_objectives;
        if (isset($objectives[$progressType])) {
            $objectives[$progressType]['current'] += $amount;
            $event->update(['global_objectives' => $objectives]);

            // Check for milestone completion
            $this->checkMilestones($event);
        }
    }

    protected function checkMilestones(CommunityEvent $event)
    {
        $progressPercentage = ($event->current_progress / $event->target_progress) * 100;
        
        foreach ($event->milestone_rewards as $milestone => $rewards) {
            if ($progressPercentage >= $milestone && !$this->isMilestoneReached($event, $milestone)) {
                $this->grantMilestoneRewards($event, $milestone, $rewards);
                $this->markMilestoneReached($event, $milestone);
            }
        }
    }

    protected function notifyAllUsers(string $eventType, $data)
    {
        // Implementation for mass notification
        broadcast(new \App\Events\CommunityEventNotification($eventType, $data));
    }
}
```

## Frontend Implementation

### 1. Dynamic Quest Interface

```typescript
// src/components/quests/DynamicQuestBoard.tsx
import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { $http } from '@/lib/http';

interface Quest {
  id: number;
  generated_title: string;
  generated_description: string;
  objectives: Array<{
    type: string;
    target: number;
    current_progress: number;
    parameters: any;
  }>;
  rewards: {
    coins?: number;
    experience?: number;
    energy?: number;
  };
  progress_percentage: number;
  expires_at: string;
  status: string;
}

export const DynamicQuestBoard: React.FC = () => {
  const [selectedQuest, setSelectedQuest] = useState<Quest | null>(null);

  const { data: quests, refetch } = useQuery({
    queryKey: ['active-quests'],
    queryFn: () => $http.get('/api/quests/active').then(res => res.data)
  });

  const { data: communityEvents } = useQuery({
    queryKey: ['community-events'],
    queryFn: () => $http.get('/api/events/active').then(res => res.data)
  });

  const abandonQuestMutation = useMutation({
    mutationFn: (questId: number) => $http.delete(`/api/quests/${questId}`),
    onSuccess: () => refetch()
  });

  const joinEventMutation = useMutation({
    mutationFn: (eventId: number) => $http.post(`/api/events/${eventId}/join`),
    onSuccess: () => refetch()
  });

  const renderQuestCard = (quest: Quest) => (
    <div key={quest.id} className="quest-card">
      <div className="quest-header">
        <h3>{quest.generated_title}</h3>
        <span className={`quest-status ${quest.status}`}>{quest.status}</span>
      </div>
      
      <p className="quest-description">{quest.generated_description}</p>
      
      <div className="quest-objectives">
        {quest.objectives.map((objective, index) => (
          <div key={index} className="objective">
            <span>{objective.type.replace('_', ' ')}</span>
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ width: `${(objective.current_progress / objective.target) * 100}%` }}
              />
            </div>
            <span>{objective.current_progress}/{objective.target}</span>
          </div>
        ))}
      </div>

      <div className="quest-rewards">
        <h4>Rewards:</h4>
        {Object.entries(quest.rewards).map(([type, amount]) => (
          <span key={type} className="reward-item">
            {amount} {type}
          </span>
        ))}
      </div>

      <div className="quest-actions">
        <button onClick={() => setSelectedQuest(quest)}>View Details</button>
        <button 
          onClick={() => abandonQuestMutation.mutate(quest.id)}
          className="abandon-btn"
        >
          Abandon
        </button>
      </div>
    </div>
  );

  return (
    <div className="quest-board">
      <div className="quest-tabs">
        <button className="tab active">Daily Quests</button>
        <button className="tab">Community Events</button>
        <button className="tab">Bounties</button>
      </div>

      <div className="quest-content">
        <div className="active-quests">
          <h2>Active Quests</h2>
          {quests?.map(renderQuestCard)}
        </div>

        <div className="community-events">
          <h2>Community Events</h2>
          {communityEvents?.map((event: any) => (
            <div key={event.id} className="event-card">
              <h3>{event.name}</h3>
              <p>{event.description}</p>
              <div className="event-progress">
                <div className="progress-bar">
                  <div 
                    className="progress-fill"
                    style={{ width: `${event.current_progress}%` }}
                  />
                </div>
                <span>{event.current_progress}% Complete</span>
              </div>
              <button onClick={() => joinEventMutation.mutate(event.id)}>
                Join Event
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
```

## Next Steps

1. **AI-Driven Quest Generation**: Implement machine learning for personalized quest creation
2. **Real-time Event System**: Add live community events with real-time progress tracking
3. **Bounty System Integration**: Connect bounties with the PvP battle system
4. **Exploration Mechanics**: Create discoverable content and hidden areas
5. **Quest Chain System**: Implement interconnected quest storylines

This dynamic quest system creates endless content variety and keeps players engaged with personalized, adaptive challenges.
