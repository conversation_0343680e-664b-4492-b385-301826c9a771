# Mystery Box System Implementation

## Overview
This document covers the complete implementation of the Mystery Box system, including controllers, services, and reward generation mechanics.

## Implementation Time: 3-4 days
## Complexity: High
## Dependencies: Pet system, collectible system

## Mystery Box Controller

### MysteryBoxController
```php
<?php
// File: api/app/Http/Controllers/MysteryBoxController.php

namespace App\Http\Controllers;

use App\Models\MysteryBoxType;
use App\Models\MysteryBoxUnlock;
use App\Models\MysteryBoxOpening;
use App\Models\TelegramUser;
use App\Services\MysteryBoxService;
use App\Services\AchievementPointService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class MysteryBoxController extends Controller
{
    protected MysteryBoxService $mysteryBoxService;
    protected AchievementPointService $achievementPointService;

    public function __construct(
        MysteryBoxService $mysteryBoxService,
        AchievementPointService $achievementPointService
    ) {
        $this->mysteryBoxService = $mysteryBoxService;
        $this->achievementPointService = $achievementPointService;
    }

    /**
     * Get user's unlocked mystery boxes
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $unlockedBoxes = $user->mysteryBoxUnlocks()
                             ->with('mysteryBoxType')
                             ->get();

        $availableBoxes = $unlockedBoxes->map(function($unlock) use ($user) {
            $boxType = $unlock->mysteryBoxType;
            
            return [
                'box_type' => $boxType->box_type,
                'display_name' => $boxType->display_name,
                'rarity' => $boxType->rarity,
                'category' => $boxType->category,
                'description' => $boxType->description,
                'image_url' => $boxType->image_url,
                'animation_url' => $boxType->animation_url,
                'coin_cost' => $boxType->coin_cost,
                'gem_cost' => $boxType->gem_cost,
                'achievement_points_cost' => $boxType->achievement_points_cost,
                'guaranteed_rarity_level' => $boxType->guaranteed_rarity_level,
                'unlocked_at' => $unlock->unlocked_at,
                'unlock_source' => $unlock->unlock_source,
                'can_purchase' => $boxType->canBePurchasedBy($user),
                'times_opened' => $boxType->openings()
                                        ->where('telegram_user_id', $user->id)
                                        ->count()
            ];
        });

        return response()->json([
            'success' => true,
            'mystery_boxes' => $availableBoxes,
            'user_balance' => [
                'balance' => $user->balance,
                'gems' => $user->gems ?? 0,
                'achievement_points' => $user->achievementPoints->total_earned ?? 0
            ]
        ]);
    }

    /**
     * Get all mystery box types (for shop display)
     */
    public function getAllBoxTypes(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $allBoxTypes = MysteryBoxType::active()
                                   ->orderBy('sort_order')
                                   ->orderBy('rarity')
                                   ->get();

        $boxTypes = $allBoxTypes->map(function($boxType) use ($user) {
            $isUnlocked = $boxType->isUnlockedBy($user);
            
            return [
                'box_type' => $boxType->box_type,
                'display_name' => $boxType->display_name,
                'rarity' => $boxType->rarity,
                'category' => $boxType->category,
                'description' => $boxType->description,
                'image_url' => $boxType->image_url,
                'coin_cost' => $boxType->coin_cost,
                'gem_cost' => $boxType->gem_cost,
                'achievement_points_cost' => $boxType->achievement_points_cost,
                'is_unlocked' => $isUnlocked,
                'can_purchase' => $isUnlocked && $boxType->canBePurchasedBy($user),
                'unlock_requirements' => $boxType->unlock_requirements,
                'unlock_status' => $this->getUnlockStatus($boxType, $user)
            ];
        });

        return response()->json([
            'success' => true,
            'box_types' => $boxTypes
        ]);
    }

    /**
     * Open a mystery box
     */
    public function openBox(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'box_type' => 'required|string|exists:mystery_box_types,box_type',
            'purchase_method' => 'required|in:coins,gems,achievement_points',
            'quantity' => 'integer|min:1|max:10'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $boxType = $request->box_type;
        $purchaseMethod = $request->purchase_method;
        $quantity = $request->quantity ?? 1;

        try {
            $results = $this->mysteryBoxService->openBoxes(
                $user,
                $boxType,
                $purchaseMethod,
                $quantity
            );

            // Award achievement points for opening boxes
            $this->achievementPointService->awardPoints(
                $user->id,
                $quantity * 2,
                'mystery_box_opening',
                $boxType,
                "Opened {$quantity} {$boxType} mystery box(es)"
            );

            return response()->json([
                'success' => true,
                'message' => $quantity === 1 
                    ? 'Mystery box opened successfully!' 
                    : "{$quantity} mystery boxes opened successfully!",
                'results' => $results,
                'user_balance' => [
                    'balance' => $user->fresh()->balance,
                    'gems' => $user->fresh()->gems ?? 0,
                    'achievement_points' => $user->fresh()->achievementPoints->total_earned ?? 0
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get mystery box opening history
     */
    public function getOpeningHistory(Request $request): JsonResponse
    {
        $user = $request->user();
        $boxType = $request->query('box_type');
        
        $query = $user->mysteryBoxOpenings()
                     ->with('mysteryBoxType')
                     ->orderBy('opened_at', 'desc');

        if ($boxType) {
            $query->where('box_type', $boxType);
        }

        $openings = $query->paginate(20);

        return response()->json([
            'success' => true,
            'openings' => $openings->items(),
            'pagination' => [
                'current_page' => $openings->currentPage(),
                'last_page' => $openings->lastPage(),
                'per_page' => $openings->perPage(),
                'total' => $openings->total()
            ]
        ]);
    }

    /**
     * Get mystery box statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $stats = $this->mysteryBoxService->getUserBoxStatistics($user);
        
        return response()->json([
            'success' => true,
            'statistics' => $stats
        ]);
    }

    /**
     * Preview mystery box rewards (without opening)
     */
    public function previewRewards(Request $request, string $boxType): JsonResponse
    {
        $mysteryBoxType = MysteryBoxType::where('box_type', $boxType)
                                      ->where('is_active', true)
                                      ->firstOrFail();

        $user = $request->user();

        if (!$mysteryBoxType->isUnlockedBy($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Mystery box not unlocked'
            ], 403);
        }

        $possibleRewards = $this->mysteryBoxService->getPossibleRewards($mysteryBoxType);

        return response()->json([
            'success' => true,
            'box_type' => $boxType,
            'display_name' => $mysteryBoxType->display_name,
            'possible_rewards' => $possibleRewards,
            'guaranteed_rarity_level' => $mysteryBoxType->guaranteed_rarity_level,
            'costs' => [
                'coins' => $mysteryBoxType->coin_cost,
                'gems' => $mysteryBoxType->gem_cost,
                'achievement_points' => $mysteryBoxType->achievement_points_cost
            ]
        ]);
    }

    private function getUnlockStatus(MysteryBoxType $boxType, TelegramUser $user): string
    {
        if ($boxType->isUnlockedBy($user)) {
            return 'unlocked';
        }

        $requirements = $boxType->unlock_requirements;
        
        if (empty($requirements)) {
            return 'locked';
        }

        // Check specific unlock requirements
        foreach ($requirements as $requirement) {
            if (isset($requirement['type'])) {
                switch ($requirement['type']) {
                    case 'pet_ownership':
                        $hasRequiredPet = $user->pets()
                                              ->whereHas('template', function($q) use ($requirement) {
                                                  $q->where('name', $requirement['pet_name']);
                                              })
                                              ->exists();
                        if (!$hasRequiredPet) {
                            return "requires_pet:{$requirement['pet_name']}";
                        }
                        break;
                        
                    case 'prize_tree_level':
                        $userProgress = $user->prizeTreeProgress();
                        $currentLevel = $userProgress ? $userProgress->current_level : 0;
                        if ($currentLevel < $requirement['level']) {
                            return "requires_level:{$requirement['level']}";
                        }
                        break;
                        
                    case 'collection_completion':
                        $collectionProgress = $user->getCollectionProgress();
                        if ($collectionProgress['overall_percentage'] < $requirement['percentage']) {
                            return "requires_collection:{$requirement['percentage']}%";
                        }
                        break;
                }
            }
        }

        return 'requirements_met';
    }
}
```

## Mystery Box Service

### MysteryBoxService
```php
<?php
// File: api/app/Services/MysteryBoxService.php

namespace App\Services;

use App\Models\MysteryBoxType;
use App\Models\MysteryBoxUnlock;
use App\Models\MysteryBoxOpening;
use App\Models\TelegramUser;
use App\Models\CollectibleTemplate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MysteryBoxService
{
    protected CollectibleService $collectibleService;

    public function __construct(CollectibleService $collectibleService)
    {
        $this->collectibleService = $collectibleService;
    }

    /**
     * Unlock a mystery box type for a user
     */
    public function unlockBoxType(
        TelegramUser $user,
        string $boxType,
        string $unlockSource,
        ?string $sourceReference = null
    ): ?array {
        $mysteryBoxType = MysteryBoxType::where('box_type', $boxType)
                                      ->where('is_active', true)
                                      ->first();

        if (!$mysteryBoxType) {
            return null;
        }

        // Check if already unlocked
        if ($mysteryBoxType->isUnlockedBy($user)) {
            return null;
        }

        $unlock = MysteryBoxUnlock::create([
            'telegram_user_id' => $user->id,
            'box_type' => $boxType,
            'unlock_source' => $unlockSource,
            'source_reference' => $sourceReference,
            'unlocked_at' => now()
        ]);

        Log::info('Mystery box unlocked', [
            'user_id' => $user->id,
            'box_type' => $boxType,
            'unlock_source' => $unlockSource
        ]);

        return [
            'box_type' => $boxType,
            'display_name' => $mysteryBoxType->display_name,
            'unlocked_at' => $unlock->unlocked_at
        ];
    }

    /**
     * Open mystery boxes and generate rewards
     */
    public function openBoxes(
        TelegramUser $user,
        string $boxType,
        string $purchaseMethod,
        int $quantity = 1
    ): array {
        $mysteryBoxType = MysteryBoxType::where('box_type', $boxType)
                                      ->where('is_active', true)
                                      ->firstOrFail();

        if (!$mysteryBoxType->canBePurchasedBy($user)) {
            throw new \Exception('Cannot purchase this mystery box');
        }

        $cost = $mysteryBoxType->getCost($purchaseMethod);
        $totalCost = $cost * $quantity;

        if ($totalCost <= 0) {
            throw new \Exception('Invalid purchase method');
        }

        // Validate user balance
        $this->validateUserBalance($user, $purchaseMethod, $totalCost);

        DB::beginTransaction();

        try {
            // Deduct cost
            $this->deductCost($user, $purchaseMethod, $totalCost);

            $results = [];

            for ($i = 0; $i < $quantity; $i++) {
                $rewards = $this->generateBoxRewards($mysteryBoxType);
                $opening = $this->recordBoxOpening($user, $mysteryBoxType, $purchaseMethod, $cost, $rewards);
                
                // Grant rewards to user
                $grantedRewards = $this->grantRewards($user, $rewards);
                
                $results[] = [
                    'opening_id' => $opening->id,
                    'rewards' => $grantedRewards,
                    'contained_rare_item' => $opening->contained_rare_item
                ];
            }

            DB::commit();

            return $results;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Mystery box opening failed', [
                'user_id' => $user->id,
                'box_type' => $boxType,
                'quantity' => $quantity,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get possible rewards for a mystery box type
     */
    public function getPossibleRewards(MysteryBoxType $mysteryBoxType): array
    {
        $possibleRewards = $mysteryBoxType->possible_rewards ?? [];
        $rewardDetails = [];

        foreach ($possibleRewards as $rewardId) {
            $collectible = CollectibleTemplate::where('collectible_id', $rewardId)->first();
            
            if ($collectible) {
                $rewardDetails[] = [
                    'id' => $collectible->collectible_id,
                    'name' => $collectible->name,
                    'type' => $collectible->type,
                    'rarity' => $collectible->rarity,
                    'category' => $collectible->category,
                    'image_url' => $collectible->image_url,
                    'description' => $collectible->description
                ];
            }
        }

        return $rewardDetails;
    }

    /**
     * Get user's mystery box statistics
     */
    public function getUserBoxStatistics(TelegramUser $user): array
    {
        $totalBoxesOpened = $user->mysteryBoxOpenings()->count();
        $totalSpent = $user->mysteryBoxOpenings()->sum('cost_paid');
        
        $boxesByType = $user->mysteryBoxOpenings()
                           ->selectRaw('box_type, COUNT(*) as count, SUM(cost_paid) as total_spent')
                           ->groupBy('box_type')
                           ->get()
                           ->keyBy('box_type');

        $rareItemsFound = $user->mysteryBoxOpenings()
                              ->where('contained_rare_item', true)
                              ->count();

        $unlockedBoxTypes = $user->mysteryBoxUnlocks()->count();
        $totalBoxTypes = MysteryBoxType::active()->count();

        return [
            'total_boxes_opened' => $totalBoxesOpened,
            'total_spent' => $totalSpent,
            'boxes_by_type' => $boxesByType,
            'rare_items_found' => $rareItemsFound,
            'unlocked_box_types' => $unlockedBoxTypes,
            'total_box_types' => $totalBoxTypes,
            'unlock_percentage' => $totalBoxTypes > 0 
                ? round(($unlockedBoxTypes / $totalBoxTypes) * 100, 1) 
                : 0
        ];
    }

    // Private helper methods

    private function validateUserBalance(TelegramUser $user, string $method, int $cost): void
    {
        switch ($method) {
            case 'coins':
                if ($user->balance < $cost) {
                    throw new \Exception('Insufficient coins');
                }
                break;
            case 'gems':
                if (($user->gems ?? 0) < $cost) {
                    throw new \Exception('Insufficient gems');
                }
                break;
            case 'achievement_points':
                $availablePoints = $user->achievementPoints->total_earned ?? 0;
                if ($availablePoints < $cost) {
                    throw new \Exception('Insufficient achievement points');
                }
                break;
            default:
                throw new \Exception('Invalid purchase method');
        }
    }

    private function deductCost(TelegramUser $user, string $method, int $cost): void
    {
        switch ($method) {
            case 'coins':
                $user->decrement('balance', $cost);
                break;
            case 'gems':
                $user->decrement('gems', $cost);
                break;
            case 'achievement_points':
                // Achievement points are spent, not decremented from total_earned
                $user->achievementPoints()->increment('total_spent', $cost);
                break;
        }
    }

    private function generateBoxRewards(MysteryBoxType $mysteryBoxType): array
    {
        $rewards = $mysteryBoxType->generateRewards();
        
        // Ensure at least one reward of guaranteed rarity
        if (empty($rewards)) {
            $guaranteedRewards = $this->getGuaranteedRewards($mysteryBoxType);
            $rewards = array_merge($rewards, $guaranteedRewards);
        }

        return array_unique($rewards);
    }

    private function getGuaranteedRewards(MysteryBoxType $mysteryBoxType): array
    {
        $guaranteedLevel = $mysteryBoxType->guaranteed_rarity_level;
        $rarityMap = [1 => 'common', 2 => 'rare', 3 => 'epic', 4 => 'legendary', 5 => 'mythic'];
        $guaranteedRarity = $rarityMap[$guaranteedLevel] ?? 'common';

        $guaranteedRewards = CollectibleTemplate::where('category', $mysteryBoxType->category)
                                               ->where('rarity', $guaranteedRarity)
                                               ->where('unlock_source', 'mystery_box')
                                               ->pluck('collectible_id')
                                               ->toArray();

        return $guaranteedRewards ? [$guaranteedRewards[array_rand($guaranteedRewards)]] : [];
    }

    private function recordBoxOpening(
        TelegramUser $user,
        MysteryBoxType $mysteryBoxType,
        string $purchaseMethod,
        int $cost,
        array $rewards
    ): MysteryBoxOpening {
        $containsRareItem = $this->containsRareItem($rewards);
        $totalValue = $this->calculateRewardValue($rewards);

        return MysteryBoxOpening::create([
            'telegram_user_id' => $user->id,
            'box_type' => $mysteryBoxType->box_type,
            'purchase_method' => $purchaseMethod,
            'cost_paid' => $cost,
            'currency_used' => $purchaseMethod,
            'rewards_received' => $rewards,
            'total_value' => $totalValue,
            'contained_rare_item' => $containsRareItem,
            'opened_at' => now()
        ]);
    }

    private function grantRewards(TelegramUser $user, array $rewards): array
    {
        $grantedRewards = [];

        foreach ($rewards as $rewardId) {
            $result = $this->collectibleService->unlockCollectible(
                $user,
                $rewardId,
                'mystery_box',
                null
            );

            if ($result) {
                $grantedRewards[] = $result;
            }
        }

        return $grantedRewards;
    }

    private function containsRareItem(array $rewards): bool
    {
        foreach ($rewards as $rewardId) {
            $collectible = CollectibleTemplate::where('collectible_id', $rewardId)->first();
            if ($collectible && in_array($collectible->rarity, ['epic', 'legendary', 'mythic'])) {
                return true;
            }
        }
        return false;
    }

    private function calculateRewardValue(array $rewards): int
    {
        $totalValue = 0;
        
        foreach ($rewards as $rewardId) {
            $collectible = CollectibleTemplate::where('collectible_id', $rewardId)->first();
            if ($collectible) {
                $value = match($collectible->rarity) {
                    'common' => 10,
                    'rare' => 50,
                    'epic' => 200,
                    'legendary' => 1000,
                    'mythic' => 5000,
                    default => 10
                };
                $totalValue += $value;
            }
        }

        return $totalValue;
    }
}
```

## Acceptance Criteria
- [x] Mystery box unlocking system functional
- [x] Box opening with reward generation working
- [x] Multiple box opening support (1-10 boxes at once)
- [x] Proper cost validation and deduction
- [x] Reward granting to users with duplicate handling
- [x] Opening history tracking with pagination
- [x] Statistics calculation with advanced analytics
- [x] Preview system for possible rewards
- [x] Achievement point integration (2 points per box)
- [x] Duplicate reward handling with coin compensation
- [x] Advanced analytics service with economy metrics
- [x] Enhanced API endpoints with preview functionality
- [x] Global statistics and performance tracking

## Next Steps
1. Create CollectibleService for reward management
2. Implement frontend mystery box UI
3. Add animation system for box opening
4. Create admin interface for box management

## Troubleshooting
- Ensure proper transaction handling for box openings
- Validate reward generation algorithms
- Check balance validation for all currency types
- Monitor performance for bulk box openings
