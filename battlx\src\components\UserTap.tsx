import React, { useEffect, useRef, useState, useCallback } from "react";
import { useClicksStore } from "../store/clicks-store";
import { useUserStore } from "../store/user-store";
import { useComboStore } from "../store/combo-store";
import { usePrizeFeatureStore } from "../store/prize-feature-store";
import { useDebounce } from "@uidotdev/usehooks";
import { $http } from "@/lib/http";
import levelConfig from "@/config/level-config";
import ComboSystem from "./combo/ComboSystem";

// Type for points
type Point = { x: number; y: number };

export default function UserTap(props: React.HTMLProps<HTMLDivElement>) {
  const userAnimateRef = useRef<HTMLDivElement | null>(null);
  const characterRef = useRef<HTMLImageElement | null>(null); // Ref for character image
  const trailCanvasRef = useRef<HTMLCanvasElement | null>(null); // Ref for trail canvas
  const splashContainerRef = useRef<HTMLDivElement | null>(null); // Ref for splash container

  const [clicksCount, setClicksCount] = useState(0);
  const debounceClicksCount = useDebounce(clicksCount, 1000);

  // State for swipe tracking
  const [swipeStart, setSwipeStart] = useState<Point | null>(null);
  const [swipePath, setSwipePath] = useState<Point[]>([]);
  const [swipeStartTime, setSwipeStartTime] = useState<number>(0);
  const [intersectionPoint, setIntersectionPoint] = useState<Point | null>(null);

  const { clicks, addClick, removeClick } = useClicksStore();
  const { UserTap: performUserTap, incraseEnergy, currentComboMultiplier, ...user } = useUserStore();
  const { addCombo, checkComboTimeout, incrementFrenzyMeter, checkFrenzyTimeout, frenzyActive, getCurrentMultiplier } = useComboStore();
  const { tapEnabled, slashEffectsEnabled, comboEnabled, frenzyEnabled } = usePrizeFeatureStore();

  // --- Helper Functions ---

  // Get the bounding box of the character image
  const getCharacterBounds = useCallback(() => {
    if (!characterRef.current) return null;
    const rect = characterRef.current.getBoundingClientRect();
    return {
      left: rect.left,
      right: rect.right,
      top: rect.top,
      bottom: rect.bottom,
      width: rect.width,
      height: rect.height,
      centerX: rect.left + rect.width / 2,
      centerY: rect.top + rect.height / 2,
    };
  }, []);

  // Check if two line segments intersect
  const lineSegmentsIntersect = useCallback((
    x1: number, y1: number, x2: number, y2: number,
    x3: number, y3: number, x4: number, y4: number
  ): boolean => {
    const dx1 = x2 - x1;
    const dy1 = y2 - y1;
    const dx2 = x4 - x3;
    const dy2 = y4 - y3;
    const determinant = dx1 * dy2 - dy1 * dx2;
    if (determinant === 0) return false;
    const t1 = ((x3 - x1) * dy2 - (y3 - y1) * dx2) / determinant;
    const t2 = ((x1 - x3) * dy1 - (y1 - y3) * dx1) / -determinant;
    return t1 >= 0 && t1 <= 1 && t2 >= 0 && t2 <= 1;
  }, []);

  // Check if a line intersects with a rectangle
  const lineIntersectsRect = useCallback((
    lineStart: Point,
    lineEnd: Point,
    rect: { left: number; right: number; top: number; bottom: number }
  ): boolean => {
    const edges = [
      { start: { x: rect.left, y: rect.top }, end: { x: rect.right, y: rect.top } },
      { start: { x: rect.right, y: rect.top }, end: { x: rect.right, y: rect.bottom } },
      { start: { x: rect.left, y: rect.bottom }, end: { x: rect.right, y: rect.bottom } },
      { start: { x: rect.left, y: rect.top }, end: { x: rect.left, y: rect.bottom } },
    ];

    for (const edge of edges) {
      if (lineSegmentsIntersect(
        lineStart.x, lineStart.y, lineEnd.x, lineEnd.y,
        edge.start.x, edge.start.y, edge.end.x, edge.end.y
      )) {
        return true;
      }
    }

    // Check if either endpoint is inside the rectangle
    const isStartInside = lineStart.x >= rect.left && lineStart.x <= rect.right &&
                          lineStart.y >= rect.top && lineStart.y <= rect.bottom;
    const isEndInside = lineEnd.x >= rect.left && lineEnd.x <= rect.right &&
                        lineEnd.y >= rect.top && lineEnd.y <= rect.bottom;

    return isStartInside || isEndInside;
  }, [lineSegmentsIntersect]);

  // Calculate the intersection point between a line and the character bounds
  const calculateIntersectionPoint = useCallback((
    lineStart: Point,
    lineEnd: Point,
    bounds: ReturnType<typeof getCharacterBounds>
  ): Point => {
    if (!bounds) return lineEnd;

    // Find the intersection point with the rectangle edges
    const edges = [
      { start: { x: bounds.left, y: bounds.top }, end: { x: bounds.right, y: bounds.top } }, // Top
      { start: { x: bounds.right, y: bounds.top }, end: { x: bounds.right, y: bounds.bottom } }, // Right
      { start: { x: bounds.left, y: bounds.bottom }, end: { x: bounds.right, y: bounds.bottom } }, // Bottom
      { start: { x: bounds.left, y: bounds.top }, end: { x: bounds.left, y: bounds.bottom } } // Left
    ];

    let closestIntersection: Point | null = null;
    let minDistSq = Infinity;

    for (const edge of edges) {
      const dx1 = lineEnd.x - lineStart.x;
      const dy1 = lineEnd.y - lineStart.y;
      const dx2 = edge.end.x - edge.start.x;
      const dy2 = edge.end.y - edge.start.y;
      const determinant = dx1 * dy2 - dy1 * dx2;

      if (determinant !== 0) {
        const t1 = ((edge.start.x - lineStart.x) * dy2 - (edge.start.y - lineStart.y) * dx2) / determinant;
        const t2 = ((lineStart.x - edge.start.x) * dy1 - (lineStart.y - edge.start.y) * dx1) / -determinant;

        if (t1 >= 0 && t1 <= 1 && t2 >= 0 && t2 <= 1) {
          const intersectX = lineStart.x + t1 * dx1;
          const intersectY = lineStart.y + t1 * dy1;
          const distSq = (intersectX - lineStart.x)**2 + (intersectY - lineStart.y)**2;

          // Find the intersection point closest to the start of the segment
          if (distSq < minDistSq) {
            minDistSq = distSq;
            closestIntersection = { x: intersectX, y: intersectY };
          }
        }
      }
    }

    // Fallback: If no edge intersection found, use the character center
    if (!closestIntersection) {
      return { x: bounds.centerX, y: bounds.centerY };
    }

    return closestIntersection;
  }, []);

  // Check if the swipe path intersects with the character
  const checkIntersection = useCallback((path: Point[]): boolean => {
    const bounds = getCharacterBounds();
    if (!bounds || path.length < 2) return false;

    for (let i = 1; i < path.length; i++) {
      const start = path[i - 1];
      const end = path[i];
      if (lineIntersectsRect(start, end, bounds)) {
        const intersectPt = calculateIntersectionPoint(start, end, bounds);
        setIntersectionPoint(intersectPt);
        return true;
      }
    }
    return false;
  }, [getCharacterBounds, lineIntersectsRect, calculateIntersectionPoint]);

  // Check if the swipe is long enough to be considered valid
  const isValidSwipe = useCallback((path: Point[]): boolean => {
    if (path.length < 2) return false;

    let totalLength = 0;
    for (let i = 1; i < path.length; i++) {
      const dx = path[i].x - path[i - 1].x;
      const dy = path[i].y - path[i - 1].y;
      totalLength += Math.sqrt(dx*dx + dy*dy);
    }

    // Minimum length threshold (in pixels)
    const MIN_SWIPE_LENGTH = 50;
    return totalLength >= MIN_SWIPE_LENGTH;
  }, []);

  // Calculate swipe velocity for visual effects
  const calculateSwipeVelocity = useCallback((path: Point[], duration: number): number => {
    if (path.length < 2 || duration <= 0) return 0;

    let totalLength = 0;
    for (let i = 1; i < path.length; i++) {
      const dx = path[i].x - path[i - 1].x;
      const dy = path[i].y - path[i - 1].y;
      totalLength += Math.sqrt(dx*dx + dy*dy);
    }

    // Velocity in pixels per millisecond
    return totalLength / duration;
  }, []);

  // Draw the trail on the canvas
  const drawTrail = useCallback(() => {
    const canvas = trailCanvasRef.current;
    if (!canvas || swipePath.length < 2) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Check if slash effects are enabled
    if (!slashEffectsEnabled) return;

    // Only keep the first and last points for a straight line (Fruit Ninja style)
    const startPoint = swipePath[0];
    const endPoint = swipePath[swipePath.length - 1];

    // Calculate the length of the line for better visibility
    const dx = endPoint.x - startPoint.x;
    const dy = endPoint.y - startPoint.y;
    const lineLength = Math.sqrt(dx * dx + dy * dy);

    // Only draw if the line is long enough
    if (lineLength < 10) return;

    // Draw trail with improved styling
    ctx.beginPath();

    // Thicker line during Frenzy mode
    ctx.lineWidth = frenzyActive && frenzyEnabled ? 18 : 12;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Create gradient for trail with gothic theme colors
    const gradient = ctx.createLinearGradient(
      startPoint.x, startPoint.y,
      endPoint.x, endPoint.y
    );

    if (frenzyActive && frenzyEnabled) {
      // Gold gradient during Frenzy mode
      gradient.addColorStop(0, '#FFD700'); // Gold
      gradient.addColorStop(0.5, '#FFA500'); // Orange
      gradient.addColorStop(1, '#FF8C00'); // Dark Orange
    } else {
      // Normal gradient
      gradient.addColorStop(0, '#4A154B'); // ds-purple
      gradient.addColorStop(0.5, '#CFB53B'); // ds-gold
      gradient.addColorStop(1, '#2D0C2D'); // ds-dark-purple
    }

    ctx.strokeStyle = gradient;

    // Add glow effect - optimized during Frenzy mode
    if (!(frenzyActive && frenzyEnabled)) {
      // Normal glow effect
      ctx.shadowColor = '#CFB53B';
      ctx.shadowBlur = 15;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
    } else {
      // Simplified glow during Frenzy for better performance
      // Only apply shadow if the line is short enough (performance optimization)
      if (lineLength < 300) {
        ctx.shadowColor = '#FFD700';
        ctx.shadowBlur = 20;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
      }
    }

    // Draw straight line
    ctx.moveTo(startPoint.x, startPoint.y);
    ctx.lineTo(endPoint.x, endPoint.y);
    ctx.stroke();

    // Reset shadow
    ctx.shadowBlur = 0;

    // No particles along the line for better performance
    // Particles will only be created on hit in the createSplashEffect function
  }, [swipePath, frenzyActive, slashEffectsEnabled, frenzyEnabled]);

  // Fade out the trail animation
  const fadeOutTrail = useCallback(() => {
    const canvas = trailCanvasRef.current;
    if (!canvas || swipePath.length < 2) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Check if slash effects are enabled
    if (!slashEffectsEnabled) return;

    // Get start and end points for straight line
    const startPoint = swipePath[0];
    const endPoint = swipePath[swipePath.length - 1];

    // Calculate line properties
    const dx = endPoint.x - startPoint.x;
    const dy = endPoint.y - startPoint.y;
    const lineLength = Math.sqrt(dx * dx + dy * dy);
    if (lineLength < 10) return;

    // Smoother fade out animation with more frames
    let opacity = 1;
    const fadeInterval = setInterval(() => {
      // Fade out slower during Frenzy mode for more dramatic effect
      opacity -= (frenzyActive && frenzyEnabled) ? 0.08 : 0.1;
      if (opacity <= 0) {
        clearInterval(fadeInterval);
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        return;
      }

      // Redraw with reduced opacity
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.globalAlpha = opacity;

      // Redraw path with glow effect that increases as opacity decreases
      ctx.beginPath();
      // Line gets thicker as it fades, more so during Frenzy mode
      const baseWidth = (frenzyActive && frenzyEnabled) ? 18 : 12;
      ctx.lineWidth = baseWidth * (1 + (1 - opacity) * 0.5);
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      // Enhanced gradient with more vibrant colors as it fades
      const gradient = ctx.createLinearGradient(
        startPoint.x, startPoint.y,
        endPoint.x, endPoint.y
      );

      if (frenzyActive && frenzyEnabled) {
        // Gold gradient during Frenzy mode
        gradient.addColorStop(0, `rgba(255, 215, 0, ${opacity})`);
        gradient.addColorStop(0.5, `rgba(255, 165, 0, ${opacity})`);
        gradient.addColorStop(1, `rgba(255, 140, 0, ${opacity})`);
      } else {
        // Normal gradient
        gradient.addColorStop(0, `rgba(74, 21, 75, ${opacity})`);
        gradient.addColorStop(0.5, `rgba(207, 181, 59, ${opacity})`);
        gradient.addColorStop(1, `rgba(45, 12, 45, ${opacity})`);
      }

      ctx.strokeStyle = gradient;

      // Optimized glow effect for better performance
      if (!(frenzyActive && frenzyEnabled)) {
        // Normal glow effect
        const glowIntensity = 15 * (1 + (1 - opacity));
        ctx.shadowColor = '#CFB53B';
        ctx.shadowBlur = glowIntensity;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
      } else {
        // During Frenzy mode, only apply shadow if opacity is high enough (performance optimization)
        if (opacity > 0.4) {
          const glowIntensity = 20 * (1 + (1 - opacity));
          ctx.shadowColor = '#FFD700';
          ctx.shadowBlur = glowIntensity;
          ctx.shadowOffsetX = 0;
          ctx.shadowOffsetY = 0;
        }
      }

      // Draw straight line
      ctx.moveTo(startPoint.x, startPoint.y);
      ctx.lineTo(endPoint.x, endPoint.y);
      ctx.stroke();

      // No particles during fade out for better performance
      // Particles only appear on hit in the createSplashEffect function

    }, (frenzyActive && frenzyEnabled) ? 15 : 20); // Faster animation during Frenzy mode
  }, [swipePath, frenzyActive, slashEffectsEnabled, frenzyEnabled]);

  // Create splash effect at the intersection point
  const createSplashEffect = useCallback((position: Point, size = 60) => {
    // Check if slash effects are enabled
    if (!slashEffectsEnabled) return;

    // Create main splash effect with enhanced size
    const splash = document.createElement('div');
    splash.className = 'slice-splash';
    splash.style.left = `${position.x}px`;
    splash.style.top = `${position.y}px`;
    splash.style.width = `${size * 1.5}px`; // Larger splash
    splash.style.height = `${size * 1.5}px`;

    // Add custom glow effect
    splash.style.boxShadow = '0 0 20px rgba(207, 181, 59, 0.8)';

    // Add GPU acceleration hint for better performance
    splash.style.transform = 'translateZ(0)';
    splash.style.willChange = 'opacity';

    const container = splashContainerRef.current;
    if (container) {
      container.appendChild(splash);

      // Optimize particle count based on whether Frenzy is active
      // Fewer particles during Frenzy mode for better performance
      const particleCount = (frenzyActive && frenzyEnabled) ? 8 : 12;

      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'slice-particle';

        // Position particles in a circular pattern
        const angle = (Math.PI * 2 * i) / particleCount;
        const distance = size * 0.4; // Slightly wider distribution
        const offsetX = Math.cos(angle) * distance;
        const offsetY = Math.sin(angle) * distance;

        particle.style.left = `${position.x + offsetX}px`;
        particle.style.top = `${position.y + offsetY}px`;

        // Varied sizes for particles
        const particleSize = Math.random() * 12 + 6; // Larger particles
        particle.style.width = `${particleSize}px`;
        particle.style.height = `${particleSize}px`;

        // Add GPU acceleration hint for better performance
        particle.style.transform = 'translateZ(0)';
        particle.style.willChange = 'opacity';

        // More color variation
        const colorIndex = i % 3;
        if (colorIndex === 0) {
          particle.style.backgroundColor = (frenzyActive && frenzyEnabled) ? '#FFD700' : '#CFB53B'; // Gold
        } else if (colorIndex === 1) {
          particle.style.backgroundColor = '#4A154B'; // Purple
        } else {
          particle.style.backgroundColor = '#2D0C2D'; // Dark purple
        }

        // Add custom glow to particles - less intense during Frenzy for performance
        particle.style.boxShadow = (frenzyActive && frenzyEnabled) ?
          '0 0 5px rgba(255, 215, 0, 0.6)' :
          '0 0 10px rgba(207, 181, 59, 0.6)';

        container.appendChild(particle);

        // Create additional smaller particles only in non-Frenzy mode or selectively during Frenzy
        if (!(frenzyActive && frenzyEnabled) && i % 2 === 0 || (frenzyActive && frenzyEnabled) && i % 4 === 0) {
          const smallParticle = document.createElement('div');
          smallParticle.className = 'slice-particle';

          // Random position near the main particle
          const smallAngle = angle + (Math.random() - 0.5) * 0.5;
          const smallDistance = distance * (0.7 + Math.random() * 0.6);
          const smallOffsetX = Math.cos(smallAngle) * smallDistance;
          const smallOffsetY = Math.sin(smallAngle) * smallDistance;

          smallParticle.style.left = `${position.x + smallOffsetX}px`;
          smallParticle.style.top = `${position.y + smallOffsetY}px`;

          // Smaller size
          const smallParticleSize = Math.random() * 6 + 3;
          smallParticle.style.width = `${smallParticleSize}px`;
          smallParticle.style.height = `${smallParticleSize}px`;

          // Add GPU acceleration hint for better performance
          smallParticle.style.transform = 'translateZ(0)';
          smallParticle.style.willChange = 'opacity';

          // Alternate color
          smallParticle.style.backgroundColor = colorIndex === 0 ?
            ((frenzyActive && frenzyEnabled) ? '#FFD700' : '#4A154B') :
            ((frenzyActive && frenzyEnabled) ? '#FFA500' : '#CFB53B');

          container.appendChild(smallParticle);

          // Remove small particles
          setTimeout(() => {
            if (container.contains(smallParticle)) {
              container.removeChild(smallParticle);
            }
          }, 700); // Slightly longer animation
        }

        // Remove particles after animation completes
        setTimeout(() => {
          if (container.contains(particle)) {
            container.removeChild(particle);
          }
        }, 700); // Slightly longer animation
      }

      // Remove splash after animation completes
      setTimeout(() => {
        if (container.contains(splash)) {
          container.removeChild(splash);
        }
      }, 700); // Slightly longer animation
    }
  }, [frenzyActive, slashEffectsEnabled, frenzyEnabled]);

  // Reset swipe state
  const resetSwipeState = useCallback(() => {
    setSwipeStart(null);
    setSwipePath([]);
    setIntersectionPoint(null);
  }, []);

  // Handle successful slice
  const handleSuccessfulSlice = useCallback((position: Point, effectParams: { splashSize: number }) => {
    // Check if tapping is enabled
    if (!tapEnabled) return;

    if (!performUserTap()) return;

    setClicksCount(prev => prev + 1);

    // Record hit for combo system if combo is enabled
    if (comboEnabled) {
      addCombo();

      // Increment Frenzy meter based on current combo multiplier
      // The incrementFrenzyMeter function will only work if combo is active
      const { comboMultiplier, comboActive } = useComboStore.getState();

      // Only calculate increment if combo is active and frenzy is enabled
      if (comboActive && frenzyEnabled) {
        // Higher increment when already at or above threshold
        const meterIncrement = comboMultiplier >= 10 ? 20 : Math.max(1, comboMultiplier / 2);
        incrementFrenzyMeter(meterIncrement);
      }
    }

    // Create splash effect at intersection point
    // Make splash effect larger during Frenzy mode
    const splashSize = (frenzyActive && frenzyEnabled) ? effectParams.splashSize * 1.5 : effectParams.splashSize;
    createSplashEffect(position, splashSize);

    // Get the current multiplier for display purposes
    let totalMultiplier = 1;

    // Only apply combo and frenzy multipliers if those features are enabled
    if (comboEnabled || frenzyEnabled) {
      totalMultiplier = getCurrentMultiplier();
    }

    // Calculate value with combo and frenzy multipliers
    const baseValue = user.earn_per_tap;
    const comboValue = Math.floor(baseValue * totalMultiplier);

    // Add score animation at intersection point
    addClick({
      id: new Date().getTime(),
      value: comboValue,
      style: {
        top: position.y,
        left: position.x,
        // Make text larger and different color during Frenzy mode
        color: (frenzyActive && frenzyEnabled) ? '#FFD700' : undefined,
        fontSize: (frenzyActive && frenzyEnabled) ? '40px' : undefined,
      },
    });
  }, [performUserTap, createSplashEffect, addClick, user.earn_per_tap, addCombo, incrementFrenzyMeter, frenzyActive, tapEnabled, comboEnabled, frenzyEnabled]);

  // --- Event Handlers ---

  // Handle touch/mouse start
  const handleTouchStart = useCallback((e: React.TouchEvent | React.MouseEvent) => {
    if (user.available_energy < user.earn_per_tap) return;

    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    setSwipeStart({x: clientX, y: clientY});
    setSwipePath([{x: clientX, y: clientY}]);
    setSwipeStartTime(Date.now());
    setIntersectionPoint(null);
  }, [user.available_energy, user.earn_per_tap]);

  // Handle touch/mouse move
  const handleTouchMove = useCallback((e: React.TouchEvent | React.MouseEvent) => {
    if (!swipeStart) return;

    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;

    setSwipePath(prev => [...prev, {x: clientX, y: clientY}]);

    // Draw trail in real-time
    requestAnimationFrame(drawTrail);
  }, [swipeStart, drawTrail]);

  // Handle touch/mouse end
  const handleTouchEnd = useCallback((_e: React.TouchEvent | React.MouseEvent) => {
    if (!swipeStart || swipePath.length < 2) {
      resetSwipeState();
      return;
    }

    const duration = Date.now() - swipeStartTime;
    const velocity = calculateSwipeVelocity(swipePath, duration);

    // Calculate effect parameters based on velocity
    const splashSize = Math.min(100, Math.max(40, velocity * 0.2));

    // Only process if it's a valid swipe (not just a tap)
    if (isValidSwipe(swipePath)) {
      // Check if swipe intersects with character
      if (checkIntersection(swipePath)) {
        // Handle successful slice
        handleSuccessfulSlice(intersectionPoint || swipePath[swipePath.length - 1], {
          splashSize
        });
      }
    }

    // Fade out trail
    fadeOutTrail();

    // Reset state after animation completes
    setTimeout(resetSwipeState, 500);
  }, [
    swipeStart,
    swipePath,
    swipeStartTime,
    calculateSwipeVelocity,
    isValidSwipe,
    checkIntersection,
    intersectionPoint,
    handleSuccessfulSlice,
    fadeOutTrail,
    resetSwipeState
  ]);

  useEffect(() => {
    const count = debounceClicksCount;
    setClicksCount(0);
    if (count === 0) return;

    $http
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .post<Record<string, any>>("/clicker/tap", {
        count,
        energy: user.available_energy,
        timestamp: Math.floor(Date.now() / 1000),
      })
      .then(({ data }) => {
        if (data.leveled_up) {
          useUserStore.setState({
            level: data.level || user.level,
            earn_per_tap: data.earn_per_tap,
            max_energy: data.max_energy,
            balance: data.balance,
          });
        }
      })
      .catch(() => setClicksCount(count));
  }, [debounceClicksCount]);

  useEffect(() => {
    useClicksStore.setState({ clicks: [] });

    const energyInterval = setInterval(() => {
      incraseEnergy(3);
    }, 3000);

    // Check for combo and frenzy timeouts every 500ms
    const timeoutInterval = setInterval(() => {
      checkComboTimeout();
      checkFrenzyTimeout();
    }, 500);

    return () => {
      clearInterval(energyInterval);
      clearInterval(timeoutInterval);
    };
  }, [checkComboTimeout]);
// Initialize canvas size when component mounts
useEffect(() => {
  const resizeCanvas = () => {
    const canvas = trailCanvasRef.current;
    if (canvas) {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    }
  };

  resizeCanvas();
  window.addEventListener('resize', resizeCanvas);

  return () => {
    window.removeEventListener('resize', resizeCanvas);
  };
}, []);

return (
  <div {...props}>
    {/* Combo System Component */}
    <ComboSystem />
    {/* Canvas for slice trail */}
    <canvas
      ref={trailCanvasRef}
      className="slice-trail-canvas"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 40
      }}
    />

    {/* Container for splash effects */}
    <div
      ref={splashContainerRef}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 45
      }}
    />



    {/* Main Content */}
    <div className="mt-24">
      <div>
        <div
          className="flex items-center justify-center mx-auto outline-none select-none relative"
          style={{
            opacity: user.available_energy < user.earn_per_tap ? 0.8 : 1,
            cursor: user.available_energy < user.earn_per_tap ? 'not-allowed' : 'pointer'
          }}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onMouseDown={handleTouchStart}
          onMouseMove={handleTouchMove}
          onMouseUp={handleTouchEnd}
          onMouseLeave={handleTouchEnd}
        >
          <img
            ref={characterRef}
            src={levelConfig.frogs[user.level?.level || 1]}
            alt="level image"
            className="object-contain max-w-full w-80 h-80"
          />
        </div>
      </div>
    </div>

    {/* Click Animations */}
    <div ref={userAnimateRef} className="user-tap-animate">
      {clicks.map((click) => (
        <div
          key={click.id}
          onAnimationEnd={() => removeClick(click.id)}
          style={click.style}
        >
          +{click.value}
        </div>
      ))}
    </div>
  </div>
);
}