<?php

namespace App\Http\Controllers;

use App\Models\Mission;
use App\Models\MissionLevel;
use App\Models\MissionCompletion;
use App\Models\TelegramUserMission;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserMissionController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        $missions = Mission::query()
            ->with([
                'nextLevel' => fn ($q) => $q->whereNotIn(
                    'id',
                    fn ($q) =>
                    $q->select('mission_level_id')
                        ->from('telegram_user_missions')
                        ->where('telegram_user_id', $user->id)
                ),
                'requiredMission',
                'completions' => fn($q) => $q->where('telegram_user_id', $user->id)
            ])
            ->withSum(['levels as production_per_hour' => fn ($q) => $q->whereIn(
                'id',
                fn ($q) =>
                $q->select('mission_level_id')
                    ->from('telegram_user_missions')
                    ->where('telegram_user_id', $user->id)
            )], 'production_per_hour')
            ->when($request->get('type'), fn ($q) => $q->where('missions.mission_type_id', $request->get('type')))
            ->get()
            ->map(function ($mission) use ($user) {
                $mission->is_completed = $mission->isCompletedBy($user->id);
                $mission->is_unlocked = !$mission->required_mission_id ||
                    $mission->requiredMission->isCompletedBy($user->id);
                return $mission;
            });

        return response()->json($missions);
    }

    public function store(Request $request, MissionLevel $missionLevel)
    {
        $user = $request->user();

        if ($user->balance < $missionLevel->cost) {
            return response()->json([
                'message' => 'Insufficient balance',
            ], 400);
        }

        DB::transaction(function () use ($user, $missionLevel) {
            // Create mission level purchase record
            TelegramUserMission::create([
                'telegram_user_id' => $user->id,
                'mission_level_id' => $missionLevel->id,
                'level' => $missionLevel->level,
            ]);

            // Update user stats
            $user->update([
                'production_per_hour' => $user->production_per_hour + $missionLevel->production_per_hour,
                'balance' => $user->balance - $missionLevel->cost,
            ]);

            // For boss missions (type 2), mark as completed when purchased
            if ($missionLevel->mission->mission_type_id === 2) {
                MissionCompletion::create([
                    'telegram_user_id' => $user->id,
                    'mission_id' => $missionLevel->mission_id,
                    'completed_at' => now(),
                ]);

                // Check and unlock dependent missions
                $dependentMissions = Mission::where('required_mission_id', $missionLevel->mission_id)->get();
                foreach ($dependentMissions as $dependentMission) {
                    // Create level 1 for dependent mission to unlock it
                    TelegramUserMission::create([
                        'telegram_user_id' => $user->id,
                        'mission_level_id' => $dependentMission->levels()->where('level', 1)->first()->id,
                        'level' => 1,
                    ]);
                }
            }
        });

        $missionLevel->load('mission');

        $nextLevel = MissionLevel::query()
            ->where('mission_id', $missionLevel->mission_id)
            ->where('level', $missionLevel->level + 1)
            ->first();

        $dependentMissionsUnlocked = [];
        if ($missionLevel->mission->mission_type_id === 2) {
            $dependentMissionsUnlocked = Mission::where('required_mission_id', $missionLevel->mission_id)
                ->get()
                ->map(fn($mission) => [
                    'id' => $mission->id,
                    'name' => $mission->name,
                    'type' => $mission->mission_type_id
                ]);
        }

        return response()->json([
            'message' => "Mission {$missionLevel->mission->name} upgraded to level {$missionLevel->level}",
            'next_level' => $nextLevel,
            'user' => $user,
            'mission_completed' => $missionLevel->mission->mission_type_id === 2,
            'dependent_missions_unlocked' => $dependentMissionsUnlocked
        ]);
    }
}
