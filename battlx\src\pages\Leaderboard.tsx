import { Swiper, SwiperRef, SwiperSlide } from "swiper/react";
import { EffectFade, Navigation } from "swiper/modules";
import SwapPrevIcon from "@/components/icons/SwapPrevIcon";
import SwapNextIcon from "@/components/icons/SwapNextIcon";
import { useEffect, useRef, useState } from "react";
import { useUserStore } from "@/store/user-store";
import { compactNumber } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { $http } from "@/lib/http";
import { UserType } from "@/types/UserType";
import levelConfig from "@/config/level-config";
import { uesStore } from "@/store";
import { BattlxIcon } from "@/components/icons/BattlxIcon";

export default function Leaderboard() {
  const userData = useUserStore();
  const [activeIndex, setActiveIndex] = useState(0);
  const { levels } = uesStore();
  const swiperRef = useRef<SwiperRef | null>(null);

  const leaderboard = useQuery({
    queryKey: ["leaderboard", levels?.[activeIndex]?.id],
    queryFn: () =>
      $http.$get<UserType[]>("/clicker/leaderboard", {
        params: { level_id: levels?.[activeIndex].id },
      }),
    staleTime: Infinity,
    enabled: !!levels?.[activeIndex]?.level,
  });

  useEffect(() => {
    if (userData.level?.level && levels) {
      const index = levels.findIndex((item) => item.level === userData.level?.level);
      if (index !== -1) {
        setActiveIndex(index);
        if (swiperRef.current) swiperRef.current.swiper.slideTo(index);
      }
    }
  }, []);

  return (
    <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover flex-1">
      <div className="flex flex-col flex-1 w-full h-full px-6 py-8 pb-24 mt-12 modal-body">
        <div className="">
          <Swiper
            ref={swiperRef}
            spaceBetween={30}
            modules={[EffectFade, Navigation]}
            effect="fade"
            fadeEffect={{ crossFade: true }}
            className="rounded-xl"
            navigation={{
              enabled: true,
              nextEl: ".custom-swiper-button-next",
              prevEl: ".custom-swiper-button-prev",
            }}
            onSlideChange={(swiper) => {
              setActiveIndex(swiper.activeIndex);
              // Reset leaderboard data during transition
              leaderboard.refetch();
            }}
          >
            {levels?.map((item, i) => (
              <SwiperSlide key={`slide-${i}`}>
                <div
                  className="py-4 bg-center bg-cover rounded-xl"
                  style={{
                    backgroundImage: userData.balance >= item.from_balance
                      ? `url('${levelConfig.bg[item.level]}')`
                      : `linear-gradient(rgba(26, 22, 23, 0.95), rgba(26, 22, 23, 0.95)), url('${levelConfig.bg[item.level]}')`
                  }}
                >
                  {userData.balance >= item.from_balance ? (
                    <img
                      src={levelConfig.frogs[item.level]}
                      alt="level image"
                      className="object-contain mx-auto w-60 h-60"
                    />
                  ) : (
                    <div className="flex items-center justify-center">
                      <BattlxIcon
                        icon="lock"
                        size="lg"
                        className="text-9xl text-center w-[200px] h-[200px] text-[#9B8B6C] opacity-80"
                      />
                    </div>
                  )}
                  <p className="mt-4 text-lg text-center text-white">
                    {item.name}
                  </p>
                  <p className="text-sm text-center text-white/70">
                    From {compactNumber(item.from_balance)}
                  </p>
                  {userData.balance < item.from_balance && (
                    <p className="text-sm text-center text-[#9B8B6C]/80 mt-2">
                      Unlocks at {compactNumber(item.from_balance)} coins
                    </p>
                  )}
                </div>
              </SwiperSlide>
            ))}
            <button className="absolute z-[999] left-2.5 w-10 h-10 flex items-center justify-center text-[#9B8B6C] custom-swiper-button-prev top-1/2 -translate-y-1/2 disabled:opacity-30 bg-[#1A1617]/80 p-2 rounded-lg border border-[#B3B3B3]/20 shadow-[0_4px_15px_rgba(74,14,14,0.3)] transition-all duration-300 hover:bg-[#4A0E0E]/30">
              <SwapPrevIcon />
            </button>
            <button className="absolute z-[999] right-2.5 w-10 h-10 flex items-center justify-center text-[#9B8B6C] custom-swiper-button-next top-1/2 -translate-y-1/2 disabled:opacity-30 bg-[#1A1617]/80 p-2 rounded-lg border border-[#B3B3B3]/20 shadow-[0_4px_15px_rgba(74,14,14,0.3)] transition-all duration-300 hover:bg-[#4A0E0E]/30">
              <SwapNextIcon />
            </button>
          </Swiper>
        </div>
        {levels?.[activeIndex] &&
          levels?.[activeIndex]?.level === userData.level?.level && (
            <div className="mt-2">
              <div className="flex items-center justify-between gap-2 ">
                <div className="flex items-center text-2xl font-bold">
                  <span>{userData.level?.name}</span>
                </div>
                <span className="font-medium">
                  {compactNumber(userData.balance)}/{compactNumber(userData.level!.to_balance)}
                </span>
              </div>
              <div className="bg-[#1A1617] border overflow-hidden border-[#B3B3B3]/20 rounded-lg mt-2 h-4 w-full relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)]">
                <div
                  className="bg-gradient-to-r from-[#4A0E0E] via-[#732626] to-[#9B8B6C] h-full transition-all duration-300"
                  style={{
                    width: `${(userData.balance / userData.level!.to_balance) * 100}%`,
                  }}
                />
              </div>
            </div>
          )}
        <div className="relative flex-1 mt-6">
          <div
            key={`leaderboard-${activeIndex}`}
            className="absolute inset-0 w-full h-full divide-y divide-[#D9D9D9]/10 overflow-y-auto transition-opacity duration-300"
          >
            {leaderboard.isLoading ? (
              <div className="flex items-center justify-center h-full">
                <BattlxIcon icon="loading" className="w-12 h-12 animate-spin text-[#9B8B6C]" />
              </div>
            ) : leaderboard.data && leaderboard.data?.length > 0 ? (
              leaderboard.data.map((item, key) => (
                <div
                  key={key}
                  className="flex items-center py-2 gap-2.5 px-4 hover:bg-[#4A0E0E]/10 transition-all duration-300"
                  style={{
                    animation: `fadeIn 300ms ease ${key * 50}ms forwards`
                  }}
                >
                  <span className="w-6 text-left text-[#9B8B6C]">{key + 1}</span>
                  <span className="text-[#B3B3B3]">
                    {item.first_name} {item.last_name}
                  </span>
                  <div className="flex items-center gap-2 ml-auto">
                    <BattlxIcon
                      icon="coins"
                      className="w-5 h-5 text-[#9B8B6C]"
                    />
                    <span className="text-[#9B8B6C]">{compactNumber(item.production_per_hour)}</span>
                  </div>
                </div>
              ))
            ) : (
              <div className="flex items-center justify-center h-full text-white">
                No data
              </div>
            )}
          </div>
        </div>
        {levels &&
          levels[activeIndex]?.level === userData.level?.level &&
          !leaderboard.data?.some((item) => item.id === userData.id) && (
            <div
              key={`user-row-${activeIndex}`}
              className="mt-2 flex items-center py-2 gap-2.5 px-4 bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)] transition-opacity duration-300"
              style={{ animation: 'fadeIn 300ms ease forwards' }}
            >
              <span className="w-6 text-right text-[#9B8B6C]">+99</span>
              <span className="text-[#B3B3B3]">You</span>
              <div className="flex items-center gap-2 ml-auto">
                <BattlxIcon
                  icon="coins"
                  className="w-5 h-5 text-[#9B8B6C]"
                />
                <span className="text-[#9B8B6C]">{userData.balance}</span>
              </div>
            </div>
          )}
      </div>
    </div>
  );
}
