<?php

namespace App\Services;

use App\Models\MysteryBoxOpening;
use App\Models\MysteryBoxType;
use App\Models\TelegramUser;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class MysteryBoxAnalyticsService
{
    /**
     * Get global mystery box statistics
     */
    public function getGlobalStatistics(): array
    {
        return Cache::remember('mystery_box_global_stats', 300, function() {
            $totalOpenings = MysteryBoxOpening::count();
            $totalSpent = MysteryBoxOpening::sum('cost_paid');
            $totalValue = MysteryBoxOpening::sum('total_value');
            $rareItemsFound = MysteryBoxOpening::where('contained_rare_item', true)->count();

            $averageValue = $totalOpenings > 0 ? round($totalValue / $totalOpenings, 2) : 0;
            $averageCost = $totalOpenings > 0 ? round($totalSpent / $totalOpenings, 2) : 0;
            $rareItemRate = $totalOpenings > 0 ? round(($rareItemsFound / $totalOpenings) * 100, 1) : 0;

            return [
                'total_openings' => $totalOpenings,
                'total_spent' => $totalSpent,
                'total_value_distributed' => $totalValue,
                'rare_items_found' => $rareItemsFound,
                'average_value_per_opening' => $averageValue,
                'average_cost_per_opening' => $averageCost,
                'rare_item_rate_percentage' => $rareItemRate,
                'net_value_flow' => $totalValue - $totalSpent
            ];
        });
    }

    /**
     * Get mystery box performance by type
     */
    public function getBoxTypePerformance(): array
    {
        return Cache::remember('mystery_box_type_performance', 300, function() {
            $boxTypes = MysteryBoxType::active()->get();
            $performance = [];

            foreach ($boxTypes as $boxType) {
                $openings = $boxType->openings();
                
                $stats = [
                    'box_type' => $boxType->box_type,
                    'display_name' => $boxType->display_name,
                    'rarity' => $boxType->rarity,
                    'total_openings' => $openings->count(),
                    'total_revenue' => $openings->sum('cost_paid'),
                    'total_value_given' => $openings->sum('total_value'),
                    'rare_items_given' => $openings->where('contained_rare_item', true)->count(),
                    'average_value' => 0,
                    'profit_margin' => 0,
                    'rare_item_rate' => 0,
                    'popularity_rank' => 0
                ];

                if ($stats['total_openings'] > 0) {
                    $stats['average_value'] = round($stats['total_value_given'] / $stats['total_openings'], 2);
                    $stats['profit_margin'] = round((($stats['total_revenue'] - $stats['total_value_given']) / $stats['total_revenue']) * 100, 1);
                    $stats['rare_item_rate'] = round(($stats['rare_items_given'] / $stats['total_openings']) * 100, 1);
                }

                $performance[] = $stats;
            }

            // Sort by total openings for popularity ranking
            usort($performance, function($a, $b) {
                return $b['total_openings'] <=> $a['total_openings'];
            });

            // Add popularity rank
            foreach ($performance as $index => &$stats) {
                $stats['popularity_rank'] = $index + 1;
            }

            return $performance;
        });
    }

    /**
     * Get user spending patterns
     */
    public function getUserSpendingPatterns(): array
    {
        return Cache::remember('mystery_box_spending_patterns', 600, function() {
            $spendingByMethod = MysteryBoxOpening::select('purchase_method', DB::raw('COUNT(*) as count'), DB::raw('SUM(cost_paid) as total_spent'))
                                               ->groupBy('purchase_method')
                                               ->get()
                                               ->map(function($item) {
                                                   return [
                                                       'method' => $item->purchase_method,
                                                       'count' => $item->count,
                                                       'total_spent' => $item->total_spent,
                                                       'average_per_opening' => $item->count > 0 ? round($item->total_spent / $item->count, 2) : 0
                                                   ];
                                               });

            $dailyOpenings = MysteryBoxOpening::select(DB::raw('DATE(opened_at) as date'), DB::raw('COUNT(*) as count'))
                                             ->where('opened_at', '>=', now()->subDays(30))
                                             ->groupBy(DB::raw('DATE(opened_at)'))
                                             ->orderBy('date')
                                             ->get();

            $hourlyDistribution = MysteryBoxOpening::select(DB::raw('HOUR(opened_at) as hour'), DB::raw('COUNT(*) as count'))
                                                  ->where('opened_at', '>=', now()->subDays(7))
                                                  ->groupBy(DB::raw('HOUR(opened_at)'))
                                                  ->orderBy('hour')
                                                  ->get();

            return [
                'spending_by_method' => $spendingByMethod,
                'daily_openings_last_30_days' => $dailyOpenings,
                'hourly_distribution_last_7_days' => $hourlyDistribution
            ];
        });
    }

    /**
     * Get reward distribution analysis
     */
    public function getRewardDistributionAnalysis(): array
    {
        return Cache::remember('mystery_box_reward_distribution', 600, function() {
            $openings = MysteryBoxOpening::with('mysteryBoxType')->get();
            
            $rewardsByRarity = [];
            $totalRewards = 0;

            foreach ($openings as $opening) {
                $rewards = $opening->rewards_received ?? [];
                $totalRewards += count($rewards);

                foreach ($rewards as $collectibleId) {
                    $collectible = \App\Models\CollectibleTemplate::where('collectible_id', $collectibleId)->first();
                    if ($collectible) {
                        $rarity = $collectible->rarity;
                        if (!isset($rewardsByRarity[$rarity])) {
                            $rewardsByRarity[$rarity] = [
                                'count' => 0,
                                'total_value' => 0,
                                'percentage' => 0
                            ];
                        }
                        $rewardsByRarity[$rarity]['count']++;
                        $rewardsByRarity[$rarity]['total_value'] += $collectible->estimated_value;
                    }
                }
            }

            // Calculate percentages
            foreach ($rewardsByRarity as $rarity => &$data) {
                $data['percentage'] = $totalRewards > 0 ? round(($data['count'] / $totalRewards) * 100, 1) : 0;
                $data['average_value'] = $data['count'] > 0 ? round($data['total_value'] / $data['count'], 2) : 0;
            }

            return [
                'total_rewards_distributed' => $totalRewards,
                'rewards_by_rarity' => $rewardsByRarity,
                'rarity_distribution_healthy' => $this->isRarityDistributionHealthy($rewardsByRarity)
            ];
        });
    }

    /**
     * Get top spenders analysis
     */
    public function getTopSpendersAnalysis(int $limit = 10): array
    {
        $topSpenders = DB::table('mystery_box_openings')
                        ->select('telegram_user_id', DB::raw('COUNT(*) as total_openings'), DB::raw('SUM(cost_paid) as total_spent'), DB::raw('SUM(total_value) as total_value_received'))
                        ->groupBy('telegram_user_id')
                        ->orderBy('total_spent', 'desc')
                        ->limit($limit)
                        ->get();

        $spenderData = [];
        foreach ($topSpenders as $spender) {
            $user = TelegramUser::find($spender->telegram_user_id);
            $netResult = $spender->total_value_received - $spender->total_spent;
            
            $spenderData[] = [
                'user_id' => $spender->telegram_user_id,
                'username' => $user ? $user->username : 'Unknown',
                'total_openings' => $spender->total_openings,
                'total_spent' => $spender->total_spent,
                'total_value_received' => $spender->total_value_received,
                'net_result' => $netResult,
                'average_per_opening' => $spender->total_openings > 0 ? round($spender->total_spent / $spender->total_openings, 2) : 0,
                'roi_percentage' => $spender->total_spent > 0 ? round(($netResult / $spender->total_spent) * 100, 1) : 0
            ];
        }

        return $spenderData;
    }

    /**
     * Get mystery box economy health metrics
     */
    public function getEconomyHealthMetrics(): array
    {
        $totalSpent = MysteryBoxOpening::sum('cost_paid');
        $totalValueGiven = MysteryBoxOpening::sum('total_value');
        $totalOpenings = MysteryBoxOpening::count();

        $recentOpenings = MysteryBoxOpening::where('opened_at', '>=', now()->subDays(7))->count();
        $previousWeekOpenings = MysteryBoxOpening::whereBetween('opened_at', [now()->subDays(14), now()->subDays(7)])->count();

        $growthRate = $previousWeekOpenings > 0 ? round((($recentOpenings - $previousWeekOpenings) / $previousWeekOpenings) * 100, 1) : 0;

        $profitMargin = $totalSpent > 0 ? round((($totalSpent - $totalValueGiven) / $totalSpent) * 100, 1) : 0;

        $activeUsers = DB::table('mystery_box_openings')
                        ->distinct('telegram_user_id')
                        ->where('opened_at', '>=', now()->subDays(30))
                        ->count();

        return [
            'total_revenue' => $totalSpent,
            'total_value_distributed' => $totalValueGiven,
            'profit_margin_percentage' => $profitMargin,
            'weekly_growth_rate' => $growthRate,
            'active_users_last_30_days' => $activeUsers,
            'average_openings_per_user' => $activeUsers > 0 ? round($totalOpenings / $activeUsers, 1) : 0,
            'economy_status' => $this->getEconomyStatus($profitMargin, $growthRate),
            'recommendations' => $this->getEconomyRecommendations($profitMargin, $growthRate)
        ];
    }

    // Private helper methods

    private function isRarityDistributionHealthy(array $rewardsByRarity): bool
    {
        // Check if rare items are actually rare (should be less than 20% of total)
        $rarePercentage = ($rewardsByRarity['rare']['percentage'] ?? 0) + 
                         ($rewardsByRarity['epic']['percentage'] ?? 0) + 
                         ($rewardsByRarity['legendary']['percentage'] ?? 0) + 
                         ($rewardsByRarity['mythic']['percentage'] ?? 0);

        return $rarePercentage <= 20;
    }

    private function getEconomyStatus(float $profitMargin, float $growthRate): string
    {
        if ($profitMargin >= 20 && $growthRate >= 0) {
            return 'healthy';
        } elseif ($profitMargin >= 10 && $growthRate >= -10) {
            return 'stable';
        } elseif ($profitMargin >= 0) {
            return 'concerning';
        } else {
            return 'critical';
        }
    }

    private function getEconomyRecommendations(float $profitMargin, float $growthRate): array
    {
        $recommendations = [];

        if ($profitMargin < 10) {
            $recommendations[] = 'Consider adjusting reward values or box costs to improve profit margins';
        }

        if ($growthRate < -20) {
            $recommendations[] = 'User engagement is declining - consider promotional events or new box types';
        }

        if ($profitMargin > 40) {
            $recommendations[] = 'High profit margins detected - consider increasing reward values to improve user satisfaction';
        }

        if (empty($recommendations)) {
            $recommendations[] = 'Economy is performing well - maintain current balance';
        }

        return $recommendations;
    }
}
