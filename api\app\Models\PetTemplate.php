<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PetTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'category', 'rarity', 'description', 'image_url', 'animation_url',
        'coin_cost', 'gem_cost', 'prize_tree_unlock_level', 'is_premium_only',
        'base_happiness', 'max_happiness', 'happiness_decay_rate',
        'mystery_box_unlocks', 'collectible_reward_id', 'max_level',
        'evolution_levels', 'evolution_images', 'is_active', 'sort_order'
    ];

    protected $casts = [
        'mystery_box_unlocks' => 'array',
        'evolution_levels' => 'array',
        'evolution_images' => 'array',
        'is_premium_only' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function pets(): HasMany
    {
        return $this->hasMany(Pet::class);
    }

    public function collectibleReward()
    {
        return $this->belongsTo(CollectibleTemplate::class, 'collectible_reward_id', 'collectible_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByRarity($query, $rarity)
    {
        return $query->where('rarity', $rarity);
    }

    public function scopeAvailableForPurchase($query)
    {
        return $query->where('is_active', true)
                    ->where(function($q) {
                        $q->where('coin_cost', '>', 0)
                          ->orWhere('gem_cost', '>', 0);
                    });
    }

    // Accessors
    public function getImageUrlAttribute($value)
    {
        return $value ? asset($value) : null;
    }

    public function getAnimationUrlAttribute($value)
    {
        return $value ? asset($value) : null;
    }

    public function getRarityColorAttribute()
    {
        return match($this->rarity) {
            'common' => '#9CA3AF',
            'rare' => '#3B82F6',
            'epic' => '#8B5CF6',
            'legendary' => '#F59E0B',
            'mythic' => '#EF4444',
            default => '#6B7280'
        };
    }

    // Business Logic Methods
    public function canBePurchasedBy(TelegramUser $user): bool
    {
        // Check if user already owns this pet
        if ($user->pets()->where('pet_template_id', $this->id)->exists()) {
            return false;
        }

        // Check prize tree unlock requirement
        if ($this->prize_tree_unlock_level) {
            $userProgress = $user->prizeTreeProgress();
            if (!$userProgress || $userProgress->current_level < $this->prize_tree_unlock_level) {
                return false;
            }
        }

        return $this->is_active;
    }

    public function getPurchaseCost(string $method): int
    {
        return match($method) {
            'coins' => $this->coin_cost,
            'gems' => $this->gem_cost,
            default => 0
        };
    }

    public function getEvolutionImageForLevel(int $level): ?string
    {
        $evolutionLevels = $this->evolution_levels ?? [];
        $evolutionImages = $this->evolution_images ?? [];
        
        $evolutionStage = 0;
        foreach ($evolutionLevels as $index => $requiredLevel) {
            if ($level >= $requiredLevel) {
                $evolutionStage = $index + 1;
            }
        }
        
        return $evolutionImages[$evolutionStage] ?? $this->image_url;
    }

    public function getNextEvolutionLevel(int $currentLevel): ?int
    {
        $evolutionLevels = $this->evolution_levels ?? [];
        
        foreach ($evolutionLevels as $level) {
            if ($currentLevel < $level) {
                return $level;
            }
        }
        
        return null;
    }
}
