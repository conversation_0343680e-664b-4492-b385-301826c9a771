import { $http } from './http';
import { AxiosError } from 'axios';
import { UserType } from '@/types/UserType';

interface GameScore {
  score: number;
  game_id: string;
}

interface GameState {
  state: Record<string, any>;
  game_id: string;
}

interface PlayStatus {
  allowed: boolean;
  free?: boolean;
  plays_remaining?: number;
  reason?: string;
  cost?: number;
  balance?: number;
}

interface PlayResult {
  success: boolean;
  plays_remaining: number;
  balance: number;
}

// Export ApiResponse for use in components
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  exists?: boolean;
  message?: string;
  user?: UserType;  // Added for unlock response
}

/**
 * Game API Service
 * 
 * Provides methods for interacting with game-related endpoints
 */
export const gameApi = {
  /**
   * Save a game score
   * @param score The score to save
   * @param gameId The game ID (defaults to 'tower')
   */
  saveScore: async (score: number, gameId: string = 'tower') => {
    try {
      const { data } = await $http.post<ApiResponse<GameScore>>('/game/score', { 
        score,
        game_id: gameId 
      });
      return data;
    } catch (error) {
      console.error('Failed to save score:', error);
      throw error;
    }
  },
  
  /**
   * Get the game leaderboard
   * @param gameId The game ID (defaults to 'tower')
   * @param limit The maximum number of entries to return
   */
  getLeaderboard: async (gameId: string = 'tower', limit: number = 100) => {
    try {
      const { data } = await $http.get<ApiResponse<any>>('/game/leaderboard', {
        params: {
          game_id: gameId,
          limit
        }
      });
      return data;
    } catch (error) {
      console.error('Failed to get leaderboard:', error);
      throw error;
    }
  },
  
  /**
   * Check if user can play a specific game
   * @param gameId The ID of the game (e.g., 'tower', 'rabbit')
   * @returns PlayStatus with availability and conditions
   */
  checkPlayAvailability: async (gameId: string) => {
    try {
      const { data } = await $http.get<ApiResponse<PlayStatus>>('/game/play-availability', {
        params: { game_id: gameId }
      });
      return data;
    } catch (error) {
      console.error(`Failed to check play availability for ${gameId}:`, error);
      throw error;
    }
  },

  /**
   * Use a play attempt
   * @param paid Whether this is a paid play
   * @returns PlayResult with updated state
   */
  usePlay: async (paid: boolean = false, quantity: number = 1) => {
    try {
      const { data } = await $http.post<ApiResponse<PlayResult>>('/game/use-play', {
        paid,
        quantity
      });
      return data;
    } catch (error: any) {
      if (error?.response?.status === 400 && error?.response?.data) {
        const errorData = error.response.data;
        // Return formatted error response that includes play status
        return {
          success: false,
          message: errorData.message || 'Failed to use play',
          data: errorData.data || {
            plays_remaining: 0,
            balance: 0
          }
        } as ApiResponse<PlayResult>;
      }
      console.error('Failed to use play:', error);
      throw error;
    }
  },

  /**
   * Unlock a specific game
   * Requires sufficient balance based on the game
   * @param gameId The ID of the game to unlock (e.g., 'tower', 'rabbit')
   * @returns ApiResponse with updated user data
   */
  unlockGame: async (gameId: string) => {
    try {
      const { data } = await $http.post<ApiResponse<{ user: UserType }>>('/game/unlock', {
        game_id: gameId
      });
      return data;
    } catch (error) {
      console.error(`Failed to unlock ${gameId} game:`, error);
      throw error;
    }
  },

  /**
   * Save the current game state
   * @param gameId The game ID
   * @param state The game state to save
   */
  saveGameState: async (gameId: string, state: Record<string, any>) => {
    try {
      const { data } = await $http.post<ApiResponse<GameState>>('/game/state', {
        game_id: gameId,
        state
      });
      return data;
    } catch (error) {
      console.error('Failed to save game state:', error);
      throw error;
    }
  },

  /**
   * Get the saved game state
   * @param gameId The game ID
   */
  getGameState: async (gameId: string) => {
    try {
      const { data } = await $http.get<ApiResponse<GameState>>(`/game/state/${gameId}`);
      return data.data?.state;
    } catch (error) {
      console.error('Failed to get game state:', error);
      throw error;
    }
  },

  /**
   * Delete a saved game state
   * @param gameId The game ID
   */
  deleteGameState: async (gameId: string) => {
    try {
      await $http.delete(`/game/state/${gameId}`);
    } catch (error) {
      console.error('Failed to delete game state:', error);
      throw error;
    }
  },

  /**
   * Check if a saved state exists for a game
   * @param gameId The game ID
   */
  hasSavedState: async (gameId: string): Promise<boolean> => {
    try {
      const { data } = await $http.head<ApiResponse<void>>(`/game/state/${gameId}`);
      return !!data.exists;
    } catch (error) {
      const axiosError = error as AxiosError;
      if (axiosError.response?.status === 404) {
        return false;
      }
      console.error('Failed to check saved state:', error);
      throw error;
    }
  }
};
