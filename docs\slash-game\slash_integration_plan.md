# Slash Game Integration Plan

## Overview
This document outlines the integration plan for the Slash game with the existing Telegram web app infrastructure. It covers how the game will be connected to the backend, how it will interact with the GameWrapper component, and how it will be integrated with the user interface.

## Current Status Analysis
- The GameWrapper component is already set up to handle different games
- The game registry in `battlx/src/games/registry.ts` already includes the Slash game configuration
- The SlashGameDrawer component exists for unlocking the game
- The backend has basic support for the Slash game in the GameController

## Integration Tasks

### 1. Update the GameWrapper Component
Ensure the GameWrapper component properly handles the Slash game in the frontend.

```typescript
// In GameWrapper.tsx - checkAndStartGame method
const checkAndStartGame = useCallback(async (paid: boolean = false) => {
  // ...existing logic...
  
  // For Rabbit and Slash Games, skip the usePlay API call since they have unlimited plays
  let playResult;
  if (gameId === 'rabbit' || gameId === 'slash') {
    // For Rabbit and Slash Games, just return a successful result without calling usePlay
    playResult = {
      success: true,
      data: {
        plays_remaining: 999, // Unlimited plays
        balance: playData.balance || 0
      }
    };
  } else {
    // For Tower Game and others, use a play attempt
    playResult = await gameApi.usePlay(paid, 1);
    if (!playResult.success || !playResult.data) {
      throw new Error(playResult.message || 'Failed to start game');
    }
  }
  
  // ...existing logic...
}, [navigate, gameId]);
```

### 2. Update the Game API Service
Ensure the game API service properly handles the Slash game.

```typescript
// In game-api.ts - usePlay method
usePlay: async (paid: boolean = false, quantity: number = 1, gameId: string) => {
  try {
    const { data } = await $http.post<ApiResponse<PlayResult>>('/game/use-play', {
      paid,
      quantity,
      game_id: gameId // Add game_id parameter
    });
    return data;
  } catch (error: any) {
    if (error?.response?.status === 400 && error?.response?.data) {
      const errorData = error.response.data;
      // Return formatted error response that includes play status
      return {
        success: false,
        message: errorData.message || 'Failed to use play',
        data: errorData.data || {
          plays_remaining: 0,
          balance: 0
        }
      } as ApiResponse<PlayResult>;
    }
    console.error('Failed to use play:', error);
    throw error;
  }
}
```

### 3. Update the User Store
Ensure the user store properly handles the Slash game.

```typescript
// In user-store.ts - getGameStatus method
getGameStatus: (gameId) => {
  const state = get();
  if (gameId === 'tower') {
    return {
      unlocked: state.tower_game_unlocked,
      highScore: state.game_score
    };
  }
  if (gameId === 'rabbit') {
    return {
      unlocked: state.rabbit_game_unlocked,
      highScore: state.game_score
    };
  }
  if (gameId === 'slash') {
    return {
      unlocked: state.slash_game_unlocked,
      highScore: state.game_score
    };
  }
  
  // ...existing fallback logic...
}
```

### 4. Connect the Slash Game to the Backend
Implement the necessary code to connect the Slash game to the backend for score tracking and achievements.

```javascript
// In gameCore.js - Add methods to communicate with the backend
// Add these methods to the GameCore class

// Set game score and send to backend
setScore(score) {
    this.score = score;
    
    // Call the setGameScore callback if it exists
    if (this.setGameScore) {
        this.setGameScore(score);
    }
}

// Handle game over and send final score to backend
gameOver(score) {
    this.isGameOver = true;
    
    // Call the onGameOver callback if it exists
    if (this.onGameOver) {
        this.onGameOver(score);
    }
    
    // Show game over UI
    if (this.mainUI) {
        this.mainUI.showGameOver(score);
    }
}
```

### 5. Implement Score Tracking in the Slash Game
Add score tracking to the Slash game and connect it to the backend.

```javascript
// In gameCore.js - Add score tracking
// Add these properties to the GameCore constructor
this.score = 0;
this.combo = 0;
this.comboTimer = null;
this.comboTimeout = 1000; // 1 second to maintain combo

// Add this method to the GameCore class
addScore(points, comboMultiplier = true) {
    // Add base points
    let finalPoints = points;
    
    // Apply combo multiplier if enabled
    if (comboMultiplier && this.combo > 0) {
        finalPoints = Math.floor(points * (1 + this.combo * 0.1));
    }
    
    // Update score
    this.score += finalPoints;
    
    // Update UI
    if (this.mainUI) {
        this.mainUI.setScore(this.score);
    }
    
    // Send score to backend
    this.setScore(this.score);
    
    return finalPoints;
}

// Add this method to the GameCore class
increaseCombo() {
    // Clear existing combo timer
    if (this.comboTimer) {
        clearTimeout(this.comboTimer);
    }
    
    // Increase combo
    this.combo++;
    
    // Update UI
    if (this.mainUI) {
        this.mainUI.setCombo(this.combo);
    }
    
    // Set combo timer
    this.comboTimer = setTimeout(() => {
        this.resetCombo();
    }, this.comboTimeout);
}

// Add this method to the GameCore class
resetCombo() {
    // Reset combo
    this.combo = 0;
    
    // Update UI
    if (this.mainUI) {
        this.mainUI.setCombo(0);
    }
    
    // Clear combo timer
    if (this.comboTimer) {
        clearTimeout(this.comboTimer);
        this.comboTimer = null;
    }
}
```

### 6. Implement Achievement Tracking
Add achievement tracking to the Slash game.

```javascript
// In gameCore.js - Add achievement tracking
// Add these properties to the GameCore constructor
this.achievements = {
    slashCount: 0,
    comboMax: 0,
    fruitSlashed: 0,
    bombsAvoided: 0,
    specialItemsCollected: 0
};

// Add this method to the GameCore class
updateAchievement(type, value = 1) {
    switch (type) {
        case 'slash':
            this.achievements.slashCount += value;
            break;
        case 'combo':
            this.achievements.comboMax = Math.max(this.achievements.comboMax, value);
            break;
        case 'fruit':
            this.achievements.fruitSlashed += value;
            break;
        case 'bomb':
            this.achievements.bombsAvoided += value;
            break;
        case 'special':
            this.achievements.specialItemsCollected += value;
            break;
    }
    
    // Check for achievement milestones
    this.checkAchievementMilestones();
}

// Add this method to the GameCore class
checkAchievementMilestones() {
    // Check slash count milestones
    const slashMilestones = [50, 100, 500, 1000];
    for (const milestone of slashMilestones) {
        if (this.achievements.slashCount >= milestone) {
            // Achievement unlocked
            console.log(`Achievement: ${milestone} slashes!`);
        }
    }
    
    // Check combo milestones
    const comboMilestones = [5, 10, 20, 50];
    for (const milestone of comboMilestones) {
        if (this.achievements.comboMax >= milestone) {
            // Achievement unlocked
            console.log(`Achievement: ${milestone}x combo!`);
        }
    }
    
    // Check fruit slashed milestones
    const fruitMilestones = [100, 500, 1000, 5000];
    for (const milestone of fruitMilestones) {
        if (this.achievements.fruitSlashed >= milestone) {
            // Achievement unlocked
            console.log(`Achievement: ${milestone} fruits slashed!`);
        }
    }
}
```

### 7. Implement Game State Management
Add game state management to the Slash game.

```javascript
// In gameCore.js - Add game state management
// Add these properties to the GameCore constructor
this.gameState = 'menu'; // 'menu', 'playing', 'paused', 'gameOver'

// Add this method to the GameCore class
setState(state) {
    this.gameState = state;
    
    switch (state) {
        case 'menu':
            // Show menu UI
            if (this.mainUI) {
                this.mainUI.showMenu();
            }
            break;
        case 'playing':
            // Start or resume game
            this.isPaused = false;
            // Hide menu UI
            if (this.mainUI) {
                this.mainUI.hideMenu();
            }
            break;
        case 'paused':
            // Pause game
            this.isPaused = true;
            // Show pause UI
            if (this.mainUI) {
                this.mainUI.showPause();
            }
            break;
        case 'gameOver':
            // Game over
            this.isGameOver = true;
            // Show game over UI
            if (this.mainUI) {
                this.mainUI.showGameOver(this.score);
            }
            break;
    }
}
```
