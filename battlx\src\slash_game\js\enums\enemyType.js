// Enemy types enum
const EnemyType = {
    GHOST1: 'GHOST1',
    GHOST2: 'GHOST2',
    GHOST3: 'GHOST3',
    GHOST_BOSS1: 'GHOST_BOSS1',
    GHOST_BOSS2: 'GHOST_BOSS2',
    GHOST_SWARM: 'GHOST_SWARM',
    ZOMBIE1: 'ZOMBIE1',
    ZOMBIE2: 'ZOMBIE2',
    POO: 'POO',
    BAT: 'BAT',
    BAT_SWARM: 'BAT_SWARM',
    OGRE: 'OGRE',
    GOBLIN_BOSS: 'GOBLIN_BOSS',
    GOBLIN: 'GOBLIN',
    HORN: 'HORN',
    ALIEN: 'ALIEN',
    ALIEN_BOSS: 'ALIEN_BOSS',
    TELEPORTER: 'TELEPORTER' // New enemy type for teleportation effects
};

// Attach to window object for global access
window.EnemyType = EnemyType;
