import CopyIcon from "@/components/icons/CopyIcon";
import { Button } from "@/components/ui/button";
import { $http } from "@/lib/http";
import { compactNumber } from "@/lib/utils";
import { uesStore } from "@/store";
import { useUserStore } from "@/store/user-store";
import { PaginationResponse } from "@/types/Response";
import { UserType } from "@/types/UserType";
import { useQuery } from "@tanstack/react-query";
import { useCopyToClipboard } from "@uidotdev/usehooks";
import { BattlxIcon } from "@/components/icons/BattlxIcon";
import { useMemo, useState } from "react";
import { toast } from "react-toastify";

const shareMessage = encodeURI(
  "Join the dark realm of Battlx with me!"
);

interface ReferralTask {
  id: number;
  title: string;
  number_of_referrals: number;
  reward: number;
}

export default function Friends() {
  const [, copy] = useCopyToClipboard();
  const { telegram_id } = useUserStore();
  const { referral, levels } = uesStore();
  const [showMoreBonuses, setShowMoreBonuses] = useState(false);

  const referralLink = useMemo(
    () => `${import.meta.env.VITE_BOT_URL}/?startapp=ref${telegram_id}`,
    [telegram_id]
  );

  const referredUsers = useQuery({
    queryKey: ["referredUsers"],
    queryFn: () => $http.$get<PaginationResponse<UserType>>("/referred-users"),
  });

  const referralTasks = useQuery({
    queryKey: ["referralTasks"],
    queryFn: () => $http.$get<ReferralTask[]>("/referral-tasks"),
  });

  const currentFriendsCount = referredUsers.data?.meta?.total || 0;

  const nextTask = useMemo(() => {
    if (!referralTasks.data) return null;
    return referralTasks.data
      .sort((a, b) => a.number_of_referrals - b.number_of_referrals)
      .find(task => task.number_of_referrals > currentFriendsCount);
  }, [referralTasks.data, currentFriendsCount]);

  const progressPercent = useMemo(() => {
    if (!nextTask) return 0;
    const previousTask = referralTasks.data
      ?.sort((a, b) => a.number_of_referrals - b.number_of_referrals)
      .filter(task => task.number_of_referrals <= currentFriendsCount)
      .pop();
    
    const startCount = previousTask ? previousTask.number_of_referrals : 0;
    const progress = ((currentFriendsCount - startCount) / (nextTask.number_of_referrals - startCount)) * 100;
    return Math.min(100, Math.max(0, progress));
  }, [nextTask, currentFriendsCount, referralTasks.data]);

  return (
    <div className="flex flex-col justify-end bg-[url('/images/bg.png')] bg-cover bg-center min-h-screen relative">
      <div className="flex flex-col flex-1 w-full h-full px-4 sm:px-6 py-6 pb-20 mt-8 modal-body bg-black/40 backdrop-blur-sm relative">
        <h1 className="text-3xl font-bold text-center uppercase tracking-wider text-[#B3B3B3] font-gothic">Friends</h1>
        <p className="mt-3 font-medium text-center text-[#9B8B6C] tracking-wide">
          Summon allies to receive dark rewards
        </p>
        
        <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-3">
          <button className="flex items-center w-full gap-3 px-4 py-3.5 bg-[#120D0E] rounded-xl border border-[#B3B3B3]/20 relative overflow-hidden transform transition-all duration-500 hover:scale-[1.02] active:scale-[0.98] before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.07)_0px,rgba(155,139,108,0.07)_1px,transparent_1px,transparent_10px)] after:absolute after:inset-0 after:opacity-0 after:bg-gradient-to-r after:from-[#4A0E0E]/40 after:to-transparent after:transition-opacity hover:after:opacity-100 shadow-[0_4px_20px_rgba(74,14,14,0.4)]">
            <div className="relative z-10 flex items-center gap-3 w-full">
              <BattlxIcon
                icon="chest"
                className="w-10 h-10 sm:w-12 sm:h-12 opacity-90 text-[#9B8B6C] flex-shrink-0"
              />
              <div className="text-sm font-medium text-left min-w-0">
                <p className="text-[#B3B3B3] text-base mb-0.5 truncate">Invite a friend</p>
                <div className="flex items-center space-x-1.5 flex-wrap">
                  <BattlxIcon
                    icon="coins"
                    className="w-5 h-5 opacity-90 text-[#9B8B6C]"
                  />
                  <span className="font-bold text-[#9B8B6C] whitespace-nowrap">
                    +{referral.base.welcome.toLocaleString()}
                  </span>
                  <span className="text-sm text-[#B3B3B3]/70 whitespace-nowrap">for both</span>
                </div>
              </div>
            </div>
          </button>

          <button className="flex items-center w-full gap-3 px-4 py-3.5 bg-[#120D0E] rounded-xl border border-[#B3B3B3]/20 relative overflow-hidden transform transition-all duration-500 hover:scale-[1.02] active:scale-[0.98] before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.07)_0px,rgba(155,139,108,0.07)_1px,transparent_1px,transparent_10px)] after:absolute after:inset-0 after:opacity-0 after:bg-gradient-to-r after:from-[#4A0E0E]/40 after:to-transparent after:transition-opacity hover:after:opacity-100 shadow-[0_4px_20px_rgba(74,14,14,0.4)]">
            <div className="relative z-10 flex items-center gap-3 w-full">
              <BattlxIcon
                icon="chest"
                className="w-10 h-10 sm:w-12 sm:h-12 opacity-90 text-[#9B8B6C] flex-shrink-0"
              />
              <div className="text-sm font-medium min-w-0">
                <p className="text-[#B3B3B3] text-base mb-0.5 truncate">Invite Premium friend</p>
                <div className="flex items-center space-x-1.5 flex-wrap">
                  <BattlxIcon
                    icon="coins"
                    className="w-5 h-5 opacity-90 text-[#9B8B6C]"
                  />
                  <span className="font-bold text-[#9B8B6C] whitespace-nowrap">
                    +{referral.premium.welcome.toLocaleString()}
                  </span>
                  <span className="text-sm text-[#B3B3B3]/70 whitespace-nowrap">for both</span>
                </div>
              </div>
            </div>
          </button>
        </div>

        <div className="relative flex-1 mt-2">
          <div className="absolute inset-0 w-full h-[calc(100%-1rem)] py-4 mt-4 overflow-y-auto scrollbar-thin scrollbar-thumb-[#4A0E0E] scrollbar-track-[#1A1617]">
            {!showMoreBonuses ? (
              <div className="text-center">
                <button
                  className="px-6 py-2.5 text-sm font-medium transition-all duration-300 rounded-lg border border-[#B3B3B3]/20 bg-[#120D0E] text-[#9B8B6C] shadow-[0_4px_15px_rgba(74,14,14,0.4)] hover:bg-[#4A0E0E]/30 transform hover:scale-105 active:scale-95"
                  onClick={() => setShowMoreBonuses((value) => !value)}
                >
                  Reveal Dark Rewards
                </button>
              </div>
            ) : (
              <>
                <p
                  className="mt-8 text-base font-bold uppercase tracking-wider text-[#9B8B6C] cursor-pointer hover:text-[#B3B3B3] transition-colors"
                  onClick={() => setShowMoreBonuses((value) => !value)}
                >
                  Ascension Rewards
                </p>
                <div className="relative flex-1 mt-4 min-h-60">
                  <div className="absolute inset-0 w-full h-full overflow-y-auto scrollbar-thin scrollbar-thumb-[#4A0E0E] scrollbar-track-[#1A1617]">
                    <table className="w-full">
                      <thead className="text-sm text-[#9B8B6C]/50">
                        <tr className="border-b border-[#D9D9D9]/10">
                          <th className="px-3 py-2.5 text-left">Level</th>
                          <th className="px-3 py-2.5 text-right">Reward</th>
                          <th className="px-3 py-2.5 text-right">Premium</th>
                        </tr>
                      </thead>
                      <tbody>
                        {levels
                          .filter((item) => referral.base.levelUp[item.level])
                          .map((item, key) => (
                            <tr
                              key={key}
                              className="border-b border-[#D9D9D9]/10 hover:bg-[#4A0E0E]/10 transition-colors"
                            >
                              <td className="px-3 py-3 text-sm text-[#B3B3B3]">{item.name}</td>
                              <td className="px-3 py-3">
                                <div className="flex items-center justify-end gap-1.5">
                                  <BattlxIcon
                                    icon="coins"
                                    className="w-4 h-4 text-[#9B8B6C]"
                                  />
                                  <span className="text-sm font-medium text-[#9B8B6C]">
                                    {referral.base.levelUp[
                                      item.level
                                    ].toLocaleString()}
                                  </span>
                                </div>
                              </td>
                              <td className="px-3 py-3">
                                <div className="flex items-center justify-end gap-1.5">
                                  <BattlxIcon icon="coins" className="w-4 h-4 text-[#9B8B6C]" />
                                  <span className="text-sm font-medium text-[#9B8B6C]">
                                    {(
                                      referral.premium.levelUp[item.level] || 0
                                    ).toLocaleString()}
                                  </span>
                                </div>
                              </td>
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </>
            )}
            
            <p className="mt-8 text-base font-bold uppercase tracking-wider text-[#9B8B6C]">
              Your Dark Alliance {" "}
              {referredUsers.data?.meta
                ? `(${referredUsers.data?.meta.total})`
                : null}
            </p>
            
            {referredUsers.isLoading ? (
              <div className="flex items-center justify-center w-full h-16">
                <div className="w-6 h-6 border-2 border-t-[#9B8B6C] rounded-full border-[#4A0E0E] animate-spin"></div>
              </div>
            ) : referredUsers.data?.data?.length ? (
              <div className="mt-4 space-y-3">
                {referredUsers.data.data.map((item, key) => (
                  <div
                    key={key}
                    className="flex items-center justify-between px-5 py-4 bg-[#120D0E] rounded-xl border border-[#B3B3B3]/20 relative overflow-hidden transform transition-all duration-500 hover:scale-[1.02] before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.07)_0px,rgba(155,139,108,0.07)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_20px_rgba(74,14,14,0.4)]">
                    <div className="flex items-center gap-3">
                      <BattlxIcon
                        icon="avatar"
                        className="w-10 h-10 text-[#9B8B6C]"
                      />
                      <div>
                        <p className="text-base font-medium text-[#B3B3B3]">
                          {item.first_name} {item.last_name}
                        </p>
                        <p className="text-sm text-[#9B8B6C]/70">{item.level?.name}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <BattlxIcon
                        icon="coins"
                        className="w-5 h-5 opacity-90 text-[#9B8B6C]"
                      />
                      <span className="text-base font-medium text-[#9B8B6C]">
                        {compactNumber(item.balance)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center px-5 mt-4 border-2 border-dashed rounded-xl border-[#B3B3B3]/20 h-16 bg-[#120D0E] shadow-[0_4px_20px_rgba(74,14,14,0.4)]">
                <p className="text-sm font-medium text-center text-[#B3B3B3]/50">
                  Your alliance awaits its first member
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="sticky bottom-20">
          {nextTask && (
            <div className="px-5 py-4 mb-4 bg-[#120D0E] rounded-xl border border-[#B3B3B3]/20 relative overflow-hidden shadow-[0_4px_20px_rgba(74,14,14,0.4)] transform hover:scale-[1.01] transition-transform duration-300 z-10">
              <div className="flex justify-between items-center mb-3">
                <span className="text-sm font-medium text-[#B3B3B3]">{nextTask.title}</span>
                <div className="flex items-center gap-1.5">
                  <BattlxIcon icon="coins" className="w-5 h-5 text-[#9B8B6C]" />
                  <span className="text-sm font-bold text-[#9B8B6C]">
                    {nextTask.reward.toLocaleString()}
                  </span>
                </div>
              </div>
              <div className="flex items-center gap-3 text-sm text-[#9B8B6C]">
                <span className="font-medium">{currentFriendsCount}</span>
                <div className="flex-1 w-full bg-[#5A0000] border overflow-hidden border-[#FF0000]/10 rounded-lg h-4 relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(255,0,0,0.03)_0px,rgba(255,0,0,0.03)_1px,transparent_1px,transparent_10px)] shadow-[0_0_15px_rgba(255,0,0,0.15)]">
                  <div
                    className="bg-gradient-to-l from-[#000000] via-[#000000] to-[#000000] h-full transition-all duration-700 ease-out shadow-[inset_0_0_20px_rgba(255,0,0,0.4),0_0_10px_rgba(255,0,0,0.3)]"
                    style={{
                      width: `${progressPercent}%`,
                    }}
                  />
                </div>
                <span className="font-medium">{nextTask.number_of_referrals}</span>
              </div>
            </div>
          )}

          <div className="flex gap-3">
            <Button
              className="flex-shrink-0 bg-[#120D0E] text-[#9B8B6C] border border-[#B3B3B3]/20 hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_20px_rgba(74,14,14,0.4)] px-5 py-3 transform hover:scale-105 active:scale-95"
              onClick={() => {
                copy(referralLink);
                toast.success("Dark pact link copied");
              }}
            >
              <CopyIcon className="w-5 h-5" />
            </Button>
            <Button
              className="flex-1 bg-[#120D0E] text-[#9B8B6C] border border-[#B3B3B3]/20 hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_20px_rgba(74,14,14,0.4)] px-5 py-3 text-base transform hover:scale-105 active:scale-95"
              onClick={() =>
                Telegram.WebApp.openTelegramLink(
                  `https://t.me/share/url?text=${shareMessage}&url=${referralLink}`
                )
              }
            >
              Summon an Ally
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
