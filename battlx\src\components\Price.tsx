import { cn } from "@/lib/utils";
import { BattlxIcon } from "./icons/BattlxIcon";
import React from "react";

type Props = React.HtmlHTMLAttributes<HTMLDivElement> & {
  amount: number | string;
};
export default function Price({ amount, className, ...props }: Props) {
  return (
    <div
      className={cn("flex items-center space-x-1 text-[#9B8B6C]", className)}
      {...props}
    >
      <BattlxIcon
        icon="coins"
        className="w-5 h-5 opacity-80 text-[#9B8B6C]"
      />
      <span className="font-medium">{amount}</span>
    </div>
  );
}
