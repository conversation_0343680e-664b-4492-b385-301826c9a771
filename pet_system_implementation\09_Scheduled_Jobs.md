# Scheduled Jobs Implementation

## Overview
This document covers the implementation of scheduled jobs for the Pet System, including daily happiness decay, notifications, and maintenance tasks.

## Implementation Time: 1-2 days
## Complexity: Medium
## Dependencies: Pet services, notification system

## Daily Happiness Decay Job

### PetHappinessDecayJob
```php
<?php
// File: api/app/Jobs/PetHappinessDecayJob.php

namespace App\Jobs;

use App\Services\PetService;
use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class PetHappinessDecayJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    protected PetService $petService;
    protected NotificationService $notificationService;

    public function __construct()
    {
        $this->petService = app(PetService::class);
        $this->notificationService = app(NotificationService::class);
    }

    public function handle(): void
    {
        Log::info('Starting daily pet happiness decay job');

        try {
            // Process happiness decay for all pets
            $result = $this->petService->processDailyHappinessDecay();
            
            Log::info('Pet happiness decay completed', [
                'processed' => $result['processed'],
                'errors' => $result['errors']
            ]);

            // Send notifications for pets needing attention
            $this->sendAttentionNotifications();

        } catch (\Exception $e) {
            Log::error('Pet happiness decay job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw $e;
        }
    }

    private function sendAttentionNotifications(): void
    {
        try {
            $notifications = $this->petService->getPetsNeedingAttentionForNotifications();
            
            foreach ($notifications as $notification) {
                $this->notificationService->sendPetAttentionNotification($notification);
            }
            
            Log::info('Pet attention notifications sent', [
                'count' => count($notifications)
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send pet attention notifications', [
                'error' => $e->getMessage()
            ]);
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Pet happiness decay job failed permanently', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
```

## Collection Progress Update Job

### UpdateCollectionProgressJob
```php
<?php
// File: api/app/Jobs/UpdateCollectionProgressJob.php

namespace App\Jobs;

use App\Models\TelegramUser;
use App\Services\CollectibleService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateCollectionProgressJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 600; // 10 minutes
    public $tries = 2;

    protected CollectibleService $collectibleService;

    public function __construct()
    {
        $this->collectibleService = app(CollectibleService::class);
    }

    public function handle(): void
    {
        Log::info('Starting collection progress update job');

        try {
            $processed = 0;
            $errors = 0;

            TelegramUser::whereHas('collectibles')
                       ->chunk(100, function($users) use (&$processed, &$errors) {
                           foreach ($users as $user) {
                               try {
                                   $this->updateUserCollectionProgress($user);
                                   $processed++;
                               } catch (\Exception $e) {
                                   $errors++;
                                   Log::error('Failed to update collection progress for user', [
                                       'user_id' => $user->id,
                                       'error' => $e->getMessage()
                                   ]);
                               }
                           }
                       });

            Log::info('Collection progress update completed', [
                'processed' => $processed,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            Log::error('Collection progress update job failed', [
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    private function updateUserCollectionProgress(TelegramUser $user): void
    {
        // This would trigger recalculation of all collection sets for the user
        $collectionSets = \App\Models\CollectionSet::active()->pluck('set_id');
        
        foreach ($collectionSets as $setId) {
            // Force update collection progress
            $this->collectibleService->updateCollectionProgress($user, $setId);
        }
    }
}
```

## Pet Evolution Check Job

### PetEvolutionCheckJob
```php
<?php
// File: api/app/Jobs/PetEvolutionCheckJob.php

namespace App\Jobs;

use App\Models\Pet;
use App\Services\PetService;
use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class PetEvolutionCheckJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300;
    public $tries = 2;

    protected PetService $petService;
    protected NotificationService $notificationService;

    public function __construct()
    {
        $this->petService = app(PetService::class);
        $this->notificationService = app(NotificationService::class);
    }

    public function handle(): void
    {
        Log::info('Starting pet evolution check job');

        try {
            $evolutionsProcessed = 0;
            $notificationsSent = 0;

            // Find pets ready for evolution
            Pet::readyForEvolution()
               ->with(['user', 'template'])
               ->chunk(50, function($pets) use (&$evolutionsProcessed, &$notificationsSent) {
                   foreach ($pets as $pet) {
                       try {
                           // Check if auto-evolution is enabled for user
                           if ($pet->user->auto_evolve_pets ?? false) {
                               $evolved = $this->petService->processPetEvolution($pet);
                               if ($evolved) {
                                   $evolutionsProcessed++;
                                   
                                   // Send evolution notification
                                   $this->notificationService->sendPetEvolutionNotification([
                                       'user_id' => $pet->telegram_user_id,
                                       'pet_id' => $pet->id,
                                       'pet_name' => $pet->display_name,
                                       'evolution_stage' => $pet->evolution_stage,
                                       'message' => "Your {$pet->display_name} has evolved! 🎉"
                                   ]);
                                   $notificationsSent++;
                               }
                           } else {
                               // Send evolution available notification
                               $this->notificationService->sendPetEvolutionAvailableNotification([
                                   'user_id' => $pet->telegram_user_id,
                                   'pet_id' => $pet->id,
                                   'pet_name' => $pet->display_name,
                                   'message' => "Your {$pet->display_name} is ready to evolve! ⭐"
                               ]);
                               $notificationsSent++;
                           }
                       } catch (\Exception $e) {
                           Log::error('Failed to process pet evolution', [
                               'pet_id' => $pet->id,
                               'error' => $e->getMessage()
                           ]);
                       }
                   }
               });

            Log::info('Pet evolution check completed', [
                'evolutions_processed' => $evolutionsProcessed,
                'notifications_sent' => $notificationsSent
            ]);

        } catch (\Exception $e) {
            Log::error('Pet evolution check job failed', [
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }
}
```

## Database Cleanup Job

### PetSystemCleanupJob
```php
<?php
// File: api/app/Jobs/PetSystemCleanupJob.php

namespace App\Jobs;

use App\Models\PetInteraction;
use App\Models\PetHappinessLog;
use App\Models\MysteryBoxOpening;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PetSystemCleanupJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 600; // 10 minutes
    public $tries = 1;

    public function handle(): void
    {
        Log::info('Starting pet system cleanup job');

        try {
            $cleanupResults = [
                'old_interactions' => $this->cleanupOldInteractions(),
                'old_happiness_logs' => $this->cleanupOldHappinessLogs(),
                'old_box_openings' => $this->cleanupOldBoxOpenings()
            ];

            Log::info('Pet system cleanup completed', $cleanupResults);

        } catch (\Exception $e) {
            Log::error('Pet system cleanup job failed', [
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    private function cleanupOldInteractions(): int
    {
        // Keep interactions for 90 days
        $cutoffDate = Carbon::now()->subDays(90);
        
        return PetInteraction::where('interaction_time', '<', $cutoffDate)->delete();
    }

    private function cleanupOldHappinessLogs(): int
    {
        // Keep happiness logs for 30 days
        $cutoffDate = Carbon::now()->subDays(30);
        
        return PetHappinessLog::where('logged_at', '<', $cutoffDate)->delete();
    }

    private function cleanupOldBoxOpenings(): int
    {
        // Keep box opening records for 180 days
        $cutoffDate = Carbon::now()->subDays(180);
        
        return MysteryBoxOpening::where('opened_at', '<', $cutoffDate)->delete();
    }
}
```

## Notification Service

### NotificationService
```php
<?php
// File: api/app/Services/NotificationService.php

namespace App\Services;

use App\Models\TelegramUser;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class NotificationService
{
    protected string $telegramBotToken;
    protected string $telegramApiUrl;

    public function __construct()
    {
        $this->telegramBotToken = config('telegram.bot_token');
        $this->telegramApiUrl = "https://api.telegram.org/bot{$this->telegramBotToken}";
    }

    /**
     * Send pet attention notification
     */
    public function sendPetAttentionNotification(array $notification): bool
    {
        try {
            $user = TelegramUser::find($notification['user_id']);
            
            if (!$user || !$user->notifications_enabled) {
                return false;
            }

            $message = $notification['message'];
            $inlineKeyboard = [
                'inline_keyboard' => [[
                    [
                        'text' => '🎮 Play with Pet',
                        'web_app' => ['url' => config('app.webapp_url')]
                    ]
                ]]
            ];

            return $this->sendTelegramMessage(
                $user->telegram_id,
                $message,
                $inlineKeyboard
            );

        } catch (\Exception $e) {
            Log::error('Failed to send pet attention notification', [
                'notification' => $notification,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send pet evolution notification
     */
    public function sendPetEvolutionNotification(array $notification): bool
    {
        try {
            $user = TelegramUser::find($notification['user_id']);
            
            if (!$user || !$user->notifications_enabled) {
                return false;
            }

            $message = $notification['message'];
            $inlineKeyboard = [
                'inline_keyboard' => [[
                    [
                        'text' => '✨ View Pet',
                        'web_app' => ['url' => config('app.webapp_url') . '/pets']
                    ]
                ]]
            ];

            return $this->sendTelegramMessage(
                $user->telegram_id,
                $message,
                $inlineKeyboard
            );

        } catch (\Exception $e) {
            Log::error('Failed to send pet evolution notification', [
                'notification' => $notification,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send pet evolution available notification
     */
    public function sendPetEvolutionAvailableNotification(array $notification): bool
    {
        try {
            $user = TelegramUser::find($notification['user_id']);
            
            if (!$user || !$user->notifications_enabled) {
                return false;
            }

            $message = $notification['message'];
            $inlineKeyboard = [
                'inline_keyboard' => [[
                    [
                        'text' => '⭐ Evolve Pet',
                        'web_app' => ['url' => config('app.webapp_url') . '/pets']
                    ]
                ]]
            ];

            return $this->sendTelegramMessage(
                $user->telegram_id,
                $message,
                $inlineKeyboard
            );

        } catch (\Exception $e) {
            Log::error('Failed to send pet evolution available notification', [
                'notification' => $notification,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    private function sendTelegramMessage(string $chatId, string $message, ?array $replyMarkup = null): bool
    {
        try {
            $payload = [
                'chat_id' => $chatId,
                'text' => $message,
                'parse_mode' => 'HTML'
            ];

            if ($replyMarkup) {
                $payload['reply_markup'] = json_encode($replyMarkup);
            }

            $response = Http::post("{$this->telegramApiUrl}/sendMessage", $payload);

            return $response->successful();

        } catch (\Exception $e) {
            Log::error('Failed to send Telegram message', [
                'chat_id' => $chatId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
}
```

## Job Scheduling Configuration

### Console Kernel Updates
```php
<?php
// File: api/app/Console/Kernel.php

protected function schedule(Schedule $schedule): void
{
    // Existing schedules...
    
    // Pet system scheduled jobs
    $schedule->job(new \App\Jobs\PetHappinessDecayJob())
             ->dailyAt('06:00')
             ->withoutOverlapping()
             ->onOneServer();

    $schedule->job(new \App\Jobs\PetEvolutionCheckJob())
             ->hourly()
             ->withoutOverlapping()
             ->onOneServer();

    $schedule->job(new \App\Jobs\UpdateCollectionProgressJob())
             ->dailyAt('02:00')
             ->withoutOverlapping()
             ->onOneServer();

    $schedule->job(new \App\Jobs\PetSystemCleanupJob())
             ->weekly()
             ->sundays()
             ->at('01:00')
             ->withoutOverlapping()
             ->onOneServer();
}
```

## Queue Configuration

### Queue Worker Setup
```bash
# File: deployment/queue-worker.conf (Supervisor configuration)

[program:battlx-queue-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/api/artisan queue:work --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/battlx-queue-worker.log
stopwaitsecs=3600
```

### Environment Configuration
```env
# File: api/.env additions

# Queue Configuration
QUEUE_CONNECTION=database
QUEUE_FAILED_DRIVER=database

# Notification Settings
TELEGRAM_BOT_TOKEN=your_bot_token_here
WEBAPP_URL=https://your-webapp-url.com

# Pet System Settings
PET_HAPPINESS_DECAY_ENABLED=true
PET_AUTO_EVOLUTION_ENABLED=false
PET_NOTIFICATIONS_ENABLED=true
```

## Testing Jobs

### Job Testing Commands
```bash
# Test individual jobs
php artisan queue:work --once
php artisan schedule:test

# Dispatch jobs manually for testing
php artisan tinker
>>> \App\Jobs\PetHappinessDecayJob::dispatch();
>>> \App\Jobs\PetEvolutionCheckJob::dispatch();
>>> \App\Jobs\UpdateCollectionProgressJob::dispatch();
>>> \App\Jobs\PetSystemCleanupJob::dispatch();
```

## Acceptance Criteria
- [ ] Daily happiness decay job running correctly
- [ ] Pet evolution check job functional
- [ ] Collection progress updates working
- [ ] Database cleanup job operational
- [ ] Notification service sending messages
- [ ] Queue workers processing jobs
- [ ] Proper error handling and logging

## Next Steps
1. Implement API routes configuration
2. Create validation rules for requests
3. Set up event system for pet actions
4. Create admin interface for job monitoring

## Troubleshooting
- Monitor queue worker status regularly
- Check job failure logs for debugging
- Ensure database connections are stable
- Verify Telegram bot token configuration
- Test notification delivery thoroughly
