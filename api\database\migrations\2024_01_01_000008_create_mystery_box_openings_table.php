<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('mystery_box_openings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->string('box_type', 50);
            
            // Purchase information
            $table->enum('purchase_method', ['coins', 'gems', 'achievement_points', 'free']);
            $table->integer('cost_paid');
            $table->string('currency_used', 20);
            
            // Rewards received
            $table->json('rewards_received'); // Array of collectibles/items received
            $table->integer('total_value'); // Estimated value of rewards
            $table->boolean('contained_rare_item')->default(false);
            
            $table->timestamp('opened_at');
            $table->timestamps();
            
            $table->index(['telegram_user_id', 'opened_at']);
            $table->index(['box_type', 'opened_at']);
            
            $table->foreign('box_type')
                  ->references('box_type')
                  ->on('mystery_box_types')
                  ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mystery_box_openings');
    }
};
