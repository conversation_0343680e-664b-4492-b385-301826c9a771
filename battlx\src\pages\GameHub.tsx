import { useParams, useNavigate } from 'react-router-dom';
import { useEffect, useState, useMemo } from 'react'; // Added useMemo
import TowerGameDrawer from '@/components/games/TowerGameDrawer';
import RabbitGameDrawer from '@/components/games/RabbitGameDrawer';
import SlashGameDrawer from '@/components/games/SlashGameDrawer';
import { GAMES } from '../games/registry';
import { GameWrapper } from '../components/games/GameWrapper';
import { useUserStore } from '../store/user-store';

/**
 * GameHub Component
 *
 * Main entry point for the game hub that:
 * - Handles game selection via URL parameters
 * - Checks game unlock requirements
 * - Configures Telegram WebApp
 * - Renders the appropriate game wrapper
 */
export function GameHub() {
    // Get gameId from URL params, default to 'tower' if not specified
    const { gameId = 'tower' } = useParams<{ gameId?: string }>();
    const navigate = useNavigate();
    const userStore = useUserStore();

    // Debug: Log current game ID and unlock status
    console.log('Current Game ID:', gameId);
    console.log('Game Unlock Status:', {
        tower: userStore.tower_game_unlocked,
        rabbit: userStore.rabbit_game_unlocked,
        slash: userStore.slash_game_unlocked
    });

    // Access Telegram WebApp
    const webApp = window.Telegram.WebApp;

    useEffect(() => {
        // Apply consistent Telegram WebApp styling
        webApp.setHeaderColor("#000");
        webApp.setBackgroundColor("#000");
        webApp.expand();

        // Check if the app supports fullscreen mode (Bot API 8.0+)
        if (webApp.isVersionAtLeast('8.0')) {
            // Request fullscreen mode for better gaming experience
            // Use type assertion to bypass TypeScript errors for newer API methods
            (webApp as any).requestFullscreen();

            // Listen for fullscreen changes
            webApp.onEvent('viewportChanged', () => {
                // Check if we're in fullscreen mode (this event works with older type definitions)
                console.log('Game viewport changed, height:', webApp.viewportHeight);
            });
        }
    }, []);

    // Get game config from registry
    const gameConfig = GAMES[gameId];

    // Handle invalid game ID
    if (!gameConfig) {
        return (
            <div className="flex h-screen items-center justify-center">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-white">Game not found</h2>
                    <button
                        className="mt-4 px-4 py-2 bg-primary rounded"
                        onClick={() => navigate('/games')}
                    >
                        Back to Games
                    </button>
                </div>
            </div>
        );
    }

    const [showUnlockDrawer, setShowUnlockDrawer] = useState(false);

    // Determine if the current game is locked based on gameId and userStore
    const isGameLocked = useMemo(() => {
        let locked;
        if (gameId === 'tower') {
            locked = !userStore.tower_game_unlocked;
        } else if (gameId === 'rabbit') {
            locked = !userStore.rabbit_game_unlocked;
        } else if (gameId === 'slash') {
            locked = !userStore.slash_game_unlocked;
        } else {
            locked = true; // Default to locked if gameId is unknown
        }
        // Debug: Log lock status computation
        console.log('Game Lock Status:', {
            gameId,
            locked,
            tower_unlocked: userStore.tower_game_unlocked,
            rabbit_unlocked: userStore.rabbit_game_unlocked,
            slash_unlocked: userStore.slash_game_unlocked
        });
        return locked;
    }, [gameId, userStore.tower_game_unlocked, userStore.rabbit_game_unlocked, userStore.slash_game_unlocked]);

    // Show unlock drawer if the current game is locked
    useEffect(() => {
        // Debug: Log drawer state changes
        console.log('Drawer State Update:', {
            gameId,
            isGameLocked,
            currentShowState: showUnlockDrawer,
            willSetTo: isGameLocked
        });

        if (isGameLocked) {
            console.log(`Opening unlock drawer for ${gameId} game`);
            setShowUnlockDrawer(true);
        } else {
            console.log(`Closing unlock drawer for ${gameId} game`);
            setShowUnlockDrawer(false); // Ensure drawer closes if game becomes unlocked
        }
    }, [isGameLocked, gameId, showUnlockDrawer]);

    // Note: The original code had a separate "Locked Screen" which might be redundant
    // if the drawer handles the unlock flow. We'll rely on the drawer for now.
    // If the game is locked, GameWrapper might redirect or handle it internally too.

    // Render the game wrapper and the appropriate unlock drawer
    return (
        <div className="game-hub fixed inset-0 bg-black">
            {/* GameWrapper should ideally handle the locked state internally or redirect */}
            <GameWrapper
                gameId={gameConfig.id}
                canvasId={gameConfig.canvasId}
            />

            {/* Conditionally render the correct unlock drawer */}
            {gameId === 'tower' ? (
                <TowerGameDrawer
                    open={showUnlockDrawer}
                    onOpenChange={setShowUnlockDrawer}
                />
            ) : gameId === 'rabbit' ? (
                <RabbitGameDrawer
                    open={showUnlockDrawer}
                    onOpenChange={setShowUnlockDrawer}
                />
            ) : gameId === 'slash' ? (
                <SlashGameDrawer
                    open={showUnlockDrawer}
                    onOpenChange={setShowUnlockDrawer}
                />
            ) : null}
        </div>
    );
}
