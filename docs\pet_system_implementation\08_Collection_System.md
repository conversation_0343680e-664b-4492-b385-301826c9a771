# Collection System Implementation

## Overview
This document covers the implementation of the collectible system, including collection management, set completion tracking, and reward distribution.

## Implementation Time: 2-3 days
## Complexity: Medium-High
## Dependencies: Pet system, mystery box system

## Collection Controller

### CollectionController
```php
<?php
// File: api/app/Http/Controllers/CollectionController.php

namespace App\Http\Controllers;

use App\Models\CollectibleTemplate;
use App\Models\CollectionSet;
use App\Models\TelegramUser;
use App\Services\CollectibleService;
use App\Services\AchievementPointService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CollectionController extends Controller
{
    protected CollectibleService $collectibleService;
    protected AchievementPointService $achievementPointService;

    public function __construct(
        CollectibleService $collectibleService,
        AchievementPointService $achievementPointService
    ) {
        $this->collectibleService = $collectibleService;
        $this->achievementPointService = $achievementPointService;
    }

    /**
     * Get user's complete collection
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $category = $request->query('category');
        $rarity = $request->query('rarity');
        $owned = $request->query('owned'); // 'true', 'false', or null for all
        
        $collectibles = $this->collectibleService->getUserCollection(
            $user,
            $category,
            $rarity,
            $owned
        );

        $collectionProgress = $user->getCollectionProgress();
        $collectionSets = $this->collectibleService->getCollectionSets($user);

        return response()->json([
            'success' => true,
            'collectibles' => $collectibles,
            'collection_progress' => $collectionProgress,
            'collection_sets' => $collectionSets,
            'filters' => [
                'categories' => ['shadow', 'undead', 'demon', 'spirit', 'beast'],
                'rarities' => ['common', 'rare', 'epic', 'legendary', 'mythic'],
                'types' => ['artifact', 'trophy', 'relic', 'essence', 'scroll']
            ]
        ]);
    }

    /**
     * Get collection sets with completion status
     */
    public function getCollectionSets(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $collectionSets = $this->collectibleService->getCollectionSetsDetailed($user);

        return response()->json([
            'success' => true,
            'collection_sets' => $collectionSets
        ]);
    }

    /**
     * Get specific collection set details
     */
    public function getCollectionSet(Request $request, string $setId): JsonResponse
    {
        $user = $request->user();
        
        $collectionSet = CollectionSet::where('set_id', $setId)
                                     ->where('is_active', true)
                                     ->firstOrFail();

        $setDetails = $this->collectibleService->getCollectionSetDetails($user, $setId);

        return response()->json([
            'success' => true,
            'collection_set' => $setDetails
        ]);
    }

    /**
     * Claim collection set completion rewards
     */
    public function claimSetRewards(Request $request, string $setId): JsonResponse
    {
        $user = $request->user();
        
        try {
            $result = $this->collectibleService->claimCollectionSetRewards($user, $setId);
            
            // Award achievement points for set completion
            $this->achievementPointService->awardPoints(
                $user->id,
                50,
                'collection_set_completion',
                $setId,
                "Completed collection set: {$result['set_name']}"
            );

            return response()->json([
                'success' => true,
                'message' => 'Collection set rewards claimed successfully!',
                'rewards' => $result['rewards'],
                'set_name' => $result['set_name'],
                'user_balance' => [
                    'balance' => $user->fresh()->balance,
                    'gems' => $user->fresh()->gems ?? 0
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Get collection statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $statistics = $this->collectibleService->getCollectionStatistics($user);

        return response()->json([
            'success' => true,
            'statistics' => $statistics
        ]);
    }

    /**
     * Get collection milestones and achievements
     */
    public function getMilestones(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $milestones = $this->collectibleService->getCollectionMilestones($user);

        return response()->json([
            'success' => true,
            'milestones' => $milestones
        ]);
    }

    /**
     * Get recently obtained collectibles
     */
    public function getRecentCollectibles(Request $request): JsonResponse
    {
        $user = $request->user();
        $limit = $request->query('limit', 10);
        
        $recentCollectibles = $user->collectibles()
                                  ->with('collectibleTemplate')
                                  ->orderBy('obtained_at', 'desc')
                                  ->limit($limit)
                                  ->get();

        return response()->json([
            'success' => true,
            'recent_collectibles' => $recentCollectibles->map(function($collectible) {
                return [
                    'id' => $collectible->collectible_id,
                    'name' => $collectible->collectibleTemplate->name,
                    'type' => $collectible->collectibleTemplate->type,
                    'rarity' => $collectible->collectibleTemplate->rarity,
                    'category' => $collectible->collectibleTemplate->category,
                    'image_url' => $collectible->collectibleTemplate->image_url,
                    'obtained_at' => $collectible->obtained_at,
                    'unlock_source' => $collectible->unlock_source,
                    'days_ago' => $collectible->obtained_at->diffInDays(now())
                ];
            })
        ]);
    }

    /**
     * Search collectibles
     */
    public function search(Request $request): JsonResponse
    {
        $user = $request->user();
        $query = $request->query('q');
        
        if (!$query || strlen($query) < 2) {
            return response()->json([
                'success' => false,
                'message' => 'Search query must be at least 2 characters'
            ], 422);
        }

        $results = $this->collectibleService->searchCollectibles($user, $query);

        return response()->json([
            'success' => true,
            'search_query' => $query,
            'results' => $results
        ]);
    }
}
```

## Collectible Service

### CollectibleService
```php
<?php
// File: api/app/Services/CollectibleService.php

namespace App\Services;

use App\Models\Collectible;
use App\Models\CollectibleTemplate;
use App\Models\CollectionSet;
use App\Models\UserCollectionProgress;
use App\Models\TelegramUser;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CollectibleService
{
    /**
     * Unlock a collectible for a user
     */
    public function unlockCollectible(
        TelegramUser $user,
        string $collectibleId,
        string $unlockSource,
        ?string $sourceReference = null
    ): ?array {
        $collectibleTemplate = CollectibleTemplate::where('collectible_id', $collectibleId)
                                                 ->where('is_active', true)
                                                 ->first();

        if (!$collectibleTemplate) {
            return null;
        }

        // Check if user already owns this collectible
        if ($collectibleTemplate->isOwnedBy($user)) {
            return null;
        }

        DB::beginTransaction();

        try {
            // Create collectible record
            $collectible = Collectible::create([
                'telegram_user_id' => $user->id,
                'collectible_id' => $collectibleId,
                'unlock_source' => $unlockSource,
                'source_reference' => $sourceReference,
                'obtained_at' => now()
            ]);

            // Update collection progress
            $this->updateCollectionProgress($user, $collectibleTemplate->collection_set_id);

            // Check for set completion
            $setCompletionResult = $this->checkSetCompletion($user, $collectibleTemplate->collection_set_id);

            DB::commit();

            Log::info('Collectible unlocked', [
                'user_id' => $user->id,
                'collectible_id' => $collectibleId,
                'unlock_source' => $unlockSource
            ]);

            return [
                'collectible' => [
                    'id' => $collectibleTemplate->collectible_id,
                    'name' => $collectibleTemplate->name,
                    'type' => $collectibleTemplate->type,
                    'rarity' => $collectibleTemplate->rarity,
                    'category' => $collectibleTemplate->category,
                    'image_url' => $collectibleTemplate->image_url,
                    'description' => $collectibleTemplate->description
                ],
                'set_completion' => $setCompletionResult,
                'obtained_at' => $collectible->obtained_at
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to unlock collectible', [
                'user_id' => $user->id,
                'collectible_id' => $collectibleId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get user's collection with filtering
     */
    public function getUserCollection(
        TelegramUser $user,
        ?string $category = null,
        ?string $rarity = null,
        ?string $owned = null
    ): array {
        $query = CollectibleTemplate::active();

        if ($category) {
            $query->byCategory($category);
        }

        if ($rarity) {
            $query->byRarity($rarity);
        }

        $collectibleTemplates = $query->orderBy('collection_set_id')
                                    ->orderBy('set_position')
                                    ->get();

        $userCollectibles = $user->collectibles()->pluck('collectible_id')->toArray();

        $collection = $collectibleTemplates->map(function($template) use ($userCollectibles, $owned) {
            $isOwned = in_array($template->collectible_id, $userCollectibles);
            
            // Apply owned filter
            if ($owned === 'true' && !$isOwned) {
                return null;
            }
            if ($owned === 'false' && $isOwned) {
                return null;
            }

            return [
                'id' => $template->collectible_id,
                'name' => $template->name,
                'type' => $template->type,
                'rarity' => $template->rarity,
                'category' => $template->category,
                'description' => $template->description,
                'image_url' => $template->image_url,
                'collection_set_id' => $template->collection_set_id,
                'set_position' => $template->set_position,
                'is_owned' => $isOwned,
                'unlock_source' => $template->unlock_source,
                'unlock_requirement' => $template->unlock_requirement
            ];
        })->filter()->values();

        return $collection->toArray();
    }

    /**
     * Get collection sets with basic completion info
     */
    public function getCollectionSets(TelegramUser $user): array
    {
        $collectionSets = CollectionSet::active()
                                      ->orderBy('sort_order')
                                      ->orderBy('category')
                                      ->get();

        return $collectionSets->map(function($set) use ($user) {
            $progress = $this->getSetProgress($user, $set->set_id);
            
            return [
                'set_id' => $set->set_id,
                'name' => $set->name,
                'category' => $set->category,
                'description' => $set->description,
                'icon_url' => $set->icon_url,
                'total_collectibles' => $set->total_collectibles,
                'owned_collectibles' => $progress['owned'],
                'completion_percentage' => $progress['percentage'],
                'is_completed' => $progress['is_completed'],
                'rewards_claimed' => $progress['rewards_claimed']
            ];
        })->toArray();
    }

    /**
     * Get detailed collection set information
     */
    public function getCollectionSetDetails(TelegramUser $user, string $setId): array
    {
        $collectionSet = CollectionSet::where('set_id', $setId)
                                     ->where('is_active', true)
                                     ->firstOrFail();

        $collectibles = CollectibleTemplate::bySet($setId)
                                          ->orderBy('set_position')
                                          ->get();

        $userCollectibles = $user->collectibles()->pluck('collectible_id')->toArray();
        $progress = $this->getSetProgress($user, $setId);

        $collectibleDetails = $collectibles->map(function($template) use ($userCollectibles) {
            $isOwned = in_array($template->collectible_id, $userCollectibles);
            
            return [
                'id' => $template->collectible_id,
                'name' => $template->name,
                'type' => $template->type,
                'rarity' => $template->rarity,
                'description' => $template->description,
                'image_url' => $template->image_url,
                'set_position' => $template->set_position,
                'is_owned' => $isOwned,
                'unlock_source' => $template->unlock_source,
                'unlock_requirement' => $template->unlock_requirement
            ];
        });

        return [
            'set_id' => $collectionSet->set_id,
            'name' => $collectionSet->name,
            'category' => $collectionSet->category,
            'description' => $collectionSet->description,
            'icon_url' => $collectionSet->icon_url,
            'total_collectibles' => $collectionSet->total_collectibles,
            'required_for_completion' => $collectionSet->required_for_completion,
            'completion_rewards' => $collectionSet->completion_rewards,
            'bonus_mystery_box_type' => $collectionSet->bonus_mystery_box_type,
            'collectibles' => $collectibleDetails,
            'progress' => $progress
        ];
    }

    /**
     * Claim collection set completion rewards
     */
    public function claimCollectionSetRewards(TelegramUser $user, string $setId): array
    {
        $collectionSet = CollectionSet::where('set_id', $setId)
                                     ->where('is_active', true)
                                     ->firstOrFail();

        $progress = UserCollectionProgress::where('telegram_user_id', $user->id)
                                         ->where('set_id', $setId)
                                         ->first();

        if (!$progress || !$progress->is_completed) {
            throw new \Exception('Collection set not completed');
        }

        if ($progress->rewards_claimed) {
            throw new \Exception('Rewards already claimed');
        }

        DB::beginTransaction();

        try {
            // Mark rewards as claimed
            $progress->update([
                'rewards_claimed' => true,
                'rewards_claimed_at' => now()
            ]);

            // Grant rewards
            $rewards = $this->grantSetCompletionRewards($user, $collectionSet);

            DB::commit();

            return [
                'set_name' => $collectionSet->name,
                'rewards' => $rewards
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get collection statistics
     */
    public function getCollectionStatistics(TelegramUser $user): array
    {
        $totalCollectibles = CollectibleTemplate::active()->count();
        $ownedCollectibles = $user->collectibles()->count();

        $collectiblesByCategory = $user->collectibles()
                                      ->join('collectible_templates', 'collectibles.collectible_id', '=', 'collectible_templates.collectible_id')
                                      ->selectRaw('collectible_templates.category, COUNT(*) as count')
                                      ->groupBy('collectible_templates.category')
                                      ->pluck('count', 'category')
                                      ->toArray();

        $collectiblesByRarity = $user->collectibles()
                                    ->join('collectible_templates', 'collectibles.collectible_id', '=', 'collectible_templates.collectible_id')
                                    ->selectRaw('collectible_templates.rarity, COUNT(*) as count')
                                    ->groupBy('collectible_templates.rarity')
                                    ->pluck('count', 'rarity')
                                    ->toArray();

        $collectiblesBySource = $user->collectibles()
                                    ->selectRaw('unlock_source, COUNT(*) as count')
                                    ->groupBy('unlock_source')
                                    ->pluck('count', 'unlock_source')
                                    ->toArray();

        $completedSets = UserCollectionProgress::where('telegram_user_id', $user->id)
                                              ->where('is_completed', true)
                                              ->count();

        $totalSets = CollectionSet::active()->count();

        return [
            'total_collectibles' => $ownedCollectibles,
            'total_possible_collectibles' => $totalCollectibles,
            'collection_percentage' => $totalCollectibles > 0 
                ? round(($ownedCollectibles / $totalCollectibles) * 100, 1) 
                : 0,
            'collectibles_by_category' => $collectiblesByCategory,
            'collectibles_by_rarity' => $collectiblesByRarity,
            'collectibles_by_source' => $collectiblesBySource,
            'completed_sets' => $completedSets,
            'total_sets' => $totalSets,
            'set_completion_percentage' => $totalSets > 0 
                ? round(($completedSets / $totalSets) * 100, 1) 
                : 0
        ];
    }

    /**
     * Search collectibles by name or description
     */
    public function searchCollectibles(TelegramUser $user, string $query): array
    {
        $collectibleTemplates = CollectibleTemplate::active()
                                                  ->where(function($q) use ($query) {
                                                      $q->where('name', 'ILIKE', "%{$query}%")
                                                        ->orWhere('description', 'ILIKE', "%{$query}%");
                                                  })
                                                  ->orderBy('name')
                                                  ->get();

        $userCollectibles = $user->collectibles()->pluck('collectible_id')->toArray();

        return $collectibleTemplates->map(function($template) use ($userCollectibles) {
            $isOwned = in_array($template->collectible_id, $userCollectibles);
            
            return [
                'id' => $template->collectible_id,
                'name' => $template->name,
                'type' => $template->type,
                'rarity' => $template->rarity,
                'category' => $template->category,
                'description' => $template->description,
                'image_url' => $template->image_url,
                'is_owned' => $isOwned
            ];
        })->toArray();
    }

    // Private helper methods

    private function updateCollectionProgress(TelegramUser $user, string $setId): void
    {
        $collectionSet = CollectionSet::where('set_id', $setId)->first();
        if (!$collectionSet) {
            return;
        }

        $ownedCollectibles = $user->collectibles()
                                 ->join('collectible_templates', 'collectibles.collectible_id', '=', 'collectible_templates.collectible_id')
                                 ->where('collectible_templates.collection_set_id', $setId)
                                 ->pluck('collectibles.collectible_id')
                                 ->toArray();

        $totalCollectibles = CollectibleTemplate::bySet($setId)->count();
        $ownedCount = count($ownedCollectibles);
        $completionPercentage = $totalCollectibles > 0 ? ($ownedCount / $totalCollectibles) * 100 : 0;
        $isCompleted = $ownedCount >= $collectionSet->required_for_completion;

        $allCollectibles = CollectibleTemplate::bySet($setId)->pluck('collectible_id')->toArray();
        $missingCollectibles = array_diff($allCollectibles, $ownedCollectibles);

        UserCollectionProgress::updateOrCreate(
            [
                'telegram_user_id' => $user->id,
                'set_id' => $setId
            ],
            [
                'collectibles_owned' => $ownedCount,
                'total_collectibles' => $totalCollectibles,
                'completion_percentage' => $completionPercentage,
                'is_completed' => $isCompleted,
                'completed_at' => $isCompleted ? now() : null,
                'owned_collectible_ids' => $ownedCollectibles,
                'missing_collectible_ids' => $missingCollectibles
            ]
        );
    }

    private function checkSetCompletion(TelegramUser $user, string $setId): ?array
    {
        $progress = UserCollectionProgress::where('telegram_user_id', $user->id)
                                         ->where('set_id', $setId)
                                         ->first();

        if (!$progress || !$progress->is_completed) {
            return null;
        }

        $collectionSet = CollectionSet::where('set_id', $setId)->first();
        
        return [
            'set_completed' => true,
            'set_name' => $collectionSet->name,
            'completion_rewards' => $collectionSet->completion_rewards,
            'bonus_mystery_box' => $collectionSet->bonus_mystery_box_type
        ];
    }

    private function getSetProgress(TelegramUser $user, string $setId): array
    {
        $progress = UserCollectionProgress::where('telegram_user_id', $user->id)
                                         ->where('set_id', $setId)
                                         ->first();

        if (!$progress) {
            return [
                'owned' => 0,
                'total' => 0,
                'percentage' => 0,
                'is_completed' => false,
                'rewards_claimed' => false
            ];
        }

        return [
            'owned' => $progress->collectibles_owned,
            'total' => $progress->total_collectibles,
            'percentage' => $progress->completion_percentage,
            'is_completed' => $progress->is_completed,
            'rewards_claimed' => $progress->rewards_claimed,
            'completed_at' => $progress->completed_at
        ];
    }

    private function grantSetCompletionRewards(TelegramUser $user, CollectionSet $collectionSet): array
    {
        $rewards = $collectionSet->completion_rewards ?? [];
        $grantedRewards = [];

        foreach ($rewards as $reward) {
            switch ($reward['type']) {
                case 'coins':
                    $user->increment('balance', $reward['amount']);
                    $grantedRewards[] = [
                        'type' => 'coins',
                        'amount' => $reward['amount']
                    ];
                    break;

                case 'gems':
                    $user->increment('gems', $reward['amount']);
                    $grantedRewards[] = [
                        'type' => 'gems',
                        'amount' => $reward['amount']
                    ];
                    break;

                case 'collectible':
                    $collectibleResult = $this->unlockCollectible(
                        $user,
                        $reward['collectible_id'],
                        'collection_bonus',
                        $collectionSet->set_id
                    );
                    if ($collectibleResult) {
                        $grantedRewards[] = [
                            'type' => 'collectible',
                            'collectible' => $collectibleResult['collectible']
                        ];
                    }
                    break;
            }
        }

        return $grantedRewards;
    }
}
```

## Acceptance Criteria
- [ ] Collection display with filtering working
- [ ] Collection set tracking functional
- [ ] Set completion rewards system operational
- [ ] Collection statistics accurate
- [ ] Search functionality working
- [ ] Progress tracking updated correctly

## Next Steps
1. Create frontend collection UI components
2. Implement collection page design
3. Add collection milestone system
4. Create admin interface for collection management

## Troubleshooting
- Ensure collection progress updates correctly
- Validate set completion logic
- Check reward distribution accuracy
- Monitor performance for large collections
