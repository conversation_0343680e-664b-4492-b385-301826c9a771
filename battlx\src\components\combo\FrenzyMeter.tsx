import React, { useEffect, useState } from 'react';
import { useComboStore } from '@/store/combo-store';

/**
 * FrenzyMeter component that displays the current frenzy meter value
 */
const FrenzyMeter: React.FC = () => {
  const { frenzyMeterValue, frenzyActive, comboActive } = useComboStore();
  const [prevMeterValue, setPrevMeterValue] = useState(0);
  const [isResetting, setIsResetting] = useState(false);

  // Track meter value changes to detect resets
  useEffect(() => {
    // If meter value decreased significantly, it's a reset
    if (prevMeterValue > 20 && frenzyMeterValue < prevMeterValue * 0.5) {
      setIsResetting(true);

      // Remove reset animation after a short delay
      const timer = setTimeout(() => {
        setIsResetting(false);
      }, 500);

      return () => clearTimeout(timer);
    }

    setPrevMeterValue(frenzyMeterValue);
  }, [frenzyMeterValue, prevMeterValue]);

  // Don't show meter during active Frenzy
  if (frenzyActive) return null;

  // Determine meter color based on state
  const getMeterColor = () => {
    if (isResetting) return '#FF4500'; // Orange-red when resetting
    if (frenzyMeterValue >= 100) return '#FFD700'; // Gold when full
    if (!comboActive) return '#666666'; // Gray when combo not active
    return '#4A154B'; // Default purple
  };

  // Determine border color based on state
  const getBorderColor = () => {
    if (isResetting) return 'rgba(255, 69, 0, 0.7)'; // Orange-red when resetting
    if (!comboActive) return 'rgba(150, 150, 150, 0.5)'; // Gray when combo not active
    return 'rgba(207, 181, 59, 0.5)'; // Default gold
  };

  return (
    <div
      className="frenzy-meter"
      style={{
        position: 'absolute',
        top: '140px', // Positioned below the Appbar
        left: '50%',
        transform: 'translateX(-50%)',
        width: '200px',
        height: '11px', // Slightly thinner
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        borderRadius: '5px',
        overflow: 'hidden',
        border: `1px solid ${getBorderColor()}`,
        zIndex: 90,
        opacity: comboActive ? 0.9 : 0.6, // More visible when combo active
        transition: 'opacity 0.3s ease, border-color 0.3s ease'
      }}
    >
      <div
        style={{
          width: `${frenzyMeterValue}%`,
          height: '100%',
          backgroundColor: getMeterColor(),
          transition: isResetting ? 'width 0.2s ease-in' : 'width 0.3s ease-out',
          boxShadow: frenzyMeterValue >= 100 ? '0 0 10px #FFD700' : 'none',
          animation: isResetting ? 'frenzy-meter-reset 0.5s ease-in' : 'none'
        }}
      />

      {/* Label that appears when combo is not active */}
      {!comboActive && frenzyMeterValue > 0 && (
        <div
          style={{
            position: 'absolute',
            top: '10px',
            left: '50%',
            transform: 'translateX(-50%)',
            color: '#FFD700',
            fontSize: '10px',
            whiteSpace: 'nowrap',
            textShadow: '0 0 2px #000',
            fontWeight: 'bold',
            opacity: 0.9
          }}
        >
          Activate Combo First!
        </div>
      )}
    </div>
  );
};

export default FrenzyMeter;
