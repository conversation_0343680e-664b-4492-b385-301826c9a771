// Enemy constants

const ENEMIES = {
    // Zombie enemies
    [EnemyType.ZOMBIE1]: [
        {
            level: 1,
            maxHp: 5,
            speed: 120,
            power: 3,
            knockback: 0.5,
            deathKB: 1,
            xp: 1.2,
            killedAmount: 0,
            spriteName: 'ghost1_enemy.png', // Temporary using ghost sprite
            aiType: 'DIRECT_CHASE',
            skills: ['HPxLevel']
        }
    ],
    [EnemyType.ZOMBIE2]: [
        {
            level: 2,
            maxHp: 8,
            speed: 100,
            power: 4,
            knockback: 0.3,
            deathKB: 1,
            xp: 1.5,
            killedAmount: 0,
            spriteName: 'ghost2_enemy.png', // Temporary using ghost sprite
            aiType: 'FLANKING',
            skills: ['HPxLevel']
        }
    ],
    // Easy enemies
    [EnemyType.GHOST1]: [
        {
            level: 1,
            maxHp: 0.1,
            speed: 140,
            power: 5,  // Reduced from 5
            knockback: 1,
            deathKB: 2,
            xp: 1,
            killedAmount: 0,
            spriteName: 'ghost_enemy.png',
            alpha: 0.5,
            aiType: 'DIRECT_CHASE'
        }
    ],
    [EnemyType.GHOST2]: [
        {
            level: 1,
            maxHp: 0.1,
            speed: 150,
            power: 4,  // Reduced from 4
            knockback: 1,
            deathKB: 2,
            xp: 1,
            killedAmount: 0,
            spriteName: 'ghost1_enemy.png',
            alpha: 0.5,
            aiType: 'SWARMING'
        }
    ],
    [EnemyType.GHOST3]: [
        {
            level: 1,
            maxHp: 0.1,
            speed: 160,
            power: 3,  // Reduced from 3
            knockback: 1,
            deathKB: 2,
            xp: 1,
            killedAmount: 0,
            spriteName: 'ghost2_enemy.png',
            alpha: 0.5,
            aiType: 'FLANKING'
        }
    ],
    [EnemyType.BAT]: [
        {
            level: 1,
            maxHp: 1,
            speed: 250,
            power: 5,  // Reduced from 5
            knockback: 0,
            deathKB: 0,
            killedAmount: 0,
            xp: 1.5,
            spriteName: 'bat_enemy.png',
            aiType: 'CIRCLING',
            canShoot: true,  // Enable shooting for BAT enemies
            skills: ['Ranged']
        }
    ],
    // Medium enemies
    [EnemyType.OGRE]: [
        {
            level: 1,
            maxHp: 20,
            speed: 130,
            power: 15,  // Reduced from 15
            knockback: 0.8,
            deathKB: 7,
            killedAmount: 0,
            xp: 1.5,
            spriteName: 'ogre_enemy.png',
            aiType: 'DIRECT_CHASE',
            skills: ['HPxLevel']
        }
    ],
    // Difficult enemies
    [EnemyType.HORN]: [
        {
            level: 10,
            maxHp: 50,
            speed: 80,
            power: 20,  // Reduced from 20
            knockback: 0,
            deathKB: 0,
            killedAmount: 0,
            xp: 3,
            spriteName: 'horns_enemy.png',
            aiType: 'AMBUSH',
            skills: ['HPxLevel']
        }
    ],
    // Boss enemies
    [EnemyType.GHOST_BOSS1]: [
        {
            level: 5,
            maxHp: 100,
            speed: 100,
            power: 15,  // Reduced from 15
            knockback: 0.5,
            deathKB: 5,
            killedAmount: 0,
            xp: 10,
            spriteName: 'ghost_boss1.png',
            scale: 1.5,
            aiType: 'TACTICAL',
            skills: ['HPxLevel']
        }
    ],

    // Swarm enemies
    [EnemyType.GHOST_SWARM]: [
        {
            level: 1,
            maxHp: 0.1,
            speed: 180,
            power: 1,
            knockback: 0,
            deathKB: 0,
            killedAmount: 0,
            xp: 0.5,
            spriteName: 'ghost_enemy.png',
            alpha: 0.5,
            aiType: 'SWARMING',
            skills: ['FixedDirection']
        }
    ],

    [EnemyType.BAT_SWARM]: [
        {
            level: 1,
            maxHp: 0.5,
            speed: 200,
            power: 1,
            knockback: 0,
            deathKB: 0,
            killedAmount: 0,
            xp: 0.5,
            spriteName: 'bat_enemy.png',
            aiType: 'CIRCLING',
            canShoot: true,  // Enable shooting for BAT_SWARM enemies
            skills: ['FixedDirection', 'Ranged']
        }
    ],

    // Special Teleporter enemy
    [EnemyType.TELEPORTER]: [
        {
            level: 2,
            maxHp: 3,           // Medium health - not too easy to kill
            speed: 180,         // Fast but not as fast as BAT
            power: 2,           // Low damage - focus is on teleportation
            knockback: 0.2,      // Low knockback
            deathKB: 1,
            killedAmount: 0,
            xp: 2,              // Good XP reward for killing
            spriteName: 'teleporter_enemy.png',
            aiType: 'TELEPORTING',  // New AI type for teleporting behavior
            alpha: 0.8,          // Slightly transparent
            skills: ['Teleport'] // Special teleport skill
        }
    ]
};

// Attach to window object for global access
window.ENEMIES = ENEMIES;
