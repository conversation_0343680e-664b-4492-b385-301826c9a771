# React Components Implementation - Part 1: Core Pet Components

## Overview
This document covers the implementation of core React components for the Pet System, including pet collection display, pet cards, and basic interaction components.

## Implementation Time: 4-5 days
## Complexity: High
## Dependencies: API integration, state management

## Pet Collection Components

### PetCollection Component
```tsx
// File: battlx/src/components/pets/PetCollection.tsx

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePetStore } from '../../stores/petStore';
import { useUserStore } from '../../stores/userStore';
import PetCard from './PetCard';
import PetFilters from './PetFilters';
import CollectionProgress from './CollectionProgress';
import LoadingSpinner from '../common/LoadingSpinner';
import ErrorMessage from '../common/ErrorMessage';

interface PetCollectionProps {
  className?: string;
}

const PetCollection: React.FC<PetCollectionProps> = ({ className = '' }) => {
  const {
    pets,
    collectionProgress,
    loading,
    error,
    fetchPets,
    setFeaturedPet
  } = usePetStore();

  const { user } = useUserStore();
  
  const [filters, setFilters] = useState({
    category: '',
    rarity: '',
    owned: 'all'
  });

  const [sortBy, setSortBy] = useState('recent');

  useEffect(() => {
    if (user) {
      fetchPets();
    }
  }, [user, fetchPets]);

  const filteredPets = pets.filter(pet => {
    if (filters.category && pet.category !== filters.category) return false;
    if (filters.rarity && pet.rarity !== filters.rarity) return false;
    if (filters.owned === 'owned' && !pet.is_owned) return false;
    if (filters.owned === 'missing' && pet.is_owned) return false;
    return true;
  });

  const sortedPets = [...filteredPets].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'rarity':
        const rarityOrder = { common: 1, rare: 2, epic: 3, legendary: 4, mythic: 5 };
        return rarityOrder[b.rarity] - rarityOrder[a.rarity];
      case 'level':
        return (b.level || 0) - (a.level || 0);
      case 'recent':
      default:
        return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
    }
  });

  const handleSetFeatured = async (petId: string) => {
    try {
      await setFeaturedPet(petId);
    } catch (error) {
      console.error('Failed to set featured pet:', error);
    }
  };

  if (loading && pets.length === 0) {
    return (
      <div className={`pet-collection ${className}`}>
        <LoadingSpinner message="Loading your pet collection..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`pet-collection ${className}`}>
        <ErrorMessage 
          message="Failed to load pet collection" 
          onRetry={() => fetchPets()}
        />
      </div>
    );
  }

  return (
    <div className={`pet-collection ${className}`}>
      {/* Collection Progress */}
      <CollectionProgress 
        progress={collectionProgress}
        className="mb-6"
      />

      {/* Filters and Sorting */}
      <div className="collection-controls mb-6">
        <PetFilters
          filters={filters}
          onFiltersChange={setFilters}
          sortBy={sortBy}
          onSortChange={setSortBy}
        />
      </div>

      {/* Pet Grid */}
      <div className="pet-grid">
        <AnimatePresence mode="popLayout">
          {sortedPets.map((pet, index) => (
            <motion.div
              key={pet.id}
              layout
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ 
                duration: 0.3,
                delay: index * 0.05
              }}
            >
              <PetCard
                pet={pet}
                onSetFeatured={handleSetFeatured}
                className="pet-card"
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {sortedPets.length === 0 && (
        <div className="empty-collection">
          <div className="empty-icon">🐾</div>
          <h3>No pets found</h3>
          <p>Try adjusting your filters or start collecting pets!</p>
        </div>
      )}
    </div>
  );
};

export default PetCollection;
```

### PetCard Component
```tsx
// File: battlx/src/components/pets/PetCard.tsx

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Pet } from '../../types/pet';
import { getRarityColor, getRarityGradient } from '../../utils/petUtils';
import PetInteractionModal from './PetInteractionModal';
import PetEvolutionIndicator from './PetEvolutionIndicator';

interface PetCardProps {
  pet: Pet;
  onSetFeatured?: (petId: string) => void;
  className?: string;
  showInteractions?: boolean;
}

const PetCard: React.FC<PetCardProps> = ({ 
  pet, 
  onSetFeatured,
  className = '',
  showInteractions = true
}) => {
  const [showModal, setShowModal] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const rarityColor = getRarityColor(pet.rarity);
  const rarityGradient = getRarityGradient(pet.rarity);

  const handleCardClick = () => {
    if (pet.is_owned && showInteractions) {
      setShowModal(true);
    }
  };

  const handleSetFeatured = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onSetFeatured && pet.is_owned) {
      onSetFeatured(pet.id);
    }
  };

  return (
    <>
      <motion.div
        className={`pet-card ${pet.is_owned ? 'owned' : 'not-owned'} ${className}`}
        onClick={handleCardClick}
        whileHover={{ scale: 1.02, y: -2 }}
        whileTap={{ scale: 0.98 }}
        style={{
          background: pet.is_owned ? rarityGradient : 'rgba(0,0,0,0.3)',
          borderColor: rarityColor,
        }}
      >
        {/* Featured Badge */}
        {pet.is_featured && (
          <div className="featured-badge">
            <span>⭐</span>
          </div>
        )}

        {/* Rarity Border */}
        <div 
          className="rarity-border"
          style={{ borderColor: rarityColor }}
        />

        {/* Pet Image */}
        <div className="pet-image-container">
          <motion.img
            src={pet.current_image || pet.image_url}
            alt={pet.name}
            className={`pet-image ${imageLoaded ? 'loaded' : ''}`}
            onLoad={() => setImageLoaded(true)}
            initial={{ opacity: 0 }}
            animate={{ opacity: imageLoaded ? 1 : 0 }}
            transition={{ duration: 0.3 }}
          />
          
          {!imageLoaded && (
            <div className="image-placeholder">
              <div className="loading-spinner" />
            </div>
          )}

          {/* Evolution Indicator */}
          {pet.is_owned && pet.can_evolve && (
            <PetEvolutionIndicator 
              evolutionStage={pet.evolution_stage}
              canEvolve={pet.can_evolve}
            />
          )}

          {/* Level Badge */}
          {pet.is_owned && (
            <div className="level-badge">
              Lv. {pet.level}
            </div>
          )}
        </div>

        {/* Pet Info */}
        <div className="pet-info">
          <h3 className="pet-name">{pet.display_name || pet.name}</h3>
          <div className="pet-category">{pet.category}</div>
          
          <div className="rarity-indicator">
            <span 
              className={`rarity-text rarity-${pet.rarity}`}
              style={{ color: rarityColor }}
            >
              {pet.rarity.toUpperCase()}
            </span>
          </div>

          {/* Happiness Bar (for owned pets) */}
          {pet.is_owned && (
            <div className="happiness-container">
              <div className="happiness-label">
                <span>😊</span>
                <span>{pet.happiness_percentage}%</span>
              </div>
              <div className="happiness-bar">
                <motion.div
                  className="happiness-fill"
                  initial={{ width: 0 }}
                  animate={{ width: `${pet.happiness_percentage}%` }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                  style={{
                    backgroundColor: pet.happiness_percentage > 70 ? '#4ade80' :
                                   pet.happiness_percentage > 30 ? '#fbbf24' : '#ef4444'
                  }}
                />
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="pet-actions">
            {pet.is_owned ? (
              <>
                {onSetFeatured && !pet.is_featured && (
                  <button
                    className="action-btn set-featured"
                    onClick={handleSetFeatured}
                  >
                    ⭐ Set Featured
                  </button>
                )}
                
                {pet.needs_attention && (
                  <div className="attention-indicator">
                    <span>⚠️ Needs Attention</span>
                  </div>
                )}
              </>
            ) : (
              <div className="not-owned-overlay">
                <span>🔒 Not Owned</span>
                {pet.unlock_requirement && (
                  <div className="unlock-hint">
                    {pet.unlock_requirement}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Interaction Indicators */}
        {pet.is_owned && showInteractions && (
          <div className="interaction-indicators">
            <div className={`interaction-icon ${pet.can_feed ? 'available' : 'cooldown'}`}>
              🍖
            </div>
            <div className={`interaction-icon ${pet.can_play ? 'available' : 'cooldown'}`}>
              🎾
            </div>
            <div className={`interaction-icon ${pet.can_pet ? 'available' : 'cooldown'}`}>
              ❤️
            </div>
          </div>
        )}
      </motion.div>

      {/* Interaction Modal */}
      {showModal && pet.is_owned && (
        <PetInteractionModal
          pet={pet}
          isOpen={showModal}
          onClose={() => setShowModal(false)}
        />
      )}
    </>
  );
};

export default PetCard;
```

### PetFilters Component
```tsx
// File: battlx/src/components/pets/PetFilters.tsx

import React from 'react';
import { motion } from 'framer-motion';

interface PetFiltersProps {
  filters: {
    category: string;
    rarity: string;
    owned: string;
  };
  onFiltersChange: (filters: any) => void;
  sortBy: string;
  onSortChange: (sortBy: string) => void;
}

const PetFilters: React.FC<PetFiltersProps> = ({
  filters,
  onFiltersChange,
  sortBy,
  onSortChange
}) => {
  const categories = [
    { value: '', label: 'All Categories' },
    { value: 'shadow', label: 'Shadow' },
    { value: 'undead', label: 'Undead' },
    { value: 'demon', label: 'Demon' },
    { value: 'spirit', label: 'Spirit' },
    { value: 'beast', label: 'Beast' }
  ];

  const rarities = [
    { value: '', label: 'All Rarities' },
    { value: 'common', label: 'Common' },
    { value: 'rare', label: 'Rare' },
    { value: 'epic', label: 'Epic' },
    { value: 'legendary', label: 'Legendary' },
    { value: 'mythic', label: 'Mythic' }
  ];

  const ownedOptions = [
    { value: 'all', label: 'All Pets' },
    { value: 'owned', label: 'Owned Only' },
    { value: 'missing', label: 'Missing Only' }
  ];

  const sortOptions = [
    { value: 'recent', label: 'Recently Added' },
    { value: 'name', label: 'Name' },
    { value: 'rarity', label: 'Rarity' },
    { value: 'level', label: 'Level' }
  ];

  const handleFilterChange = (key: string, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  return (
    <motion.div
      className="pet-filters"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="filters-row">
        {/* Category Filter */}
        <div className="filter-group">
          <label>Category</label>
          <select
            value={filters.category}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className="filter-select"
          >
            {categories.map(category => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
        </div>

        {/* Rarity Filter */}
        <div className="filter-group">
          <label>Rarity</label>
          <select
            value={filters.rarity}
            onChange={(e) => handleFilterChange('rarity', e.target.value)}
            className="filter-select"
          >
            {rarities.map(rarity => (
              <option key={rarity.value} value={rarity.value}>
                {rarity.label}
              </option>
            ))}
          </select>
        </div>

        {/* Owned Filter */}
        <div className="filter-group">
          <label>Status</label>
          <select
            value={filters.owned}
            onChange={(e) => handleFilterChange('owned', e.target.value)}
            className="filter-select"
          >
            {ownedOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Sort Options */}
        <div className="filter-group">
          <label>Sort By</label>
          <select
            value={sortBy}
            onChange={(e) => onSortChange(e.target.value)}
            className="filter-select"
          >
            {sortOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Quick Filters */}
      <div className="quick-filters">
        <button
          className={`quick-filter ${filters.owned === 'owned' ? 'active' : ''}`}
          onClick={() => handleFilterChange('owned', filters.owned === 'owned' ? 'all' : 'owned')}
        >
          My Pets
        </button>
        
        <button
          className={`quick-filter ${filters.rarity === 'legendary' || filters.rarity === 'mythic' ? 'active' : ''}`}
          onClick={() => handleFilterChange('rarity', 
            filters.rarity === 'legendary' || filters.rarity === 'mythic' ? '' : 'legendary'
          )}
        >
          Rare Pets
        </button>
        
        <button
          className="quick-filter clear-filters"
          onClick={() => onFiltersChange({ category: '', rarity: '', owned: 'all' })}
        >
          Clear All
        </button>
      </div>
    </motion.div>
  );
};

export default PetFilters;
```

### CollectionProgress Component
```tsx
// File: battlx/src/components/pets/CollectionProgress.tsx

import React from 'react';
import { motion } from 'framer-motion';
import { CollectionProgress as CollectionProgressType } from '../../types/pet';

interface CollectionProgressProps {
  progress: CollectionProgressType;
  className?: string;
}

const CollectionProgress: React.FC<CollectionProgressProps> = ({ 
  progress, 
  className = '' 
}) => {
  if (!progress) return null;

  const { pets, collectibles, overall_percentage } = progress;

  return (
    <motion.div
      className={`collection-progress ${className}`}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="progress-header">
        <h3>Collection Progress</h3>
        <div className="overall-percentage">
          {overall_percentage.toFixed(1)}% Complete
        </div>
      </div>

      <div className="progress-sections">
        {/* Pets Progress */}
        <div className="progress-section">
          <div className="section-header">
            <span className="section-icon">🐾</span>
            <span className="section-title">Pets</span>
            <span className="section-count">{pets.owned}/{pets.total}</span>
          </div>
          
          <div className="progress-bar">
            <motion.div
              className="progress-fill pets"
              initial={{ width: 0 }}
              animate={{ width: `${pets.percentage}%` }}
              transition={{ duration: 0.8, delay: 0.2 }}
            />
            <span className="progress-text">{pets.percentage.toFixed(1)}%</span>
          </div>
        </div>

        {/* Collectibles Progress */}
        <div className="progress-section">
          <div className="section-header">
            <span className="section-icon">💎</span>
            <span className="section-title">Collectibles</span>
            <span className="section-count">{collectibles.owned}/{collectibles.total}</span>
          </div>
          
          <div className="progress-bar">
            <motion.div
              className="progress-fill collectibles"
              initial={{ width: 0 }}
              animate={{ width: `${collectibles.percentage}%` }}
              transition={{ duration: 0.8, delay: 0.4 }}
            />
            <span className="progress-text">{collectibles.percentage.toFixed(1)}%</span>
          </div>
        </div>
      </div>

      {/* Overall Progress */}
      <div className="overall-progress">
        <div className="overall-bar">
          <motion.div
            className="overall-fill"
            initial={{ width: 0 }}
            animate={{ width: `${overall_percentage}%` }}
            transition={{ duration: 1, delay: 0.6 }}
          />
        </div>
      </div>

      {/* Milestones */}
      <div className="milestones">
        {[25, 50, 75, 100].map(milestone => (
          <div
            key={milestone}
            className={`milestone ${overall_percentage >= milestone ? 'achieved' : ''}`}
            style={{ left: `${milestone}%` }}
          >
            <div className="milestone-marker" />
            <div className="milestone-label">{milestone}%</div>
          </div>
        ))}
      </div>
    </motion.div>
  );
};

export default CollectionProgress;
```

## Acceptance Criteria
- [ ] Pet collection displays correctly with filtering
- [ ] Pet cards show proper information and interactions
- [ ] Collection progress tracking functional
- [ ] Animations smooth and performant
- [ ] Responsive design for mobile devices
- [ ] Error handling and loading states
- [ ] Accessibility features implemented

## Next Steps
1. Continue with Part 2: Pet Interaction Components
2. Implement pet shop components
3. Create mystery box components
4. Add collection page components

## Troubleshooting
- Ensure proper TypeScript types are defined
- Test animations on lower-end devices
- Verify image loading and fallbacks
- Check responsive breakpoints
- Test touch interactions on mobile
