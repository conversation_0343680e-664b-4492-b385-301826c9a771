/**
 * Utility functions
 */
const Utils = {
    /**
     * Get a random number between min and max
     * @param {number} min - Minimum value
     * @param {number} max - Maximum value
     * @returns {number} - Random number
     */
    random(min, max) {
        return Math.random() * (max - min) + min;
    },
    
    /**
     * Get a random integer between min and max
     * @param {number} min - Minimum value
     * @param {number} max - Maximum value
     * @returns {number} - Random integer
     */
    randomInt(min, max) {
        return Math.floor(this.random(min, max + 1));
    },
    
    /**
     * Check if two rectangles are colliding
     * @param {Object} rect1 - First rectangle
     * @param {number} rect1.x - X position
     * @param {number} rect1.y - Y position
     * @param {number} rect1.width - Width
     * @param {number} rect1.height - Height
     * @param {Object} rect2 - Second rectangle
     * @param {number} rect2.x - X position
     * @param {number} rect2.y - Y position
     * @param {number} rect2.width - Width
     * @param {number} rect2.height - Height
     * @returns {boolean} - True if colliding
     */
    checkCollision(rect1, rect2) {
        return (
            rect1.x < rect2.x + rect2.width &&
            rect1.x + rect1.width > rect2.x &&
            rect1.y < rect2.y + rect2.height &&
            rect1.y + rect1.height > rect2.y
        );
    },
    
    /**
     * Get the best score from local storage
     * @returns {number} - Best score
     */
    getBestScore() {
        const bestScore = localStorage.getItem(CONSTANTS.STORAGE.BEST_SCORE);
        return bestScore ? parseInt(bestScore, 10) : 0;
    },
    
    /**
     * Set the best score in local storage
     * @param {number} score - Score to save
     */
    setBestScore(score) {
        const bestScore = this.getBestScore();
        if (score > bestScore) {
            localStorage.setItem(CONSTANTS.STORAGE.BEST_SCORE, score.toString());
        }
    },
    
    /**
     * Draw text with a stroke
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {string} text - Text to draw
     * @param {number} x - X position
     * @param {number} y - Y position
     * @param {Object} options - Text options
     * @param {string} options.fillStyle - Fill style
     * @param {string} options.strokeStyle - Stroke style
     * @param {string} options.font - Font
     * @param {string} options.textAlign - Text alignment
     * @param {number} options.lineWidth - Line width
     */
    drawText(ctx, text, x, y, options = {}) {
        ctx.save();
        ctx.fillStyle = options.fillStyle || 'white';
        ctx.strokeStyle = options.strokeStyle || 'black';
        ctx.font = options.font || '30px Arial';
        ctx.textAlign = options.textAlign || 'center';
        ctx.lineWidth = options.lineWidth || 3;
        
        ctx.strokeText(text, x, y);
        ctx.fillText(text, x, y);
        ctx.restore();
    }
};
