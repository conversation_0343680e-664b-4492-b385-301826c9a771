# Pet Buy & Upgrade System with Prize Tree

## Core Pet System

### Pet Categories & Rarity
```typescript
interface Pet {
  id: string;
  name: string;
  category: 'shadow' | 'undead' | 'demon' | 'spirit' | 'beast';
  rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  level: number;
  experience: number;
  mysteryBoxUnlocks: MysteryBoxUnlock[];
  collectibleUnlocks: CollectibleUnlock[];
  unlockRequirement: UnlockRequirement;
}

interface MysteryBoxUnlock {
  boxType: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  prizeTreeLevel: number;
  isUnlocked: boolean;
}

interface CollectibleUnlock {
  collectibleId: string;
  collectibleName: string;
  collectibleType: 'artifact' | 'trophy' | 'relic' | 'essence' | 'scroll';
  rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  prizeTreeLevel: number;
  isUnlocked: boolean;
}
```

### Pet Collection Structure
**Shadow Pets:**
- **<PERSON> Wolf** (Common) - Unlocks Common Mystery Box + Shadow Essence collectible
- **Dark Raven** (Rare) - Unlocks Rare Mystery Box + Raven Feather artifact
- **Void Stalker** (Epic) - Unlocks Epic Mystery Box + Void Crystal relic
- **Shadow Lord** (Legendary) - Unlocks Legendary Mystery Box + Shadow Crown trophy
- **Nightmare King** (Mythic) - Unlocks Mythic Mystery Box + Nightmare Scroll

**Undead Pets:**
- **Skeleton Minion** (Common) - Unlocks Common Mystery Box + Bone Fragment essence
- **Zombie Hound** (Rare) - Unlocks Rare Mystery Box + Decay Potion artifact
- **Lich Apprentice** (Epic) - Unlocks Epic Mystery Box + Necromancy Tome relic
- **Death Knight** (Legendary) - Unlocks Legendary Mystery Box + Death's Blade trophy
- **Bone Dragon** (Mythic) - Unlocks Mythic Mystery Box + Dragon Soul Scroll

**Demon Pets:**
- **Imp Familiar** (Common) - Unlocks Common Mystery Box + Sulfur Essence
- **Hell Hound** (Rare) - Unlocks Rare Mystery Box + Hellfire Collar artifact
- **Succubus** (Epic) - Unlocks Epic Mystery Box + Charm Amulet relic
- **Demon Lord** (Legendary) - Unlocks Legendary Mystery Box + Infernal Throne trophy
- **Archfiend** (Mythic) - Unlocks Mythic Mystery Box + Apocalypse Scroll

**Spirit Pets:**
- **Wisp** (Common) - Unlocks Common Mystery Box + Spirit Light essence
- **Banshee** (Rare) - Unlocks Rare Mystery Box + Wailing Veil artifact
- **Phantom** (Epic) - Unlocks Epic Mystery Box + Ethereal Chain relic
- **Wraith Lord** (Legendary) - Unlocks Legendary Mystery Box + Spectral Scepter trophy
- **Ancient Spirit** (Mythic) - Unlocks Mythic Mystery Box + Eternity Scroll

**Beast Pets:**
- **Shadow Bat** (Common) - Unlocks Common Mystery Box + Night Wing essence
- **Dire Wolf** (Rare) - Unlocks Rare Mystery Box + Alpha Fang artifact
- **Chimera** (Epic) - Unlocks Epic Mystery Box + Triple Heart relic
- **Hydra** (Legendary) - Unlocks Legendary Mystery Box + Regeneration Orb trophy
- **Leviathan** (Mythic) - Unlocks Mythic Mystery Box + Ocean's Depth Scroll

## Pet Purchase System

### Purchase Methods
```typescript
interface PetPurchase {
  method: 'coins' | 'gems' | 'prize_tree' | 'mystery_box' | 'breeding';
  cost: number;
  currency: string;
  availability: 'always' | 'limited' | 'event' | 'unlock_required';
}

const petPurchaseMethods = {
  coins: {
    common: 1000,
    rare: 5000,
    epic: 25000,
    legendary: 100000,
    mythic: 500000
  },
  gems: {
    common: 10,
    rare: 50,
    epic: 200,
    legendary: 800,
    mythic: 3000
  },
  prizeTree: {
    // Unlocked through prize tree progression
    unlockNodes: [5, 15, 30, 50, 75] // Tree levels for each rarity
  }
};
```

### Pet Shop Interface
**Shop Categories:**
- **Available Now** - Pets you can buy immediately
- **Locked** - Pets requiring prize tree progression
- **Limited Time** - Special event pets
- **Breeding Only** - Pets only obtainable through breeding

**Purchase Flow:**
1. Select pet from shop
2. Confirm purchase method (coins/gems)
3. Purchase animation with pet reveal
4. Pet automatically added to collection
5. Notification of new abilities unlocked

## Pet Upgrade System

### Upgrade Mechanics
```typescript
interface PetUpgrade {
  currentLevel: number;
  maxLevel: number;
  experienceRequired: number;
  upgradeCost: UpgradeCost;
  statBonus: StatBonus;
  abilityUnlocks: AbilityUnlock[];
}

interface UpgradeCost {
  coins: number;
  materials: MaterialRequirement[];
  time: number; // seconds
}

class PetUpgradeCalculator {
  calculateUpgradeCost(pet: Pet, targetLevel: number): UpgradeCost {
    const baseCost = pet.rarity === 'common' ? 100 : 
                    pet.rarity === 'rare' ? 500 :
                    pet.rarity === 'epic' ? 2000 :
                    pet.rarity === 'legendary' ? 10000 : 50000;
    
    const levelMultiplier = Math.pow(1.5, targetLevel - 1);
    const rarityMultiplier = this.getRarityMultiplier(pet.rarity);
    
    return {
      coins: Math.floor(baseCost * levelMultiplier * rarityMultiplier),
      materials: this.calculateMaterialRequirements(pet, targetLevel),
      time: this.calculateUpgradeTime(pet, targetLevel)
    };
  }
}
```

### Upgrade Materials
**Material Types:**
- **Dark Essence** - Common upgrade material from daily activities
- **Soul Fragments** - Rare material from mystery boxes
- **Ancient Runes** - Epic material from guild activities
- **Divine Crystals** - Legendary material from special events
- **Void Shards** - Mythic material from prize tree milestones

### Pet Evolution System
**Evolution Paths:**
- **Level 10** - First evolution (visual upgrade + stat boost)
- **Level 25** - Second evolution (new ability unlock)
- **Level 50** - Final evolution (ultimate form + special effects)
- **Level 100** - Transcendence (prestige system with permanent bonuses)

## Prize Tree Integration

### Prize Tree Structure
```typescript
interface PrizeTreeNode {
  id: number;
  level: number;
  cost: number;
  currency: 'coins' | 'gems' | 'points';
  reward: PrizeReward;
  requirements: NodeRequirement[];
  isUnlocked: boolean;
  isClaimed: boolean;
}

interface PrizeReward {
  type: 'pet_unlock' | 'mystery_box_unlock' | 'collectible_unlock' | 'currency';
  petId?: string;
  mysteryBoxType?: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  collectibleId?: string;
  quantity?: number;
}
```

### Pet Mystery Box Unlock Section
**Separate Prize Tree Branch for Mystery Boxes:**
- **Level 5** - Shadow Wolf unlocks Common Mystery Box access
- **Level 10** - Dark Raven unlocks Rare Mystery Box access
- **Level 20** - Void Stalker unlocks Epic Mystery Box access
- **Level 35** - Shadow Lord unlocks Legendary Mystery Box access
- **Level 50** - Nightmare King unlocks Mythic Mystery Box access

**Each Pet Category Unlocks Different Box Types:**
- **Shadow Pets** → Shadow-themed mystery boxes with dark collectibles
- **Undead Pets** → Necromancy-themed boxes with bone/decay items
- **Demon Pets** → Infernal boxes with fire/brimstone collectibles
- **Spirit Pets** → Ethereal boxes with ghostly artifacts
- **Beast Pets** → Primal boxes with nature/creature essences

### Collectible Unlock System
**Prize Tree Collectible Rewards:**
- **Pet Purchase** - Automatically unlocks associated collectible
- **Mystery Box Opening** - Chance to find rare collectibles
- **Prize Tree Milestones** - Direct collectible rewards at major levels
- **Collection Completion** - Bonus collectibles for completing sets

## Collectible System

### Collectible Categories
```typescript
interface Collectible {
  id: string;
  name: string;
  type: 'artifact' | 'trophy' | 'relic' | 'essence' | 'scroll';
  rarity: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic';
  category: 'shadow' | 'undead' | 'demon' | 'spirit' | 'beast';
  description: string;
  unlockSource: 'pet_purchase' | 'mystery_box' | 'prize_tree' | 'collection_bonus';
  displayImage: string;
  collectionSet: string;
}

const collectibleTypes = {
  essence: "Basic magical materials and components",
  artifact: "Enchanted items with mysterious properties",
  relic: "Ancient objects of great power",
  trophy: "Prestigious awards and ceremonial items",
  scroll: "Legendary documents and forbidden knowledge"
};
```

### Collectible Sets
**Shadow Collection:**
- Shadow Essence (Common) → Raven Feather (Rare) → Void Crystal (Epic) → Shadow Crown (Legendary) → Nightmare Scroll (Mythic)
- **Set Bonus**: Unlock exclusive Shadow Mystery Box with unique collectibles

**Undead Collection:**
- Bone Fragment (Common) → Decay Potion (Rare) → Necromancy Tome (Epic) → Death's Blade (Legendary) → Dragon Soul Scroll (Mythic)
- **Set Bonus**: Unlock exclusive Undead Mystery Box with unique collectibles

**Demon Collection:**
- Sulfur Essence (Common) → Hellfire Collar (Rare) → Charm Amulet (Epic) → Infernal Throne (Legendary) → Apocalypse Scroll (Mythic)
- **Set Bonus**: Unlock exclusive Demon Mystery Box with unique collectibles

### /collectible Page Design
**Page Layout:**
- **Collection Grid** - Visual display of all collectibles with rarity borders
- **Category Tabs** - Filter by Shadow, Undead, Demon, Spirit, Beast
- **Rarity Filter** - Show only specific rarity levels
- **Set Progress** - Progress bars for each collection set
- **Search Function** - Find specific collectibles by name
- **Sort Options** - By rarity, acquisition date, or alphabetical

## Home Screen Integration

### Pet Showcase Widget
```typescript
interface PetShowcaseWidget {
  featuredPet: Pet | null;
  displayMode: 'favorite' | 'newest' | 'rarest' | 'random';
  interactionState: PetInteractionState;
  lastInteraction: Date;
  nextInteractionAvailable: Date;
}

interface PetInteractionState {
  happiness: number;        // 0-100
  lastFed: Date;
  lastPlayed: Date;
  dailyInteractionCount: number;
  maxDailyInteractions: number;
  bonusMultiplier: number;
}
```

**Widget Features:**
- **Mini Pet Display** - 3D animated pet model (64x64px)
- **Pet Name & Rarity** - Text display with rarity color coding
- **Interaction Button** - "Feed", "Play", or "Pet" action
- **Happiness Meter** - Visual bar showing pet mood (0-100%)
- **Daily Progress** - Shows interactions completed today (3/5)
- **Quick Access** - Tap pet to open full pet collection

### Collection Progress Bar
```typescript
interface CollectionProgress {
  totalPets: number;
  ownedPets: number;
  totalCollectibles: number;
  ownedCollectibles: number;
  completedSets: number;
  totalSets: number;
  overallProgress: number;
  nextMilestone: ProgressMilestone;
}

interface ProgressMilestone {
  target: number;
  reward: string;
  description: string;
  progress: number;
}
```

**Progress Bar Features:**
- **Overall Collection %** - Combined pets + collectibles completion
- **Visual Progress Bar** - Animated fill with gradient colors
- **Next Milestone** - "5 more pets to unlock Rare Mystery Box"
- **Quick Stats** - "12/25 Pets | 8/50 Collectibles | 2/5 Sets"
- **Tap to Expand** - Shows detailed breakdown by category

### Daily Pet Interaction System
```typescript
interface DailyInteraction {
  type: 'feed' | 'play' | 'pet' | 'train';
  energyCost: number;
  happinessGain: number;
  experienceGain: number;
  cooldownMinutes: number;
  dailyLimit: number;
  rewards: InteractionReward[];
}

interface InteractionReward {
  type: 'coins' | 'experience' | 'materials' | 'collectible_chance';
  amount: number;
  probability: number;
  bonusMultiplier: number;
}

const dailyInteractions = {
  feed: {
    type: 'feed',
    energyCost: 5,
    happinessGain: 20,
    experienceGain: 10,
    cooldownMinutes: 60,
    dailyLimit: 5,
    rewards: [
      { type: 'coins', amount: 50, probability: 100, bonusMultiplier: 1.0 },
      { type: 'materials', amount: 1, probability: 30, bonusMultiplier: 1.0 }
    ]
  },
  play: {
    type: 'play',
    energyCost: 10,
    happinessGain: 30,
    experienceGain: 15,
    cooldownMinutes: 120,
    dailyLimit: 3,
    rewards: [
      { type: 'coins', amount: 100, probability: 100, bonusMultiplier: 1.0 },
      { type: 'experience', amount: 25, probability: 50, bonusMultiplier: 1.0 },
      { type: 'collectible_chance', amount: 1, probability: 10, bonusMultiplier: 1.0 }
    ]
  },
  pet: {
    type: 'pet',
    energyCost: 2,
    happinessGain: 10,
    experienceGain: 5,
    cooldownMinutes: 30,
    dailyLimit: 10,
    rewards: [
      { type: 'coins', amount: 25, probability: 100, bonusMultiplier: 1.0 }
    ]
  }
};
```

## User Interface Design

### Home Screen Widget Layout
```typescript
interface HomeScreenLayout {
  petShowcaseWidget: PetShowcaseWidget;
  collectionProgressBar: CollectionProgressBar;
  dailyInteractionPanel: DailyInteractionPanel;
  existingElements: ExistingHomeElements;
}

interface PetShowcaseWidget {
  position: 'top-right' | 'below-balance' | 'above-tap-area';
  size: { width: 120, height: 80 };
  petDisplay: MiniPetDisplay;
  interactionButton: InteractionButton;
  happinessIndicator: HappinessBar;
}

interface CollectionProgressBar {
  position: 'below-pet-widget' | 'top-banner';
  size: { width: '90%', height: 24 };
  progressFill: ProgressFill;
  milestoneIndicator: MilestoneMarker;
  quickStats: QuickStatsDisplay;
}
```

**Home Screen Integration:**
- **Pet Widget Placement** - Small corner widget that doesn't interfere with main gameplay
- **Progress Bar Position** - Horizontal bar showing collection completion
- **Interaction Notifications** - Subtle alerts when pets need attention
- **Quick Access Buttons** - Fast navigation to pet collection and collectibles

### Pet Collection Screen
```typescript
interface PetCollectionUI {
  petGrid: PetCard[];
  filterOptions: FilterOption[];
  sortOptions: SortOption[];
  selectedPet: Pet | null;
  interactionPanel: InteractionPanel;
  mysteryBoxPreview: MysteryBoxPreview;
}

interface PetCard {
  pet: Pet;
  happinessLevel: number;
  lastInteraction: Date;
  mysteryBoxUnlocks: MysteryBoxUnlock[];
  collectibleUnlocks: CollectibleUnlock[];
  interactionButton: InteractionButton;
}
```

**UI Elements:**
- **Pet Cards** - Visual representation with rarity borders and happiness indicators
- **Interaction Buttons** - Feed, Play, Pet actions with cooldown timers
- **Mystery Box Preview** - Shows which boxes this pet unlocks
- **Collectible Preview** - Shows which collectibles this pet provides
- **Happiness Meters** - Visual bars showing pet mood and interaction needs

### Pet Shop Interface
**Shop Layout:**
- **Featured Pets** - Rotating selection of special pets
- **Category Tabs** - Shadow, Undead, Demon, Spirit, Beast
- **Rarity Filters** - Filter by rarity level
- **Purchase Buttons** - Clear pricing in coins/gems
- **Preview System** - 3D model viewer for pets

### Upgrade Interface
**Upgrade Panel:**
- **Current Stats** - Before upgrade values
- **Projected Stats** - After upgrade values with green arrows
- **Material Requirements** - Visual material icons with quantities
- **Upgrade Timer** - Countdown for upgrade completion
- **Instant Complete** - Gem option to skip timer

## Monetization Integration

### Premium Features
**Gem Purchases:**
- **Instant Upgrades** - Skip upgrade timers
- **Premium Pets** - Exclusive gem-only pets
- **Material Bundles** - Bulk upgrade materials
- **Pet Slots** - Expand pet collection capacity
- **Breeding Boosts** - Increase breeding success rates

### Prize Tree Monetization
**Tree Progression:**
- **Free Path** - Basic progression with coins
- **Premium Path** - Gem purchases for faster progression
- **Milestone Rewards** - Special pets at major milestones
- **Season Passes** - Limited-time premium tree content

## Technical Implementation

### Database Schema
```sql
CREATE TABLE pets (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    pet_type VARCHAR(50) NOT NULL,
    category ENUM('shadow', 'undead', 'demon', 'spirit', 'beast'),
    rarity ENUM('common', 'rare', 'epic', 'legendary', 'mythic'),
    level INT DEFAULT 1,
    experience INT DEFAULT 0,
    happiness INT DEFAULT 50,
    last_fed TIMESTAMP NULL,
    last_played TIMESTAMP NULL,
    last_petted TIMESTAMP NULL,
    daily_interaction_count INT DEFAULT 0,
    daily_interaction_reset TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_featured BOOLEAN DEFAULT FALSE,
    mystery_box_unlocks JSON,
    collectible_unlocks JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE collectibles (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    collectible_id VARCHAR(50) NOT NULL,
    collectible_name VARCHAR(100) NOT NULL,
    collectible_type ENUM('artifact', 'trophy', 'relic', 'essence', 'scroll'),
    category ENUM('shadow', 'undead', 'demon', 'spirit', 'beast'),
    rarity ENUM('common', 'rare', 'epic', 'legendary', 'mythic'),
    unlock_source ENUM('pet_purchase', 'mystery_box', 'prize_tree', 'collection_bonus'),
    obtained_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_collectible (user_id, collectible_id)
);

CREATE TABLE mystery_box_unlocks (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    box_type ENUM('common', 'rare', 'epic', 'legendary', 'mythic'),
    category ENUM('shadow', 'undead', 'demon', 'spirit', 'beast'),
    unlocked_by_pet VARCHAR(36),
    prize_tree_level INT,
    unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (unlocked_by_pet) REFERENCES pets(id)
);

CREATE TABLE prize_tree_progress (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    current_level INT DEFAULT 0,
    total_points INT DEFAULT 0,
    claimed_rewards JSON,
    mystery_box_unlocks JSON,
    collectible_unlocks JSON,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### API Endpoints
```typescript
// Pet Management
GET /api/pets/collection/{userId}
POST /api/pets/purchase
PUT /api/pets/{petId}/upgrade
GET /api/pets/{petId}/mystery-box-unlocks
POST /api/pets/{petId}/interact
GET /api/pets/featured/{userId}
PUT /api/pets/{petId}/set-featured

// Daily Interactions
POST /api/pets/{petId}/feed
POST /api/pets/{petId}/play
POST /api/pets/{petId}/pet
GET /api/pets/{petId}/interaction-status
GET /api/pets/daily-summary/{userId}

// Home Screen Widgets
GET /api/home/<USER>/{userId}
GET /api/home/<USER>/{userId}
GET /api/home/<USER>/{userId}

// Collectibles
GET /api/collectibles/{userId}
GET /api/collectibles/{userId}/category/{category}
POST /api/collectibles/unlock
GET /api/collectibles/sets/{userId}

// Mystery Box Unlocks
GET /api/mystery-boxes/available/{userId}
POST /api/mystery-boxes/unlock/{boxType}
GET /api/mystery-boxes/unlocked/{userId}

// Prize Tree
GET /api/prize-tree/{userId}
POST /api/prize-tree/claim-reward
PUT /api/prize-tree/unlock-node
GET /api/prize-tree/mystery-box-section/{userId}

// Pet Shop
GET /api/shop/pets
GET /api/shop/pets/{category}
POST /api/shop/purchase/{petId}
```

## Collectible Page Implementation

### Frontend Route Structure
```typescript
// Add to router.tsx
{
  path: "collectible",
  element: <CollectiblePage />,
}

// CollectiblePage Component
interface CollectiblePageState {
  collectibles: Collectible[];
  selectedCategory: string;
  selectedRarity: string;
  searchTerm: string;
  sortBy: 'name' | 'rarity' | 'date';
  collectionSets: CollectionSet[];
}
```

### Collectible Display System
```typescript
interface CollectibleCard {
  collectible: Collectible;
  isOwned: boolean;
  unlockProgress?: number;
  setProgress?: SetProgress;
  displayEffects: VisualEffect[];
}

interface SetProgress {
  setName: string;
  ownedCount: number;
  totalCount: number;
  completionBonus: CollectionBonus;
  isCompleted: boolean;
}
```

## Engagement Mechanics

### Daily Interaction Benefits
**Interaction Rewards:**
- **Feed Pet** - 50 coins + 30% chance for materials
- **Play with Pet** - 100 coins + 50% chance for XP + 10% chance for collectible
- **Pet the Pet** - 25 coins + happiness boost
- **Happy Pet Bonus** - Pets with 80%+ happiness give 2x rewards

**Interaction Limits:**
- **Feed** - 5 times per day, 1-hour cooldown
- **Play** - 3 times per day, 2-hour cooldown
- **Pet** - 10 times per day, 30-minute cooldown
- **Happiness Decay** - Decreases 5 points per day without interaction

### Home Screen Engagement
**Pet Showcase Widget:**
- **Attention Alerts** - Red notification when pet happiness below 30%
- **Interaction Ready** - Green glow when interaction available
- **Milestone Celebrations** - Special animations for collection progress
- **Quick Rewards** - Tap widget for instant interaction without leaving home

**Collection Progress Bar:**
- **Animated Milestones** - Progress bar fills with particle effects
- **Next Goal Preview** - "3 more pets to unlock Epic Mystery Box"
- **Achievement Notifications** - Pop-up celebrations for completed sets
- **Social Sharing** - Share collection milestones to friends

### Retention Hooks
**Daily Engagement:**
- **Pet Needs Notifications** - Push notifications when pets need attention
- **Daily Interaction Streaks** - Bonus rewards for consecutive days
- **Collection Milestones** - Regular goals to maintain engagement
- **Mystery Box Unlocks** - New content unlocked through pet ownership

**Social Features:**
- **Pet Showcase** - Display favorite pet on profile
- **Collection Leaderboards** - Compete with friends for completion %
- **Rare Pet Bragging** - Show off mythic pets in social feeds
- **Gift System** - Send pet food/toys to friends

This enhanced pet system creates a **collection-focused experience** with **daily engagement mechanics** where pets serve as **keys to unlock mystery boxes and collectibles**. The home screen integration provides **constant engagement touchpoints** while the `/collectible` page becomes a **trophy room** for showcasing discoveries, creating strong **completion drive** and **social showing-off** opportunities.
