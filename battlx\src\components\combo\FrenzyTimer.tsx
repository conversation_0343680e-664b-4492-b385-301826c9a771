import React, { useState, useEffect } from 'react';
import { useComboStore, FRENZY_DURATION } from '@/store/combo-store';

/**
 * FrenzyTimer component that displays the remaining time during Frenzy mode
 */
const FrenzyTimer: React.FC = () => {
  const { frenzyActive, frenzyEndTime, getCurrentMultiplier } = useComboStore();
  const [timeRemaining, setTimeRemaining] = useState(FRENZY_DURATION);

  useEffect(() => {
    if (!frenzyActive) return;

    const updateTimer = () => {
      const now = Date.now();
      const remaining = Math.max(0, frenzyEndTime - now);
      setTimeRemaining(remaining);
    };

    // Update timer less frequently for better performance (200ms instead of 100ms)
    const timerInterval = setInterval(updateTimer, 200);
    updateTimer(); // Initial update

    return () => clearInterval(timerInterval);
  }, [frenzyActive, frenzyEndTime]);

  // Don't render if not in Frenzy mode
  if (!frenzyActive) return null;

  // Calculate seconds remaining (without decimal for better performance)
  const secondsRemaining = Math.ceil(timeRemaining / 1000);

  // Calculate progress percentage
  const progressPercentage = (timeRemaining / FRENZY_DURATION) * 100;

  return (
    <div
      className="frenzy-timer"
      style={{
        position: 'absolute',
        top: '20%',
        left: '50%',
        transform: 'translateX(-50%)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        zIndex: 95
      }}
    >
      <div
        className="frenzy-title"
        style={{
          color: '#FFD700',
          fontSize: '2.5rem',
          fontWeight: 'bold',
          textShadow: '0 0 10px #FFD700',
          fontFamily: 'Olnova-HeavyCond, sans-serif',
          textTransform: 'uppercase',
          letterSpacing: '2px',
          marginBottom: '10px',
          willChange: 'transform', // Performance optimization hint
          transform: 'translateZ(0)' // Force GPU acceleration
        }}
      >
        FRENZY MODE
      </div>

      <div
        className="frenzy-timer-text"
        style={{
          color: '#FFD700',
          fontSize: '1.8rem',
          fontWeight: 'bold',
          textShadow: '0 0 5px #FFD700',
          marginBottom: '5px',
          willChange: 'transform', // Performance optimization hint
          transform: 'translateZ(0)' // Force GPU acceleration
        }}
      >
        {secondsRemaining}s
      </div>

      {/* Multiplier display */}
      <div
        className="frenzy-multiplier"
        style={{
          color: '#FFD700',
          fontSize: '1.4rem',
          fontWeight: 'bold',
          textShadow: '0 0 5px #FFD700',
          marginBottom: '10px',
          willChange: 'transform',
          transform: 'translateZ(0)',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          padding: '2px 10px',
          borderRadius: '15px',
          border: '1px solid #FFD700'
        }}
      >
        {getCurrentMultiplier()}x Multiplier
      </div>

      {/* Progress bar - optimized for performance */}
      <div
        className="frenzy-progress-container"
        style={{
          width: '200px',
          height: '8px', // Slightly thinner for better performance
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          borderRadius: '4px',
          overflow: 'hidden',
          border: '1px solid #FFD700',
          willChange: 'transform', // Performance optimization hint
          transform: 'translateZ(0)' // Force GPU acceleration
        }}
      >
        <div
          className="frenzy-progress-bar"
          style={{
            width: `${progressPercentage}%`,
            height: '100%',
            backgroundColor: '#FFD700',
            transition: 'width 0.2s linear', // Slower transition for better performance
            transform: 'translateZ(0)' // Force GPU acceleration
          }}
        />
      </div>
    </div>
  );
};

export default FrenzyTimer;
