# Home Screen Integration Implementation

## Overview
This document covers the integration of the Pet System into the existing home screen, including the pet widget, quick interactions, and status indicators.

## Implementation Time: 2-3 days
## Complexity: Medium
## Dependencies: Pet components, existing home screen layout

## Home Screen Pet Widget

### PetWidget Component
```tsx
// File: battlx/src/components/home/<USER>

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePetStore } from '../../stores/petStore';
import { useUserStore } from '../../stores/userStore';
import { Pet } from '../../types/pet';
import PetQuickInteraction from './PetQuickInteraction';
import PetStatusIndicator from './PetStatusIndicator';

interface PetWidgetProps {
  className?: string;
  compact?: boolean;
}

const PetWidget: React.FC<PetWidgetProps> = ({ 
  className = '', 
  compact = false 
}) => {
  const { featuredPet, fetchFeaturedPet, interactWithPet } = usePetStore();
  const { user } = useUserStore();
  
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [lastInteraction, setLastInteraction] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      fetchFeaturedPet();
    }
  }, [user, fetchFeaturedPet]);

  const handleQuickInteraction = async (interactionType: string) => {
    if (!featuredPet) return;

    try {
      await interactWithPet(featuredPet.id, interactionType);
      setLastInteraction(interactionType);
      
      // Hide the interaction feedback after 2 seconds
      setTimeout(() => setLastInteraction(null), 2000);
    } catch (error) {
      console.error('Quick interaction failed:', error);
    }
  };

  const navigateToPets = () => {
    // Navigate to pets page
    window.location.href = '/pets';
  };

  if (!featuredPet) {
    return (
      <motion.div
        className={`pet-widget no-pet ${className}`}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <div className="no-pet-content">
          <div className="no-pet-icon">🐾</div>
          <div className="no-pet-text">
            <h4>No Pet Selected</h4>
            <p>Get your first pet to start your collection!</p>
          </div>
          <button 
            className="get-pet-btn"
            onClick={() => window.location.href = '/pet-shop'}
          >
            Get Pet
          </button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className={`pet-widget ${compact ? 'compact' : ''} ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      onClick={navigateToPets}
    >
      {/* Pet Image and Basic Info */}
      <div className="pet-display">
        <div className="pet-image-container">
          <motion.img
            src={featuredPet.current_image}
            alt={featuredPet.display_name}
            className="pet-image"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          />
          
          {/* Status Indicators */}
          <PetStatusIndicator pet={featuredPet} />
          
          {/* Level Badge */}
          <div className="level-badge">
            Lv. {featuredPet.level}
          </div>

          {/* Evolution Indicator */}
          {featuredPet.can_evolve && (
            <motion.div
              className="evolution-indicator"
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                repeatType: 'loop'
              }}
            >
              ⭐
            </motion.div>
          )}
        </div>

        <div className="pet-info">
          <h3 className="pet-name">{featuredPet.display_name}</h3>
          
          {!compact && (
            <>
              <div className="pet-category">{featuredPet.category}</div>
              
              {/* Happiness Bar */}
              <div className="happiness-container">
                <div className="happiness-label">
                  <span>😊</span>
                  <span>{featuredPet.happiness_percentage}%</span>
                </div>
                <div className="happiness-bar">
                  <motion.div
                    className="happiness-fill"
                    initial={{ width: 0 }}
                    animate={{ width: `${featuredPet.happiness_percentage}%` }}
                    transition={{ duration: 0.5 }}
                    style={{
                      backgroundColor: featuredPet.happiness_percentage > 70 ? '#4ade80' :
                                     featuredPet.happiness_percentage > 30 ? '#fbbf24' : '#ef4444'
                    }}
                  />
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      {!compact && (
        <div className="pet-actions">
          <motion.button
            className="quick-actions-toggle"
            onClick={(e) => {
              e.stopPropagation();
              setShowQuickActions(!showQuickActions);
            }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {showQuickActions ? '✕' : '⚡'}
          </motion.button>

          <AnimatePresence>
            {showQuickActions && (
              <motion.div
                className="quick-actions"
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <PetQuickInteraction
                  type="feed"
                  available={featuredPet.can_feed}
                  onInteract={() => handleQuickInteraction('feed')}
                  energyCost={5}
                  userEnergy={user?.available_energy || 0}
                />
                
                <PetQuickInteraction
                  type="play"
                  available={featuredPet.can_play}
                  onInteract={() => handleQuickInteraction('play')}
                  energyCost={10}
                  userEnergy={user?.available_energy || 0}
                />
                
                <PetQuickInteraction
                  type="pet"
                  available={featuredPet.can_pet}
                  onInteract={() => handleQuickInteraction('pet')}
                  energyCost={2}
                  userEnergy={user?.available_energy || 0}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}

      {/* Interaction Feedback */}
      <AnimatePresence>
        {lastInteraction && (
          <motion.div
            className="interaction-feedback"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3 }}
          >
            <span className="feedback-icon">
              {lastInteraction === 'feed' ? '🍖' : 
               lastInteraction === 'play' ? '🎾' : '❤️'}
            </span>
            <span className="feedback-text">
              {lastInteraction === 'feed' ? 'Fed!' : 
               lastInteraction === 'play' ? 'Played!' : 'Petted!'}
            </span>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Attention Indicator */}
      {featuredPet.needs_attention && (
        <motion.div
          className="attention-indicator"
          animate={{ 
            scale: [1, 1.1, 1],
            opacity: [0.7, 1, 0.7]
          }}
          transition={{ 
            duration: 1.5,
            repeat: Infinity,
            repeatType: 'loop'
          }}
        >
          ⚠️ Needs Attention
        </motion.div>
      )}
    </motion.div>
  );
};

export default PetWidget;
```

### PetQuickInteraction Component
```tsx
// File: battlx/src/components/home/<USER>

import React from 'react';
import { motion } from 'framer-motion';

interface PetQuickInteractionProps {
  type: 'feed' | 'play' | 'pet';
  available: boolean;
  onInteract: () => void;
  energyCost: number;
  userEnergy: number;
}

const PetQuickInteraction: React.FC<PetQuickInteractionProps> = ({
  type,
  available,
  onInteract,
  energyCost,
  userEnergy
}) => {
  const icons = {
    feed: '🍖',
    play: '🎾',
    pet: '❤️'
  };

  const canAfford = userEnergy >= energyCost;
  const isDisabled = !available || !canAfford;

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isDisabled) {
      onInteract();
    }
  };

  return (
    <motion.button
      className={`quick-interaction ${type} ${isDisabled ? 'disabled' : ''}`}
      onClick={handleClick}
      whileHover={isDisabled ? {} : { scale: 1.1 }}
      whileTap={isDisabled ? {} : { scale: 0.9 }}
      disabled={isDisabled}
      title={
        !available ? 'On cooldown' :
        !canAfford ? `Need ${energyCost} energy` :
        `${type.charAt(0).toUpperCase() + type.slice(1)} (${energyCost} energy)`
      }
    >
      <span className="interaction-icon">{icons[type]}</span>
      <span className="energy-cost">⚡{energyCost}</span>
      
      {isDisabled && (
        <div className="disabled-overlay">
          {!available ? '⏰' : '⚡'}
        </div>
      )}
    </motion.button>
  );
};

export default PetQuickInteraction;
```

### PetStatusIndicator Component
```tsx
// File: battlx/src/components/home/<USER>

import React from 'react';
import { motion } from 'framer-motion';
import { Pet } from '../../types/pet';

interface PetStatusIndicatorProps {
  pet: Pet;
}

const PetStatusIndicator: React.FC<PetStatusIndicatorProps> = ({ pet }) => {
  const getStatusInfo = () => {
    if (pet.can_evolve) {
      return {
        icon: '⭐',
        color: '#fbbf24',
        label: 'Can Evolve!',
        priority: 1
      };
    }

    if (pet.happiness_percentage < 30) {
      return {
        icon: '😢',
        color: '#ef4444',
        label: 'Unhappy',
        priority: 2
      };
    }

    if (pet.happiness_percentage > 80) {
      return {
        icon: '😊',
        color: '#4ade80',
        label: 'Very Happy',
        priority: 4
      };
    }

    if (pet.interactions_today === 0) {
      return {
        icon: '💤',
        color: '#6b7280',
        label: 'Waiting',
        priority: 3
      };
    }

    return {
      icon: '😐',
      color: '#fbbf24',
      label: 'Content',
      priority: 5
    };
  };

  const status = getStatusInfo();

  return (
    <motion.div
      className="pet-status-indicator"
      initial={{ opacity: 0, scale: 0 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3, delay: 0.2 }}
    >
      <motion.div
        className="status-icon"
        style={{ backgroundColor: status.color }}
        animate={status.priority <= 2 ? {
          scale: [1, 1.2, 1],
          rotate: [0, 5, -5, 0]
        } : {}}
        transition={status.priority <= 2 ? {
          duration: 1.5,
          repeat: Infinity,
          repeatType: 'loop'
        } : {}}
      >
        {status.icon}
      </motion.div>
      
      <div className="status-tooltip">
        {status.label}
      </div>
    </motion.div>
  );
};

export default PetStatusIndicator;
```

## Home Screen Layout Integration

### Updated Home Component
```tsx
// File: battlx/src/pages/Home.tsx (additions)

import React from 'react';
import PetWidget from '../components/home/<USER>';
import PetNotificationBanner from '../components/home/<USER>';
// ... other imports

const Home: React.FC = () => {
  return (
    <div className="home-page">
      {/* Existing header content */}
      <div className="home-header">
        {/* ... existing header elements ... */}
      </div>

      {/* Pet Notification Banner */}
      <PetNotificationBanner />

      {/* Main Content Grid */}
      <div className="home-content">
        {/* Pet Widget - Featured prominently */}
        <div className="widget-section pet-section">
          <PetWidget className="home-pet-widget" />
        </div>

        {/* Existing game widgets */}
        <div className="widget-section games-section">
          {/* ... existing game components ... */}
        </div>

        {/* Other widgets */}
        <div className="widget-section stats-section">
          {/* ... existing stats components ... */}
        </div>
      </div>
    </div>
  );
};

export default Home;
```

### PetNotificationBanner Component
```tsx
// File: battlx/src/components/home/<USER>

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { usePetStore } from '../../stores/petStore';

const PetNotificationBanner: React.FC = () => {
  const { petsNeedingAttention, fetchPetsNeedingAttention } = usePetStore();
  const [showBanner, setShowBanner] = useState(false);
  const [dismissed, setDismissed] = useState(false);

  useEffect(() => {
    fetchPetsNeedingAttention();
  }, [fetchPetsNeedingAttention]);

  useEffect(() => {
    if (petsNeedingAttention.length > 0 && !dismissed) {
      setShowBanner(true);
    } else {
      setShowBanner(false);
    }
  }, [petsNeedingAttention, dismissed]);

  const handleDismiss = () => {
    setDismissed(true);
    setShowBanner(false);
  };

  const navigateToPets = () => {
    window.location.href = '/pets';
  };

  if (!showBanner || petsNeedingAttention.length === 0) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        className="pet-notification-banner"
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -50 }}
        transition={{ duration: 0.3 }}
      >
        <div className="banner-content">
          <div className="banner-icon">
            <motion.span
              animate={{ 
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0]
              }}
              transition={{ 
                duration: 1.5,
                repeat: Infinity,
                repeatType: 'loop'
              }}
            >
              🐾
            </motion.span>
          </div>
          
          <div className="banner-text">
            <h4>Your pets need attention!</h4>
            <p>
              {petsNeedingAttention.length === 1 
                ? `${petsNeedingAttention[0].name} is feeling lonely`
                : `${petsNeedingAttention.length} pets are waiting for you`
              }
            </p>
          </div>

          <div className="banner-actions">
            <button 
              className="care-btn"
              onClick={navigateToPets}
            >
              Care for Pets
            </button>
            
            <button 
              className="dismiss-btn"
              onClick={handleDismiss}
            >
              ✕
            </button>
          </div>
        </div>

        {/* Pet Avatars */}
        <div className="needy-pets">
          {petsNeedingAttention.slice(0, 3).map((pet, index) => (
            <motion.div
              key={pet.id}
              className="needy-pet-avatar"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
            >
              <img src={pet.current_image} alt={pet.name} />
              <div className="pet-happiness">
                {pet.happiness_percentage}%
              </div>
            </motion.div>
          ))}
          
          {petsNeedingAttention.length > 3 && (
            <div className="more-pets">
              +{petsNeedingAttention.length - 3}
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default PetNotificationBanner;
```

## CSS Styles for Home Integration

### Home Pet Widget Styles
```scss
// File: battlx/src/styles/components/home/<USER>

.pet-widget {
  background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(30,30,30,0.9) 100%);
  border: 2px solid #444;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    border-color: #666;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
  }

  &.compact {
    padding: 12px;
    
    .pet-display {
      flex-direction: row;
      align-items: center;
      gap: 12px;
    }

    .pet-image-container {
      width: 60px;
      height: 60px;
    }
  }

  .pet-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .pet-image-container {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid rgba(255,255,255,0.2);

    .pet-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .level-badge {
      position: absolute;
      top: -5px;
      right: -5px;
      background: #fbbf24;
      color: #000;
      border-radius: 12px;
      padding: 2px 6px;
      font-size: 10px;
      font-weight: bold;
    }

    .evolution-indicator {
      position: absolute;
      top: -8px;
      left: -8px;
      font-size: 16px;
      filter: drop-shadow(0 0 4px rgba(251, 191, 36, 0.8));
    }
  }

  .pet-info {
    text-align: center;
    
    .pet-name {
      font-size: 16px;
      font-weight: bold;
      color: #fff;
      margin: 0 0 4px 0;
    }

    .pet-category {
      font-size: 12px;
      color: #888;
      text-transform: capitalize;
      margin-bottom: 8px;
    }
  }

  .happiness-container {
    width: 100%;
    
    .happiness-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      margin-bottom: 4px;
    }

    .happiness-bar {
      width: 100%;
      height: 6px;
      background: rgba(255,255,255,0.2);
      border-radius: 3px;
      overflow: hidden;

      .happiness-fill {
        height: 100%;
        border-radius: 3px;
        transition: width 0.5s ease;
      }
    }
  }

  .pet-actions {
    position: relative;
    margin-top: 12px;

    .quick-actions-toggle {
      background: #444;
      border: 1px solid #666;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #555;
        transform: scale(1.05);
      }
    }

    .quick-actions {
      position: absolute;
      bottom: 40px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 8px;
      background: rgba(0,0,0,0.9);
      padding: 8px;
      border-radius: 20px;
      border: 1px solid #444;
    }
  }

  .interaction-feedback {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: #fff;
    padding: 8px 12px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    pointer-events: none;
  }

  .attention-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #ef4444;
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: bold;
  }

  &.no-pet {
    .no-pet-content {
      text-align: center;
      padding: 20px;

      .no-pet-icon {
        font-size: 48px;
        margin-bottom: 12px;
        opacity: 0.5;
      }

      .no-pet-text {
        h4 {
          color: #fff;
          margin: 0 0 8px 0;
        }

        p {
          color: #888;
          font-size: 14px;
          margin: 0 0 16px 0;
        }
      }

      .get-pet-btn {
        background: #3b82f6;
        color: #fff;
        border: none;
        padding: 8px 16px;
        border-radius: 8px;
        cursor: pointer;
        transition: background 0.2s ease;

        &:hover {
          background: #2563eb;
        }
      }
    }
  }
}

.quick-interaction {
  background: #333;
  border: 1px solid #555;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover:not(.disabled) {
    background: #444;
    transform: scale(1.1);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    .disabled-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0,0,0,0.7);
      border-radius: 50%;
      font-size: 12px;
    }
  }

  .interaction-icon {
    font-size: 14px;
  }

  .energy-cost {
    font-size: 8px;
    position: absolute;
    bottom: -2px;
    right: -2px;
    background: #fbbf24;
    color: #000;
    border-radius: 6px;
    padding: 1px 3px;
  }
}

.pet-status-indicator {
  position: absolute;
  top: 5px;
  left: 5px;

  .status-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #fff;
  }

  .status-tooltip {
    position: absolute;
    top: 25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
  }

  &:hover .status-tooltip {
    opacity: 1;
  }
}

.pet-notification-banner {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: #fff;
  padding: 12px 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;

  .banner-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;

    .banner-icon {
      font-size: 24px;
    }

    .banner-text {
      h4 {
        margin: 0 0 4px 0;
        font-size: 14px;
      }

      p {
        margin: 0;
        font-size: 12px;
        opacity: 0.9;
      }
    }

    .banner-actions {
      display: flex;
      gap: 8px;

      .care-btn {
        background: rgba(255,255,255,0.2);
        color: #fff;
        border: 1px solid rgba(255,255,255,0.3);
        padding: 6px 12px;
        border-radius: 6px;
        cursor: pointer;
        transition: background 0.2s ease;

        &:hover {
          background: rgba(255,255,255,0.3);
        }
      }

      .dismiss-btn {
        background: none;
        border: none;
        color: #fff;
        cursor: pointer;
        padding: 6px;
        opacity: 0.7;
        transition: opacity 0.2s ease;

        &:hover {
          opacity: 1;
        }
      }
    }
  }

  .needy-pets {
    display: flex;
    gap: 8px;
    align-items: center;

    .needy-pet-avatar {
      position: relative;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid rgba(255,255,255,0.3);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .pet-happiness {
        position: absolute;
        bottom: -4px;
        right: -4px;
        background: rgba(0,0,0,0.8);
        color: #fff;
        font-size: 8px;
        padding: 1px 3px;
        border-radius: 6px;
      }
    }

    .more-pets {
      background: rgba(255,255,255,0.2);
      color: #fff;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: bold;
    }
  }
}
```

## Acceptance Criteria
- [ ] Pet widget displays on home screen correctly
- [ ] Quick interactions work from home screen
- [ ] Pet status indicators show accurate information
- [ ] Notification banner appears for pets needing attention
- [ ] Navigation to pet pages functional
- [ ] Responsive design for mobile devices
- [ ] Smooth animations and transitions

## Next Steps
1. Create pet interaction UI components
2. Implement mystery box UI components
3. Create collectible page components
4. Add state management integration

## Troubleshooting
- Ensure pet data loads correctly on home screen
- Test quick interactions don't interfere with navigation
- Verify notification banner dismissal works
- Check responsive layout on different screen sizes
- Test touch interactions on mobile devices
