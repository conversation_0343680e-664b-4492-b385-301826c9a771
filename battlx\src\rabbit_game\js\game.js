/**
 * Main Game Class
 * Initializes the game and manages states
 */
class Game {
    /**
     * @param {string} canvasId - Canvas element ID
     * @param {Object} options - Game options for Laravel integration
     * @param {number} options.width - Canvas width
     * @param {number} options.height - Canvas height
     * @param {boolean} options.debug - Enable debug mode
     * @param {boolean} options.usePathGenerator - Whether to use the path generator for assets
     * @param {Function} options.setGameScore - Callback for score updates
     * @param {Function} options.onGameOver - Callback for game over
     */
    constructor(canvasId, options = {}) {
        // Enable debug mode for development
        window.DEBUG_SPRITES = options.debug || false;

        // Store integration options
        this.options = {
            width: options.width || 540,
            height: options.height || 960,
            debug: options.debug || false,
            usePathGenerator: options.usePathGenerator || false,
            pathGenerator: options.pathGenerator || null,
            setGameScore: options.setGameScore || null,
            onGameOver: options.onGameOver || null
        };

        // Create engine
        this.engine = new Engine(canvasId, {
            width: this.options.width,
            height: this.options.height,
            debug: this.options.debug
        });

        // Create asset loader with options
        this.assetLoader = new AssetLoader(this.engine, {
            usePathGenerator: this.options.usePathGenerator,
            pathGenerator: this.options.pathGenerator
        });

        // Create loading state
        this.loadingProgress = 0;
    }

    /**
     * Start the game
     */
    start() {
        // Store integration callbacks in the engine for state access
        if (this.options.setGameScore) {
            this.engine.setVariable('setGameScore', this.options.setGameScore);
        }
        if (this.options.onGameOver) {
            this.engine.setVariable('onGameOver', this.options.onGameOver);
        }

        // Show loading screen
        this.showLoadingScreen();

        // Load assets
        this.assetLoader.loadAll(() => {
            // Create game states
            this.createGameStates();

            // Start with menu state
            this.engine.setState(CONSTANTS.STATES.MENU);

            // Start game loop
            this.engine.start();
        });
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        const ctx = this.engine.ctx;
        const width = this.engine.width;
        const height = this.engine.height;

        // Animation frame for loading screen
        const updateLoading = () => {
            // Clear canvas
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, width, height);

            // Update progress
            this.loadingProgress = this.assetLoader.getProgress();

            // Draw loading text
            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 30px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('LOADING...', width / 2, height / 2 - 50);

            // Draw progress bar
            const barWidth = width * 0.7;
            const barHeight = 20;
            const barX = (width - barWidth) / 2;
            const barY = height / 2;

            // Draw bar background
            ctx.fillStyle = '#333333';
            ctx.fillRect(barX, barY, barWidth, barHeight);

            // Draw progress
            ctx.fillStyle = '#00FF00';
            ctx.fillRect(barX, barY, barWidth * this.loadingProgress, barHeight);

            // Draw percentage
            ctx.fillStyle = '#FFFFFF';
            ctx.font = 'bold 16px Arial';
            ctx.fillText(`${Math.round(this.loadingProgress * 100)}%`, width / 2, barY + barHeight + 20);

            // Continue animation if not done loading
            if (this.loadingProgress < 1) {
                requestAnimationFrame(updateLoading);
            }
        };

        // Start loading animation
        updateLoading();
    }

    /**
     * Create game states
     */
    createGameStates() {
        // Create states
        const menuState = new MenuState(this.engine);
        const playState = new PlayState(this.engine);
        const gameOverState = new GameOverState(this.engine);

        // Add states to engine
        this.engine.addState(CONSTANTS.STATES.MENU, menuState);
        this.engine.addState(CONSTANTS.STATES.PLAYING, playState);
        this.engine.addState(CONSTANTS.STATES.GAME_OVER, gameOverState);
    }
}
