// EnemyProjectile class for handling enemy projectiles
window.EnemyProjectile = class EnemyProjectile {
    constructor(x, y, targetX, targetY, enemyType) {
        // Only log occasionally to reduce console spam
        if (Math.random() < 0.1) {
            
        }
        this.x = x;
        this.y = y;
        this.enemyType = enemyType;
        this.isDead = false;
        // Customize based on enemy type
        if (enemyType.includes('BAT')) {
            this.radius = 10; // Slightly larger for BAT projectiles
            this.lifetime = 0;
            this.maxLifetime = 2000; // 2 seconds max lifetime for BAT projectiles
            this.speed = 5; // Faster for BAT projectiles
            this.damage = 4; // More damage for BAT projectiles
            this.color = 'rgba(255, 0, 0, 0.8)'; // Red color for BAT projectiles
        } else {
            this.radius = 8; // Smaller than player projectiles
            this.lifetime = 0;
            this.maxLifetime = 3000; // 3 seconds max lifetime
            this.speed = 3; // Slower than player projectiles
            this.damage = 3; // Less damage than direct contact
            this.color = 'rgba(255, 0, 0, 0.7)'; // Default red color
        }

        // Calculate direction toward target
        const dx = targetX - x;
        const dy = targetY - y;
        const dist = Math.sqrt(dx * dx + dy * dy);

        // Normalize direction
        this.direction = {
            x: dx / dist,
            y: dy / dist
        };

        // Set sprite based on enemy type
        if (enemyType.includes('BAT')) {
            this.spriteName = 'bat_projectile.png';
            // If no sprite exists, we'll use a fallback shape
        } else {
            this.spriteName = 'enemy_projectile.png';
        }
    }

    // Update the projectile
    update(deltaTime) {
        if (this.isDead) return;

        // Update lifetime
        this.lifetime += deltaTime;
        if (this.lifetime >= this.maxLifetime) {
            this.die();
            return;
        }

        // Move projectile
        this.x += this.direction.x * this.speed * deltaTime / 16;
        this.y += this.direction.y * this.speed * deltaTime / 16;

        // Check for collisions with player
        if (Game.core && Game.core.player && !Game.core.player.isDead && !Game.core.player.isInvul) {
            // Calculate distance manually if distance function is not available
            let dist;
            if (typeof distance === 'function') {
                dist = distance(this.x, this.y, Game.core.player.x, Game.core.player.y);
            } else {
                // Fallback to manual calculation
                const dx = this.x - Game.core.player.x;
                const dy = this.y - Game.core.player.y;
                dist = Math.sqrt(dx * dx + dy * dy);
                
            }

            if (dist < this.radius + Game.core.player.radius) {
                // Player takes damage
                Game.core.player.takeDamage(this.damage);

                // Show damage number
                Game.core.showDamageAt(Game.core.player.x, Game.core.player.y, this.damage);

                // Die after hitting player
                this.die();
            }
        }

        // Check if out of bounds
        if (Game.core && Game.core.canvas) {
            const canvas = Game.core.canvas;
            const cameraX = Game.core.camera.x;
            const cameraY = Game.core.camera.y;
            const screenWidth = canvas.width;
            const screenHeight = canvas.height;

            // Check if projectile is far outside the screen
            const margin = 100; // Extra margin beyond screen
            if (
                this.x < cameraX - screenWidth/2 - margin ||
                this.x > cameraX + screenWidth/2 + margin ||
                this.y < cameraY - screenHeight/2 - margin ||
                this.y > cameraY + screenHeight/2 + margin
            ) {
                this.die();
            }
        }
    }

    // Draw the projectile
    draw(ctx, camera, sprites) {
        if (this.isDead) return;

        // Calculate screen position
        const screenX = this.x - camera.x + camera.width / 2;
        const screenY = this.y - camera.y + camera.height / 2;

        // Get the sprite
        const sprite = sprites[this.spriteName];
        if (sprite) {
            // Draw the sprite
            ctx.save();

            // Calculate rotation angle based on direction
            const angle = Math.atan2(this.direction.y, this.direction.x);

            // Translate to position, rotate, then draw
            ctx.translate(screenX, screenY);
            ctx.rotate(angle);
            ctx.drawImage(
                sprite,
                -sprite.width / 2,
                -sprite.height / 2,
                sprite.width,
                sprite.height
            );

            ctx.restore();
        } else {
            // Draw a fallback shape if sprite not found
            ctx.save();

            // Draw a circle with custom color
            ctx.beginPath();
            ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
            ctx.fillStyle = this.color || 'rgba(255, 0, 0, 0.7)';
            ctx.fill();

            // Draw a trail
            ctx.beginPath();
            ctx.moveTo(screenX, screenY);
            ctx.lineTo(
                screenX - this.direction.x * this.radius * 2,
                screenY - this.direction.y * this.radius * 2
            );

            // Use a matching trail color
            const trailColor = this.color ? this.color.replace('0.8', '0.5').replace('0.7', '0.5') : 'rgba(255, 0, 0, 0.5)';
            ctx.strokeStyle = trailColor;
            ctx.lineWidth = 2;
            ctx.stroke();

            // For BAT projectiles, add a glow effect
            if (this.enemyType && this.enemyType.includes('BAT')) {
                ctx.beginPath();
                ctx.arc(screenX, screenY, this.radius * 1.5, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(255, 0, 0, 0.2)';
                ctx.fill();
            }

            ctx.restore();
        }

        // Draw debug collision circle
        if (Game.core && Game.core.debug) {
            ctx.beginPath();
            ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
            ctx.stroke();
        }
    }

    // Die and mark as dead (actual removal happens in gameCore)
    die() {
        if (this.isDead) return;
        this.isDead = true;
        // Only log occasionally to reduce console spam
        if (Math.random() < 0.1) {
            
        }

        // We no longer remove the projectile here to avoid array index issues
        // The gameCore update loop will handle removal of dead projectiles
    }
}
