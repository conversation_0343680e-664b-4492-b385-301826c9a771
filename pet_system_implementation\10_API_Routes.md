# API Routes Configuration

## Overview
This document covers the complete API routes configuration for the Pet System, including authentication, middleware, and route organization.

## Implementation Time: 1 day
## Complexity: Low-Medium
## Dependencies: Controllers and middleware setup

## Pet System Routes

### Main Routes File
```php
<?php
// File: api/routes/api.php

use App\Http\Controllers\PetController;
use App\Http\Controllers\PetShopController;
use App\Http\Controllers\MysteryBoxController;
use App\Http\Controllers\CollectionController;
use App\Http\Controllers\PetAdminController;
use Illuminate\Support\Facades\Route;

// Existing routes...

// Pet System Routes (Protected by auth middleware)
Route::middleware(['auth:sanctum', 'telegram.user'])->group(function () {
    
    // Pet Management Routes
    Route::prefix('pets')->name('pets.')->group(function () {
        Route::get('/', [PetController::class, 'index'])->name('index');
        Route::get('/featured', [PetController::class, 'getFeaturedPet'])->name('featured');
        Route::get('/needing-attention', [PetController::class, 'getPetsNeedingAttention'])->name('needing-attention');
        Route::post('/purchase', [PetController::class, 'purchase'])->name('purchase');
        
        Route::prefix('{pet}')->group(function () {
            Route::post('/interact', [PetController::class, 'interact'])->name('interact');
            Route::put('/featured', [PetController::class, 'setFeaturedPet'])->name('set-featured');
            Route::put('/nickname', [PetController::class, 'updateNickname'])->name('update-nickname');
            Route::put('/favorite', [PetController::class, 'toggleFavorite'])->name('toggle-favorite');
            Route::get('/interactions', [PetController::class, 'getInteractionHistory'])->name('interactions');
        });
    });

    // Pet Shop Routes
    Route::prefix('pet-shop')->name('pet-shop.')->group(function () {
        Route::get('/', [PetShopController::class, 'index'])->name('index');
        Route::get('/featured', [PetShopController::class, 'getFeaturedPets'])->name('featured');
    });

    // Mystery Box Routes
    Route::prefix('mystery-boxes')->name('mystery-boxes.')->group(function () {
        Route::get('/', [MysteryBoxController::class, 'index'])->name('index');
        Route::get('/all-types', [MysteryBoxController::class, 'getAllBoxTypes'])->name('all-types');
        Route::post('/open', [MysteryBoxController::class, 'openBox'])->name('open');
        Route::get('/history', [MysteryBoxController::class, 'getOpeningHistory'])->name('history');
        Route::get('/statistics', [MysteryBoxController::class, 'getStatistics'])->name('statistics');
        Route::get('/{boxType}/preview', [MysteryBoxController::class, 'previewRewards'])->name('preview');
    });

    // Collection Routes
    Route::prefix('collection')->name('collection.')->group(function () {
        Route::get('/', [CollectionController::class, 'index'])->name('index');
        Route::get('/sets', [CollectionController::class, 'getCollectionSets'])->name('sets');
        Route::get('/sets/{setId}', [CollectionController::class, 'getCollectionSet'])->name('sets.show');
        Route::post('/sets/{setId}/claim-rewards', [CollectionController::class, 'claimSetRewards'])->name('sets.claim-rewards');
        Route::get('/statistics', [CollectionController::class, 'getStatistics'])->name('statistics');
        Route::get('/milestones', [CollectionController::class, 'getMilestones'])->name('milestones');
        Route::get('/recent', [CollectionController::class, 'getRecentCollectibles'])->name('recent');
        Route::get('/search', [CollectionController::class, 'search'])->name('search');
    });
});

// Admin Routes (Protected by admin middleware)
Route::middleware(['auth:sanctum', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    
    // Pet System Admin Routes
    Route::prefix('pets')->name('pets.')->group(function () {
        Route::get('/', [PetAdminController::class, 'index'])->name('index');
        Route::post('/', [PetAdminController::class, 'store'])->name('store');
        Route::get('/{petTemplate}', [PetAdminController::class, 'show'])->name('show');
        Route::put('/{petTemplate}', [PetAdminController::class, 'update'])->name('update');
        Route::delete('/{petTemplate}', [PetAdminController::class, 'destroy'])->name('destroy');
        
        // Pet Template Management
        Route::prefix('templates')->name('templates.')->group(function () {
            Route::get('/', [PetAdminController::class, 'getTemplates'])->name('index');
            Route::post('/', [PetAdminController::class, 'createTemplate'])->name('store');
            Route::put('/{id}', [PetAdminController::class, 'updateTemplate'])->name('update');
            Route::delete('/{id}', [PetAdminController::class, 'deleteTemplate'])->name('destroy');
        });
        
        // User Pet Management
        Route::prefix('user-pets')->name('user-pets.')->group(function () {
            Route::get('/', [PetAdminController::class, 'getUserPets'])->name('index');
            Route::post('/grant', [PetAdminController::class, 'grantPetToUser'])->name('grant');
            Route::delete('/{pet}', [PetAdminController::class, 'removePetFromUser'])->name('remove');
        });
    });

    // Mystery Box Admin Routes
    Route::prefix('mystery-boxes')->name('mystery-boxes.')->group(function () {
        Route::get('/types', [MysteryBoxAdminController::class, 'getBoxTypes'])->name('types.index');
        Route::post('/types', [MysteryBoxAdminController::class, 'createBoxType'])->name('types.store');
        Route::put('/types/{id}', [MysteryBoxAdminController::class, 'updateBoxType'])->name('types.update');
        Route::delete('/types/{id}', [MysteryBoxAdminController::class, 'deleteBoxType'])->name('types.destroy');
        
        Route::get('/openings', [MysteryBoxAdminController::class, 'getOpenings'])->name('openings.index');
        Route::get('/statistics', [MysteryBoxAdminController::class, 'getStatistics'])->name('statistics');
    });

    // Collection Admin Routes
    Route::prefix('collections')->name('collections.')->group(function () {
        Route::get('/templates', [CollectionAdminController::class, 'getTemplates'])->name('templates.index');
        Route::post('/templates', [CollectionAdminController::class, 'createTemplate'])->name('templates.store');
        Route::put('/templates/{id}', [CollectionAdminController::class, 'updateTemplate'])->name('templates.update');
        Route::delete('/templates/{id}', [CollectionAdminController::class, 'deleteTemplate'])->name('templates.destroy');
        
        Route::get('/sets', [CollectionAdminController::class, 'getSets'])->name('sets.index');
        Route::post('/sets', [CollectionAdminController::class, 'createSet'])->name('sets.store');
        Route::put('/sets/{id}', [CollectionAdminController::class, 'updateSet'])->name('sets.update');
        Route::delete('/sets/{id}', [CollectionAdminController::class, 'deleteSet'])->name('sets.destroy');
        
        Route::get('/user-progress', [CollectionAdminController::class, 'getUserProgress'])->name('user-progress');
    });
});
```

## Route Model Binding

### Custom Route Model Binding
```php
<?php
// File: api/app/Providers/RouteServiceProvider.php

namespace App\Providers;

use App\Models\Pet;
use App\Models\PetTemplate;
use App\Models\MysteryBoxType;
use App\Models\CollectionSet;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        parent::boot();

        // Pet route model binding with user ownership check
        Route::bind('pet', function (string $value, Request $request) {
            $user = $request->user();
            
            return Pet::where('id', $value)
                     ->where('telegram_user_id', $user->id)
                     ->firstOrFail();
        });

        // Pet template binding
        Route::bind('petTemplate', function (string $value) {
            return PetTemplate::where('id', $value)
                             ->where('is_active', true)
                             ->firstOrFail();
        });

        // Mystery box type binding
        Route::bind('mysteryBoxType', function (string $value) {
            return MysteryBoxType::where('box_type', $value)
                                ->where('is_active', true)
                                ->firstOrFail();
        });

        // Collection set binding
        Route::bind('collectionSet', function (string $value) {
            return CollectionSet::where('set_id', $value)
                               ->where('is_active', true)
                               ->firstOrFail();
        });
    }
}
```

## Middleware Configuration

### Pet System Middleware
```php
<?php
// File: api/app/Http/Middleware/PetSystemMiddleware.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PetSystemMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required'
            ], 401);
        }

        // Check if pet system is enabled for user
        if (!$this->isPetSystemEnabled($user)) {
            return response()->json([
                'success' => false,
                'message' => 'Pet system not available'
            ], 403);
        }

        return $next($request);
    }

    private function isPetSystemEnabled($user): bool
    {
        // Check if user has access to pet system
        // This could be based on user level, subscription, etc.
        return true; // For now, enable for all users
    }
}
```

### Rate Limiting Middleware
```php
<?php
// File: api/app/Http/Middleware/PetInteractionRateLimit.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class PetInteractionRateLimit
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        $interactionType = $request->input('interaction_type');
        
        // Rate limit pet interactions to prevent abuse
        $key = "pet_interaction:{$user->id}:{$interactionType}";
        $attempts = Cache::get($key, 0);
        
        $limits = [
            'feed' => 50,   // 50 feeds per hour
            'play' => 30,   // 30 plays per hour
            'pet' => 100,   // 100 pets per hour
        ];
        
        $limit = $limits[$interactionType] ?? 10;
        
        if ($attempts >= $limit) {
            return response()->json([
                'success' => false,
                'message' => 'Rate limit exceeded. Please try again later.',
                'retry_after' => 3600 // 1 hour
            ], 429);
        }
        
        // Increment counter
        Cache::put($key, $attempts + 1, 3600); // 1 hour TTL
        
        return $next($request);
    }
}
```

## API Response Middleware

### API Response Formatter
```php
<?php
// File: api/app/Http/Middleware/FormatApiResponse.php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class FormatApiResponse
{
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);
        
        // Only format JSON responses
        if (!$response instanceof JsonResponse) {
            return $response;
        }
        
        $data = $response->getData(true);
        
        // Add standard response structure if not already present
        if (!isset($data['success'])) {
            $formattedData = [
                'success' => $response->getStatusCode() >= 200 && $response->getStatusCode() < 300,
                'data' => $data,
                'timestamp' => now()->toISOString(),
                'version' => '1.0'
            ];
            
            $response->setData($formattedData);
        }
        
        return $response;
    }
}
```

## Route Caching and Optimization

### Route Caching Commands
```bash
# Cache routes for production
php artisan route:cache

# Clear route cache
php artisan route:clear

# List all routes
php artisan route:list --path=pets
php artisan route:list --path=mystery-boxes
php artisan route:list --path=collection
```

## API Documentation Routes

### API Documentation
```php
<?php
// File: api/routes/web.php additions

// API Documentation routes (if using Swagger/OpenAPI)
Route::get('/api/documentation', function () {
    return view('api-documentation');
})->name('api.documentation');

// Health check endpoint
Route::get('/api/health', function () {
    return response()->json([
        'status' => 'healthy',
        'timestamp' => now(),
        'services' => [
            'database' => 'connected',
            'cache' => 'connected',
            'queue' => 'running'
        ]
    ]);
})->name('api.health');
```

## Route Testing

### Route Testing Examples
```php
<?php
// File: api/tests/Feature/PetRoutesTest.php

namespace Tests\Feature;

use App\Models\TelegramUser;
use App\Models\Pet;
use App\Models\PetTemplate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PetRoutesTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_get_pet_collection()
    {
        $user = TelegramUser::factory()->create();
        
        $response = $this->actingAs($user)
                        ->getJson('/api/pets');
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'pets',
                    'collection_progress'
                ]);
    }

    public function test_user_can_purchase_pet()
    {
        $user = TelegramUser::factory()->create(['balance' => 10000]);
        $petTemplate = PetTemplate::factory()->create(['coin_cost' => 1000]);
        
        $response = $this->actingAs($user)
                        ->postJson('/api/pets/purchase', [
                            'pet_template_id' => $petTemplate->id,
                            'purchase_method' => 'coins'
                        ]);
        
        $response->assertStatus(200)
                ->assertJson(['success' => true]);
        
        $this->assertDatabaseHas('pets', [
            'telegram_user_id' => $user->id,
            'pet_template_id' => $petTemplate->id
        ]);
    }

    public function test_user_can_interact_with_pet()
    {
        $user = TelegramUser::factory()->create(['available_energy' => 100]);
        $pet = Pet::factory()->create(['telegram_user_id' => $user->id]);
        
        $response = $this->actingAs($user)
                        ->postJson("/api/pets/{$pet->id}/interact", [
                            'interaction_type' => 'feed'
                        ]);
        
        $response->assertStatus(200)
                ->assertJson(['success' => true]);
    }

    public function test_user_cannot_interact_with_other_users_pet()
    {
        $user1 = TelegramUser::factory()->create();
        $user2 = TelegramUser::factory()->create();
        $pet = Pet::factory()->create(['telegram_user_id' => $user2->id]);
        
        $response = $this->actingAs($user1)
                        ->postJson("/api/pets/{$pet->id}/interact", [
                            'interaction_type' => 'feed'
                        ]);
        
        $response->assertStatus(404);
    }
}
```

## Middleware Registration

### Kernel Middleware Registration
```php
<?php
// File: api/app/Http/Kernel.php

protected $middlewareAliases = [
    // Existing middleware...
    
    'pet.system' => \App\Http\Middleware\PetSystemMiddleware::class,
    'pet.rate.limit' => \App\Http\Middleware\PetInteractionRateLimit::class,
    'format.api' => \App\Http\Middleware\FormatApiResponse::class,
];

protected $middlewareGroups = [
    'api' => [
        // Existing middleware...
        'format.api',
    ],
];
```

## Route Groups with Middleware

### Enhanced Route Configuration
```php
<?php
// File: api/routes/api.php additions

// Pet interaction routes with rate limiting
Route::middleware(['auth:sanctum', 'telegram.user', 'pet.rate.limit'])->group(function () {
    Route::post('/pets/{pet}/interact', [PetController::class, 'interact']);
    Route::post('/mystery-boxes/open', [MysteryBoxController::class, 'openBox']);
});

// Pet system routes with system check
Route::middleware(['auth:sanctum', 'telegram.user', 'pet.system'])->group(function () {
    // All pet system routes here
});
```

## Acceptance Criteria
- [ ] All pet system routes properly defined
- [ ] Route model binding working correctly
- [ ] Middleware protecting routes appropriately
- [ ] Rate limiting preventing abuse
- [ ] API responses properly formatted
- [ ] Route caching optimized for production
- [ ] Route tests passing

## Next Steps
1. Create validation rules for all requests
2. Implement event system for pet actions
3. Set up admin interface controllers
4. Create comprehensive API documentation

## Troubleshooting
- Use `php artisan route:list` to debug route issues
- Check middleware order and dependencies
- Verify route model binding constraints
- Test rate limiting thresholds
- Monitor API response times and optimize as needed
