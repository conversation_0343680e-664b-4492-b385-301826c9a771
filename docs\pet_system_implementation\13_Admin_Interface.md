# Admin Interface Implementation

## Overview
This document covers the implementation of comprehensive admin interfaces for managing the Pet System, including pet templates, mystery boxes, collections, and user management.

## Implementation Time: 3-4 days
## Complexity: High
## Dependencies: All pet system components

## Pet Admin Controller

### PetAdminController
```php
<?php
// File: api/app/Http/Controllers/Admin/PetAdminController.php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Pet;
use App\Models\PetTemplate;
use App\Models\TelegramUser;
use App\Services\PetService;
use App\Services\AchievementPointService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class PetAdminController extends Controller
{
    protected PetService $petService;
    protected AchievementPointService $achievementPointService;

    public function __construct(PetService $petService, AchievementPointService $achievementPointService)
    {
        $this->petService = $petService;
        $this->achievementPointService = $achievementPointService;
    }

    /**
     * Get all pet templates with statistics
     */
    public function getTemplates(Request $request): JsonResponse
    {
        $perPage = $request->query('per_page', 20);
        $search = $request->query('search');
        $category = $request->query('category');
        $rarity = $request->query('rarity');
        
        $query = PetTemplate::with(['pets' => function($q) {
            $q->selectRaw('pet_template_id, COUNT(*) as total_owned')
              ->groupBy('pet_template_id');
        }]);

        if ($search) {
            $query->where('name', 'ILIKE', "%{$search}%");
        }

        if ($category) {
            $query->where('category', $category);
        }

        if ($rarity) {
            $query->where('rarity', $rarity);
        }

        $templates = $query->orderBy('sort_order')
                          ->orderBy('created_at', 'desc')
                          ->paginate($perPage);

        return response()->json([
            'success' => true,
            'templates' => $templates->items(),
            'pagination' => [
                'current_page' => $templates->currentPage(),
                'last_page' => $templates->lastPage(),
                'per_page' => $templates->perPage(),
                'total' => $templates->total()
            ],
            'statistics' => $this->getTemplateStatistics()
        ]);
    }

    /**
     * Create new pet template
     */
    public function createTemplate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100|unique:pet_templates,name',
            'category' => 'required|in:shadow,undead,demon,spirit,beast',
            'rarity' => 'required|in:common,rare,epic,legendary,mythic',
            'description' => 'required|string|max:500',
            'image_url' => 'required|string|max:255',
            'animation_url' => 'nullable|string|max:255',
            'coin_cost' => 'required|integer|min:0',
            'gem_cost' => 'required|integer|min:0',
            'prize_tree_unlock_level' => 'nullable|integer|min:1|max:100',
            'is_premium_only' => 'boolean',
            'base_happiness' => 'required|integer|min:1|max:100',
            'max_happiness' => 'required|integer|min:50|max:200',
            'happiness_decay_rate' => 'required|integer|min:1|max:20',
            'mystery_box_unlocks' => 'required|array',
            'mystery_box_unlocks.*' => 'string|exists:mystery_box_types,box_type',
            'collectible_reward_id' => 'required|string|exists:collectible_templates,collectible_id',
            'max_level' => 'required|integer|min:10|max:200',
            'evolution_levels' => 'required|array|size:4',
            'evolution_levels.*' => 'integer|min:1',
            'evolution_images' => 'required|array|size:4',
            'evolution_images.*' => 'string|max:255',
            'sort_order' => 'integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $template = PetTemplate::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Pet template created successfully',
                'template' => $template
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create pet template: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update pet template
     */
    public function updateTemplate(Request $request, int $id): JsonResponse
    {
        $template = PetTemplate::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100|unique:pet_templates,name,' . $id,
            'category' => 'required|in:shadow,undead,demon,spirit,beast',
            'rarity' => 'required|in:common,rare,epic,legendary,mythic',
            'description' => 'required|string|max:500',
            'image_url' => 'required|string|max:255',
            'animation_url' => 'nullable|string|max:255',
            'coin_cost' => 'required|integer|min:0',
            'gem_cost' => 'required|integer|min:0',
            'prize_tree_unlock_level' => 'nullable|integer|min:1|max:100',
            'is_premium_only' => 'boolean',
            'base_happiness' => 'required|integer|min:1|max:100',
            'max_happiness' => 'required|integer|min:50|max:200',
            'happiness_decay_rate' => 'required|integer|min:1|max:20',
            'mystery_box_unlocks' => 'required|array',
            'collectible_reward_id' => 'required|string|exists:collectible_templates,collectible_id',
            'max_level' => 'required|integer|min:10|max:200',
            'evolution_levels' => 'required|array|size:4',
            'evolution_images' => 'required|array|size:4',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $template->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Pet template updated successfully',
                'template' => $template->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update pet template: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete pet template
     */
    public function deleteTemplate(int $id): JsonResponse
    {
        $template = PetTemplate::findOrFail($id);

        // Check if any users own this pet
        $ownedCount = Pet::where('pet_template_id', $id)->count();
        
        if ($ownedCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "Cannot delete pet template. {$ownedCount} users own this pet."
            ], 400);
        }

        try {
            $template->delete();

            return response()->json([
                'success' => true,
                'message' => 'Pet template deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete pet template: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user pets with management options
     */
    public function getUserPets(Request $request): JsonResponse
    {
        $perPage = $request->query('per_page', 20);
        $userId = $request->query('user_id');
        $petTemplateId = $request->query('pet_template_id');
        $search = $request->query('search');

        $query = Pet::with(['user', 'template']);

        if ($userId) {
            $query->where('telegram_user_id', $userId);
        }

        if ($petTemplateId) {
            $query->where('pet_template_id', $petTemplateId);
        }

        if ($search) {
            $query->whereHas('user', function($q) use ($search) {
                $q->where('username', 'ILIKE', "%{$search}%")
                  ->orWhere('first_name', 'ILIKE', "%{$search}%");
            })->orWhereHas('template', function($q) use ($search) {
                $q->where('name', 'ILIKE', "%{$search}%");
            })->orWhere('nickname', 'ILIKE', "%{$search}%");
        }

        $pets = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'pets' => $pets->items(),
            'pagination' => [
                'current_page' => $pets->currentPage(),
                'last_page' => $pets->lastPage(),
                'per_page' => $pets->perPage(),
                'total' => $pets->total()
            ]
        ]);
    }

    /**
     * Grant pet to user
     */
    public function grantPetToUser(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:telegram_users,id',
            'pet_template_id' => 'required|exists:pet_templates,id',
            'nickname' => 'nullable|string|max:50',
            'level' => 'integer|min:1|max:200',
            'happiness' => 'integer|min:0|max:200',
            'reason' => 'required|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = TelegramUser::findOrFail($request->user_id);
        $petTemplate = PetTemplate::findOrFail($request->pet_template_id);

        // Check if user already owns this pet
        if ($user->pets()->where('pet_template_id', $petTemplate->id)->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'User already owns this pet'
            ], 400);
        }

        try {
            DB::beginTransaction();

            $pet = $user->pets()->create([
                'pet_template_id' => $petTemplate->id,
                'level' => $request->level ?? 1,
                'experience' => 0,
                'happiness' => $request->happiness ?? $petTemplate->base_happiness,
                'nickname' => $request->nickname,
                'evolution_stage' => 0,
                'is_featured' => $user->pets()->count() === 0 // First pet becomes featured
            ]);

            // Award achievement points
            $this->achievementPointService->awardPoints(
                $user->id,
                50,
                'admin_grant',
                $pet->id,
                "Admin granted: {$petTemplate->name} - {$request->reason}"
            );

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Pet granted to user successfully',
                'pet' => $pet->load(['template', 'user'])
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to grant pet: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove pet from user
     */
    public function removePetFromUser(Pet $pet, Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $petData = [
                'id' => $pet->id,
                'user_id' => $pet->telegram_user_id,
                'template_name' => $pet->template->name,
                'level' => $pet->level,
                'reason' => $request->reason
            ];

            // If this was the featured pet, set another pet as featured
            if ($pet->is_featured) {
                $nextPet = $pet->user->pets()->where('id', '!=', $pet->id)->first();
                if ($nextPet) {
                    $nextPet->update(['is_featured' => true]);
                }
            }

            $pet->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Pet removed from user successfully',
                'removed_pet' => $petData
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove pet: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get pet system statistics
     */
    public function getStatistics(): JsonResponse
    {
        $stats = [
            'total_templates' => PetTemplate::count(),
            'active_templates' => PetTemplate::where('is_active', true)->count(),
            'total_pets_owned' => Pet::count(),
            'unique_pet_owners' => Pet::distinct('telegram_user_id')->count(),
            'templates_by_category' => PetTemplate::selectRaw('category, COUNT(*) as count')
                                                 ->groupBy('category')
                                                 ->pluck('count', 'category'),
            'templates_by_rarity' => PetTemplate::selectRaw('rarity, COUNT(*) as count')
                                               ->groupBy('rarity')
                                               ->pluck('count', 'rarity'),
            'pets_by_category' => Pet::join('pet_templates', 'pets.pet_template_id', '=', 'pet_templates.id')
                                    ->selectRaw('pet_templates.category, COUNT(*) as count')
                                    ->groupBy('pet_templates.category')
                                    ->pluck('count', 'category'),
            'average_pets_per_user' => Pet::count() > 0 ? round(Pet::count() / Pet::distinct('telegram_user_id')->count(), 2) : 0,
            'most_popular_pets' => Pet::join('pet_templates', 'pets.pet_template_id', '=', 'pet_templates.id')
                                     ->selectRaw('pet_templates.name, COUNT(*) as owned_count')
                                     ->groupBy('pet_templates.id', 'pet_templates.name')
                                     ->orderByDesc('owned_count')
                                     ->limit(10)
                                     ->get(),
            'recent_purchases' => Pet::with(['template', 'user'])
                                    ->orderBy('created_at', 'desc')
                                    ->limit(10)
                                    ->get()
        ];

        return response()->json([
            'success' => true,
            'statistics' => $stats
        ]);
    }

    /**
     * Bulk update pet templates
     */
    public function bulkUpdateTemplates(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'template_ids' => 'required|array',
            'template_ids.*' => 'integer|exists:pet_templates,id',
            'updates' => 'required|array',
            'updates.is_active' => 'boolean',
            'updates.coin_cost' => 'integer|min:0',
            'updates.gem_cost' => 'integer|min:0',
            'updates.sort_order' => 'integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $updatedCount = PetTemplate::whereIn('id', $request->template_ids)
                                      ->update($request->updates);

            return response()->json([
                'success' => true,
                'message' => "Successfully updated {$updatedCount} pet templates",
                'updated_count' => $updatedCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to bulk update templates: ' . $e->getMessage()
            ], 500);
        }
    }

    private function getTemplateStatistics(): array
    {
        return [
            'total_templates' => PetTemplate::count(),
            'active_templates' => PetTemplate::where('is_active', true)->count(),
            'by_category' => PetTemplate::selectRaw('category, COUNT(*) as count')
                                       ->groupBy('category')
                                       ->pluck('count', 'category'),
            'by_rarity' => PetTemplate::selectRaw('rarity, COUNT(*) as count')
                                     ->groupBy('rarity')
                                     ->pluck('count', 'rarity')
        ];
    }
}
```

## Mystery Box Admin Controller

### MysteryBoxAdminController
```php
<?php
// File: api/app/Http/Controllers/Admin/MysteryBoxAdminController.php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\MysteryBoxType;
use App\Models\MysteryBoxOpening;
use App\Models\MysteryBoxUnlock;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class MysteryBoxAdminController extends Controller
{
    /**
     * Get all mystery box types
     */
    public function getBoxTypes(Request $request): JsonResponse
    {
        $perPage = $request->query('per_page', 20);
        $search = $request->query('search');
        
        $query = MysteryBoxType::withCount(['unlocks', 'openings']);

        if ($search) {
            $query->where('display_name', 'ILIKE', "%{$search}%")
                  ->orWhere('box_type', 'ILIKE', "%{$search}%");
        }

        $boxTypes = $query->orderBy('sort_order')
                         ->orderBy('created_at', 'desc')
                         ->paginate($perPage);

        return response()->json([
            'success' => true,
            'box_types' => $boxTypes->items(),
            'pagination' => [
                'current_page' => $boxTypes->currentPage(),
                'last_page' => $boxTypes->lastPage(),
                'per_page' => $boxTypes->perPage(),
                'total' => $boxTypes->total()
            ],
            'statistics' => $this->getBoxTypeStatistics()
        ]);
    }

    /**
     * Create new mystery box type
     */
    public function createBoxType(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'box_type' => 'required|string|max:50|unique:mystery_box_types,box_type',
            'display_name' => 'required|string|max:100',
            'rarity' => 'required|in:common,rare,epic,legendary,mythic',
            'category' => 'required|in:shadow,undead,demon,spirit,beast,universal',
            'description' => 'required|string|max:500',
            'image_url' => 'required|string|max:255',
            'animation_url' => 'nullable|string|max:255',
            'coin_cost' => 'required|integer|min:0',
            'gem_cost' => 'required|integer|min:0',
            'achievement_points_cost' => 'required|integer|min:0',
            'possible_rewards' => 'required|array',
            'possible_rewards.*' => 'string|exists:collectible_templates,collectible_id',
            'reward_weights' => 'required|array',
            'reward_weights.*' => 'integer|min:1',
            'guaranteed_rarity_level' => 'required|integer|min:1|max:5',
            'unlock_requirements' => 'nullable|array',
            'is_purchasable' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $boxType = MysteryBoxType::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Mystery box type created successfully',
                'box_type' => $boxType
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create mystery box type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update mystery box type
     */
    public function updateBoxType(Request $request, int $id): JsonResponse
    {
        $boxType = MysteryBoxType::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'box_type' => 'required|string|max:50|unique:mystery_box_types,box_type,' . $id,
            'display_name' => 'required|string|max:100',
            'rarity' => 'required|in:common,rare,epic,legendary,mythic',
            'category' => 'required|in:shadow,undead,demon,spirit,beast,universal',
            'description' => 'required|string|max:500',
            'image_url' => 'required|string|max:255',
            'animation_url' => 'nullable|string|max:255',
            'coin_cost' => 'required|integer|min:0',
            'gem_cost' => 'required|integer|min:0',
            'achievement_points_cost' => 'required|integer|min:0',
            'possible_rewards' => 'required|array',
            'reward_weights' => 'required|array',
            'guaranteed_rarity_level' => 'required|integer|min:1|max:5',
            'unlock_requirements' => 'nullable|array',
            'is_purchasable' => 'boolean',
            'is_active' => 'boolean',
            'sort_order' => 'integer|min:0'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $boxType->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Mystery box type updated successfully',
                'box_type' => $boxType->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update mystery box type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get mystery box opening statistics
     */
    public function getStatistics(): JsonResponse
    {
        $stats = [
            'total_box_types' => MysteryBoxType::count(),
            'active_box_types' => MysteryBoxType::where('is_active', true)->count(),
            'total_openings' => MysteryBoxOpening::count(),
            'total_unlocks' => MysteryBoxUnlock::count(),
            'openings_by_box_type' => MysteryBoxOpening::selectRaw('box_type, COUNT(*) as count')
                                                      ->groupBy('box_type')
                                                      ->pluck('count', 'box_type'),
            'openings_by_method' => MysteryBoxOpening::selectRaw('purchase_method, COUNT(*) as count')
                                                    ->groupBy('purchase_method')
                                                    ->pluck('count', 'purchase_method'),
            'total_revenue' => [
                'coins' => MysteryBoxOpening::where('currency_used', 'coins')->sum('cost_paid'),
                'gems' => MysteryBoxOpening::where('currency_used', 'gems')->sum('cost_paid'),
                'achievement_points' => MysteryBoxOpening::where('currency_used', 'achievement_points')->sum('cost_paid')
            ],
            'rare_items_found' => MysteryBoxOpening::where('contained_rare_item', true)->count(),
            'recent_openings' => MysteryBoxOpening::with(['user', 'mysteryBoxType'])
                                                  ->orderBy('opened_at', 'desc')
                                                  ->limit(10)
                                                  ->get()
        ];

        return response()->json([
            'success' => true,
            'statistics' => $stats
        ]);
    }

    private function getBoxTypeStatistics(): array
    {
        return [
            'total_types' => MysteryBoxType::count(),
            'active_types' => MysteryBoxType::where('is_active', true)->count(),
            'by_category' => MysteryBoxType::selectRaw('category, COUNT(*) as count')
                                          ->groupBy('category')
                                          ->pluck('count', 'category'),
            'by_rarity' => MysteryBoxType::selectRaw('rarity, COUNT(*) as count')
                                        ->groupBy('rarity')
                                        ->pluck('count', 'rarity')
        ];
    }
}
```

## Acceptance Criteria
- [ ] Pet template management fully functional
- [ ] Mystery box type administration working
- [ ] User pet management operational
- [ ] Bulk operations implemented
- [ ] Statistics and analytics available
- [ ] Proper validation and error handling
- [ ] Admin permissions enforced

## Next Steps
1. Create collection admin controllers
2. Implement background task management
3. Set up comprehensive monitoring
4. Create admin dashboard frontend

## Troubleshooting
- Ensure admin middleware is properly configured
- Validate all admin operations with proper permissions
- Test bulk operations with large datasets
- Monitor admin action logs for security
- Verify statistics calculations are accurate
