import axios from 'axios';
import { useAuth } from './useAuth';

export function useApi() {
    const { user } = useAuth();

    const instance = axios.create({
        baseURL: import.meta.env.VITE_API_URL,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': user?.token ? `Bearer ${user.token}` : ''
        }
    });

    return {
        get: instance.get,
        post: instance.post,
        put: instance.put,
        delete: instance.delete
    };
}