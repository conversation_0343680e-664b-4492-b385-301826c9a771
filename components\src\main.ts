import { GameInstance } from '../../games/registry';
import { resourceManager } from '../../games/resource-manager';

/**
 * Initialize and return a Slash Game instance
 * @param options Game options from GameWrapper
 * @returns GameInstance compatible with the registry
 */
export const slashGame = (options: any): GameInstance => {
  // Game instance reference
  let gameInstance: any = null;
  let canvasElement: HTMLCanvasElement | null = null;
  let gameInitialized = false;
  let scriptsLoaded = false;

  // Create GameInstance wrapper
  const gameInstanceWrapper: GameInstance = {
    // Load assets and initialize game
    load: (onReady, onProgress) => {
      // First load all required scripts
      loadGameScripts()
        .then(() => {
          try {
            // Get the canvas element
            canvasElement = document.getElementById(options.canvasId) as HTMLCanvasElement;
            if (!canvasElement) {
              console.error(`Canvas element with ID ${options.canvasId} not found`);
              onProgress({
                success: 0,
                total: 1,
                failed: 1
              });
              return;
            }

            // Check if required classes are available
            if (!(window as any).Game) {
              console.error('Game class not found in window object');
              onProgress({
                success: 0,
                total: 1,
                failed: 1
              });
              return;
            }

            // Create game instance
            gameInstance = new (window as any).Game(canvasElement);

            // Store integration callbacks in the game for state access
            if (options.setGameScore && gameInstance.core) {
              gameInstance.core.setVariable = gameInstance.core.setVariable || function(this: any, name: string, value: any) {
                this[name] = value;
              };
              gameInstance.core.setVariable('setGameScore', options.setGameScore);
            }
            if (options.onGameOver && gameInstance.core) {
              gameInstance.core.setVariable = gameInstance.core.setVariable || function(this: any, name: string, value: any) {
                this[name] = value;
              };
              gameInstance.core.setVariable('onGameOver', options.onGameOver);
            }

            // Override game over method to integrate with React
            if (gameInstance.core) {
              const originalGameOver = gameInstance.core.gameOver.bind(gameInstance.core);
              gameInstance.core.gameOver = function() {
                // Get final score (coins collected)
                const finalScore = this.playerOptions?.lifetimeCoins || 0;

                // Call the integration callback
                if (options.setGameScore) {
                  options.setGameScore(finalScore);
                }
                if (options.onGameOver) {
                  options.onGameOver();
                }

                // Call original game over
                originalGameOver();
              };
            }

            gameInitialized = true;
            onReady();
          } catch (error) {
            console.error('Error initializing Slash game:', error);
            onProgress({
              success: 0,
              total: 1,
              failed: 1
            });
          }
        })
        .catch((error) => {
          console.error('Failed to load Slash Game scripts:', error);
          onProgress({
            success: 0,
            total: 1,
            failed: 1
          });
        });

      // Report loading progress
      const updateProgress = () => {
        const progress = getLoadingProgress();
        onProgress({
          success: progress.loaded,
          total: progress.total,
          failed: 0
        });

        if (progress.loaded < progress.total) {
          requestAnimationFrame(updateProgress);
        }
      };

      updateProgress();
    },

    // Start game
    start: () => {
      if (gameInstance && gameInitialized) {
        try {
          gameInstance.start();
        } catch (error) {
          console.error('Error starting Slash game:', error);
        }
      }
    },

    // Destroy game
    destroy: () => {
      if (!gameInstance) return;

      try {
        // Stop the game
        gameInstance.stop();

        // Clean up game core
        if (gameInstance.core) {
          gameInstance.core.cleanUp();

          // Clear any timers
          if (gameInstance.core.gameTimer) {
            clearInterval(gameInstance.core.gameTimer);
          }
        }

        // Clear canvas
        if (canvasElement) {
          const ctx = canvasElement.getContext('2d');
          if (ctx) {
            ctx.clearRect(0, 0, canvasElement.width, canvasElement.height);
          }
        }

        // Release resources through the resource manager
        resourceManager.releaseResources('slash');
      } catch (error) {
        console.error('Error destroying Slash game:', error);
      }

      // Clear references
      gameInstance = null;
      gameInitialized = false;
    },

    // Reset game state
    resetGameState: () => {
      if (!gameInstance || !gameInstance.core) return;

      try {
        gameInstance.core.restart();
      } catch (error) {
        console.error('Error resetting Slash game state:', error);
      }
    },

    // Clear event listeners
    clearEventListeners: () => {
      if (!gameInstance || !gameInstance.core) return;

      try {
        // Remove event listeners
        if (gameInstance.core.input) {
          // Clear input handlers
          window.removeEventListener('keydown', gameInstance.core.input.keyDownHandler);
          window.removeEventListener('keyup', gameInstance.core.input.keyUpHandler);
        }

        if (canvasElement && gameInstance.core.joystick) {
          canvasElement.removeEventListener('touchstart', gameInstance.core.joystick.touchStartHandler);
          canvasElement.removeEventListener('touchend', gameInstance.core.joystick.touchEndHandler);
          canvasElement.removeEventListener('mousedown', gameInstance.core.joystick.mouseDownHandler);
          canvasElement.removeEventListener('mouseup', gameInstance.core.joystick.mouseUpHandler);
        }
      } catch (error) {
        console.error('Error clearing Slash game event listeners:', error);
      }
    }
  };

  /**
   * Load all required game scripts
   */
  const loadGameScripts = (): Promise<void> => {
    if (scriptsLoaded) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const scripts = [
        // Load enums first
        '/battlx/src/slash_game/js/enums/characterType.js',
        '/battlx/src/slash_game/js/enums/destructibleType.js',
        '/battlx/src/slash_game/js/enums/enemyType.js',
        '/battlx/src/slash_game/js/enums/fixedTreasures.js',
        '/battlx/src/slash_game/js/enums/npcType.js',
        '/battlx/src/slash_game/js/enums/pickupType.js',
        '/battlx/src/slash_game/js/enums/sceneType.js',
        '/battlx/src/slash_game/js/enums/treasureType.js',
        '/battlx/src/slash_game/js/enums/weaponType.js',

        // Load constants
        '/battlx/src/slash_game/js/consts/characters.js',
        '/battlx/src/slash_game/js/consts/destructibles.js',
        '/battlx/src/slash_game/js/consts/enemies.js',
        '/battlx/src/slash_game/js/consts/enemySpawnConfig.js',
        '/battlx/src/slash_game/js/consts/pickups.js',
        '/battlx/src/slash_game/js/consts/stages.js',
        '/battlx/src/slash_game/js/consts/weapons.js',

        // Load utilities and core classes
        '/battlx/src/slash_game/js/src/utils.js',
        '/battlx/src/slash_game/js/src/vector2.js',
        '/battlx/src/slash_game/js/src/containmentRect.js',
        '/battlx/src/slash_game/js/src/bgManager.js',
        '/battlx/src/slash_game/js/src/sceneManager.js',
        '/battlx/src/slash_game/js/src/stage.js',
        '/battlx/src/slash_game/js/src/ui.js',

        // Load game objects
        '/battlx/src/slash_game/js/src/player.js',
        '/battlx/src/slash_game/js/src/enemy.js',
        '/battlx/src/slash_game/js/src/enemyProjectile.js',
        '/battlx/src/slash_game/js/src/weapon.js',
        '/battlx/src/slash_game/js/src/pickup.js',
        '/battlx/src/slash_game/js/src/destructible.js',

        // Load game core and main game class
        '/battlx/src/slash_game/js/src/gameCore.js',
        '/battlx/src/slash_game/js/src/game.js'
      ];

      let loadedCount = 0;

      const loadScript = (src: string): Promise<void> => {
        return new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.src = src;
          script.onload = () => {
            loadedCount++;
            resolve();
          };
          script.onerror = () => {
            console.error(`Failed to load script: ${src}`);
            reject(new Error(`Failed to load script: ${src}`));
          };
          document.head.appendChild(script);
        });
      };

      // Load scripts sequentially to maintain dependencies
      const loadSequentially = async () => {
        try {
          for (const script of scripts) {
            await loadScript(script);
          }
          scriptsLoaded = true;
          resolve();
        } catch (error) {
          reject(error);
        }
      };

      loadSequentially();
    });
  };

  /**
   * Get loading progress
   */
  const getLoadingProgress = () => {
    // Simple progress tracking
    return {
      loaded: scriptsLoaded ? 1 : 0,
      total: 1
    };
  };

  return gameInstanceWrapper;
};