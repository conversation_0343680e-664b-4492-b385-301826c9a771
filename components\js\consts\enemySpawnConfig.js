// Enemy spawn configuration
const EnemySpawnConfig = {
    // Base spawn settings
    BASE_SETTINGS: {
        INITIAL_MIN_ENEMIES: 3,      // Start with a few enemies on screen
        INITIAL_MAX_ENEMIES: 5,      // Initial cap for early game
        MAX_TOTAL_ENEMIES: 50,       // Maximum enemies on screen at late game (increased from 30)

        // Spawn rate settings
        BASE_SPAWN_INTERVAL: 5000,   // 5 seconds between spawns initially (slower start)
        MIN_SPAWN_INTERVAL: 500,     // 0.5 seconds between spawns at fastest

        // Scaling factors
        TIME_SCALING: {
            ENEMIES_PER_MINUTE: 3,   // Add 3 to max enemies each minute (increased from 2)
            SPAWN_INTERVAL_REDUCTION_PER_MINUTE: 300, // Reduce spawn time by 300ms each minute (faster scaling)
        },

        PLAYER_LEVEL_SCALING: {
            ENEMIES_PER_LEVEL: 2,    // Each player level adds 2 max enemies (increased from 1)
            MAX_LEVEL_SCALING: 20    // Cap level-based scaling at 20 additional enemies (increased from 10)
        },

        // Phase-specific settings
        PHASE_SETTINGS: [
            { // Phase 1 (0-2 minutes): Introduction
                MIN_ENEMIES: 3,
                MAX_ENEMIES: 5,
                SPAWN_INTERVAL: 5000
            },
            { // Phase 2 (2-5 minutes): Building Tension
                MIN_ENEMIES: 5,
                MAX_ENEMIES: 10,
                SPAWN_INTERVAL: 4000
            },
            { // Phase 3 (5-8 minutes): Increasing Challenge
                MIN_ENEMIES: 8,
                MAX_ENEMIES: 15,
                SPAWN_INTERVAL: 3000
            },
            { // Phase 4 (8-10 minutes): Mini-Boss Phase
                MIN_ENEMIES: 12,
                MAX_ENEMIES: 20,
                SPAWN_INTERVAL: 2500
            },
            { // Phase 5 (10-15 minutes): Advanced Challenge
                MIN_ENEMIES: 15,
                MAX_ENEMIES: 25,
                SPAWN_INTERVAL: 2000
            },
            { // Phase 6 (15-20 minutes): Boss Phase
                MIN_ENEMIES: 20,
                MAX_ENEMIES: 30,
                SPAWN_INTERVAL: 1500
            },
            { // Phase 7 (20-25 minutes): Chaos
                MIN_ENEMIES: 25,
                MAX_ENEMIES: 40,
                SPAWN_INTERVAL: 1000
            },
            { // Phase 8 (25+ minutes): Infinite Challenge
                MIN_ENEMIES: 30,
                MAX_ENEMIES: 50,
                SPAWN_INTERVAL: 800
            }
        ]
    },

    // Enemy type limits (maximum of each type at once)
    TYPE_LIMITS: typeof EnemyType !== 'undefined' ? {
        // Common enemies
        [EnemyType.GHOST1]: 12,   // Common enemy, high limit
        [EnemyType.GHOST2]: 12,   // Medium difficulty, high limit
        [EnemyType.GHOST3]: 14,   // Harder enemy, high limit
        [EnemyType.BAT]: 14,      // Fast enemy, high limit

        // Special enemies
        [EnemyType.OGRE]: 4,      // Tanky enemy, low limit
        [EnemyType.HORN]: 2,      // Strong enemy, very low limit
        [EnemyType.GHOST_BOSS1]: 1, // Boss limit set to 1 (only one boss at a time)
        [EnemyType.TELEPORTER]: 3, // Teleporter enemy, moderate limit

        // Zombie enemies
        [EnemyType.ZOMBIE1]: 10,  // Mid-tier enemy, moderate limit
        [EnemyType.ZOMBIE2]: 8,   // Advanced enemy, low limit

        // Swarm enemies
        [EnemyType.BAT_SWARM]: 18, // Bat swarm, high limit for swarm events
        [EnemyType.GHOST_SWARM]: 15 // Ghost swarm, high limit for swarm events
    } : {},

    // Enemy type weights by phase (chance of spawning)
    TYPE_WEIGHTS: typeof EnemyType !== 'undefined' ? {
        0: { // Phase 1 (0-2 minutes): Introduction
            [EnemyType.GHOST1]: 80,  // Mostly basic enemies
            [EnemyType.BAT]: 15,     // Some fast enemies
            [EnemyType.TELEPORTER]: 5 // Few teleporter enemies
        },
        1: { // Phase 2 (2-5 minutes): Building Tension
            [EnemyType.GHOST1]: 50,  // Half basic enemies
            [EnemyType.GHOST2]: 20,  // Introduce medium difficulty
            [EnemyType.BAT]: 20,     // Consistent fast enemies
            [EnemyType.TELEPORTER]: 10 // More teleporter enemies
        },
        2: { // Phase 3 (5-8 minutes): Increasing Challenge
            [EnemyType.GHOST1]: 30,  // Fewer basic enemies
            [EnemyType.GHOST2]: 25,  // More medium difficulty
            [EnemyType.GHOST3]: 15,  // Introduce harder enemies
            [EnemyType.BAT]: 20,     // Consistent fast enemies
            [EnemyType.TELEPORTER]: 10 // Consistent teleporter enemies
        },
        3: { // Phase 4 (8-10 minutes): Mini-Boss Phase
            [EnemyType.GHOST2]: 25,  // Medium difficulty enemies
            [EnemyType.GHOST3]: 20,  // Harder enemies
            [EnemyType.BAT]: 20,     // Fast enemies
            [EnemyType.ZOMBIE1]: 15, // Introduce zombies
            [EnemyType.OGRE]: 10,    // Introduce tanky enemies
            [EnemyType.TELEPORTER]: 10 // Consistent teleporter enemies
        },
        4: { // Phase 5 (10-15 minutes): Advanced Challenge
            [EnemyType.GHOST2]: 20,  // Medium difficulty enemies
            [EnemyType.GHOST3]: 20,  // Harder enemies
            [EnemyType.BAT]: 15,     // Fast enemies
            [EnemyType.ZOMBIE1]: 15, // Zombies
            [EnemyType.ZOMBIE2]: 15, // Introduce advanced zombies
            [EnemyType.OGRE]: 10,    // Tanky enemies
            [EnemyType.TELEPORTER]: 5 // Fewer teleporter enemies
        },
        5: { // Phase 6 (15-20 minutes): Boss Phase
            [EnemyType.GHOST3]: 20,  // Harder enemies
            [EnemyType.BAT]: 15,     // Fast enemies
            [EnemyType.ZOMBIE1]: 15, // Zombies
            [EnemyType.ZOMBIE2]: 15, // Advanced zombies
            [EnemyType.OGRE]: 15,    // More tanky enemies
            [EnemyType.HORN]: 10,    // Introduce elite enemies
            [EnemyType.TELEPORTER]: 10 // More teleporter enemies
        },
        6: { // Phase 7 (20-25 minutes): Chaos
            [EnemyType.GHOST3]: 15,  // Harder enemies
            [EnemyType.BAT]: 15,     // Fast enemies
            [EnemyType.ZOMBIE1]: 15, // Zombies
            [EnemyType.ZOMBIE2]: 15, // Advanced zombies
            [EnemyType.OGRE]: 15,    // Tanky enemies
            [EnemyType.HORN]: 15,    // More elite enemies
            [EnemyType.TELEPORTER]: 10 // Consistent teleporter enemies
        },
        7: { // Phase 8 (25+ minutes): Infinite Challenge
            [EnemyType.GHOST3]: 15,  // Harder enemies
            [EnemyType.BAT]: 15,     // Fast enemies
            [EnemyType.ZOMBIE1]: 15, // Zombies
            [EnemyType.ZOMBIE2]: 15, // Advanced zombies
            [EnemyType.OGRE]: 15,    // Tanky enemies
            [EnemyType.HORN]: 15,    // Elite enemies
            [EnemyType.TELEPORTER]: 10 // Consistent teleporter enemies
        }
    } : {
        0: {}, 1: {}, 2: {}, 3: {}, 4: {}, 5: {}, 6: {}, 7: {}
    },

};

// Attach to window object for global access
window.EnemySpawnConfig = EnemySpawnConfig;