/* Global mobile consistency - unified responsive design */
:global(.modal-body) {
  position: relative;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  max-width: min(100vw, 414px);
  box-sizing: border-box;
  margin: 0 auto;
  min-height: 0; /* Allow content to shrink */
}

.missionCardsSwiper {
  padding: 1.25rem 0 7.5rem 0; /* 20px 0 120px 0 in rem */
  width: 100%;
  height: clamp(350px, 23.75rem, 400px); /* Responsive height between 350px-400px */
  margin: 0 auto;
  overflow: visible;
  position: relative;
  max-width: min(100vw, 414px);
  box-sizing: border-box;
}

/* Using global to affect Swiper's internal classes - unified responsive sizing */
:global(.swiper-slide) {
  background-position: center;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  width: clamp(200px, 60vw, 260px) !important; /* Responsive width that scales with viewport */
  transition: transform 0.3s ease, opacity 0.3s ease;
}

/* Coverflow specific styles */
:global(.swiper-slide-active) {
  transform: scale(1.1) !important;
  z-index: 10 !important;
}

:global(.swiper-slide-next),
:global(.swiper-slide-prev) {
  opacity: 0.7;
  transform: scale(0.9);
}

/* Pagination styling */
:global(.swiper-pagination) {
  bottom: 10px !important;
}

:global(.swiper-pagination-bullet) {
  background: rgba(155, 139, 108, 0.5) !important;
  opacity: 1 !important;
}

:global(.swiper-pagination-bullet-active) {
  background: #9B8B6C !important;
  transform: scale(1.2);
}

.gameCard {
  /* Modern gaming card styling - responsive design */
  background: linear-gradient(145deg, #1A1617 0%, #2A1F1F 50%, #4A0E0E 100%);
  border: 2px solid transparent;
  border-radius: 1rem; /* 16px */
  position: relative;
  isolation: isolate;
  width: clamp(190px, 55vw, 240px); /* Responsive width that scales with viewport */
  height: clamp(290px, 80vw, 340px); /* Responsive height that scales with viewport */
  padding: clamp(0.625rem, 3vw, 0.875rem); /* Responsive padding 10px-14px */
  display: flex;
  flex-direction: column;
  user-select: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
  overflow: hidden;

  /* Premium gaming card effects */
  box-shadow:
    0 0 0 1px rgba(155, 139, 108, 0.3),
    0 0.5rem 2rem rgba(0, 0, 0, 0.6),
    0 0.25rem 1rem rgba(74, 14, 14, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.gameCard::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg,
    rgba(155, 139, 108, 0.4) 0%,
    transparent 25%,
    transparent 75%,
    rgba(155, 139, 108, 0.4) 100%);
  border-radius: 16px;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* Gaming card glow effect on hover/active */
.gameCard::after {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(45deg,
    rgba(155, 139, 108, 0.6),
    rgba(74, 14, 14, 0.6),
    rgba(155, 139, 108, 0.6));
  border-radius: 18px;
  z-index: -2;
  opacity: 0;
  filter: blur(8px);
  transition: opacity 0.3s ease;
}

.cardImage {
  position: relative;
  width: 100%;
  height: clamp(150px, 45vw, 200px); /* Responsive height that scales with viewport */
  margin-bottom: 0.5rem; /* 8px in rem */
  border-radius: 0.75rem; /* 12px */
  overflow: hidden;
  background: linear-gradient(135deg, #0A0A0A 0%, #1A1617 100%);

  /* Modern gaming image frame */
  border: 2px solid rgba(155, 139, 108, 0.3);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 0.25rem 0.75rem rgba(0, 0, 0, 0.4);
}

.cardImage img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(1.1) contrast(1.1) saturate(1.2);
}

/* Image overlay for premium effect */
.cardImage::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(155, 139, 108, 0.1) 0%,
    transparent 50%,
    rgba(74, 14, 14, 0.1) 100%);
  border-radius: 10px;
  z-index: 1;
  pointer-events: none;
}

/* Modern gaming hover effects */
.gameCard:hover .cardImage img {
  transform: scale(1.08);
  filter: brightness(1.2) contrast(1.2) saturate(1.3);
}

.gameCard:hover::before {
  opacity: 1;
}

.gameCard:hover::after {
  opacity: 0.8;
}

/* Enhanced 3D hover effect for active slide */
:global(.swiper-slide-active) .gameCard:hover {
  transform: translateY(-8px) scale(1.03);
  box-shadow:
    0 0 0 2px rgba(155, 139, 108, 0.6),
    0 16px 40px rgba(0, 0, 0, 0.6),
    0 8px 24px rgba(74, 14, 14, 0.5),
    inset 0 2px 0 rgba(255, 255, 255, 0.2);
}

/* Active slide glow effect */
:global(.swiper-slide-active) .gameCard::before {
  opacity: 0.6;
}

.cardHeader {
  text-align: center;
  margin-bottom: 6px;
  padding: 0 8px;
  position: relative;
  z-index: 2;
}

.cardHeader h3 {
  font-size: clamp(0.875rem, 4vw, 1.125rem) !important; /* Responsive 14px-18px */
  font-weight: 700 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.03125rem; /* 0.5px */
  line-height: 1.2;
}

.cardContent {
  padding: 0 0.5rem; /* 8px */
  margin-bottom: 0.5rem; /* 8px */
  position: relative;
  z-index: 2;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.cardContent p {
  font-size: clamp(0.625rem, 3vw, 0.75rem) !important; /* Responsive 10px-12px */
  font-weight: 600 !important;
  text-transform: uppercase;
  letter-spacing: 0.05rem; /* 0.8px */
  margin-bottom: 0.25rem !important; /* 4px */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.cardFooter {
  margin-top: auto;
  padding: 8px;
  border-top: 1px solid rgba(155, 139, 108, 0.2);
  background: linear-gradient(90deg,
    rgba(26, 22, 23, 0.8) 0%,
    rgba(74, 14, 14, 0.3) 50%,
    rgba(26, 22, 23, 0.8) 100%);
  border-radius: 0 0 12px 12px;
  position: relative;
  z-index: 2;
}

/* Active card animation */
.gameCard:active {
  transform: scale(0.96);
  opacity: 0.95;
  box-shadow:
    0 0 0 1px rgba(155, 139, 108, 0.8),
    0 4px 16px rgba(0, 0, 0, 0.8),
    inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Premium gaming card shine effect */
.gameCard::before {
  background-size: 200% 200%;
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { background-position: 0% 0%; }
  50% { background-position: 100% 100%; }
}

/* Smooth transitions for non-active slides */
:global(.swiper-slide:not(.swiper-slide-active)) .gameCard {
  filter: brightness(0.8);
}

/* Unified responsive design - no device-specific media queries needed */
/* All sizing is now handled by clamp() functions for consistent display across all devices */