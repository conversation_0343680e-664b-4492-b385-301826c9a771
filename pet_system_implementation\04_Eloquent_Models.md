# Eloquent Models Implementation

## Overview
This document covers the creation of Eloquent models for the Pet System, including relationships, accessors, mutators, and business logic methods.

## Implementation Time: 2-3 days
## Complexity: Medium-High
## Dependencies: Database schema and seeders completed

## Core Pet Models

### PetTemplate Model
```php
<?php
// File: api/app/Models/PetTemplate.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PetTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'category', 'rarity', 'description', 'image_url', 'animation_url',
        'coin_cost', 'gem_cost', 'prize_tree_unlock_level', 'is_premium_only',
        'base_happiness', 'max_happiness', 'happiness_decay_rate',
        'mystery_box_unlocks', 'collectible_reward_id', 'max_level',
        'evolution_levels', 'evolution_images', 'is_active', 'sort_order'
    ];

    protected $casts = [
        'mystery_box_unlocks' => 'array',
        'evolution_levels' => 'array',
        'evolution_images' => 'array',
        'is_premium_only' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function pets(): HasMany
    {
        return $this->hasMany(Pet::class);
    }

    public function collectibleReward()
    {
        return $this->belongsTo(CollectibleTemplate::class, 'collectible_reward_id', 'collectible_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByRarity($query, $rarity)
    {
        return $query->where('rarity', $rarity);
    }

    public function scopeAvailableForPurchase($query)
    {
        return $query->where('is_active', true)
                    ->where(function($q) {
                        $q->where('coin_cost', '>', 0)
                          ->orWhere('gem_cost', '>', 0);
                    });
    }

    // Accessors
    public function getImageUrlAttribute($value)
    {
        return $value ? asset($value) : null;
    }

    public function getAnimationUrlAttribute($value)
    {
        return $value ? asset($value) : null;
    }

    public function getRarityColorAttribute()
    {
        return match($this->rarity) {
            'common' => '#9CA3AF',
            'rare' => '#3B82F6',
            'epic' => '#8B5CF6',
            'legendary' => '#F59E0B',
            'mythic' => '#EF4444',
            default => '#6B7280'
        };
    }

    // Business Logic Methods
    public function canBePurchasedBy(TelegramUser $user): bool
    {
        // Check if user already owns this pet
        if ($user->pets()->where('pet_template_id', $this->id)->exists()) {
            return false;
        }

        // Check prize tree unlock requirement
        if ($this->prize_tree_unlock_level) {
            $userProgress = $user->prizeTreeProgress();
            if (!$userProgress || $userProgress->current_level < $this->prize_tree_unlock_level) {
                return false;
            }
        }

        return $this->is_active;
    }

    public function getPurchaseCost(string $method): int
    {
        return match($method) {
            'coins' => $this->coin_cost,
            'gems' => $this->gem_cost,
            default => 0
        };
    }

    public function getEvolutionImageForLevel(int $level): ?string
    {
        $evolutionLevels = $this->evolution_levels ?? [];
        $evolutionImages = $this->evolution_images ?? [];
        
        $evolutionStage = 0;
        foreach ($evolutionLevels as $index => $requiredLevel) {
            if ($level >= $requiredLevel) {
                $evolutionStage = $index + 1;
            }
        }
        
        return $evolutionImages[$evolutionStage] ?? $this->image_url;
    }

    public function getNextEvolutionLevel(int $currentLevel): ?int
    {
        $evolutionLevels = $this->evolution_levels ?? [];
        
        foreach ($evolutionLevels as $level) {
            if ($currentLevel < $level) {
                return $level;
            }
        }
        
        return null;
    }
}
```

### Pet Model
```php
<?php
// File: api/app/Models/Pet.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Pet extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id', 'pet_template_id', 'level', 'experience', 'happiness',
        'last_fed', 'last_played', 'last_petted', 'daily_interaction_count',
        'daily_interaction_reset_date', 'is_featured', 'is_favorite', 'nickname',
        'evolution_stage', 'last_evolution'
    ];

    protected $casts = [
        'last_fed' => 'datetime',
        'last_played' => 'datetime',
        'last_petted' => 'datetime',
        'last_evolution' => 'datetime',
        'daily_interaction_reset_date' => 'date',
        'is_featured' => 'boolean',
        'is_favorite' => 'boolean',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(PetTemplate::class, 'pet_template_id');
    }

    public function interactions(): HasMany
    {
        return $this->hasMany(PetInteraction::class);
    }

    public function happinessLogs(): HasMany
    {
        return $this->hasMany(PetHappinessLog::class);
    }

    // Scopes
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeNeedingAttention($query)
    {
        return $query->where('happiness', '<', 30);
    }

    public function scopeReadyForEvolution($query)
    {
        return $query->whereRaw('level >= (
            SELECT JSON_EXTRACT(evolution_levels, "$[" || pets.evolution_stage || "]")
            FROM pet_templates 
            WHERE pet_templates.id = pets.pet_template_id
            AND JSON_ARRAY_LENGTH(evolution_levels) > pets.evolution_stage
        )');
    }

    // Accessors
    public function getDisplayNameAttribute(): string
    {
        return $this->nickname ?: $this->template->name;
    }

    public function getCurrentImageAttribute(): string
    {
        return $this->template->getEvolutionImageForLevel($this->level);
    }

    public function getHappinessPercentageAttribute(): float
    {
        return round(($this->happiness / $this->template->max_happiness) * 100, 1);
    }

    public function getExperienceToNextLevelAttribute(): int
    {
        return $this->calculateExperienceRequired($this->level + 1) - $this->experience;
    }

    public function getCanEvolveAttribute(): bool
    {
        $nextEvolutionLevel = $this->template->getNextEvolutionLevel($this->level);
        return $nextEvolutionLevel && $this->level >= $nextEvolutionLevel;
    }

    // Interaction Methods
    public function canInteract(string $interactionType): bool
    {
        $this->resetDailyInteractionsIfNeeded();
        
        $limits = [
            'feed' => 5,
            'play' => 3,
            'pet' => 10,
        ];
        
        $cooldowns = [
            'feed' => 60,    // minutes
            'play' => 120,   // minutes
            'pet' => 30,     // minutes
        ];
        
        // Check daily limit
        $todayInteractions = $this->interactions()
            ->where('interaction_type', $interactionType)
            ->whereDate('interaction_time', today())
            ->count();
            
        if ($todayInteractions >= ($limits[$interactionType] ?? 0)) {
            return false;
        }
        
        // Check cooldown
        $lastInteraction = $this->interactions()
            ->where('interaction_type', $interactionType)
            ->latest('interaction_time')
            ->first();
            
        if ($lastInteraction) {
            $cooldownMinutes = $cooldowns[$interactionType] ?? 0;
            $cooldownExpires = $lastInteraction->interaction_time->addMinutes($cooldownMinutes);
            
            if (now() < $cooldownExpires) {
                return false;
            }
        }
        
        return true;
    }

    public function interact(string $interactionType, TelegramUser $user): array
    {
        if (!$this->canInteract($interactionType)) {
            throw new \Exception("Cannot perform {$interactionType} interaction at this time");
        }
        
        $interactionConfig = $this->getInteractionConfig($interactionType);
        
        // Check if user has enough energy
        if ($user->available_energy < $interactionConfig['energy_cost']) {
            throw new \Exception("Not enough energy for this interaction");
        }
        
        // Calculate rewards
        $happinessBonus = $this->happiness >= 80 ? 2.0 : 1.0; // Happy pet bonus
        $rewards = [
            'coins' => (int)($interactionConfig['coins'] * $happinessBonus),
            'experience' => $interactionConfig['experience'],
            'happiness' => $interactionConfig['happiness'],
            'materials' => 0,
            'collectible' => null
        ];
        
        // Random bonus rewards
        if (rand(1, 100) <= $interactionConfig['material_chance']) {
            $rewards['materials'] = 1;
        }
        
        if (rand(1, 100) <= $interactionConfig['collectible_chance']) {
            $rewards['collectible'] = $this->getRandomCollectible();
        }
        
        // Apply changes
        $this->updateFromInteraction($interactionConfig, $rewards);
        $user->decrement('available_energy', $interactionConfig['energy_cost']);
        $user->increment('balance', $rewards['coins']);
        
        // Log interaction
        $this->interactions()->create([
            'telegram_user_id' => $user->id,
            'interaction_type' => $interactionType,
            'energy_cost' => $interactionConfig['energy_cost'],
            'happiness_gained' => $rewards['happiness'],
            'experience_gained' => $rewards['experience'],
            'coins_rewarded' => $rewards['coins'],
            'materials_rewarded' => $rewards['materials'],
            'collectible_rewarded' => $rewards['collectible'],
            'bonus_applied' => $happinessBonus > 1,
            'interaction_time' => now()
        ]);
        
        // Log happiness change
        $this->logHappinessChange($rewards['happiness'], "interaction_{$interactionType}");
        
        return $rewards;
    }

    private function getInteractionConfig(string $type): array
    {
        return match($type) {
            'feed' => [
                'energy_cost' => 5,
                'happiness' => 20,
                'experience' => 10,
                'coins' => 50,
                'material_chance' => 30,
                'collectible_chance' => 0
            ],
            'play' => [
                'energy_cost' => 10,
                'happiness' => 30,
                'experience' => 15,
                'coins' => 100,
                'material_chance' => 50,
                'collectible_chance' => 10
            ],
            'pet' => [
                'energy_cost' => 2,
                'happiness' => 10,
                'experience' => 5,
                'coins' => 25,
                'material_chance' => 0,
                'collectible_chance' => 0
            ],
            default => throw new \Exception("Unknown interaction type: {$type}")
        };
    }

    private function updateFromInteraction(array $config, array $rewards): void
    {
        $oldHappiness = $this->happiness;
        
        $this->happiness = min(
            $this->template->max_happiness,
            $this->happiness + $rewards['happiness']
        );
        
        $this->experience += $rewards['experience'];
        
        // Check for level up
        while ($this->canLevelUp()) {
            $this->levelUp();
        }
        
        // Update interaction timestamps
        $now = now();
        match($config) {
            'feed' => $this->last_fed = $now,
            'play' => $this->last_played = $now,
            'pet' => $this->last_petted = $now,
            default => null
        };
        
        $this->save();
    }

    private function canLevelUp(): bool
    {
        $requiredExp = $this->calculateExperienceRequired($this->level + 1);
        return $this->experience >= $requiredExp && $this->level < $this->template->max_level;
    }

    private function levelUp(): void
    {
        $this->level++;
        
        // Check for evolution
        if ($this->can_evolve) {
            $this->evolve();
        }
    }

    private function evolve(): void
    {
        $this->evolution_stage++;
        $this->last_evolution = now();
        $this->happiness = min($this->template->max_happiness, $this->happiness + 20); // Evolution happiness bonus
        
        $this->logHappinessChange(20, 'evolution_bonus');
    }

    private function calculateExperienceRequired(int $level): int
    {
        return (int)(100 * pow(1.2, $level - 1));
    }

    private function resetDailyInteractionsIfNeeded(): void
    {
        if ($this->daily_interaction_reset_date < today()) {
            $this->daily_interaction_count = 0;
            $this->daily_interaction_reset_date = today();
            $this->save();
        }
    }

    private function logHappinessChange(int $change, string $reason): void
    {
        $oldHappiness = $this->happiness - $change;
        
        $this->happinessLogs()->create([
            'happiness_before' => $oldHappiness,
            'happiness_after' => $this->happiness,
            'happiness_change' => $change,
            'change_reason' => $reason,
            'logged_at' => now()
        ]);
    }

    // Daily happiness decay (called by scheduled job)
    public function applyDailyHappinessDecay(): void
    {
        $decayAmount = $this->template->happiness_decay_rate;
        $oldHappiness = $this->happiness;
        
        $this->happiness = max(0, $this->happiness - $decayAmount);
        $this->save();
        
        if ($decayAmount > 0) {
            $this->logHappinessChange(-$decayAmount, 'daily_decay');
        }
    }
}
```

## Acceptance Criteria
- [ ] All models created with proper relationships
- [ ] Business logic methods implemented
- [ ] Interaction system working
- [ ] Evolution mechanics functional
- [ ] Happiness system operational

## Next Steps
1. Create remaining models (CollectibleTemplate, MysteryBoxType, etc.)
2. Implement API controllers
3. Create frontend components
4. Add scheduled jobs for daily tasks

## Troubleshooting
- Ensure foreign key relationships match database schema
- Test interaction limits and cooldowns
- Verify evolution level calculations
- Check happiness decay mechanics

# Eloquent Models Implementation

## Overview
This document covers the creation of Eloquent models for the Pet System, including relationships, accessors, mutators, and business logic methods.

## Implementation Time: 2-3 days
## Complexity: Medium-High
## Dependencies: Database schema and seeders completed

## Core Pet Models

### PetTemplate Model
```php
<?php
// File: api/app/Models/PetTemplate.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PetTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'category', 'rarity', 'description', 'image_url', 'animation_url',
        'coin_cost', 'gem_cost', 'prize_tree_unlock_level', 'is_premium_only',
        'base_happiness', 'max_happiness', 'happiness_decay_rate',
        'mystery_box_unlocks', 'collectible_reward_id', 'max_level',
        'evolution_levels', 'evolution_images', 'is_active', 'sort_order'
    ];

    protected $casts = [
        'mystery_box_unlocks' => 'array',
        'evolution_levels' => 'array',
        'evolution_images' => 'array',
        'is_premium_only' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function pets(): HasMany
    {
        return $this->hasMany(Pet::class);
    }

    public function collectibleReward()
    {
        return $this->belongsTo(CollectibleTemplate::class, 'collectible_reward_id', 'collectible_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByRarity($query, $rarity)
    {
        return $query->where('rarity', $rarity);
    }

    public function scopeAvailableForPurchase($query)
    {
        return $query->where('is_active', true)
                    ->where(function($q) {
                        $q->where('coin_cost', '>', 0)
                          ->orWhere('gem_cost', '>', 0);
                    });
    }

    // Accessors
    public function getImageUrlAttribute($value)
    {
        return $value ? asset($value) : null;
    }

    public function getAnimationUrlAttribute($value)
    {
        return $value ? asset($value) : null;
    }

    public function getRarityColorAttribute()
    {
        return match($this->rarity) {
            'common' => '#9CA3AF',
            'rare' => '#3B82F6',
            'epic' => '#8B5CF6',
            'legendary' => '#F59E0B',
            'mythic' => '#EF4444',
            default => '#6B7280'
        };
    }

    // Business Logic Methods
    public function canBePurchasedBy(TelegramUser $user): bool
    {
        // Check if user already owns this pet
        if ($user->pets()->where('pet_template_id', $this->id)->exists()) {
            return false;
        }

        // Check prize tree unlock requirement
        if ($this->prize_tree_unlock_level) {
            $userProgress = $user->prizeTreeProgress();
            if (!$userProgress || $userProgress->current_level < $this->prize_tree_unlock_level) {
                return false;
            }
        }

        return $this->is_active;
    }

    public function getPurchaseCost(string $method): int
    {
        return match($method) {
            'coins' => $this->coin_cost,
            'gems' => $this->gem_cost,
            default => 0
        };
    }

    public function getEvolutionImageForLevel(int $level): ?string
    {
        $evolutionLevels = $this->evolution_levels ?? [];
        $evolutionImages = $this->evolution_images ?? [];
        
        $evolutionStage = 0;
        foreach ($evolutionLevels as $index => $requiredLevel) {
            if ($level >= $requiredLevel) {
                $evolutionStage = $index + 1;
            }
        }
        
        return $evolutionImages[$evolutionStage] ?? $this->image_url;
    }

    public function getNextEvolutionLevel(int $currentLevel): ?int
    {
        $evolutionLevels = $this->evolution_levels ?? [];
        
        foreach ($evolutionLevels as $level) {
            if ($currentLevel < $level) {
                return $level;
            }
        }
        
        return null;
    }
}
```

### Pet Model
```php
<?php
// File: api/app/Models/Pet.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Pet extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id', 'pet_template_id', 'level', 'experience', 'happiness',
        'last_fed', 'last_played', 'last_petted', 'daily_interaction_count',
        'daily_interaction_reset_date', 'is_featured', 'is_favorite', 'nickname',
        'evolution_stage', 'last_evolution'
    ];

    protected $casts = [
        'last_fed' => 'datetime',
        'last_played' => 'datetime',
        'last_petted' => 'datetime',
        'last_evolution' => 'datetime',
        'daily_interaction_reset_date' => 'date',
        'is_featured' => 'boolean',
        'is_favorite' => 'boolean',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(PetTemplate::class, 'pet_template_id');
    }

    public function interactions(): HasMany
    {
        return $this->hasMany(PetInteraction::class);
    }

    public function happinessLogs(): HasMany
    {
        return $this->hasMany(PetHappinessLog::class);
    }

    // Scopes
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeNeedingAttention($query)
    {
        return $query->where('happiness', '<', 30);
    }

    public function scopeReadyForEvolution($query)
    {
        return $query->whereRaw('level >= (
            SELECT JSON_EXTRACT(evolution_levels, "$[" || pets.evolution_stage || "]")
            FROM pet_templates 
            WHERE pet_templates.id = pets.pet_template_id
            AND JSON_ARRAY_LENGTH(evolution_levels) > pets.evolution_stage
        )');
    }

    // Accessors
    public function getDisplayNameAttribute(): string
    {
        return $this->nickname ?: $this->template->name;
    }

    public function getCurrentImageAttribute(): string
    {
        return $this->template->getEvolutionImageForLevel($this->level);
    }

    public function getHappinessPercentageAttribute(): float
    {
        return round(($this->happiness / $this->template->max_happiness) * 100, 1);
    }

    public function getExperienceToNextLevelAttribute(): int
    {
        return $this->calculateExperienceRequired($this->level + 1) - $this->experience;
    }

    public function getCanEvolveAttribute(): bool
    {
        $nextEvolutionLevel = $this->template->getNextEvolutionLevel($this->level);
        return $nextEvolutionLevel && $this->level >= $nextEvolutionLevel;
    }

    // Interaction Methods
    public function canInteract(string $interactionType): bool
    {
        $this->resetDailyInteractionsIfNeeded();
        
        $limits = [
            'feed' => 5,
            'play' => 3,
            'pet' => 10,
        ];
        
        $cooldowns = [
            'feed' => 60,    // minutes
            'play' => 120,   // minutes
            'pet' => 30,     // minutes
        ];
        
        // Check daily limit
        $todayInteractions = $this->interactions()
            ->where('interaction_type', $interactionType)
            ->whereDate('interaction_time', today())
            ->count();
            
        if ($todayInteractions >= ($limits[$interactionType] ?? 0)) {
            return false;
        }
        
        // Check cooldown
        $lastInteraction = $this->interactions()
            ->where('interaction_type', $interactionType)
            ->latest('interaction_time')
            ->first();
            
        if ($lastInteraction) {
            $cooldownMinutes = $cooldowns[$interactionType] ?? 0;
            $cooldownExpires = $lastInteraction->interaction_time->addMinutes($cooldownMinutes);
            
            if (now() < $cooldownExpires) {
                return false;
            }
        }
        
        return true;
    }

    public function interact(string $interactionType, TelegramUser $user): array
    {
        if (!$this->canInteract($interactionType)) {
            throw new \Exception("Cannot perform {$interactionType} interaction at this time");
        }
        
        $interactionConfig = $this->getInteractionConfig($interactionType);
        
        // Check if user has enough energy
        if ($user->available_energy < $interactionConfig['energy_cost']) {
            throw new \Exception("Not enough energy for this interaction");
        }
        
        // Calculate rewards
        $happinessBonus = $this->happiness >= 80 ? 2.0 : 1.0; // Happy pet bonus
        $rewards = [
            'coins' => (int)($interactionConfig['coins'] * $happinessBonus),
            'experience' => $interactionConfig['experience'],
            'happiness' => $interactionConfig['happiness'],
            'materials' => 0,
            'collectible' => null
        ];
        
        // Random bonus rewards
        if (rand(1, 100) <= $interactionConfig['material_chance']) {
            $rewards['materials'] = 1;
        }
        
        if (rand(1, 100) <= $interactionConfig['collectible_chance']) {
            $rewards['collectible'] = $this->getRandomCollectible();
        }
        
        // Apply changes
        $this->updateFromInteraction($interactionConfig, $rewards);
        $user->decrement('available_energy', $interactionConfig['energy_cost']);
        $user->increment('balance', $rewards['coins']);
        
        // Log interaction
        $this->interactions()->create([
            'telegram_user_id' => $user->id,
            'interaction_type' => $interactionType,
            'energy_cost' => $interactionConfig['energy_cost'],
            'happiness_gained' => $rewards['happiness'],
            'experience_gained' => $rewards['experience'],
            'coins_rewarded' => $rewards['coins'],
            'materials_rewarded' => $rewards['materials'],
            'collectible_rewarded' => $rewards['collectible'],
            'bonus_applied' => $happinessBonus > 1,
            'interaction_time' => now()
        ]);
        
        // Log happiness change
        $this->logHappinessChange($rewards['happiness'], "interaction_{$interactionType}");
        
        return $rewards;
    }

    private function getInteractionConfig(string $type): array
    {
        return match($type) {
            'feed' => [
                'energy_cost' => 5,
                'happiness' => 20,
                'experience' => 10,
                'coins' => 50,
                'material_chance' => 30,
                'collectible_chance' => 0
            ],
            'play' => [
                'energy_cost' => 10,
                'happiness' => 30,
                'experience' => 15,
                'coins' => 100,
                'material_chance' => 50,
                'collectible_chance' => 10
            ],
            'pet' => [
                'energy_cost' => 2,
                'happiness' => 10,
                'experience' => 5,
                'coins' => 25,
                'material_chance' => 0,
                'collectible_chance' => 0
            ],
            default => throw new \Exception("Unknown interaction type: {$type}")
        };
    }

    private function updateFromInteraction(array $config, array $rewards): void
    {
        $oldHappiness = $this->happiness;
        
        $this->happiness = min(
            $this->template->max_happiness,
            $this->happiness + $rewards['happiness']
        );
        
        $this->experience += $rewards['experience'];
        
        // Check for level up
        while ($this->canLevelUp()) {
            $this->levelUp();
        }
        
        // Update interaction timestamps
        $now = now();
        match($config) {
            'feed' => $this->last_fed = $now,
            'play' => $this->last_played = $now,
            'pet' => $this->last_petted = $now,
            default => null
        };
        
        $this->save();
    }

    private function canLevelUp(): bool
    {
        $requiredExp = $this->calculateExperienceRequired($this->level + 1);
        return $this->experience >= $requiredExp && $this->level < $this->template->max_level;
    }

    private function levelUp(): void
    {
        $this->level++;
        
        // Check for evolution
        if ($this->can_evolve) {
            $this->evolve();
        }
    }

    private function evolve(): void
    {
        $this->evolution_stage++;
        $this->last_evolution = now();
        $this->happiness = min($this->template->max_happiness, $this->happiness + 20); // Evolution happiness bonus
        
        $this->logHappinessChange(20, 'evolution_bonus');
    }

    private function calculateExperienceRequired(int $level): int
    {
        return (int)(100 * pow(1.2, $level - 1));
    }

    private function resetDailyInteractionsIfNeeded(): void
    {
        if ($this->daily_interaction_reset_date < today()) {
            $this->daily_interaction_count = 0;
            $this->daily_interaction_reset_date = today();
            $this->save();
        }
    }

    private function logHappinessChange(int $change, string $reason): void
    {
        $oldHappiness = $this->happiness - $change;
        
        $this->happinessLogs()->create([
            'happiness_before' => $oldHappiness,
            'happiness_after' => $this->happiness,
            'happiness_change' => $change,
            'change_reason' => $reason,
            'logged_at' => now()
        ]);
    }

    // Daily happiness decay (called by scheduled job)
    public function applyDailyHappinessDecay(): void
    {
        $decayAmount = $this->template->happiness_decay_rate;
        $oldHappiness = $this->happiness;
        
        $this->happiness = max(0, $this->happiness - $decayAmount);
        $this->save();
        
        if ($decayAmount > 0) {
            $this->logHappinessChange(-$decayAmount, 'daily_decay');
        }
    }
}
```

### CollectibleTemplate Model
```php
<?php
// File: api/app/Models/CollectibleTemplate.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CollectibleTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'collectible_id', 'name', 'type', 'category', 'rarity', 'description',
        'image_url', 'collection_set_id', 'set_position', 'unlock_source',
        'unlock_requirement', 'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function userCollectibles(): HasMany
    {
        return $this->hasMany(Collectible::class, 'collectible_id', 'collectible_id');
    }

    public function collectionSet(): BelongsTo
    {
        return $this->belongsTo(CollectionSet::class, 'collection_set_id', 'set_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByRarity($query, $rarity)
    {
        return $query->where('rarity', $rarity);
    }

    public function scopeBySet($query, $setId)
    {
        return $query->where('collection_set_id', $setId);
    }

    // Accessors
    public function getImageUrlAttribute($value)
    {
        return $value ? asset($value) : null;
    }

    public function getRarityColorAttribute()
    {
        return match($this->rarity) {
            'common' => '#9CA3AF',
            'rare' => '#3B82F6',
            'epic' => '#8B5CF6',
            'legendary' => '#F59E0B',
            'mythic' => '#EF4444',
            default => '#6B7280'
        };
    }

    // Business Logic
    public function isOwnedBy(TelegramUser $user): bool
    {
        return $user->collectibles()
                   ->where('collectible_id', $this->collectible_id)
                   ->exists();
    }

    public function canBeUnlockedBy(TelegramUser $user): bool
    {
        switch ($this->unlock_source) {
            case 'pet_purchase':
                return $user->pets()
                           ->whereHas('template', function($q) {
                               $q->where('name', $this->unlock_requirement);
                           })
                           ->exists();

            case 'prize_tree':
                $userProgress = $user->prizeTreeProgress();
                return $userProgress && $userProgress->current_level >= (int)$this->unlock_requirement;

            case 'mystery_box':
                return true; // Can be unlocked from boxes

            case 'collection_bonus':
                return false; // Handled separately by collection completion

            default:
                return false;
        }
    }
}
```

### MysteryBoxType Model
```php
<?php
// File: api/app/Models/MysteryBoxType.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MysteryBoxType extends Model
{
    use HasFactory;

    protected $fillable = [
        'box_type', 'display_name', 'rarity', 'category', 'description',
        'image_url', 'animation_url', 'coin_cost', 'gem_cost', 'achievement_points_cost',
        'possible_rewards', 'reward_weights', 'guaranteed_rarity_level',
        'unlock_requirements', 'is_purchasable', 'is_active', 'sort_order'
    ];

    protected $casts = [
        'possible_rewards' => 'array',
        'reward_weights' => 'array',
        'unlock_requirements' => 'array',
        'is_purchasable' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function unlocks(): HasMany
    {
        return $this->hasMany(MysteryBoxUnlock::class, 'box_type', 'box_type');
    }

    public function openings(): HasMany
    {
        return $this->hasMany(MysteryBoxOpening::class, 'box_type', 'box_type');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePurchasable($query)
    {
        return $query->where('is_purchasable', true)->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    // Business Logic
    public function isUnlockedBy(TelegramUser $user): bool
    {
        return $this->unlocks()
                   ->where('telegram_user_id', $user->id)
                   ->exists();
    }

    public function canBePurchasedBy(TelegramUser $user): bool
    {
        if (!$this->is_purchasable || !$this->is_active) {
            return false;
        }

        return $this->isUnlockedBy($user);
    }

    public function getCost(string $currency): int
    {
        return match($currency) {
            'coins' => $this->coin_cost,
            'gems' => $this->gem_cost,
            'achievement_points' => $this->achievement_points_cost,
            default => 0
        };
    }

    public function generateRewards(): array
    {
        $rewards = [];
        $possibleRewards = $this->possible_rewards ?? [];
        $weights = $this->reward_weights ?? [];

        if (empty($possibleRewards)) {
            return [];
        }

        // Generate 1-3 rewards per box
        $rewardCount = rand(1, 3);

        for ($i = 0; $i < $rewardCount; $i++) {
            $reward = $this->selectWeightedReward($possibleRewards, $weights);
            if ($reward) {
                $rewards[] = $reward;
            }
        }

        return array_unique($rewards);
    }

    private function selectWeightedReward(array $rewards, array $weights): ?string
    {
        if (empty($rewards)) {
            return null;
        }

        $totalWeight = array_sum($weights);
        $random = rand(1, $totalWeight);

        $currentWeight = 0;
        foreach ($rewards as $index => $reward) {
            $currentWeight += $weights[$index] ?? 1;
            if ($random <= $currentWeight) {
                return $reward;
            }
        }

        return $rewards[0]; // Fallback
    }
}
```

### PetInteraction Model
```php
<?php
// File: api/app/Models/PetInteraction.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PetInteraction extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id', 'pet_id', 'interaction_type', 'energy_cost',
        'happiness_gained', 'experience_gained', 'coins_rewarded',
        'materials_rewarded', 'collectible_rewarded', 'bonus_applied',
        'interaction_time'
    ];

    protected $casts = [
        'bonus_applied' => 'boolean',
        'interaction_time' => 'datetime',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function pet(): BelongsTo
    {
        return $this->belongsTo(Pet::class);
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->whereDate('interaction_time', today());
    }

    public function scopeByType($query, $type)
    {
        return $query->where('interaction_type', $type);
    }

    public function scopeWithBonus($query)
    {
        return $query->where('bonus_applied', true);
    }
}
```

## Model Registration

### Update TelegramUser Model
```php
// File: api/app/Models/TelegramUser.php
// Add these relationships to existing TelegramUser model

public function pets(): HasMany
{
    return $this->hasMany(Pet::class, 'telegram_user_id');
}

public function featuredPet(): HasOne
{
    return $this->hasOne(Pet::class, 'telegram_user_id')->where('is_featured', true);
}

public function collectibles(): HasMany
{
    return $this->hasMany(Collectible::class, 'telegram_user_id');
}

public function mysteryBoxUnlocks(): HasMany
{
    return $this->hasMany(MysteryBoxUnlock::class, 'telegram_user_id');
}

public function petInteractions(): HasMany
{
    return $this->hasMany(PetInteraction::class, 'telegram_user_id');
}

// Business logic methods
public function getCollectionProgress(): array
{
    $totalPets = PetTemplate::active()->count();
    $ownedPets = $this->pets()->count();

    $totalCollectibles = CollectibleTemplate::active()->count();
    $ownedCollectibles = $this->collectibles()->count();

    return [
        'pets' => [
            'owned' => $ownedPets,
            'total' => $totalPets,
            'percentage' => $totalPets > 0 ? round(($ownedPets / $totalPets) * 100, 1) : 0
        ],
        'collectibles' => [
            'owned' => $ownedCollectibles,
            'total' => $totalCollectibles,
            'percentage' => $totalCollectibles > 0 ? round(($ownedCollectibles / $totalCollectibles) * 100, 1) : 0
        ],
        'overall_percentage' => $totalPets + $totalCollectibles > 0
            ? round((($ownedPets + $ownedCollectibles) / ($totalPets + $totalCollectibles)) * 100, 1)
            : 0
    ];
}

public function getPetsNeedingAttention(): Collection
{
    return $this->pets()
                ->with('template')
                ->where('happiness', '<', 30)
                ->get();
}
```

## Acceptance Criteria
- [ ] All models created with proper relationships
- [ ] Business logic methods implemented
- [ ] Interaction system working
- [ ] Evolution mechanics functional
- [ ] Happiness system operational
- [ ] Mystery box reward generation working
- [ ] Collection progress calculation accurate

## Next Steps
1. Create API controllers for pet management
2. Implement frontend components
3. Add scheduled jobs for daily tasks
4. Create admin interface for pet management

## Troubleshooting
- Ensure foreign key relationships match database schema
- Test interaction limits and cooldowns thoroughly
- Verify evolution level calculations
- Check happiness decay mechanics
- Test mystery box reward generation algorithms
