# Prize Tree UI Migration Plan

## Target Component
`battlx/src/components/prizetree/PrizeTreeCanvas.tsx`

## Style Migration Requirements

### Color Scheme Update
```ts
// Update tailwind.config.js to include:
theme: {
  extend: {
    colors: {
      brass: {
        100: '#F5F0E6',
        300: '#9B8B6C',
        500: '#6B5E45',
        900: '#453D2C'
      },
      abyss: {
        200: '#120D0E',
        400: '#0A0809'
      }
    }
  }
}
```

### Node Styling Changes
1. Gradient backgrounds:
```tsx
`bg-gradient-to-br from-brass-300 to-brass-500 border-3 border-brass-100`
```
2. Animated glow effects:
```tsx
shadow-inner-glow // Add custom shadow definition
animate={{ boxShadow: '0 0 15px rgba(155, 139, 108, 0.3)' }}
```
3. Lock state styling:
```tsx
<Lock className="w-5 h-5 text-brass-900" />
```

### Connection Line Enhancements
1. Animated dashed lines:
```ts
ctx.setLineDash([5, 5]);
ctx.lineDashOffset = Date.now() / 100; // Animate offset
```
2. Color hierarchy:
- Active: #9B8B6C
- Available: #9B8B6C80 
- Locked: #9B8B6C40

### Zoom Controls Redesign
```tsx
<button className="p-2 bg-abyss-200/80 border border-brass-300/50 backdrop-blur-sm">
  <BattlxIcon icon="zoom-in" className="w-5 h-5 text-brass-300" />
</button>
```

## Implementation Steps
1. Update Tailwind configuration
2. Refactor node rendering logic
3. Enhance canvas drawing methods
4. Redesign control components
5. Add animation presets