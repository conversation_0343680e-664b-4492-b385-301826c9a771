<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\TelegramUser;

return new class extends Migration
{
    private const TOWER_GAME_PRICE = 5000;

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Auto unlock tower game for users who already have sufficient balance
        TelegramUser::where('balance', '>=', self::TOWER_GAME_PRICE)
            ->update(['tower_game_unlocked' => true]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reset tower game unlocked status for all users
        TelegramUser::query()->update(['tower_game_unlocked' => false]);
    }
};
