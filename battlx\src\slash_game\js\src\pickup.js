// Pickup class for handling pickups (gems, coins, etc.)
class BasePickup {
    constructor(x, y, itemType) {
        this.x = x;
        this.y = y;
        this.itemType = itemType;
        this.value = 1;
        this.radius = 10;
        this.speed = 0;
        this.time = 1;
        this.goToPlayer = false;
        this.isCollected = false; // Track if the pickup has been collected
        this.currentDirection = new Vector2(0, 0);
        this.vacuumTween = null;

        // Load pickup data
        this.loadPickupData();
    }

    // Load pickup data from constants
    loadPickupData() {
        const pickupData = PICKUPS[this.itemType];
        if (!pickupData) return;

        this.value = pickupData.value;
        this.frameName = pickupData.frameName;
    }

    // Update the pickup
    update() {
        // Move towards player if activated
        if (this.goToPlayer && Game.core && Game.core.player) {
            this.moveTowardsPlayer();
        }
    }

    // Move towards the player
    moveTowardsPlayer() {
        if (!Game.core || !Game.core.player) return;

        this.currentDirection.x = Game.core.player.x - this.x;
        this.currentDirection.y = Game.core.player.y - 8 - this.y;
        this.currentDirection.normalize();

        // Calculate distance to player
        const distToPlayer = Math.sqrt(
            (Game.core.player.x - this.x) * (Game.core.player.x - this.x) +
            (Game.core.player.y - this.y) * (Game.core.player.y - this.y)
        );

        // Adjust speed based on distance - faster when further away
        let speedMultiplier = 1;
        if (distToPlayer > 300) {
            speedMultiplier = 3; // Much faster when far away
        } else if (distToPlayer > 150) {
            speedMultiplier = 2; // Faster at medium distance
        }

        // Move towards player with increasing speed
        this.x += this.speed * this.currentDirection.x * this.time * speedMultiplier;
        this.y += this.speed * this.currentDirection.y * this.time * speedMultiplier;

        // Increase speed more aggressively
        this.speed += 0.2;

        // Auto-collect if very close to player (prevents points getting stuck)
        if (distToPlayer < 20 && Game.core) {
            this.getPickedUp();
        }
    }

    // Activate vacuum effect (pull towards player)
    vacuum() {
        if (!this.goToPlayer) {
            this.time = 2; // Start with higher time multiplier
            this.goToPlayer = true;
            this.speed = 2; // Higher initial speed

            // Gradually increase the time multiplier more aggressively
            setTimeout(() => {
                this.time = 8;
            }, 200); // Faster ramp-up

            setTimeout(() => {
                this.time = 15;
            }, 500); // Faster ramp-up

            // Add a final boost for stragglers
            setTimeout(() => {
                if (this.goToPlayer && !this.isCollected) {
                    this.time = 25; // Very high multiplier for stragglers
                    this.speed = 5; // Higher speed for stragglers
                }
            }, 1500);

            return false;
        } else {
            // If already vacuuming, boost the speed a bit more
            this.speed += 0.5;
            return true;
        }
    }

    // Get picked up by the player
    getPickedUp() {
        // Prevent double collection
        if (this.isCollected) return;

        // Mark as collected
        this.isCollected = true;

        // Handle pickup based on type
        if (Game.core) {
            Game.core.getPickup(this);
        }

        // Remove from game
        this.despawn();
    }

    // Despawn the pickup
    despawn() {
        if (Game.core) {
            // Remove from pickups array
            const index = Game.core.pickups.indexOf(this);
            if (index !== -1) {
                Game.core.pickups.splice(index, 1);
            }
        }
    }

    // Draw the pickup
    draw(ctx, camera, sprites) {
        if (!sprites) return;

        // Calculate screen position
        const screenX = this.x - camera.x + camera.width / 2;
        const screenY = this.y - camera.y + camera.height / 2;

        // Get the sprite
        const sprite = sprites[this.frameName];
        if (!sprite) {
            // Draw a fallback circle if sprite not found
            ctx.beginPath();
            ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
            ctx.fillStyle = this.getPickupColor();
            ctx.fill();
            return;
        }

        // Draw the sprite
        ctx.drawImage(
            sprite,
            screenX - sprite.width / 2,
            screenY - sprite.height / 2,
            sprite.width,
            sprite.height
        );

        // Draw debug collision circle
        if (Game.core && Game.core.debug) {
            ctx.beginPath();
            ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(0, 0, 255, 0.5)';
            ctx.stroke();
        }
    }

    // Get color based on pickup type (for fallback rendering)
    getPickupColor() {
        switch (this.itemType) {
            case PickupType.GEM:
                return 'rgba(0, 255, 255, 0.8)';
            case PickupType.COIN:
                return 'rgba(255, 215, 0, 0.8)';
            case PickupType.ROAST:
                return 'rgba(139, 69, 19, 0.8)';
            case PickupType.VACUUM:
                return 'rgba(255, 0, 255, 0.8)';
            default:
                return 'rgba(255, 255, 255, 0.8)';
        }
    }
}

// GemPickup class for experience gems
class GemPickup extends BasePickup {
    constructor(x, y, value) {
        super(x, y, PickupType.GEM);
        this.value = value || 1;
    }
}

// PickupGroup class for managing groups of pickups
class PickupGroup {
    constructor() {
        this.stored = {};
        this.spawned = {};

        // Initialize storage for each pickup type
        Object.values(PickupType).forEach(type => {
            if (typeof type === 'string') {
                this.stored[type] = [];
                this.spawned[type] = [];
            }
        });
    }

    // Spawn a pickup at a position
    spawnAt(x, y, type, value) {
        const pickup = this.spawn(type, value);
        pickup.x = x;
        pickup.y = y;
        return pickup;
    }

    // Spawn a pickup
    spawn(type, value) {
        if (!this.stored[type]) {
            this.stored[type] = [];
            this.spawned[type] = [];
        }

        let pickup = this.stored[type].pop();

        if (!pickup) {
            pickup = this.make(type, value);
        } else if (value !== undefined) {
            pickup.value = value;
        }

        this.spawned[type].push(pickup);

        if (Game.core) {
            Game.core.pickups.push(pickup);
        }

        return pickup;
    }

    // Return a pickup to the pool
    return(pickup) {
        const type = pickup.itemType;

        if (!this.spawned[type]) return;

        const index = this.spawned[type].indexOf(pickup);
        if (index !== -1) {
            this.spawned[type].splice(index, 1);
        }

        if (!this.stored[type]) {
            this.stored[type] = [];
        }

        this.stored[type].push(pickup);
    }

    // Create a new pickup
    make(type, value) {
        switch (type) {
            case PickupType.GEM:
                return new GemPickup(0, 0, value);
            default:
                return new BasePickup(0, 0, type);
        }
    }
}

// Attach to window object for global access
window.BasePickup = BasePickup;
window.GemPickup = GemPickup;
window.PickupGroup = PickupGroup;
