<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class MissionTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $types = [
            ['id' => 1, 'name' => 'Eternal Harvest'],
            ['id' => 2, 'name' => 'Grave Yield'],
            ['id' => 3, 'name' => 'Necrotic Flow'],
            ['id' => 4, 'name' => 'Dark Tithes'],
        ];

        foreach ($types as $type) {
            \App\Models\MissionType::updateOrCreate(['id' => $type['id']], $type);
        }
    }
}

