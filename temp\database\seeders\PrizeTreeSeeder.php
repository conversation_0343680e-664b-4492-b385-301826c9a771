<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PrizeTree;
use App\Models\Prize;
use Illuminate\Support\Facades\DB;

class PrizeTreeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::transaction(function () {
            // Create Collector's Tree
            $collectorTree = PrizeTree::create([
                'name' => 'Collector\'s Tree',
                'description' => 'Unlock rare collectibles and special cards.',
                'icon' => 'collection',
                'theme_color' => '#9B8B6C',
                'display_order' => 1,
                'is_active' => true
            ]);
            
            // Create Cosmetic Tree
            $cosmeticTree = PrizeTree::create([
                'name' => 'Cosmetic Tree',
                'description' => 'Customize your experience with visual enhancements.',
                'icon' => 'cosmetic',
                'theme_color' => '#4A0E0E',
                'display_order' => 2,
                'is_active' => true
            ]);
            
            // Create Fortune Tree
            $fortuneTree = PrizeTree::create([
                'name' => 'Fortune Tree',
                'description' => 'Boost your economy with coin rewards and bonuses.',
                'icon' => 'fortune',
                'theme_color' => '#B3B3B3',
                'display_order' => 3,
                'is_active' => true
            ]);
            
            // Create Social Tree
            $socialTree = PrizeTree::create([
                'name' => 'Social Tree',
                'description' => 'Enhance your social interactions with special titles and emotes.',
                'icon' => 'social',
                'theme_color' => '#120D0E',
                'display_order' => 4,
                'is_active' => true
            ]);
            
            // Add prizes to Collector's Tree
            $collectorRoot = Prize::create([
                'prize_tree_id' => $collectorTree->id,
                'name' => 'Novice Collector',
                'description' => 'The foundation of all collection skills.',
                'icon' => 'collection',
                'tier' => 1,
                'position' => 1,
                'category' => 'collection',
                'cost' => 1,
                'is_root' => true,
                'reward_type' => 'title',
                'reward_data' => [
                    'title' => 'Novice Collector',
                    'color' => '#9B8B6C'
                ]
            ]);
            
            $cardEnthusiast = Prize::create([
                'prize_tree_id' => $collectorTree->id,
                'name' => 'Card Enthusiast',
                'description' => 'Unlock a rare mission card.',
                'icon' => 'card',
                'tier' => 1,
                'position' => 2,
                'category' => 'collection',
                'cost' => 1,
                'is_root' => false,
                'reward_type' => 'card',
                'reward_data' => [
                    'card_id' => 1,
                    'rarity' => 'rare'
                ]
            ]);
            
            $itemFinder = Prize::create([
                'prize_tree_id' => $collectorTree->id,
                'name' => 'Item Finder',
                'description' => 'Unlock a special collectible item.',
                'icon' => 'item',
                'tier' => 1,
                'position' => 3,
                'category' => 'collection',
                'cost' => 1,
                'is_root' => false,
                'reward_type' => 'special_item',
                'reward_data' => [
                    'item_id' => 1,
                    'item_type' => 'collectible'
                ]
            ]);
            
            // Add prerequisites
            $cardEnthusiast->prerequisites()->attach($collectorRoot->id);
            $itemFinder->prerequisites()->attach($collectorRoot->id);
            
            // Tier 2 prizes
            $advancedCollector = Prize::create([
                'prize_tree_id' => $collectorTree->id,
                'name' => 'Advanced Collector',
                'description' => 'Upgrades display case to show more items.',
                'icon' => 'collection_advanced',
                'tier' => 2,
                'position' => 1,
                'category' => 'collection',
                'cost' => 2,
                'is_root' => false,
                'reward_type' => 'special_item',
                'reward_data' => [
                    'item_id' => 2,
                    'item_type' => 'display_case'
                ]
            ]);
            
            $cardMaster = Prize::create([
                'prize_tree_id' => $collectorTree->id,
                'name' => 'Card Master',
                'description' => 'Unlocks a special edition mission card.',
                'icon' => 'card_master',
                'tier' => 2,
                'position' => 2,
                'category' => 'collection',
                'cost' => 2,
                'is_root' => false,
                'reward_type' => 'card',
                'reward_data' => [
                    'card_id' => 2,
                    'rarity' => 'epic'
                ]
            ]);
            
            // Add prerequisites
            $advancedCollector->prerequisites()->attach($collectorRoot->id);
            $cardMaster->prerequisites()->attach($cardEnthusiast->id);
            
            // Add prizes to Cosmetic Tree
            $cosmeticRoot = Prize::create([
                'prize_tree_id' => $cosmeticTree->id,
                'name' => 'Style Novice',
                'description' => 'Unlocks basic color customization.',
                'icon' => 'style',
                'tier' => 1,
                'position' => 1,
                'category' => 'cosmetic',
                'cost' => 1,
                'is_root' => true,
                'reward_type' => 'cosmetic',
                'reward_data' => [
                    'type' => 'ui_theme',
                    'visual_data' => [
                        'primary_color' => '#9B8B6C',
                        'secondary_color' => '#4A0E0E'
                    ]
                ]
            ]);
            
            $slashStylist = Prize::create([
                'prize_tree_id' => $cosmeticTree->id,
                'name' => 'Slash Stylist',
                'description' => 'Unlocks a custom slash effect color.',
                'icon' => 'slash',
                'tier' => 1,
                'position' => 2,
                'category' => 'cosmetic',
                'cost' => 1,
                'is_root' => false,
                'reward_type' => 'cosmetic',
                'reward_data' => [
                    'type' => 'slash_effect',
                    'visual_data' => [
                        'color' => '#9B8B6C',
                        'trail_length' => 10,
                        'width' => 5
                    ]
                ]
            ]);
            
            $backgroundApprentice = Prize::create([
                'prize_tree_id' => $cosmeticTree->id,
                'name' => 'Background Apprentice',
                'description' => 'Unlocks a custom background theme.',
                'icon' => 'background',
                'tier' => 1,
                'position' => 3,
                'category' => 'cosmetic',
                'cost' => 1,
                'is_root' => false,
                'reward_type' => 'cosmetic',
                'reward_data' => [
                    'type' => 'background',
                    'visual_data' => [
                        'image' => 'gothic_background_1.png',
                        'overlay_opacity' => 0.2
                    ]
                ]
            ]);
            
            // Add prerequisites
            $slashStylist->prerequisites()->attach($cosmeticRoot->id);
            $backgroundApprentice->prerequisites()->attach($cosmeticRoot->id);
            
            // Add prizes to Fortune Tree
            $fortuneRoot = Prize::create([
                'prize_tree_id' => $fortuneTree->id,
                'name' => 'Fortune Seeker',
                'description' => 'Unlocks a small coin bonus.',
                'icon' => 'fortune',
                'tier' => 1,
                'position' => 1,
                'category' => 'fortune',
                'cost' => 1,
                'is_root' => true,
                'reward_type' => 'balance',
                'reward_data' => [
                    'amount' => 100,
                    'currency' => 'coins'
                ]
            ]);
            
            $luckyCharm = Prize::create([
                'prize_tree_id' => $fortuneTree->id,
                'name' => 'Lucky Charm',
                'description' => 'Unlocks a special lucky charm item.',
                'icon' => 'luck',
                'tier' => 1,
                'position' => 2,
                'category' => 'fortune',
                'cost' => 1,
                'is_root' => false,
                'reward_type' => 'special_item',
                'reward_data' => [
                    'item_id' => 3,
                    'item_type' => 'charm'
                ]
            ]);
            
            $treasureMap = Prize::create([
                'prize_tree_id' => $fortuneTree->id,
                'name' => 'Treasure Map',
                'description' => 'Unlocks a treasure map that provides a random coin reward.',
                'icon' => 'map',
                'tier' => 1,
                'position' => 3,
                'category' => 'fortune',
                'cost' => 1,
                'is_root' => false,
                'reward_type' => 'special_item',
                'reward_data' => [
                    'item_id' => 4,
                    'item_type' => 'map'
                ]
            ]);
            
            // Add prerequisites
            $luckyCharm->prerequisites()->attach($fortuneRoot->id);
            $treasureMap->prerequisites()->attach($fortuneRoot->id);
            
            // Add prizes to Social Tree
            $socialRoot = Prize::create([
                'prize_tree_id' => $socialTree->id,
                'name' => 'Social Novice',
                'description' => 'Unlocks basic social profile customization.',
                'icon' => 'social',
                'tier' => 1,
                'position' => 1,
                'category' => 'social',
                'cost' => 1,
                'is_root' => true,
                'reward_type' => 'title',
                'reward_data' => [
                    'title' => 'Social Novice',
                    'color' => '#B3B3B3'
                ]
            ]);
            
            $friendFinder = Prize::create([
                'prize_tree_id' => $socialTree->id,
                'name' => 'Friend Finder',
                'description' => 'Unlocks enhanced friend search features.',
                'icon' => 'friend',
                'tier' => 1,
                'position' => 2,
                'category' => 'social',
                'cost' => 1,
                'is_root' => false,
                'reward_type' => 'special_item',
                'reward_data' => [
                    'item_id' => 5,
                    'item_type' => 'friend_finder'
                ]
            ]);
            
            $emoteApprentice = Prize::create([
                'prize_tree_id' => $socialTree->id,
                'name' => 'Emote Apprentice',
                'description' => 'Unlocks a basic emote pack.',
                'icon' => 'emote',
                'tier' => 1,
                'position' => 3,
                'category' => 'social',
                'cost' => 1,
                'is_root' => false,
                'reward_type' => 'emote',
                'reward_data' => [
                    'emote' => 'basic_pack',
                    'animation' => null
                ]
            ]);
            
            // Add prerequisites
            $friendFinder->prerequisites()->attach($socialRoot->id);
            $emoteApprentice->prerequisites()->attach($socialRoot->id);
        });
    }
}
