import { Link, useLocation } from "react-router-dom";
import { cn } from "../lib/utils";
import { BattlxIcon, type IconType } from "./icons/BattlxIcon";

interface NavLink {
  name: string;
  link: string;
  icon: IconType;
}

const links: NavLink[] = [
  { name: "Explore", link: "/", icon: "explore" },
  { name: "Missions", link: "/missions", icon: "missions" },
  { name: "Friends", link: "/friends", icon: "friends" },
  { name: "<PERSON>unt<PERSON>", link: "/earn", icon: "bounty" },
  { name: "Wallet", link: "/wallet", icon: "wallet" },
  { name: "Game", link: "/games", icon: "game" },
  { name: "Prizes", link: "/prizes", icon: "coins" },
];

export default function AppBar() {
  const { pathname } = useLocation();
  return (
    <div className="fixed left-0 z-10 w-full px-0 py-0 bottom-2">
      <div className="flex items-center w-full p-1.5 gap-1 max-w-container mx-auto bg-[#1A1617] rounded-xl border border-[#B3B3B3]/20 backdrop-blur-3xl relative before:absolute before:inset-0 before:bg-[repeating-linear-gradient(45deg,rgba(155,139,108,0.05)_0px,rgba(155,139,108,0.05)_1px,transparent_1px,transparent_10px)] shadow-[0_4px_15px_rgba(74,14,14,0.3)]">
        {links.map((link, key) => (
          <Link
            key={key}
            to={link.link}
            className={cn(
              "relative flex items-center rounded-lg flex-col justify-center font-medium text-[10px] px-1.5 py-1 gap-0.5 select-none flex-1 text-[#B3B3B3]/60 border border-[#9B8B6C]/20 bg-[#1A1617]/50 hover:bg-[#4A0E0E]/20 transition-all duration-300",
              pathname === link.link && "text-[#9B8B6C] border-[#9B8B6C]/50 bg-[#4A0E0E]/30"
            )}
          >
            <BattlxIcon
                icon={link.icon}
                size="md"
                className={cn(
                "opacity-50 transition-all duration-300 text-[#9B8B6C] scale-150", // 25% larger
                pathname === link.link && "opacity-100"
              )}
/>
            <span>{link.name}</span>
            <div
              className={cn(
                "absolute hidden -bottom-1 left-1/2 -translate-x-1/2 bg-gradient-to-r from-[#9B8B6C] via-[#B3B3B3] to-[#9B8B6C] rounded-sm shadow-[0_0_6px_rgba(155,139,108,0.3)] h-[2px] w-4/5",
                pathname === link.link && "block"
              )}
            />
          </Link>
        ))}
      </div>
    </div>
  );
}
