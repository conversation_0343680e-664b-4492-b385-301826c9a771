import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { BattlxIcon } from '@/components/icons/BattlxIcon';
import { Prize, PrizeTreeResponse } from '@/types/PrizeTypes';

interface PrizeTreeCanvasProps {
  tree: PrizeTreeResponse;
  userPrizes: number[];
  onNodeSelect: (node: Prize) => void;
}

interface NodePosition {
  x: number;
  y: number;
  gridX: number;
  gridY: number;
}

// Removed SkillNode interface as it's no longer needed

export default function PrizeTreeCanvas({ tree, userPrizes, onNodeSelect }: PrizeTreeCanvasProps) {
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [startDragPosition, setStartDragPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  // Professional grid-based positioning system
  const calculateGridLayout = (): Record<number, NodePosition> => {
    const positions: Record<number, NodePosition> = {};
    const nodesByTier: Record<number, Prize[]> = {};

    // Group nodes by tier
    tree.nodes.forEach(node => {
      if (!nodesByTier[node.tier]) {
        nodesByTier[node.tier] = [];
      }
      nodesByTier[node.tier].push(node);
    });

    // Grid configuration for professional layout
    const GRID_SIZE = 120; // Size of each grid cell
    const TIER_SPACING = 160; // Vertical spacing between tiers
    const MAX_NODES_PER_ROW = 5; // Maximum nodes per row for optimal layout

    const tiers = Object.keys(nodesByTier).sort((a, b) => Number(a) - Number(b));

    tiers.forEach((tier) => {
      const nodes = nodesByTier[Number(tier)];
      const tierNum = Number(tier);

      // Calculate optimal grid layout for this tier
      const nodesInTier = nodes.length;
      const nodesPerRow = Math.min(nodesInTier, MAX_NODES_PER_ROW);

      // Center the tier horizontally
      const tierWidth = nodesPerRow * GRID_SIZE;
      const startX = -tierWidth / 2 + GRID_SIZE / 2;

      nodes.forEach((node, index) => {
        const row = Math.floor(index / nodesPerRow);
        const col = index % nodesPerRow;

        // Adjust for centering when row is not full
        const nodesInCurrentRow = Math.min(nodesPerRow, nodesInTier - row * nodesPerRow);
        const rowStartX = startX + (nodesPerRow - nodesInCurrentRow) * GRID_SIZE / 2;

        const gridX = col;
        const gridY = tierNum * 2 + row; // Multiply by 2 to give space for connections

        positions[node.id] = {
          x: rowStartX + col * GRID_SIZE,
          y: tierNum * TIER_SPACING + row * GRID_SIZE,
          gridX,
          gridY
        };
      });
    });

    return positions;
  };

  // Removed connection lines as they are no longer needed for the professional design

  // Removed connection-related functions as they are no longer needed

  // Mobile touch gestures for pinch-to-zoom (if needed in future)
  // Currently using zoom controls only for mobile-first design

  // Mobile touch handlers for drag/pan
  const handleTouchStart = (event: React.TouchEvent) => {
    if (event.touches.length === 1) {
      setIsDragging(true);
      const touch = event.touches[0];
      setStartDragPosition({
        x: touch.clientX - position.x,
        y: touch.clientY - position.y
      });
    }
  };

  const handleTouchMove = (event: React.TouchEvent) => {
    if (!isDragging || event.touches.length !== 1) return;

    const touch = event.touches[0];
    setPosition({
      x: touch.clientX - startDragPosition.x,
      y: touch.clientY - startDragPosition.y
    });
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  // Mouse handlers for development/testing (fallback)
  const handleMouseStart = (event: React.MouseEvent) => {
    setIsDragging(true);
    setStartDragPosition({
      x: event.clientX - position.x,
      y: event.clientY - position.y
    });
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isDragging) return;

    setPosition({
      x: event.clientX - startDragPosition.x,
      y: event.clientY - startDragPosition.y
    });
  };

  const handleMouseEnd = () => {
    setIsDragging(false);
  };

  // Calculate node positions for the professional grid layout
  const nodePositions = tree.nodes && tree.nodes.length ? calculateGridLayout() : {};

  return (
    <div className="relative flex-1 mt-4 overflow-hidden border-2 border-[#9B8B6C]/60 rounded-xl bg-gradient-to-br from-[#0A0808] via-[#120D0E] to-[#1A1617] shadow-[0_8px_32px_rgba(0,0,0,0.6)]">
      {/* Professional skill tree background pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `
            linear-gradient(90deg, rgba(155,139,108,0.1) 1px, transparent 1px),
            linear-gradient(rgba(155,139,108,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px'
        }} />
      </div>

      {/* Removed SVG connection system - clean professional design without lines */}

      {/* Professional Skill Nodes Container - Mobile Optimized */}
      <div
        className="relative w-full h-full p-8 touch-pan-x touch-pan-y"
        style={{
          transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseStart}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseEnd}
        onMouseLeave={handleMouseEnd}
      >
        {/* Center the skill tree */}
        <div className="flex justify-center items-start min-h-full pt-16">
          <div className="relative">
            {tree.nodes && tree.nodes.map(node => {
              const pos = nodePositions[node.id] || { x: 0, y: 0, gridX: 0, gridY: 0 };
              const isUnlocked = userPrizes.includes(node.id);
              const isAvailable = !isUnlocked && node.prerequisites.every(id => userPrizes.includes(id));

              return (
                <motion.div
                  key={node.id}
                  className="absolute cursor-pointer"
                  style={{
                    left: pos.x,
                    top: pos.y,
                    transform: 'translate(-50%, -50%)',
                  }}
                  onClick={() => onNodeSelect(node)}
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 25,
                    delay: node.tier * 0.1
                  }}
                >
                  {/* Professional Square Skill Node */}
                  <div className={`
                    relative w-24 h-24 rounded-lg transition-all duration-300
                    ${isUnlocked
                      ? 'bg-gradient-to-br from-[#9B8B6C] via-[#B3B3B3] to-[#9B8B6C] shadow-[0_0_20px_rgba(155,139,108,0.6)] border-2 border-[#9B8B6C]'
                      : isAvailable
                        ? 'bg-gradient-to-br from-[#9B8B6C]/40 via-[#B3B3B3]/40 to-[#9B8B6C]/40 shadow-[0_0_12px_rgba(155,139,108,0.3)] border-2 border-[#9B8B6C]/60'
                        : 'bg-gradient-to-br from-[#333333] via-[#444444] to-[#333333] shadow-[0_0_8px_rgba(0,0,0,0.4)] border-2 border-[#666666]/40'
                    }
                  `}>
                    {/* Inner content area */}
                    <div className={`
                      absolute inset-1 rounded-md flex flex-col items-center justify-center p-2
                      ${isUnlocked
                        ? 'bg-gradient-to-br from-[#120D0E] to-[#1A1617] border border-[#9B8B6C]/30'
                        : isAvailable
                          ? 'bg-gradient-to-br from-[#120D0E]/90 to-[#1A1617]/90 border border-[#9B8B6C]/20'
                          : 'bg-gradient-to-br from-[#0A0808] to-[#120D0E] border border-[#666666]/20'
                      }
                    `}>
                      {/* Skill Icon */}
                      <div className="relative mb-1">
                        <BattlxIcon
                          icon={node.icon as any || 'boost'}
                          className={`
                            w-8 h-8 transition-all duration-300
                            ${isUnlocked
                              ? 'text-[#9B8B6C] drop-shadow-[0_0_4px_rgba(155,139,108,0.8)]'
                              : isAvailable
                                ? 'text-[#9B8B6C]/80'
                                : 'text-[#666666]'
                            }
                          `}
                        />

                        {/* Lock overlay for unavailable skills */}
                        {!isUnlocked && !isAvailable && (
                          <div className="absolute inset-0 flex items-center justify-center bg-[#000000]/70 rounded backdrop-blur-sm">
                            <BattlxIcon icon="lock" className="w-6 h-6 text-[#9B8B6C]/80 drop-shadow-[0_0_4px_rgba(155,139,108,0.6)]" />
                          </div>
                        )}
                      </div>

                      {/* Skill Name */}
                      <div className={`
                        text-xs font-bold text-center leading-tight
                        ${isUnlocked
                          ? 'text-[#9B8B6C]'
                          : isAvailable
                            ? 'text-[#9B8B6C]/80'
                            : 'text-[#666666]'
                        }
                      `}>
                        {node.name}
                      </div>
                    </div>

                    {/* Tier indicator */}
                    <div className={`
                      absolute -top-2 -right-2 w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold
                      ${isUnlocked
                        ? 'bg-[#9B8B6C] text-[#120D0E] shadow-[0_0_8px_rgba(155,139,108,0.6)]'
                        : isAvailable
                          ? 'bg-[#9B8B6C]/60 text-[#120D0E] shadow-[0_0_4px_rgba(155,139,108,0.3)]'
                          : 'bg-[#666666] text-[#120D0E] shadow-[0_0_4px_rgba(0,0,0,0.4)]'
                      }
                    `}>
                      {node.tier}
                    </div>

                    {/* Unlock glow effect */}
                    {isUnlocked && (
                      <div className="absolute inset-0 rounded-lg bg-gradient-to-br from-[#9B8B6C]/20 to-transparent animate-pulse" />
                    )}

                    {/* Available pulse effect */}
                    {isAvailable && !isUnlocked && (
                      <div className="absolute inset-0 rounded-lg border-2 border-[#9B8B6C]/40 animate-pulse" />
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Professional Zoom Controls */}
      <div className="absolute bottom-6 right-6 flex flex-col space-y-3">
        <motion.button
          className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-[#9B8B6C] to-[#B3B3B3] rounded-lg text-[#120D0E] shadow-[0_4px_15px_rgba(155,139,108,0.4)] border border-[#9B8B6C]/60 hover:shadow-[0_6px_20px_rgba(155,139,108,0.6)] transition-all duration-200"
          onClick={() => setScale(prevScale => Math.min(2, prevScale + 0.1))}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          title="Zoom In"
        >
          <span className="text-lg font-bold">+</span>
        </motion.button>

        <motion.button
          className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-[#9B8B6C] to-[#B3B3B3] rounded-lg text-[#120D0E] shadow-[0_4px_15px_rgba(155,139,108,0.4)] border border-[#9B8B6C]/60 hover:shadow-[0_6px_20px_rgba(155,139,108,0.6)] transition-all duration-200"
          onClick={() => setScale(prevScale => Math.max(0.5, prevScale - 0.1))}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          title="Zoom Out"
        >
          <span className="text-lg font-bold">-</span>
        </motion.button>

        <motion.button
          className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-[#9B8B6C] to-[#B3B3B3] rounded-lg text-[#120D0E] shadow-[0_4px_15px_rgba(155,139,108,0.4)] border border-[#9B8B6C]/60 hover:shadow-[0_6px_20px_rgba(155,139,108,0.6)] transition-all duration-200"
          onClick={() => {
            setScale(1);
            setPosition({ x: 0, y: 0 });
          }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          title="Reset View"
        >
          <BattlxIcon icon="explore" className="w-6 h-6" />
        </motion.button>
      </div>
    </div>
  );
}
