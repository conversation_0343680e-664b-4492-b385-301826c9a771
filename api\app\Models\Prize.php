<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Prize extends Model
{
    use HasFactory;

    protected $fillable = [
        'prize_tree_id',
        'name',
        'description',
        'icon',
        'tier',
        'position',
        'category',
        'cost',
        'is_root',
        'reward_type',
        'reward_data'
    ];

    protected $casts = [
        'reward_data' => 'array',
        'is_root' => 'boolean'
    ];

    /**
     * Get the prize tree that this prize belongs to.
     */
    public function prizeTree()
    {
        return $this->belongsTo(PrizeTree::class);
    }

    /**
     * Get the prerequisites for this prize.
     */
    public function prerequisites()
    {
        return $this->belongsToMany(
            self::class,
            'prize_prerequisites',
            'prize_id',
            'prerequisite_id'
        )->withTimestamps();
    }

    /**
     * Get the prizes that have this prize as a prerequisite.
     */
    public function dependents()
    {
        return $this->belongsToMany(
            self::class,
            'prize_prerequisites',
            'prerequisite_id',
            'prize_id'
        )->withTimestamps();
    }

    /**
     * Get the users that have unlocked this prize.
     */
    public function users()
    {
        return $this->belongsToMany(
            TelegramUser::class,
            'user_prizes',
            'prize_id',
            'telegram_user_id'
        )->withTimestamps()->withPivot('unlocked_at', 'is_equipped');
    }

    /**
     * Get the reward details based on the reward type.
     */
    public function getRewardDetails()
    {
        if (!$this->reward_data) {
            return null;
        }

        switch ($this->reward_type) {
            case 'cosmetic':
                return [
                    'type' => $this->reward_data['type'] ?? 'unknown',
                    'visual_data' => $this->reward_data['visual_data'] ?? [],
                    'preview_image' => $this->reward_data['preview_image'] ?? null
                ];

            case 'title':
                return [
                    'title' => $this->reward_data['title'] ?? $this->name,
                    'color' => $this->reward_data['color'] ?? '#FFFFFF'
                ];

            case 'card':
                return [
                    'card_id' => $this->reward_data['card_id'] ?? null,
                    'rarity' => $this->reward_data['rarity'] ?? 'common'
                ];

            case 'balance':
                return [
                    'amount' => $this->reward_data['amount'] ?? 0,
                    'currency' => $this->reward_data['currency'] ?? 'coins'
                ];

            case 'booster':
                return [
                    'type' => $this->reward_data['type'] ?? 'unknown',
                    'multiplier' => $this->reward_data['multiplier'] ?? 1,
                    'duration' => $this->reward_data['duration'] ?? 24
                ];

            case 'special_item':
                return [
                    'item_id' => $this->reward_data['item_id'] ?? null,
                    'item_type' => $this->reward_data['item_type'] ?? 'unknown'
                ];

            case 'emote':
                return [
                    'emote' => $this->reward_data['emote'] ?? 'basic',
                    'animation' => $this->reward_data['animation'] ?? null
                ];

            default:
                return $this->reward_data;
        }
    }
}
