# Missing Features Addendum

## Overview
This document covers additional features that could enhance the Pet System but were not included in the core implementation. These features can be added as future enhancements based on user feedback and business requirements.

## Social Features

### Pet Showcasing System
```php
<?php
// File: api/app/Models/PetShowcase.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PetShowcase extends Model
{
    protected $fillable = [
        'telegram_user_id',
        'pet_id',
        'title',
        'description',
        'image_url',
        'is_public',
        'likes_count',
        'views_count',
        'featured_until'
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'featured_until' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function pet()
    {
        return $this->belongsTo(Pet::class);
    }

    public function likes()
    {
        return $this->hasMany(PetShowcaseLike::class);
    }
}
```

### Friend System Integration
```php
<?php
// File: api/app/Services/PetSocialService.php

namespace App\Services;

use App\Models\TelegramUser;
use App\Models\Pet;
use App\Models\PetShowcase;

class PetSocialService
{
    public function getFriendsShowcase(TelegramUser $user): array
    {
        $friendIds = $user->friends()->pluck('friend_id');
        
        return PetShowcase::whereIn('telegram_user_id', $friendIds)
                         ->where('is_public', true)
                         ->with(['user', 'pet.template'])
                         ->orderBy('created_at', 'desc')
                         ->limit(20)
                         ->get()
                         ->toArray();
    }

    public function createShowcase(TelegramUser $user, Pet $pet, array $data): PetShowcase
    {
        return PetShowcase::create([
            'telegram_user_id' => $user->id,
            'pet_id' => $pet->id,
            'title' => $data['title'],
            'description' => $data['description'],
            'is_public' => $data['is_public'] ?? true
        ]);
    }

    public function likePetShowcase(TelegramUser $user, PetShowcase $showcase): bool
    {
        $existing = $showcase->likes()
                           ->where('telegram_user_id', $user->id)
                           ->exists();

        if ($existing) {
            return false;
        }

        $showcase->likes()->create([
            'telegram_user_id' => $user->id
        ]);

        $showcase->increment('likes_count');
        return true;
    }
}
```

## Pet Trading System

### Trading Models
```php
<?php
// File: api/app/Models/PetTrade.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PetTrade extends Model
{
    protected $fillable = [
        'initiator_id',
        'recipient_id',
        'initiator_pet_id',
        'recipient_pet_id',
        'initiator_coins',
        'recipient_coins',
        'status',
        'expires_at',
        'completed_at'
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'completed_at' => 'datetime'
    ];

    public function initiator()
    {
        return $this->belongsTo(TelegramUser::class, 'initiator_id');
    }

    public function recipient()
    {
        return $this->belongsTo(TelegramUser::class, 'recipient_id');
    }

    public function initiatorPet()
    {
        return $this->belongsTo(Pet::class, 'initiator_pet_id');
    }

    public function recipientPet()
    {
        return $this->belongsTo(Pet::class, 'recipient_pet_id');
    }
}
```

### Trading Service
```php
<?php
// File: api/app/Services/PetTradingService.php

namespace App\Services;

use App\Models\TelegramUser;
use App\Models\Pet;
use App\Models\PetTrade;
use Illuminate\Support\Facades\DB;

class PetTradingService
{
    public function createTradeOffer(TelegramUser $initiator, array $data): PetTrade
    {
        $recipient = TelegramUser::find($data['recipient_id']);
        
        if (!$recipient) {
            throw new \Exception('Recipient not found');
        }

        // Validate pets belong to correct users
        $initiatorPet = $initiator->pets()->find($data['initiator_pet_id']);
        $recipientPet = $recipient->pets()->find($data['recipient_pet_id']);

        if (!$initiatorPet || !$recipientPet) {
            throw new \Exception('Invalid pets for trade');
        }

        return PetTrade::create([
            'initiator_id' => $initiator->id,
            'recipient_id' => $recipient->id,
            'initiator_pet_id' => $initiatorPet->id,
            'recipient_pet_id' => $recipientPet->id,
            'initiator_coins' => $data['initiator_coins'] ?? 0,
            'recipient_coins' => $data['recipient_coins'] ?? 0,
            'status' => 'pending',
            'expires_at' => now()->addDays(7)
        ]);
    }

    public function acceptTrade(TelegramUser $user, PetTrade $trade): bool
    {
        if ($trade->recipient_id !== $user->id) {
            throw new \Exception('Unauthorized to accept this trade');
        }

        if ($trade->status !== 'pending') {
            throw new \Exception('Trade is no longer available');
        }

        if ($trade->expires_at < now()) {
            throw new \Exception('Trade has expired');
        }

        DB::beginTransaction();
        try {
            // Transfer pets
            $trade->initiatorPet->update(['telegram_user_id' => $trade->recipient_id]);
            $trade->recipientPet->update(['telegram_user_id' => $trade->initiator_id]);

            // Transfer coins if any
            if ($trade->initiator_coins > 0) {
                $trade->initiator->decrement('balance', $trade->initiator_coins);
                $trade->recipient->increment('balance', $trade->initiator_coins);
            }

            if ($trade->recipient_coins > 0) {
                $trade->recipient->decrement('balance', $trade->recipient_coins);
                $trade->initiator->increment('balance', $trade->recipient_coins);
            }

            $trade->update([
                'status' => 'completed',
                'completed_at' => now()
            ]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
}
```

## Pet Customization System

### Customization Models
```php
<?php
// File: api/app/Models/PetAccessory.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PetAccessory extends Model
{
    protected $fillable = [
        'name',
        'type',
        'rarity',
        'image_url',
        'coin_cost',
        'gem_cost',
        'unlock_requirement',
        'stat_bonuses',
        'is_active'
    ];

    protected $casts = [
        'stat_bonuses' => 'array',
        'is_active' => 'boolean'
    ];

    public function userAccessories()
    {
        return $this->hasMany(UserPetAccessory::class);
    }
}

class UserPetAccessory extends Model
{
    protected $fillable = [
        'telegram_user_id',
        'pet_id',
        'pet_accessory_id',
        'is_equipped',
        'obtained_at'
    ];

    protected $casts = [
        'is_equipped' => 'boolean',
        'obtained_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function pet()
    {
        return $this->belongsTo(Pet::class);
    }

    public function accessory()
    {
        return $this->belongsTo(PetAccessory::class, 'pet_accessory_id');
    }
}
```

## Notification System

### Push Notification Service
```php
<?php
// File: api/app/Services/PetNotificationService.php

namespace App\Services;

use App\Models\TelegramUser;
use App\Models\Pet;
use Illuminate\Support\Facades\Http;

class PetNotificationService
{
    public function sendPetCareReminder(TelegramUser $user, Pet $pet): void
    {
        $message = "🐾 {$pet->display_name} needs your attention! Happiness: {$pet->happiness_percentage}%";
        
        $this->sendTelegramNotification($user, $message);
        $this->createInAppNotification($user, 'pet_care_reminder', [
            'pet_id' => $pet->id,
            'pet_name' => $pet->display_name,
            'happiness' => $pet->happiness_percentage
        ]);
    }

    public function sendEvolutionNotification(TelegramUser $user, Pet $pet): void
    {
        $message = "🌟 {$pet->display_name} is ready to evolve! Tap to evolve your pet.";
        
        $this->sendTelegramNotification($user, $message);
        $this->createInAppNotification($user, 'pet_evolution_ready', [
            'pet_id' => $pet->id,
            'pet_name' => $pet->display_name
        ]);
    }

    private function sendTelegramNotification(TelegramUser $user, string $message): void
    {
        if (!$user->telegram_chat_id) {
            return;
        }

        Http::post("https://api.telegram.org/bot" . config('telegram.bot_token') . "/sendMessage", [
            'chat_id' => $user->telegram_chat_id,
            'text' => $message,
            'parse_mode' => 'HTML'
        ]);
    }

    private function createInAppNotification(TelegramUser $user, string $type, array $data): void
    {
        $user->notifications()->create([
            'type' => $type,
            'data' => $data,
            'is_read' => false
        ]);
    }
}
```

## Pet Skills System

### Skills Models
```php
<?php
// File: api/app/Models/PetSkill.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PetSkill extends Model
{
    protected $fillable = [
        'name',
        'description',
        'skill_type',
        'max_level',
        'base_effect',
        'level_multiplier',
        'unlock_level',
        'category'
    ];

    protected $casts = [
        'base_effect' => 'array'
    ];

    public function petSkills()
    {
        return $this->hasMany(PetSkillInstance::class);
    }
}

class PetSkillInstance extends Model
{
    protected $fillable = [
        'pet_id',
        'pet_skill_id',
        'current_level',
        'experience',
        'experience_to_next_level'
    ];

    public function pet()
    {
        return $this->belongsTo(Pet::class);
    }

    public function skill()
    {
        return $this->belongsTo(PetSkill::class, 'pet_skill_id');
    }

    public function getCurrentEffect(): array
    {
        $baseEffect = $this->skill->base_effect;
        $multiplier = $this->skill->level_multiplier;
        
        $effect = [];
        foreach ($baseEffect as $key => $value) {
            $effect[$key] = $value * (1 + ($this->current_level - 1) * $multiplier);
        }
        
        return $effect;
    }
}
```

## Pet Breeding System

### Breeding Models
```php
<?php
// File: api/app/Models/PetBreeding.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PetBreeding extends Model
{
    protected $fillable = [
        'telegram_user_id',
        'parent1_id',
        'parent2_id',
        'offspring_template_id',
        'breeding_time_hours',
        'started_at',
        'completed_at',
        'status',
        'cost_coins',
        'cost_gems'
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function parent1()
    {
        return $this->belongsTo(Pet::class, 'parent1_id');
    }

    public function parent2()
    {
        return $this->belongsTo(Pet::class, 'parent2_id');
    }

    public function offspringTemplate()
    {
        return $this->belongsTo(PetTemplate::class, 'offspring_template_id');
    }

    public function isComplete(): bool
    {
        return $this->status === 'completed' || 
               ($this->started_at && $this->started_at->addHours($this->breeding_time_hours) <= now());
    }
}
```

## Frontend Components for Missing Features

### Pet Trading Component
```typescript
// File: battlx/src/components/trading/PetTradingModal.tsx

import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface PetTradingModalProps {
  isOpen: boolean;
  onClose: () => void;
  userPets: Pet[];
  targetUser: User;
}

const PetTradingModal: React.FC<PetTradingModalProps> = ({
  isOpen,
  onClose,
  userPets,
  targetUser
}) => {
  const [selectedUserPet, setSelectedUserPet] = useState<Pet | null>(null);
  const [selectedTargetPet, setSelectedTargetPet] = useState<Pet | null>(null);
  const [coinOffer, setCoinOffer] = useState(0);

  const handleCreateTrade = async () => {
    if (!selectedUserPet || !selectedTargetPet) return;

    try {
      await fetch('/api/pet-trades', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          recipient_id: targetUser.id,
          initiator_pet_id: selectedUserPet.id,
          recipient_pet_id: selectedTargetPet.id,
          initiator_coins: coinOffer
        })
      });

      onClose();
    } catch (error) {
      console.error('Trade creation failed:', error);
    }
  };

  if (!isOpen) return null;

  return (
    <motion.div className="trading-modal-overlay">
      <motion.div className="trading-modal">
        <h2>Trade with {targetUser.username}</h2>
        
        <div className="trade-setup">
          <div className="your-offer">
            <h3>Your Offer</h3>
            <div className="pet-selection">
              {userPets.map(pet => (
                <div
                  key={pet.id}
                  className={`pet-option ${selectedUserPet?.id === pet.id ? 'selected' : ''}`}
                  onClick={() => setSelectedUserPet(pet)}
                >
                  <img src={pet.current_image} alt={pet.display_name} />
                  <span>{pet.display_name}</span>
                </div>
              ))}
            </div>
            
            <div className="coin-offer">
              <label>Additional Coins:</label>
              <input
                type="number"
                value={coinOffer}
                onChange={(e) => setCoinOffer(Number(e.target.value))}
                min="0"
              />
            </div>
          </div>

          <div className="their-pets">
            <h3>{targetUser.username}'s Pets</h3>
            {/* Similar pet selection for target user */}
          </div>
        </div>

        <div className="trade-actions">
          <button onClick={handleCreateTrade} disabled={!selectedUserPet || !selectedTargetPet}>
            Create Trade Offer
          </button>
          <button onClick={onClose}>Cancel</button>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default PetTradingModal;
```

## Implementation Priority

### Phase 1 (High Priority)
1. **Notification System** - Essential for user retention
2. **Pet Showcasing** - Increases social engagement
3. **Basic Customization** - Enhances personalization

### Phase 2 (Medium Priority)
1. **Pet Trading** - Adds economic layer
2. **Skills System** - Deepens gameplay
3. **Friend Integration** - Social features

### Phase 3 (Low Priority)
1. **Pet Breeding** - Complex feature for advanced users
2. **Advanced Customization** - Cosmetic enhancements
3. **Competition System** - Competitive elements

## Conclusion

These missing features represent opportunities for future development based on:
- **User Feedback**: What users request most
- **Engagement Metrics**: Which features drive retention
- **Business Goals**: Monetization and growth objectives
- **Technical Capacity**: Development resources available

The core Pet System provides a solid foundation that can be extended with these features incrementally, allowing for data-driven decisions about which features to prioritize.
