# Pet System Database Schema Implementation

## Overview
This document covers the complete database schema for the Pet System with Prize Tree integration, including all tables, relationships, and migrations.

## Implementation Time: 2-3 days
## Complexity: Medium
## Dependencies: Existing prize tree system

## Database Tables Overview

### Core Pet System Tables
1. `pets` - Individual pet instances owned by users
2. `pet_templates` - Master pet definitions and configurations
3. `pet_interactions` - Daily interaction tracking
4. `pet_happiness_logs` - Happiness change history

### Collectible System Tables
5. `collectibles` - User-owned collectible items
6. `collectible_templates` - Master collectible definitions
7. `collection_sets` - Collectible set definitions
8. `user_collection_progress` - Set completion tracking

### Mystery Box System Tables
9. `mystery_box_types` - Box type definitions
10. `mystery_box_unlocks` - User box unlock tracking
11. `mystery_box_openings` - Box opening history

### Integration Tables
12. `pet_mystery_box_unlocks` - Pet-to-box unlock mappings
13. `pet_collectible_rewards` - Pet-to-collectible mappings

## Migration Files

### Migration 1: Pet Templates Table
```php
<?php
// File: api/database/migrations/2024_01_01_000001_create_pet_templates_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pet_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100);
            $table->enum('category', ['shadow', 'undead', 'demon', 'spirit', 'beast']);
            $table->enum('rarity', ['common', 'rare', 'epic', 'legendary', 'mythic']);
            $table->text('description');
            $table->string('image_url')->nullable();
            $table->string('animation_url')->nullable();
            
            // Purchase configuration
            $table->integer('coin_cost')->default(0);
            $table->integer('gem_cost')->default(0);
            $table->integer('prize_tree_unlock_level')->nullable();
            $table->boolean('is_premium_only')->default(false);
            
            // Interaction configuration
            $table->integer('base_happiness')->default(50);
            $table->integer('max_happiness')->default(100);
            $table->integer('happiness_decay_rate')->default(5); // per day
            
            // Mystery box unlocks
            $table->json('mystery_box_unlocks'); // Array of box types this pet unlocks
            $table->string('collectible_reward_id'); // Collectible given when pet is obtained
            
            // Evolution configuration
            $table->integer('max_level')->default(100);
            $table->json('evolution_levels'); // [10, 25, 50, 100]
            $table->json('evolution_images'); // URLs for each evolution stage
            
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index(['category', 'rarity']);
            $table->index('is_active');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pet_templates');
    }
};
```

### Migration 2: User Pets Table
```php
<?php
// File: api/database/migrations/2024_01_01_000002_create_pets_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->foreignId('pet_template_id')->constrained('pet_templates')->onDelete('cascade');
            
            // Pet state
            $table->integer('level')->default(1);
            $table->integer('experience')->default(0);
            $table->integer('happiness')->default(50);
            
            // Interaction tracking
            $table->timestamp('last_fed')->nullable();
            $table->timestamp('last_played')->nullable();
            $table->timestamp('last_petted')->nullable();
            $table->integer('daily_interaction_count')->default(0);
            $table->date('daily_interaction_reset_date')->default(DB::raw('CURRENT_DATE'));
            
            // Status flags
            $table->boolean('is_featured')->default(false); // For home screen display
            $table->boolean('is_favorite')->default(false);
            $table->string('nickname', 50)->nullable();
            
            // Evolution tracking
            $table->integer('evolution_stage')->default(0); // 0=base, 1=first, 2=second, etc.
            $table->timestamp('last_evolution')->nullable();
            
            $table->timestamps();
            
            $table->index(['telegram_user_id', 'is_featured']);
            $table->index(['telegram_user_id', 'pet_template_id']);
            $table->unique(['telegram_user_id', 'pet_template_id']); // One pet per template per user
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pets');
    }
};
```

### Migration 3: Pet Interactions Table
```php
<?php
// File: api/database/migrations/2024_01_01_000003_create_pet_interactions_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('pet_interactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->foreignId('pet_id')->constrained('pets')->onDelete('cascade');
            
            $table->enum('interaction_type', ['feed', 'play', 'pet', 'train']);
            $table->integer('energy_cost');
            $table->integer('happiness_gained');
            $table->integer('experience_gained');
            
            // Rewards given
            $table->integer('coins_rewarded')->default(0);
            $table->integer('materials_rewarded')->default(0);
            $table->string('collectible_rewarded')->nullable();
            $table->boolean('bonus_applied')->default(false); // Happy pet bonus
            
            $table->timestamp('interaction_time');
            $table->timestamps();
            
            $table->index(['telegram_user_id', 'interaction_time']);
            $table->index(['pet_id', 'interaction_type']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('pet_interactions');
    }
};
```

### Migration 4: Collectible Templates Table
```php
<?php
// File: api/database/migrations/2024_01_01_000004_create_collectible_templates_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('collectible_templates', function (Blueprint $table) {
            $table->id();
            $table->string('collectible_id', 50)->unique(); // e.g., 'shadow_essence'
            $table->string('name', 100);
            $table->enum('type', ['artifact', 'trophy', 'relic', 'essence', 'scroll']);
            $table->enum('category', ['shadow', 'undead', 'demon', 'spirit', 'beast']);
            $table->enum('rarity', ['common', 'rare', 'epic', 'legendary', 'mythic']);
            $table->text('description');
            $table->string('image_url');
            
            // Collection set information
            $table->string('collection_set_id'); // e.g., 'shadow_collection'
            $table->integer('set_position'); // Order within the set
            
            // Unlock requirements
            $table->enum('unlock_source', ['pet_purchase', 'mystery_box', 'prize_tree', 'collection_bonus']);
            $table->string('unlock_requirement')->nullable(); // Pet ID, box type, etc.
            
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['category', 'rarity']);
            $table->index('collection_set_id');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('collectible_templates');
    }
};
```

### Migration 5: User Collectibles Table
```php
<?php
// File: api/database/migrations/2024_01_01_000005_create_collectibles_table.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('collectibles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->string('collectible_id', 50); // References collectible_templates.collectible_id
            
            $table->enum('unlock_source', ['pet_purchase', 'mystery_box', 'prize_tree', 'collection_bonus']);
            $table->string('source_reference')->nullable(); // Pet ID, box opening ID, etc.
            $table->timestamp('obtained_at');
            
            $table->timestamps();
            
            $table->unique(['telegram_user_id', 'collectible_id']);
            $table->index(['telegram_user_id', 'obtained_at']);
            
            $table->foreign('collectible_id')
                  ->references('collectible_id')
                  ->on('collectible_templates')
                  ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('collectibles');
    }
};
```

## Running Migrations

### Step 1: Create Migration Files
```bash
cd api
php artisan make:migration create_pet_templates_table
php artisan make:migration create_pets_table
php artisan make:migration create_pet_interactions_table
php artisan make:migration create_collectible_templates_table
php artisan make:migration create_collectibles_table
```

### Step 2: Copy Migration Content
Copy the migration code above into each respective file.

### Step 3: Run Migrations
```bash
php artisan migrate
```

### Step 4: Verify Tables
```bash
php artisan tinker
Schema::hasTable('pet_templates');
Schema::hasTable('pets');
Schema::hasTable('pet_interactions');
Schema::hasTable('collectible_templates');
Schema::hasTable('collectibles');
```

## Acceptance Criteria
- [x] All 5 core tables created successfully
- [x] Foreign key constraints properly established
- [x] Indexes created for performance optimization
- [x] Unique constraints prevent duplicate data
- [x] Migration rollback works correctly

## Next Steps
1. Create remaining migration files (Mystery Box system)
2. Implement database seeders for pet templates
3. Create Eloquent models with relationships
4. Set up API endpoints for pet management

## Troubleshooting
- If foreign key errors occur, ensure telegram_users table exists
- Check PostgreSQL version compatibility for JSON columns
- Verify enum values match exactly in all references
