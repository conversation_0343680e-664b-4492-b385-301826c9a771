# PvP Visual Assets & Implementation Guide

## Asset Creation Strategy

### 1. Art Style Direction

**Theme: Dark Gothic Futuristic**
- **Color Palette**: Dark backgrounds (#1A1617, #2A2A2A) with bright accent colors
- **Character Style**: Pixel art with smooth animations (32x32 or 64x64 sprites)
- **UI Style**: Consistent with your current gothic theme
- **Effects**: Glowing particles, energy trails, impact flashes

### 2. Character Asset Specifications

#### 2.1 Character Sprite Sheets
**File Structure:**
```
public/game/assets/characters/
├── striker/
│   ├── idle.png          # 4 frames, 64x64 each
│   ├── walk.png          # 6 frames, 64x64 each
│   ├── attack.png        # 8 frames, 64x64 each
│   ├── defend.png        # 4 frames, 64x64 each
│   ├── special.png       # 10 frames, 64x64 each
│   ├── hurt.png          # 3 frames, 64x64 each
│   ├── death.png         # 6 frames, 64x64 each
│   └── portrait.png      # 128x128 static image
├── guardian/
├── mystic/
├── assassin/
└── engineer/
```

#### 2.2 Sprite Sheet Format
**Each sprite sheet should be horizontal strips:**
```
Frame 1 | Frame 2 | Frame 3 | Frame 4 | ...
64x64   | 64x64   | 64x64   | 64x64   | ...
```

#### 2.3 Character Design Guidelines

**Striker (Fire Element):**
- **Colors**: Red, orange, yellow accents
- **Design**: Agile warrior with flame effects
- **Weapon**: Dual energy blades
- **Special Effects**: Fire trails, speed lines

**Guardian (Earth Element):**
- **Colors**: Brown, green, metallic gray
- **Design**: Heavy armor, defensive stance
- **Weapon**: Energy shield and hammer
- **Special Effects**: Rock barriers, ground impacts

**Mystic (Void Element):**
- **Colors**: Purple, blue, dark energy
- **Design**: Robed figure with floating elements
- **Weapon**: Staff with energy orb
- **Special Effects**: Portal effects, energy waves

### 3. Arena Asset Specifications

#### 3.1 Arena Backgrounds
```
public/game/assets/arenas/
├── desert/
│   ├── background.jpg    # 800x600 background
│   ├── tiles.png         # 80x80 tile variations
│   └── effects/
│       ├── sandstorm.png # Particle texture
│       └── heat_wave.png # Distortion effect
├── forest/
│   ├── background.jpg
│   ├── tiles.png
│   └── effects/
│       ├── leaves.png
│       └── magic_glow.png
└── urban/
    ├── background.jpg
    ├── tiles.png
    └── effects/
        ├── sparks.png
        └── neon_glow.png
```

#### 3.2 UI Asset Requirements
```
public/game/assets/ui/
├── battle/
│   ├── health_bar_bg.png     # 200x20
│   ├── health_bar_fill.png   # 200x20
│   ├── energy_bar_bg.png     # 200x15
│   ├── energy_bar_fill.png   # 200x15
│   ├── turn_indicator.png    # 64x64
│   └── action_buttons/
│       ├── attack_btn.png    # 80x80
│       ├── defend_btn.png    # 80x80
│       ├── special_btn.png   # 80x80
│       └── move_btn.png      # 80x80
├── panels/
│   ├── battle_panel_bg.png   # 400x600
│   ├── character_card_bg.png # 300x400
│   └── modal_bg.png          # 500x300
└── icons/
    ├── abilities/            # 64x64 each
    ├── status_effects/       # 32x32 each
    └── elements/             # 48x48 each
```

### 4. Asset Loading Implementation

#### 4.1 Asset Manager Service
```typescript
// battlx/src/lib/AssetManager.ts
export class AssetManager {
  private static instance: AssetManager;
  private loadedAssets: Map<string, HTMLImageElement> = new Map();
  private loadingPromises: Map<string, Promise<HTMLImageElement>> = new Map();
  private spriteSheets: Map<string, SpriteSheet> = new Map();

  static getInstance(): AssetManager {
    if (!this.instance) {
      this.instance = new AssetManager();
    }
    return this.instance;
  }

  async preloadBattleAssets(): Promise<void> {
    const essentialAssets = [
      // Character sprites
      'characters/striker/idle.png',
      'characters/striker/attack.png',
      'characters/guardian/idle.png',
      'characters/guardian/attack.png',
      
      // Arena assets
      'arenas/desert/background.jpg',
      'arenas/desert/tiles.png',
      
      // UI assets
      'ui/battle/health_bar_bg.png',
      'ui/battle/health_bar_fill.png',
      'ui/battle/action_buttons/attack_btn.png',
      'ui/battle/action_buttons/defend_btn.png',
    ];

    await Promise.all(essentialAssets.map(path => this.loadAsset(path)));
  }

  async loadAsset(path: string): Promise<HTMLImageElement> {
    if (this.loadedAssets.has(path)) {
      return this.loadedAssets.get(path)!;
    }

    if (this.loadingPromises.has(path)) {
      return this.loadingPromises.get(path)!;
    }

    const promise = new Promise<HTMLImageElement>((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.loadedAssets.set(path, img);
        this.loadingPromises.delete(path);
        resolve(img);
      };
      img.onerror = () => {
        this.loadingPromises.delete(path);
        reject(new Error(`Failed to load asset: ${path}`));
      };
      img.src = `/game/assets/${path}`;
    });

    this.loadingPromises.set(path, promise);
    return promise;
  }

  createSpriteSheet(imagePath: string, frameWidth: number, frameHeight: number, frameCount: number): SpriteSheet {
    const image = this.loadedAssets.get(imagePath);
    if (!image) {
      throw new Error(`Image not loaded: ${imagePath}`);
    }

    const spriteSheet = new SpriteSheet(image, frameWidth, frameHeight, frameCount);
    this.spriteSheets.set(imagePath, spriteSheet);
    return spriteSheet;
  }

  getSpriteSheet(imagePath: string): SpriteSheet | null {
    return this.spriteSheets.get(imagePath) || null;
  }
}

export class SpriteSheet {
  constructor(
    private image: HTMLImageElement,
    private frameWidth: number,
    private frameHeight: number,
    private frameCount: number
  ) {}

  drawFrame(
    ctx: CanvasRenderingContext2D,
    frameIndex: number,
    x: number,
    y: number,
    scale: number = 1
  ): void {
    const sourceX = (frameIndex % this.frameCount) * this.frameWidth;
    const sourceY = 0;

    ctx.drawImage(
      this.image,
      sourceX, sourceY, this.frameWidth, this.frameHeight,
      x - (this.frameWidth * scale) / 2,
      y - (this.frameHeight * scale) / 2,
      this.frameWidth * scale,
      this.frameHeight * scale
    );
  }

  getFrameCount(): number {
    return this.frameCount;
  }
}
```

#### 4.2 Animation System
```typescript
// battlx/src/lib/AnimationSystem.ts
export class AnimationSystem {
  private animations: Map<string, Animation> = new Map();

  createAnimation(
    id: string,
    spriteSheet: SpriteSheet,
    frameRate: number,
    loop: boolean = true
  ): Animation {
    const animation = new Animation(spriteSheet, frameRate, loop);
    this.animations.set(id, animation);
    return animation;
  }

  getAnimation(id: string): Animation | null {
    return this.animations.get(id) || null;
  }

  update(deltaTime: number): void {
    this.animations.forEach(animation => {
      animation.update(deltaTime);
    });
  }
}

export class Animation {
  private currentFrame: number = 0;
  private elapsedTime: number = 0;
  private frameTime: number;
  private isPlaying: boolean = true;

  constructor(
    private spriteSheet: SpriteSheet,
    frameRate: number,
    private loop: boolean = true
  ) {
    this.frameTime = 1000 / frameRate; // Convert to milliseconds
  }

  update(deltaTime: number): void {
    if (!this.isPlaying) return;

    this.elapsedTime += deltaTime;

    if (this.elapsedTime >= this.frameTime) {
      this.currentFrame++;
      this.elapsedTime = 0;

      if (this.currentFrame >= this.spriteSheet.getFrameCount()) {
        if (this.loop) {
          this.currentFrame = 0;
        } else {
          this.currentFrame = this.spriteSheet.getFrameCount() - 1;
          this.isPlaying = false;
        }
      }
    }
  }

  draw(ctx: CanvasRenderingContext2D, x: number, y: number, scale: number = 1): void {
    this.spriteSheet.drawFrame(ctx, this.currentFrame, x, y, scale);
  }

  play(): void {
    this.isPlaying = true;
  }

  pause(): void {
    this.isPlaying = false;
  }

  reset(): void {
    this.currentFrame = 0;
    this.elapsedTime = 0;
    this.isPlaying = true;
  }

  getCurrentFrame(): number {
    return this.currentFrame;
  }

  isFinished(): boolean {
    return !this.loop && this.currentFrame >= this.spriteSheet.getFrameCount() - 1;
  }
}
```

### 5. Enhanced Battle Renderer

#### 5.1 Character Renderer with Animations
```typescript
// battlx/src/components/battle/CharacterRenderer.ts
export class CharacterRenderer {
  private animations: Map<string, Animation> = new Map();
  private currentAnimation: string = 'idle';
  private position: { x: number; y: number };
  private scale: number = 1;

  constructor(
    private characterType: string,
    private assetManager: AssetManager,
    private animationSystem: AnimationSystem
  ) {
    this.initializeAnimations();
  }

  private async initializeAnimations(): Promise<void> {
    const animationConfigs = [
      { name: 'idle', frames: 4, frameRate: 8 },
      { name: 'walk', frames: 6, frameRate: 12 },
      { name: 'attack', frames: 8, frameRate: 16, loop: false },
      { name: 'defend', frames: 4, frameRate: 8 },
      { name: 'special', frames: 10, frameRate: 20, loop: false },
      { name: 'hurt', frames: 3, frameRate: 12, loop: false },
      { name: 'death', frames: 6, frameRate: 8, loop: false }
    ];

    for (const config of animationConfigs) {
      const imagePath = `characters/${this.characterType}/${config.name}.png`;
      await this.assetManager.loadAsset(imagePath);
      
      const spriteSheet = this.assetManager.createSpriteSheet(
        imagePath, 64, 64, config.frames
      );
      
      const animation = this.animationSystem.createAnimation(
        `${this.characterType}_${config.name}`,
        spriteSheet,
        config.frameRate,
        config.loop !== false
      );
      
      this.animations.set(config.name, animation);
    }
  }

  playAnimation(animationName: string): void {
    if (this.animations.has(animationName)) {
      this.currentAnimation = animationName;
      this.animations.get(animationName)!.reset();
      this.animations.get(animationName)!.play();
    }
  }

  update(deltaTime: number): void {
    const animation = this.animations.get(this.currentAnimation);
    if (animation) {
      animation.update(deltaTime);
      
      // Return to idle after non-looping animations finish
      if (animation.isFinished() && this.currentAnimation !== 'idle') {
        this.playAnimation('idle');
      }
    }
  }

  draw(ctx: CanvasRenderingContext2D, x: number, y: number): void {
    const animation = this.animations.get(this.currentAnimation);
    if (animation) {
      animation.draw(ctx, x, y, this.scale);
    }
  }

  setPosition(x: number, y: number): void {
    this.position = { x, y };
  }

  setScale(scale: number): void {
    this.scale = scale;
  }
}
```

### 6. Asset Creation Tools & Resources

#### 6.1 Recommended Tools
**For Pixel Art:**
- **Aseprite** ($19.99) - Professional pixel art tool
- **Piskel** (Free) - Web-based pixel art editor
- **GraphicsGale** (Free) - Windows pixel art software

**For UI Design:**
- **Figma** (Free) - UI design and prototyping
- **Photoshop** - Professional image editing
- **GIMP** (Free) - Open-source alternative

#### 6.2 Asset Creation Workflow
1. **Concept Art**: Sketch character designs and poses
2. **Base Sprites**: Create idle pose for each character
3. **Animation Frames**: Create movement and action frames
4. **Color Variants**: Create different skins/themes
5. **Export**: Save as PNG with transparency
6. **Optimization**: Compress images for web delivery

#### 6.3 Performance Considerations
**Image Optimization:**
```typescript
// Lazy loading for non-essential assets
const loadCharacterAssets = async (characterType: string) => {
  const priority = ['idle', 'attack']; // Load these first
  const secondary = ['walk', 'defend', 'special']; // Load these after
  
  // Load priority assets immediately
  await Promise.all(
    priority.map(anim => 
      assetManager.loadAsset(`characters/${characterType}/${anim}.png`)
    )
  );
  
  // Load secondary assets in background
  secondary.forEach(anim => 
    assetManager.loadAsset(`characters/${characterType}/${anim}.png`)
  );
};
```

### 7. Implementation Timeline

**Week 1: Basic Assets**
- Create placeholder character sprites (simple colored rectangles)
- Implement asset loading system
- Basic animation framework

**Week 2: Character Art**
- Design and create striker character sprites
- Implement character animation system
- Add basic attack/defend animations

**Week 3: Arena Assets**
- Create desert arena background and tiles
- Add environmental effects
- Implement arena rendering

**Week 4: Polish & Effects**
- Add particle effects for attacks
- Create UI assets and integrate
- Performance optimization

This asset strategy provides a clear path from basic functionality to polished visuals while maintaining performance on mobile devices.
