# PowerShell script for testing referral system
# Run this in PowerShell: .\test_referral.ps1

$API_URL = "http://localhost:8000/api"

Write-Host "=== Testing Referral System ===" -ForegroundColor Green
Write-Host ""

# Test 1: Create a referrer user (User A)
Write-Host "1. Creating referrer user (ID: 12345)..." -ForegroundColor Yellow
$referrerBody = @{
    telegram_id = 12345
    first_name = "Alice"
    last_name = "Referrer"
    username = "alice_referrer"
} | ConvertTo-Json

try {
    $referrerResponse = Invoke-RestMethod -Uri "$API_URL/auth/telegram-user" -Method Post -Body $referrerBody -ContentType "application/json"
    Write-Host "Referrer Response: $($referrerResponse | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "Error creating referrer: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 2: Create a referred user (User B) with referral
Write-Host "2. Creating referred user (ID: 67890) with referral from 12345..." -ForegroundColor Yellow
$referredBody = @{
    telegram_id = 67890
    first_name = "Bob"
    last_name = "Referred"
    username = "bob_referred"
    referred_by = "12345"
} | ConvertTo-Json

try {
    $referredResponse = Invoke-RestMethod -Uri "$API_URL/auth/telegram-user" -Method Post -Body $referredBody -ContentType "application/json"
    Write-Host "Referred Response: $($referredResponse | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "Error creating referred user: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 3: Try to create the same user again (should not give referral bonus)
Write-Host "3. Trying to create the same user again (should not give referral bonus)..." -ForegroundColor Yellow
try {
    $duplicateResponse = Invoke-RestMethod -Uri "$API_URL/auth/telegram-user" -Method Post -Body $referredBody -ContentType "application/json"
    Write-Host "Duplicate Response: $($duplicateResponse | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "Error with duplicate user: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 4: Test self-referral (should not work)
Write-Host "4. Testing self-referral (ID: 99999 referring themselves)..." -ForegroundColor Yellow
$selfReferralBody = @{
    telegram_id = 99999
    first_name = "Charlie"
    last_name = "SelfRef"
    username = "charlie_self"
    referred_by = "99999"
} | ConvertTo-Json

try {
    $selfReferralResponse = Invoke-RestMethod -Uri "$API_URL/auth/telegram-user" -Method Post -Body $selfReferralBody -ContentType "application/json"
    Write-Host "Self-referral Response: $($selfReferralResponse | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "Error with self-referral: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 5: Test referral with non-existent referrer
Write-Host "5. Testing referral with non-existent referrer (ID: 999999)..." -ForegroundColor Yellow
$invalidReferrerBody = @{
    telegram_id = 11111
    first_name = "David"
    last_name = "InvalidRef"
    username = "david_invalid"
    referred_by = "999999"
} | ConvertTo-Json

try {
    $invalidReferrerResponse = Invoke-RestMethod -Uri "$API_URL/auth/telegram-user" -Method Post -Body $invalidReferrerBody -ContentType "application/json"
    Write-Host "Invalid referrer Response: $($invalidReferrerResponse | ConvertTo-Json)" -ForegroundColor Cyan
} catch {
    Write-Host "Error with invalid referrer: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

Write-Host "=== Test completed ===" -ForegroundColor Green
Write-Host ""
Write-Host "Check the Laravel logs for detailed referral processing information:" -ForegroundColor Yellow
Write-Host "Get-Content api\storage\logs\laravel.log -Tail 50" -ForegroundColor Cyan
