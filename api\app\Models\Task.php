<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Task extends Model
{
    protected $fillable = [
        'title',
        'description',
        'url',
        'reward',
        'type_id'
    ];

    protected $casts = [
        'reward' => 'integer',
        'type_id' => 'integer'
    ];

    /**
     * Get the task type that this task belongs to.
     */
    public function type()
    {
        return $this->belongsTo(TaskType::class);
    }

    public function getImageAttribute($value)
    {
        return $value ? env("APP_STORAGE_URL", "/") . 'storage/' . $value : null;
    }

    public function telegramUsers()
    {
        return $this->belongsToMany(TelegramUser::class, 'telegram_user_tasks')
            ->withPivot('is_submitted', 'is_rewarded', 'submitted_at')
            ->withTimestamps();
    }
}
