/**
 * Asset Loader
 * Handles loading all game assets
 */
class AssetLoader {
    /**
     * @param {Engine} engine - Game engine
     */
    constructor(engine, options = {}) {
        this.engine = engine;
        this.assetsToLoad = 0;
        this.assetsLoaded = 0;
        this.onComplete = null;
        this.options = options;

        // Path generator for Laravel integration
        this.pathGenerator = options.pathGenerator || ((path) => {
            // Default path generator - for Laravel integration
            // Remove 'assets/' prefix if it exists in the path
            const cleanPath = path.replace(/^assets\//, '');
            return `/game/${cleanPath}`;
        });
    }

    /**
     * Load all game assets
     * @param {Function} onComplete - Callback when all assets are loaded
     */
    loadAll(onComplete) {
        this.onComplete = onComplete;

        // Load images
        this.loadImage('background', 'assets/graphics/background.png');
        this.loadImage('gameOverBackground', 'assets/graphics/gameOver.jpg');
        this.loadImage('platform', 'assets/graphics/platform.png');
        this.loadImage('lose', 'assets/graphics/lose.png');

        // Load sprite atlases
        // Load player sprite atlas with high-res dimensions (1416x356)
        this.loadSpriteAtlas('player', 'assets/graphics/jazz.png', {
            'player-run': {
                x: 0,
                y: 0,
                width: 1408, // 176 * 8 frames = 1408 (slightly adjusted from 1416 for even division)
                height: 176, // High-res height for running animation
                sliceX: 8,
                anims: {
                    run: { from: 0, to: 7, loop: true, speed: 18 }
                }
            },
            'player-jump': {
                x: 0,
                y: 180, // Position after the run frames
                width: 1120, // 140 * 8 frames = 1120 (proportional to original 290)
                height: 176, // High-res height for jumping animation
                sliceX: 8,
                anims: {
                    jump: { from: 0, to: 7, loop: true, speed: 80 }
                }
            }
        });

        // Load turtle sprite atlas
        this.loadImage('turtle-img', 'assets/graphics/turtle.png');
        this.loadSpriteAtlas('turtle', 'assets/graphics/turtle.png', {
            x: 0,
            y: 0,
            width: 888,
            height: 63,
            sliceX: 12,
            anims: {
                run: { from: 0, to: 11, loop: true, speed: 10 }
            }
        });

        // Load diamond sprite atlas
        this.loadImage('diamond-img', 'assets/graphics/diamond.png');
        this.loadSpriteAtlas('diamond', 'assets/graphics/diamond.png', {
            x: 0,
            y: 0,
            width: 416,
            height: 52,
            sliceX: 8,
            anims: {
                spin: { from: 0, to: 7, loop: true, speed: 10 }
            }
        });

        // Load sounds
        this.loadSound('hurtSound', 'assets/sounds/hurt.mp3');
        this.loadSound('hitSound', 'assets/sounds/hitEnemy.mp3');
        this.loadSound('jumpSound', 'assets/sounds/jump.mp3');
        this.loadSound('diamondSound', 'assets/sounds/diamond.mp3');
        this.loadSound('gameMusic', 'assets/sounds/gameMusic.mp3');
        this.loadSound('menuMusic', 'assets/sounds/menuMusic.mp3');
        this.loadSound('gameOver', 'assets/sounds/gameOver.mp3');
    }

    /**
     * Load an image
     * @param {string} id - Image identifier
     * @param {string} path - Image path
     */
    loadImage(id, path) {
        this.assetsToLoad++;
        const processedPath = this.options.usePathGenerator ? this.pathGenerator(path) : path;
        this.engine.loadImage(id, processedPath)
            .then(() => this.assetLoaded())
            .catch(error => {
                console.error(error);
                this.assetLoaded();
            });
    }

    /**
     * Load a sprite atlas
     * @param {string} id - Sprite identifier
     * @param {string} path - Image path
     * @param {Object} config - Sprite configuration
     */
    loadSpriteAtlas(id, path, config) {
        this.assetsToLoad++;
        const processedPath = this.options.usePathGenerator ? this.pathGenerator(path) : path;
        this.engine.loadSpriteAtlas(id, processedPath, config)
            .then(() => this.assetLoaded())
            .catch(error => {
                console.error(error);
                this.assetLoaded();
            });
    }

    /**
     * Load a sound
     * @param {string} id - Sound identifier
     * @param {string} path - Sound path
     */
    loadSound(id, path) {
        this.assetsToLoad++;
        const processedPath = this.options.usePathGenerator ? this.pathGenerator(path) : path;
        this.engine.loadSound(id, processedPath)
            .then(() => this.assetLoaded())
            .catch(error => {
                console.error(error);
                this.assetLoaded();
            });
    }

    /**
     * Called when an asset is loaded
     */
    assetLoaded() {
        this.assetsLoaded++;
        if (this.assetsLoaded >= this.assetsToLoad && this.onComplete) {
            this.onComplete();
        }
    }

    /**
     * Get the loading progress
     * @returns {number} - Progress (0-1)
     */
    getProgress() {
        return this.assetsToLoad > 0 ? this.assetsLoaded / this.assetsToLoad : 1;
    }
}

// Explicitly add AssetLoader to the window object
window.AssetLoader = AssetLoader;
