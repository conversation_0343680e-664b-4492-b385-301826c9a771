import { useEffect, useState } from "react";
import { TelegramWebApps } from "telegram-webapps-types";

/**
 * Hook to get the initial data from the Telegram Web Apps API already parsed.
 * @example
 * const { hash } = useTelegramInitData();
 * console.log({ hash });
 */

const fakeData = {
  user: {
    id: 1,
    first_name: "<PERSON>",
    last_name: "<PERSON><PERSON>",
    usernames: "johndo<PERSON>",
  },

  start_param: "ref1",
} as TelegramWebApps.WebAppInitData;

function useTelegramInitData() {
  const [data, setData] = useState<TelegramWebApps.WebAppInitData>({});

  useEffect(() => {
    // Check if we're in a real Telegram environment
    const isInTelegram = window.Telegram?.WebApp?.initData && window.Telegram.WebApp.initData.length > 0;

    // Check if we're running on localhost (true local development)
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

    // Use fake data only when on localhost AND not in Telegram
    const shouldUseFakeData = isLocalhost && !isInTelegram;

    console.log("Telegram Environment Check:", {
      isDev: import.meta.env.DEV,
      isLocalhost,
      isInTelegram,
      shouldUseFakeData,
      hostname: window.location.hostname,
      initDataLength: window.Telegram?.WebApp?.initData?.length || 0
    });

    if (shouldUseFakeData) {
      console.log("Using fake data for localhost development");
      setData(fakeData);
      return;
    }

    // Parse real Telegram data (works on VPS even in dev mode)
    const firstLayerInitData = Object.fromEntries(
      new URLSearchParams(window.Telegram.WebApp.initData || "")
    );

    const initData: Record<string, string> = {};

    for (const key in firstLayerInitData) {
      try {
        initData[key] = JSON.parse(firstLayerInitData[key]);
      } catch {
        initData[key] = firstLayerInitData[key];
      }
    }

    console.log("Using real Telegram data:", {
      firstLayer: firstLayerInitData,
      parsed: initData,
      start_param: initData.start_param,
      user: initData.user
    });

    setData(initData);
  }, []);

  return data;
}

export default useTelegramInitData;
