<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jazz Jackrabbit Endless Runner</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-color: #222;
            overflow: hidden;
        }
        #gameCanvas {
            background-color: #000;
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <canvas id="gameCanvas"></canvas>

    <!-- Engine -->
    <script src="js/engine.js"></script>
    
    <!-- Game Components -->
    <script src="js/src/constants.js"></script>
    <script src="js/src/utils.js"></script>
    <script src="js/src/assetLoader.js"></script>
    <script src="js/src/inputHandler.js"></script>
    
    <!-- Game Entities -->
    <script src="js/src/entities/player.js"></script>
    <script src="js/src/entities/turtle.js"></script>
    <script src="js/src/entities/diamond.js"></script>
    <script src="js/src/entities/background.js"></script>
    
    <!-- Game States -->
    <script src="js/src/states/gameState.js"></script>
    <script src="js/src/states/menuState.js"></script>
    <script src="js/src/states/playState.js"></script>
    <script src="js/src/states/gameOverState.js"></script>
    
    <!-- Main Game -->
    <script src="js/game.js"></script>
    <script>
        // Initialize the game when the window loads
        window.onload = function() {
            const game = new Game('gameCanvas');
            game.start();
        };
    </script>
</body>
</html>
