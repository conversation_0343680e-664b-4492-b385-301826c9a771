// Enemy class for handling enemies
class Enemy {
    constructor(x, y, enemyType) {
        this.x = x;
        this.y = y;
        this.hp = 3;
        this.maxHp = 3;
        this.dataMaxHp = 3;
        this.power = 10;
        this.knockback = 10;
        this.deathKB = 10;
        this.damageKB = 1;
        this.xp = 1;
        this.speed = 100;
        this.scaleMul = 1;
        this.enemyType = enemyType;
        this.HPxLevel = false;
        this.FixedDirection = false;
        this.owner = null;
        this.isDead = false;
        this.receivingDamage = false;
        this.currentDirection = new Vector2(0, 0);
        this.defaultName = '';

        // Movement behavior properties
        this.radius = 16;
        this.target = null;
        this.isTimeStopped = false;
        this.isFrozen = false;
        this.alpha = 1;

        // AI behavior properties
        this.behaviorType = '';
        this.formationType = '';
        this.groupId = null;

        // Detection and aggression ranges
        this.detectionRange = 500;
        this.aggressionRange = 300;
        this.attackRange = 50;

        // Ranged attack properties
        this.canShoot = false;
        this.shootCooldown = 0;
        this.shootCooldownMax = 4000; // 2 seconds between shots
        this.projectileDamage = 3;

        // Movement parameters
        this.wanderAngle = Math.random() * Math.PI * 2;
        this.wanderStrength = 0.3 + Math.random() * 0.4;
        this.positionOffset = {
            x: (Math.random() * 2 - 1) * 50,
            y: (Math.random() * 2 - 1) * 50
        };

        // Timing parameters
        this.nextDirectionChangeTime = 0;
        this.directionChangeInterval = 1000 + Math.random() * 2000;

        // Formation parameters
        this.formationPosition = null;
        this.formationOffset = {
            x: 0,
            y: 0
        };

        // Tactical parameters
        this.flankingDirection = Math.random() > 0.5 ? 1 : -1;
        this.circlingDirection = Math.random() > 0.5 ? 1 : -1;
        this.circlingDistance = 100 + Math.random() * 100;

        // Legacy movement properties (kept for compatibility)
        this.wanderChangeTime = 0;
        this.wanderChangeInterval = 2000 + Math.random() * 1000; // 2-3 seconds
        this.targetOffset = {
            x: (Math.random() * 2 - 1) * 30, // Random offset between -30 and 30
            y: (Math.random() * 2 - 1) * 30  // Random offset between -30 and 30
        };
        this.speedVariation = 0.9 + Math.random() * 0.2; // 90-110% speed variation

        // Core properties
        this.isCullable = true;
        this.isTimeStopped = false;
        this.isFrozen = false;
        this.defaultSpeed = 100;
        this.res_Freeze = 0;
        this.res_Rosary = 0;
        this.moveTweenIndex = 0;
        this.isTeleportOnCull = false;
        this.radius = 16; // Default collision radius
        this.spriteName = '';
        this.alpha = 1;
        this.target = null;

        // Load enemy data
        this.loadEnemyData();
    }
    // Load enemy data from constants
    loadEnemyData() {
        const enemyData = ENEMIES[this.enemyType] ? ENEMIES[this.enemyType][0] : null;
        if (!enemyData) {

            return;
        }



        this.maxHp = enemyData.maxHp;
        this.dataMaxHp = enemyData.maxHp;
        this.hp = this.maxHp;
        this.power = enemyData.power;
        this.knockback = enemyData.knockback;
        this.deathKB = enemyData.deathKB;
        this.xp = enemyData.xp;
        this.speed = enemyData.speed;
        this.defaultSpeed = enemyData.speed;
        this.spriteName = enemyData.spriteName;
        if (enemyData.alpha !== undefined) {
            this.alpha = enemyData.alpha;
        }
        if (enemyData.scale !== undefined) {
            this.scaleMul = enemyData.scale;
        }
        // Set behavior type from enemy data
        if (enemyData.aiType) {
            this.behaviorType = enemyData.aiType;
        }

        // Set shooting ability from enemy data
        if (enemyData.canShoot !== undefined) {
            this.canShoot = enemyData.canShoot;

        }
        // Check for special skills
        const skills = enemyData.skills;
        if (skills) {
            if (skills.includes('HPxLevel')) {
                this.HPxLevel = true;
            }
            if (skills.includes('FixedDirection')) {
                this.FixedDirection = true;
            }
            if (skills.includes('Ranged')) {
                this.canShoot = true;

            }
            if (skills.includes('Teleport')) {
                this.canTeleport = true;
                this.teleportCooldown = 0;
                this.teleportCooldownMax = 3000 + Math.random() * 2000; // 3-5 seconds between teleports

            }
        }

        // Initialize AI behavior
        this.initAIBehavior();
    }
    // Set the target for the enemy to follow
    setTarget(target) {
        this.target = target;
    }
    // Freeze the enemy for a duration
    freeze(delay, t = 1) {
        if (this.res_Freeze > t || this.isTimeStopped) return;
        this.isTimeStopped = true;
        this.speed = 0;
        // Unfreeze after delay
        setTimeout(() => {
            this.resumeFromFreeze();
        }, delay);
    }
    // Resume from freeze
    resumeFromFreeze() {
        this.isTimeStopped = false;
        this.speed = this.defaultSpeed;
    }
    // Take damage
    takeDamage(amount) {
        if (this.isDead) return 0;
        this.hp -= amount;
        if (this.hp <= 0) {
            this.die();
        } else {
            this.onGetDamaged();
        }
        return amount;
    }
    // Visual feedback when taking damage
    onGetDamaged() {
        this.receivingDamage = true;
        // Reset receiving damage after a short delay
        setTimeout(() => {
            this.receivingDamage = false;
        }, 120);
    }
    // Die
    die() {
        if (this.isDead) return;
        this.isDead = true;

        // Drop experience - always drop exactly 1 XP per gem
        // This ensures consistent XP calculation
        if (this.xp > 0 && Game.core) {
            // Drop the exact number of gems equal to the enemy's XP value
            for (let i = 0; i < this.xp; i++) {
                Game.core.makeGem(this.x, this.y, 1);
            }
        }
        // Drop treasure if attached (not implemented in this version)
        this.treasure = null;
        // Update kill count
        if (ENEMIES[this.enemyType] && ENEMIES[this.enemyType][0]) {
            ENEMIES[this.enemyType][0].killedAmount += 1;
        }
        if (Game.core) {
            // Ensure runEnemies is a number before incrementing
            if (typeof Game.core.playerOptions.runEnemies !== 'number') {
                Game.core.playerOptions.runEnemies = 0;
            }
            // Increment the counter by 1
            Game.core.playerOptions.runEnemies += 1;

            // Update the UI
            if (Game.core.mainUI) {
                Game.core.mainUI.updateKills();
            }
        }
        // Remove from game
        this.despawn();
    }
    // Despawn the enemy
    despawn() {
        if (Game.core) {
            // Remove from enemies array
            const index = Game.core.enemies.indexOf(this);
            if (index !== -1) {
                Game.core.enemies.splice(index, 1);

                // Find the enemy pool this enemy belongs to
                for (const pool of Game.core.stage.pools) {
                    if (pool.enemyType === this.enemyType) {
                        // Return this enemy to its pool
                        pool.return(this);
                        break;
                    }
                }
            }
        }
    }
    // Teleport on cull
    onTeleportOnCull() {
        if (Game.core && Game.core.player) {
            // Get a new position outside the screen but not too far
            const angle = Math.random() * Math.PI * 2; // Random angle around player
            const distance = Game.core.canvas.width * 0.75; // 75% of screen width
            // Calculate new position
            this.x = Game.core.player.x + Math.cos(angle) * distance;
            this.y = Game.core.player.y + Math.sin(angle) * distance;
            // Reset direction to face player
            this.currentDirection.x = Game.core.player.x - this.x;
            this.currentDirection.y = Game.core.player.y - this.y;
            this.currentDirection.normalize();
        }
    }
    // Initialize AI behavior based on enemy type
    initAIBehavior() {
        // Set default behavior based on enemy type if not already set
        if (!this.behaviorType) {
            if (this.enemyType.includes('GHOST1')) {
                // GHOST1: Basic enemy, fast but weak with fading effect
                this.behaviorType = 'DIRECT_CHASE';
                this.detectionRange = 450; // Increased from 400
                this.aggressionRange = 350; // Increased from 300
                this.attackRange = 30;
                this.alpha = 0.7; // More transparent
                this.fadeEffect = true; // Enable fading in/out effect
                this.fadeSpeed = 0.005 + Math.random() * 0.005; // Random fade speed
                this.fadeDirection = Math.random() < 0.5 ? 1 : -1; // Random initial direction

            } else if (this.enemyType.includes('GHOST2')) {
                // GHOST2: Medium enemy with swarming behavior
                this.behaviorType = 'SWARMING';
                this.detectionRange = 400;
                this.aggressionRange = 300;
                this.attackRange = 30;
                this.alpha = 0.6;
                this.swarmRadius = 60 + Math.random() * 30; // Distance to maintain from other GHOST2s

            } else if (this.enemyType.includes('GHOST3')) {
                // GHOST3: Advanced enemy with flanking behavior
                this.behaviorType = 'FLANKING';
                this.detectionRange = 500;
                this.aggressionRange = 400;
                this.attackRange = 30;
                this.alpha = 0.5;
                // Removed teleport ability - now exclusive to TELEPORTER enemy

            } else if (this.enemyType.includes('BAT')) {
                // BAT: Fast-moving enemy with ranged attack
                this.behaviorType = 'CIRCLING';
                this.detectionRange = 500;
                this.aggressionRange = 400;
                this.attackRange = 20;
                // Use original circling distance (120-160) with more randomization
                this.circlingDistance = 120 + Math.random() * 40; // Original distance for ranged attacks

                // Add random circling direction to prevent all BATs going the same way
                this.circlingDirection = Math.random() < 0.5 ? 1 : -1;

                // Add a unique offset to each BAT to prevent grouping
                this.positionOffset = {
                    x: (Math.random() * 2 - 1) * 100, // -100 to 100
                    y: (Math.random() * 2 - 1) * 100  // -100 to 100
                };

                // Add slight speed variation
                this.speedVariation = 0.8 + Math.random() * 0.4; // 0.8 to 1.2

                // Only set canShoot if it hasn't been set already
                if (this.canShoot !== true) {
                    this.canShoot = true;
                }

                this.shootCooldown = 0; // Start with no cooldown so they can shoot immediately
                // Reduce cooldown by 30% (from 1500-2500ms to 1050-1750ms)
                this.shootCooldownMax = (1500 + Math.random() * 1000) * 0.7; // 1.05-1.75 seconds between shots

            } else if (this.enemyType.includes('ZOMBIE1')) {
                // ZOMBIE1: Slow but tanky enemy with resurrection ability
                this.behaviorType = 'DIRECT_CHASE';
                this.detectionRange = 350;
                this.aggressionRange = 300;
                this.attackRange = 40;
                this.speed *= 0.8; // Slower than default
                this.maxHp *= 1.5; // More HP
                this.hp = this.maxHp;
                this.canResurrect = true; // Can resurrect once
                this.hasResurrected = false;

            } else if (this.enemyType.includes('ZOMBIE2')) {
                // ZOMBIE2: Medium-speed enemy with flanking behavior and splitting ability
                this.behaviorType = 'FLANKING';
                this.detectionRange = 400;
                this.aggressionRange = 350;
                this.attackRange = 40;
                this.canSplit = true; // Can split into two ZOMBIE1s when killed
                this.rageThreshold = 0.3; // Enters rage mode at 30% HP
                this.isEnraged = false;

            } else if (this.enemyType.includes('OGRE')) {
                // OGRE: Tanky enemy with high damage and ground pound
                this.behaviorType = 'DIRECT_CHASE';
                this.detectionRange = 300;
                this.aggressionRange = 250;
                this.attackRange = 50;
                this.maxHp *= 2; // Double HP
                this.hp = this.maxHp;
                this.power *= 1.5; // More damage
                this.canGroundPound = true; // Can perform ground pound attack
                this.groundPoundCooldown = 0;
                this.groundPoundCooldownMax = 5000; // 5 seconds between ground pounds

            } else if (this.enemyType.includes('HORN')) {
                // HORN: Elite enemy with tactical behavior and buffing ability
                this.behaviorType = 'TACTICAL';
                this.detectionRange = 450;
                this.aggressionRange = 350;
                this.attackRange = 60;
                this.maxHp *= 1.5; // More HP
                this.hp = this.maxHp;
                this.power *= 1.2; // More damage
                this.canBuff = true; // Can buff nearby enemies
                this.buffCooldown = 0;
                this.buffCooldownMax = 8000; // 8 seconds between buffs
                this.buffRadius = 100; // Buff radius

            } else if (this.enemyType.includes('BOSS')) {
                // BOSS: Main boss with multiple attack patterns and aggressive behavior
                this.behaviorType = 'DIRECT_CHASE'; // Direct chase for maximum aggression
                this.detectionRange = 1000; // Very large detection range
                this.aggressionRange = 300; // Much smaller aggression range to get closer
                this.attackRange = 30; // Very small attack range to force very close combat
                this.speed *= 1.2; // 20% faster to be more aggressive
                this.maxHp *= 5; // Much more HP
                this.hp = this.maxHp;
                this.power *= 2.5; // Increased from 2x to 2.5x for more damage
                this.canSummon = true; // Can summon minions
                this.summonCooldown = 0;
                this.summonCooldownMax = 8000; // Reduced from 10s to 8s for more frequent summons
                this.phase = 1; // Boss has multiple phases
                this.phaseThresholds = [0.7, 0.4, 0.1]; // Phase transition HP thresholds

            } else if (this.enemyType.includes('TELEPORTER')) {
                // TELEPORTER: Enemy with teleportation abilities
                this.behaviorType = 'TELEPORTING';
                this.detectionRange = 500;
                this.aggressionRange = 400;
                this.attackRange = 30;
                this.alpha = 0.8; // Slightly transparent

                // Teleportation abilities
                this.canTeleport = true;
                this.teleportCooldown = 0;
                this.teleportCooldownMax = 3000 + Math.random() * 2000; // 3-5 seconds between teleports

                // Visual effects
                this.fadeEffect = true;
                this.fadeSpeed = 0.008 + Math.random() * 0.004;
                this.fadeDirection = Math.random() < 0.5 ? 1 : -1;

                // TELEPORTER enemy initialized
            } else {
                // Default behavior for unknown enemy types
                this.behaviorType = 'DIRECT_CHASE';
                this.detectionRange = 400;
                this.aggressionRange = 300;
                this.attackRange = 40;
            }
        }

        // Set formation type based on enemy type
        if (!this.formationType) {
            if (this.enemyType.includes('BOSS')) {
                this.formationType = 'CIRCLE';
            } else if (this.enemyType.includes('SWARM')) {
                this.formationType = 'SCATTERED';
            } else {
                // Random formation type for regular enemies
                const formationChance = Math.random();
                if (formationChance < 0.3) {
                    this.formationType = 'NONE';
                } else if (formationChance < 0.5) {
                    this.formationType = 'LINE';
                } else if (formationChance < 0.7) {
                    this.formationType = 'CIRCLE';
                } else if (formationChance < 0.9) {
                    this.formationType = 'FLANKING';
                } else {
                    this.formationType = 'SURROUND';
                }
            }
        }
    }

    // Set the group ID for formation behavior
    setGroupId(groupId) {
        this.groupId = groupId;
    }

    // Set the formation type
    setFormationType(formationType) {
        this.formationType = formationType;
    }

    // Update the enemy
    update(deltaTime) {
        if (this.isDead) return;
        // Skip update if time stopped
        if (this.isTimeStopped) return;

        // Update special behaviors based on enemy type
        this.updateSpecialBehaviors(deltaTime);

        // Get nearby enemies for formation behavior and collision avoidance
        let nearbyEnemies = [];
        if (Game.core && Game.core.enemies) {
            // Find enemies of the same type within a certain range
            nearbyEnemies = Game.core.enemies.filter(enemy => {
                if (enemy === this || enemy.isDead || enemy.isTimeStopped) return false;

                // Check if enemy is nearby (within 200 pixels)
                const dx = enemy.x - this.x;
                const dy = enemy.y - this.y;
                const distSquared = dx * dx + dy * dy;

                return distSquared < 200 * 200;
            });
        }

        // Use AI to determine movement direction
        if (this.target) {
            const direction = this.getAIMovementDirection(deltaTime, nearbyEnemies);

            // Update current direction
            if (direction.x !== 0 || direction.y !== 0) {
                this.currentDirection.x = direction.x;
                this.currentDirection.y = direction.y;
            }

            // Handle shooting for enemies that can shoot
            if (this.enemyType.includes('BAT')) {
                // Force enable shooting for BAT enemies if not already enabled
                if (!this.canShoot) {
                    this.canShoot = true;
                    this.shootCooldown = 0;
                    this.shootCooldownMax = 1500 + Math.random() * 1000;
                }
            }

            if (this.canShoot && Game.core && this.target) {
                // Update cooldown
                if (this.shootCooldown > 0) {
                    this.shootCooldown -= deltaTime;
                    // Check if cooldown expired
                }

                // Check if we can shoot
                if (this.shootCooldown <= 0) {
                    // Calculate distance to target
                    let distToTarget;
                    if (typeof distance === 'function') {
                        distToTarget = distance(this.x, this.y, this.target.x, this.target.y);
                    } else {
                        // Fallback to manual calculation
                        const dx = this.x - this.target.x;
                        const dy = this.y - this.target.y;
                        distToTarget = Math.sqrt(dx * dx + dy * dy);
                        // Using fallback distance calculation
                    }

                    // Only shoot when at ideal circling distance
                    let idealDistanceMin, idealDistanceMax;

                    // Special case for BAT enemies - wider shooting range
                    if (this.enemyType.includes('BAT')) {
                        // For BAT enemies, be even more lenient with the shooting range
                        idealDistanceMin = this.circlingDistance * 0.5; // Much more lenient minimum distance
                        idealDistanceMax = this.circlingDistance * 1.5; // Much more lenient maximum distance
                        // For BAT enemies, occasionally shoot regardless of distance
                        if (Math.random() < 0.1) { // 10% chance to shoot anyway
                            this.shoot();
                            this.shootCooldown = this.shootCooldownMax;
                            return; // Skip the distance check
                        }
                    } else {
                        idealDistanceMin = this.circlingDistance * 0.8;
                        idealDistanceMax = this.circlingDistance * 1.2;
                    }

                    if (distToTarget >= idealDistanceMin && distToTarget <= idealDistanceMax) {
                        try {
                            this.shoot();
                            // Reset cooldown
                            this.shootCooldown = this.shootCooldownMax;
                        } catch (error) {
                            // Error handling without logging
                        }
                    }
                }
            }
        }
        // Fallback to basic behavior if target not available
        else if (this.target && !this.FixedDirection) {
            // Basic direction calculation
            this.currentDirection.x = this.target.x - this.x;
            this.currentDirection.y = this.target.y - this.y;
            this.currentDirection.normalize();
        }

        // Calculate movement speed
        const baseEnemySpeed = Game.core ? Game.core.enemySpeed : 0.77 * 0.3;
        const damage = this.receivingDamage ? -this.knockback * this.damageKB : 1;

        // Set enemy speed multiplier - REDUCED BY 10x for ALL enemies
        let speedMultiplier = 0.03; // Reduced from 0.3 to 0.03 (10 times less)

        // Apply speed variation only for BAT enemies
        if (this.enemyType.includes('BAT') && this.speedVariation) {
            speedMultiplier = 0.03 * this.speedVariation; // Apply variation only to BATs
        }

        // Update position with smoother movement
        const speedX = baseEnemySpeed * this.speed * speedMultiplier * this.currentDirection.x * damage * deltaTime / 16;
        const speedY = baseEnemySpeed * this.speed * speedMultiplier * this.currentDirection.y * damage * deltaTime / 16;
        this.x += speedX;
        this.y += speedY;
    }
    // Draw the enemy
    draw(ctx, camera, sprites) {
        if (this.isDead) return;
        if (!sprites) return;
        // Calculate screen position
        const screenX = this.x - camera.x + camera.width / 2;
        const screenY = this.y - camera.y + camera.height / 2;
        // Get the sprite
        const sprite = sprites[this.spriteName];
        if (!sprite) {
            // Draw a fallback shape if sprite not found
            this.drawFallbackShape(ctx, screenX, screenY);
            return;
        }
        ctx.save();
        // Apply flipping based on direction
        if (this.currentDirection.x > 0) {
            ctx.scale(-1, 1);
            ctx.translate(-screenX * 2, 0);
        }
        // Apply alpha
        ctx.globalAlpha = this.alpha !== undefined ? this.alpha : 1;
        // Apply damage effect
        if (this.receivingDamage) {
            ctx.fillStyle = '#ffffff';
            ctx.globalCompositeOperation = 'lighter';
        }
        // Apply frozen effect
        if (this.isTimeStopped) {
            ctx.fillStyle = '#00ffff';
            ctx.globalCompositeOperation = 'multiply';
        }
        // Draw the sprite
        const scale = this.scaleMul || 1;
        ctx.drawImage(
            sprite,
            screenX - sprite.width / 2 * scale,
            screenY - sprite.height / 2 * scale,
            sprite.width * scale,
            sprite.height * scale
        );
        ctx.restore();
        // Draw health bar
        this.drawHealthBar(ctx, screenX, screenY);
        // Draw enemy type for debugging
        if (Game.core && Game.core.debug) {
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.fillText(`Type: ${this.enemyType}`, screenX - 20, screenY - 30);
        }
        // Draw debug collision circle
        if (Game.core && Game.core.debug) {
            ctx.beginPath();
            ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
            ctx.strokeStyle = 'rgba(255, 0, 0, 0.5)';
            ctx.stroke();
        }
    }

    // Draw a fallback shape for the enemy if sprite is not found
    drawFallbackShape(ctx, x, y) {
        ctx.save();

        // Different shapes based on enemy type
        switch (this.enemyType) {
            case EnemyType.GHOST1:
            case EnemyType.GHOST2:
            case EnemyType.GHOST3:
                // Ghost shape
                ctx.beginPath();
                ctx.arc(x, y, 15, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(200, 200, 255, 0.7)';
                ctx.fill();
                break;

            case EnemyType.BAT:
                // Bat shape
                ctx.beginPath();
                ctx.arc(x, y, 10, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(50, 50, 50, 0.8)';
                ctx.fill();

                // Wings
                ctx.beginPath();
                ctx.moveTo(x - 15, y);
                ctx.lineTo(x - 5, y - 10);
                ctx.lineTo(x, y);
                ctx.lineTo(x + 5, y - 10);
                ctx.lineTo(x + 15, y);
                ctx.fillStyle = 'rgba(50, 50, 50, 0.8)';
                ctx.fill();
                break;

            case EnemyType.OGRE:
                // Ogre shape
                ctx.beginPath();
                ctx.arc(x, y, 20, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(0, 150, 0, 0.8)';
                ctx.fill();
                break;

            case EnemyType.HORN:
                // Horned enemy shape
                ctx.beginPath();
                ctx.arc(x, y, 18, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(150, 0, 0, 0.8)';
                ctx.fill();

                // Horns
                ctx.beginPath();
                ctx.moveTo(x - 10, y - 10);
                ctx.lineTo(x - 15, y - 25);
                ctx.moveTo(x + 10, y - 10);
                ctx.lineTo(x + 15, y - 25);
                ctx.strokeStyle = 'rgba(150, 0, 0, 0.8)';
                ctx.lineWidth = 3;
                ctx.stroke();
                break;

            case EnemyType.GHOST_BOSS1:
                // Boss shape
                ctx.beginPath();
                ctx.arc(x, y, 30, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(255, 100, 100, 0.8)';
                ctx.fill();

                // Crown
                ctx.beginPath();
                ctx.moveTo(x - 20, y - 20);
                ctx.lineTo(x - 15, y - 35);
                ctx.lineTo(x - 5, y - 25);
                ctx.lineTo(x, y - 35);
                ctx.lineTo(x + 5, y - 25);
                ctx.lineTo(x + 15, y - 35);
                ctx.lineTo(x + 20, y - 20);
                ctx.fillStyle = 'rgba(255, 215, 0, 0.8)';
                ctx.fill();
                break;

            default:
                // Default shape
                ctx.beginPath();
                ctx.arc(x, y, 15, 0, Math.PI * 2);
                ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';
                ctx.fill();
                break;
        }

        // Draw enemy type for debugging
        if (Game.core && Game.core.debug) {
            ctx.fillStyle = 'white';
            ctx.font = '10px Arial';
            ctx.fillText(`Type: ${this.enemyType}`, x - 20, y - 30);
        }

        ctx.restore();

        // Draw health bar
        this.drawHealthBar(ctx, x, y);
    }
    // Draw the health bar
    drawHealthBar(ctx, x, y) {
        const barWidth = 30;
        const barHeight = 3;
        const barX = x - barWidth / 2;
        const barY = y - 25;
        // Background
        ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        ctx.fillRect(barX, barY, barWidth, barHeight);
        // Health
        const healthPercent = this.hp / this.maxHp;
        ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
        ctx.fillRect(barX, barY, barWidth * healthPercent, barHeight);
    }

    // Placeholder for future enemy avoidance behavior
    // Currently disabled as per user request
    avoidOtherEnemies() {
        // Method intentionally left empty
        return;
    }

    // Handle teleport on cull - teleport enemy to a new position outside the screen
    onTeleportOnCull() {
        if (!Game.core || !Game.core.player) return;

        // Use the same approach as boss spawning which works well
        // Generate a random angle and distance
        const angle = Math.random() * Math.PI * 2;
        const distance = 256 * Math.random();

        // Calculate position relative to canvas size
        const canvas = Game.core.canvas;
        let x, y;

        // Use the exact same formula as boss spawning
        x = Game.core.player.x + 0.9 * Math.cos(angle) * (canvas.width + distance);
        y = Game.core.player.y + 0.9 * Math.sin(angle) * (canvas.height + distance);

        // Teleport to the new position
        this.x = x;
        this.y = y;

        // Reset direction
        this.currentDirection = new Vector2(0, 0);

        // Reset formation position
        this.formationPosition = null;

        // Reset teleport flag
        this.isTeleportOnCull = false;

        // Enemy teleported to new position
    }

    // Update special behaviors based on enemy type
    updateSpecialBehaviors(deltaTime) {
        // GHOST1 fading effect
        if (this.fadeEffect) {
            // Oscillate alpha between 0.4 and 0.8
            this.alpha += (this.fadeDirection || 1) * (this.fadeSpeed || 0.005) * deltaTime;
            if (this.alpha > 0.8) {
                this.alpha = 0.8;
                this.fadeDirection = -1;
            } else if (this.alpha < 0.4) {
                this.alpha = 0.4;
                this.fadeDirection = 1;
            }
        }

        // TELEPORTER teleport ability
        if (this.enemyType === EnemyType.TELEPORTER && this.canTeleport && this.teleportCooldown !== undefined) {
            this.teleportCooldown -= deltaTime;
            if (this.teleportCooldown <= 0 && Math.random() < 0.01) { // 1% chance per frame when off cooldown
                this.teleport();
                this.teleportCooldown = this.teleportCooldownMax || 3000;
            }
        }

        // ZOMBIE2 rage mode
        if (this.rageThreshold && !this.isEnraged && this.hp <= this.maxHp * this.rageThreshold) {
            this.enterRageMode();
        }

        // OGRE ground pound
        if (this.canGroundPound && this.groundPoundCooldown !== undefined) {
            this.groundPoundCooldown -= deltaTime;
            if (this.groundPoundCooldown <= 0 && this.target &&
                distance(this.x, this.y, this.target.x, this.target.y) < this.attackRange * 1.5) {
                this.performGroundPound();
                this.groundPoundCooldown = this.groundPoundCooldownMax || 5000;
            }
        }

        // HORN buff ability
        if (this.canBuff && this.buffCooldown !== undefined) {
            this.buffCooldown -= deltaTime;
            if (this.buffCooldown <= 0) {
                this.buffNearbyEnemies();
                this.buffCooldown = this.buffCooldownMax || 8000;
            }
        }

        // BOSS phase transitions and summon ability
        if (this.phaseThresholds) {
            // Check for phase transitions
            const hpRatio = this.hp / this.maxHp;
            for (let i = 0; i < this.phaseThresholds.length; i++) {
                if (hpRatio <= this.phaseThresholds[i] && this.phase === i + 1) {
                    this.transitionToNextPhase();
                    break;
                }
            }

            // Summon minions
            if (this.canSummon && this.summonCooldown !== undefined) {
                this.summonCooldown -= deltaTime;
                if (this.summonCooldown <= 0) {
                    this.summonMinions();
                    this.summonCooldown = this.summonCooldownMax || 10000;
                }
            }
        }
    }

    // Teleport ability for TELEPORTER enemies
    teleport() {
        if (!Game.core || !Game.core.player || this.enemyType !== EnemyType.TELEPORTER) return;

        // Get a random position around the player
        const angle = Math.random() * Math.PI * 2;
        const distance = 80 + Math.random() * 80; // 80-160 units from player

        // Calculate new position
        const newX = Game.core.player.x + Math.cos(angle) * distance;
        const newY = Game.core.player.y + Math.sin(angle) * distance;

        // Create a more visible teleport effect at old position
        this.createTeleportEffect(this.x, this.y, 'VANISH');

        // Move to new position
        this.x = newX;
        this.y = newY;

        // Create a more visible teleport effect at new position
        this.createTeleportEffect(this.x, this.y, 'APPEAR');

        // Make the enemy briefly invulnerable after teleporting
        this.isInvul = true;
        setTimeout(() => {
            this.isInvul = false;
        }, 500); // 0.5 seconds of invulnerability


    }

    // Create a teleport effect for TELEPORTER enemies
    createTeleportEffect(x, y, type) {
        if (!Game.core || this.enemyType !== EnemyType.TELEPORTER) return;

        // Create a visual text effect
        if (Game.core.showDamageAt) {
            // Text and color based on vanish or appear
            const text = type === 'VANISH' ? '↑WARP↑' : '↓WARP↓';
            const color = type === 'VANISH' ? 'rgba(255, 100, 255, 0.9)' : 'rgba(255, 150, 255, 0.9)';

            Game.core.showDamageAt(x, y, text, color);
        }

        // If we have access to the canvas, create a particle effect
        if (Game.core.ctx) {
            const ctx = Game.core.ctx;
            const camera = Game.core.camera;

            // Calculate screen position
            const screenX = x - camera.x + camera.width / 2;
            const screenY = y - camera.y + camera.height / 2;

            // Draw a flash effect
            ctx.save();
            ctx.beginPath();

            // Larger, colorful effect for TELEPORTER enemies
            ctx.arc(screenX, screenY, 40, 0, Math.PI * 2);
            ctx.fillStyle = type === 'VANISH' ? 'rgba(255, 100, 255, 0.3)' : 'rgba(255, 150, 255, 0.3)';
            ctx.fill();
            ctx.restore();

            // Create simple particles that fade out quickly
            const particleCount = 12; // More particles for better effect

            for (let i = 0; i < particleCount; i++) {
                const particleAngle = Math.random() * Math.PI * 2;
                const particleDistance = 10 + Math.random() * 20;
                const particleX = screenX + Math.cos(particleAngle) * particleDistance;
                const particleY = screenY + Math.sin(particleAngle) * particleDistance;

                ctx.save();
                ctx.beginPath();
                ctx.arc(particleX, particleY, 4 + Math.random() * 4, 0, Math.PI * 2);
                ctx.fillStyle = type === 'VANISH' ? 'rgba(255, 100, 255, 0.7)' : 'rgba(255, 150, 255, 0.7)';
                ctx.fill();
                ctx.restore();
            }
        }
    }

    // ZOMBIE2 rage mode
    enterRageMode() {
        this.isEnraged = true;
        this.speed *= 1.5; // 50% speed increase
        this.power *= 1.3; // 30% damage increase

        // Visual indicator
        if (Game.core && Game.core.showDamageAt) {
            Game.core.showDamageAt(this.x, this.y, 'RAGE!', 'rgba(255, 0, 0, 0.8)');
        }


    }

    // OGRE ground pound ability
    performGroundPound() {
        if (!Game.core) return;

        // Visual effect
        if (Game.core.showDamageAt) {
            Game.core.showDamageAt(this.x, this.y, 'POUND!', 'rgba(0, 150, 0, 0.8)');
        }

        // Stun nearby enemies
        if (Game.core.enemies) {
            Game.core.enemies.forEach(enemy => {
                if (enemy !== this && !enemy.isDead && !enemy.isTimeStopped) {
                    const dx = enemy.x - this.x;
                    const dy = enemy.y - this.y;
                    const dist = Math.sqrt(dx * dx + dy * dy);

                    if (dist < 100) { // 100 pixel radius
                        // Stun the enemy
                        enemy.isTimeStopped = true;

                        // Set a timeout to unstun
                        setTimeout(() => {
                            enemy.isTimeStopped = false;
                        }, 1000); // 1 second stun

                        // Visual effect
                        if (Game.core.showDamageAt) {
                            Game.core.showDamageAt(enemy.x, enemy.y, 'STUNNED!', 'rgba(255, 255, 0, 0.8)');
                        }
                    }
                }
            });
        }


    }

    // HORN buff ability
    buffNearbyEnemies() {
        if (!Game.core) return;

        // Visual effect
        if (Game.core.showDamageAt) {
            Game.core.showDamageAt(this.x, this.y, 'BUFF!', 'rgba(255, 215, 0, 0.8)');
        }

        // Buff nearby enemies
        if (Game.core.enemies) {
            let buffCount = 0;

            Game.core.enemies.forEach(enemy => {
                if (enemy !== this && !enemy.isDead && !enemy.isTimeStopped) {
                    const dx = enemy.x - this.x;
                    const dy = enemy.y - this.y;
                    const dist = Math.sqrt(dx * dx + dy * dy);

                    if (dist < this.buffRadius) { // Use buff radius
                        // Buff the enemy
                        enemy.speed *= 1.2; // 20% speed increase
                        enemy.power *= 1.2; // 20% damage increase

                        // Visual effect
                        if (Game.core.showDamageAt) {
                            Game.core.showDamageAt(enemy.x, enemy.y, '+BUFF', 'rgba(255, 215, 0, 0.8)');
                        }

                        buffCount++;
                    }
                }
            });


        }
    }

    // BOSS phase transition
    transitionToNextPhase() {
        this.phase++;

        // Visual effect
        if (Game.core && Game.core.showDamageAt) {
            Game.core.showDamageAt(this.x, this.y, `PHASE ${this.phase}!`, 'rgba(255, 0, 0, 0.8)');
        }

        // Phase-specific enhancements
        switch(this.phase) {
            case 2: // Phase 2
                this.speed *= 1.2; // 20% speed increase
                break;

            case 3: // Phase 3
                this.power *= 1.3; // 30% damage increase
                break;

            case 4: // Phase 4 (final)
                this.speed *= 1.3; // 30% speed increase
                this.power *= 1.3; // 30% damage increase
                break;
        }


    }

    // BOSS summon minions
    summonMinions() {
        if (!Game.core || !Game.core.player) return;

        // Visual effect
        if (Game.core.showDamageAt) {
            Game.core.showDamageAt(this.x, this.y, 'SUMMON!', 'rgba(255, 0, 255, 0.8)');
        }

        // Determine which minions to summon based on phase
        let minionType, minionCount;

        switch(this.phase) {
            case 1: // Phase 1: Summon GHOST1
                minionType = EnemyType.GHOST1;
                minionCount = 3;
                break;

            case 2: // Phase 2: Summon GHOST2
                minionType = EnemyType.GHOST2;
                minionCount = 2;
                break;

            case 3: // Phase 3: Summon GHOST3
                minionType = EnemyType.GHOST3;
                minionCount = 2;
                break;

            case 4: // Phase 4: Summon HORN
                minionType = EnemyType.HORN;
                minionCount = 1;
                break;

            default: // Default: Summon GHOST1
                minionType = EnemyType.GHOST1;
                minionCount = 3;
                break;
        }

        // Summon minions
        for (let i = 0; i < minionCount; i++) {
            // Calculate position around the boss
            const angle = (i / minionCount) * Math.PI * 2;
            const distance = 50; // 50 units from boss

            const x = this.x + Math.cos(angle) * distance;
            const y = this.y + Math.sin(angle) * distance;

            // Create a pool for the minion
            const pool = new EnemyGroup();
            pool.init(minionType);

            // Spawn the minion
            const minion = pool.spawnAt(x, y);

            if (minion) {
                // Visual effect
                if (Game.core.showDamageAt) {
                    Game.core.showDamageAt(x, y, 'MINION!', 'rgba(255, 0, 255, 0.8)');
                }
            }
        }


    }

    // Get AI movement direction
    getAIMovementDirection(_deltaTime, nearbyEnemies) {
        if (!this.target || this.isDead || this.isTimeStopped) {
            return { x: 0, y: 0 };
        }

        // Check if it's time to update direction
        const now = Date.now();
        if (now >= this.nextDirectionChangeTime) {
            this.updateWanderAngle();
            this.nextDirectionChangeTime = now + this.directionChangeInterval;
        }

        // Get direction based on behavior type
        let direction;

        switch (this.behaviorType) {
            case 'DIRECT_CHASE':
                direction = this.getDirectChaseDirection();
                break;

            case 'FLANKING':
                direction = this.getFlankingDirection();
                break;

            case 'CIRCLING':
                direction = this.getCirclingDirection();
                break;

            case 'SWARMING':
                direction = this.getSwarmingDirection(nearbyEnemies);
                break;

            case 'AMBUSH':
                direction = this.getAmbushDirection();
                break;

            case 'RANGED':
                direction = this.getRangedDirection();
                break;

            case 'TACTICAL':
                direction = this.getTacticalDirection(nearbyEnemies);
                break;

            case 'TELEPORTING':
                direction = this.getTeleportingDirection();
                break;

            default:
                direction = this.getDirectChaseDirection();
                break;
        }

        // Apply formation behavior if in a formation
        if (this.formationType !== 'NONE' && nearbyEnemies && nearbyEnemies.length > 0) {
            const formationDirection = this.getFormationDirection(nearbyEnemies);

            // Blend formation direction with individual behavior
            direction.x = direction.x * 0.7 + formationDirection.x * 0.3;
            direction.y = direction.y * 0.7 + formationDirection.y * 0.3;
        }

        // Apply separation to avoid crowding
        const separationDirection = this.getSeparationDirection(nearbyEnemies);

        // Blend separation with current direction (stronger separation when closer)
        const separationWeight = Math.min(1.0, separationDirection.weight);
        direction.x = direction.x * (1 - separationWeight) + separationDirection.x * separationWeight;
        direction.y = direction.y * (1 - separationWeight) + separationDirection.y * separationWeight;

        // Normalize direction
        const magnitude = Math.sqrt(direction.x * direction.x + direction.y * direction.y);
        if (magnitude > 0) {
            direction.x /= magnitude;
            direction.y /= magnitude;
        }

        return direction;
    }

    // Update the wander angle for random movement
    updateWanderAngle() {
        // Add some randomness to the wander angle
        this.wanderAngle += (Math.random() * 2 - 1) * 0.5;

        // Only update position offset for non-BAT enemies
        // BAT enemies need to keep their original offset to prevent grouping
        if (!this.enemyType.includes('BAT')) {
            this.positionOffset = {
                x: (Math.random() * 2 - 1) * 50,
                y: (Math.random() * 2 - 1) * 50
            };
        }
    }

    // Get direction for direct chase behavior
    getDirectChaseDirection() {
        if (!this.target) return { x: 0, y: 0 };

        // Direct path to target with slight offset
        const dx = (this.target.x + this.positionOffset.x) - this.x;
        const dy = (this.target.y + this.positionOffset.y) - this.y;

        // Add slight wander
        const wanderX = Math.cos(this.wanderAngle) * this.wanderStrength;
        const wanderY = Math.sin(this.wanderAngle) * this.wanderStrength;

        return {
            x: dx + wanderX,
            y: dy + wanderY
        };
    }

    // Get direction for flanking behavior
    getFlankingDirection() {
        if (!this.target) return { x: 0, y: 0 };

        const dx = this.target.x - this.x;
        const dy = this.target.y - this.y;
        const distToTarget = Math.sqrt(dx * dx + dy * dy);

        // If far from target, approach directly
        if (distToTarget > this.aggressionRange) {
            return this.getDirectChaseDirection();
        }

        // Calculate perpendicular direction for flanking
        const perpX = -dy * this.flankingDirection;
        const perpY = dx * this.flankingDirection;

        // Normalize perpendicular direction
        const perpMagnitude = Math.sqrt(perpX * perpX + perpY * perpY);
        const normalizedPerpX = perpX / perpMagnitude;
        const normalizedPerpY = perpY / perpMagnitude;

        // Blend direct approach with perpendicular movement
        // More perpendicular movement when closer to target
        const directWeight = Math.min(1.0, distToTarget / this.aggressionRange);
        const flankWeight = 1.0 - directWeight;

        // Normalized direction to target
        const normalizedDx = dx / distToTarget;
        const normalizedDy = dy / distToTarget;

        return {
            x: normalizedDx * directWeight + normalizedPerpX * flankWeight,
            y: normalizedDy * directWeight + normalizedPerpY * flankWeight
        };
    }

    // Get direction for circling behavior
    getCirclingDirection() {
        if (!this.target) return { x: 0, y: 0 };

        // Calculate direction to target (with offset for BAT enemies)
        let targetX = this.target.x;
        let targetY = this.target.y;

        // Apply position offset for BAT enemies to prevent grouping
        if (this.enemyType.includes('BAT') && this.positionOffset) {
            targetX += this.positionOffset.x;
            targetY += this.positionOffset.y;
        }

        const dx = targetX - this.x;
        const dy = targetY - this.y;
        const distToTarget = Math.sqrt(dx * dx + dy * dy);

        // If far from target, approach directly
        if (distToTarget > this.aggressionRange) {
            return this.getDirectChaseDirection();
        }

        // Calculate perpendicular direction for circling
        const perpX = -dy * this.circlingDirection;
        const perpY = dx * this.circlingDirection;

        // Normalize perpendicular direction
        const perpMagnitude = Math.sqrt(perpX * perpX + perpY * perpY);
        const normalizedPerpX = perpX / perpMagnitude;
        const normalizedPerpY = perpY / perpMagnitude;

        // Calculate radial direction (towards or away from target)
        const normalizedDx = dx / distToTarget;
        const normalizedDy = dy / distToTarget;

        // Adjust distance to maintain ideal circling distance
        let radialWeight = 0;

        // For BAT enemies, use stronger distance maintenance
        if (this.enemyType.includes('BAT')) {
            // Calculate how far we are from ideal distance
            const distanceRatio = distToTarget / this.circlingDistance;

            if (distanceRatio < 0.8) {
                // Too close, move away very strongly
                radialWeight = -1.5;
            } else if (distanceRatio > 1.2) {
                // Too far, move closer very strongly
                radialWeight = 1.5;
            } else if (distanceRatio < 0.9) {
                // Slightly too close
                radialWeight = -0.8;
            } else if (distanceRatio > 1.1) {
                // Slightly too far
                radialWeight = 0.8;
            }

            // Movement calculation for BAT enemies

            // Adjust the balance between circling and maintaining distance
            const circlingWeight = 0.4; // Reduced from 0.6 to prioritize distance maintenance

            return {
                x: normalizedPerpX * circlingWeight + normalizedDx * radialWeight,
                y: normalizedPerpY * circlingWeight + normalizedDy * radialWeight
            };
        } else {
            // Standard behavior for other enemies
            if (distToTarget < this.circlingDistance * 0.8) {
                // Too close, move away
                radialWeight = -0.5;
            } else if (distToTarget > this.circlingDistance * 1.2) {
                // Too far, move closer
                radialWeight = 0.5;
            }

            return {
                x: normalizedPerpX * 0.8 + normalizedDx * radialWeight,
                y: normalizedPerpY * 0.8 + normalizedDy * radialWeight
            };
        }
    }

    // Get direction for swarming behavior
    getSwarmingDirection(nearbyEnemies) {
        if (!this.target) return { x: 0, y: 0 };

        // Start with direct chase direction
        const baseDirection = this.getDirectChaseDirection();

        // If no nearby enemies, just use direct chase
        if (!nearbyEnemies || nearbyEnemies.length === 0) {
            return baseDirection;
        }

        // Calculate average position of nearby enemies
        let avgX = 0;
        let avgY = 0;

        // Count only enemies of the same type for GHOST2 coordination
        const sameTypeEnemies = nearbyEnemies.filter(enemy =>
            enemy.enemyType === this.enemyType);

        if (sameTypeEnemies.length === 0) {
            return baseDirection;
        }

        sameTypeEnemies.forEach(enemy => {
            avgX += enemy.x;
            avgY += enemy.y;
        });

        avgX /= sameTypeEnemies.length;
        avgY /= sameTypeEnemies.length;

        // For GHOST2 enemies, implement coordinated swarming
        if (this.enemyType.includes('GHOST2')) {
            // Calculate direction to target
            const dx = this.target.x - this.x;
            const dy = this.target.y - this.y;

            // Calculate ideal swarm radius (distance from target)
            const swarmRadius = this.swarmRadius || 80;

            // Calculate angle to target
            const angleToTarget = Math.atan2(dy, dx);

            // Calculate ideal position in the swarm
            // Each GHOST2 tries to maintain a position in a circle around the target
            const swarmAngle = angleToTarget + (this.id % 8) * (Math.PI / 4); // Distribute around circle

            const idealX = this.target.x + Math.cos(swarmAngle) * swarmRadius;
            const idealY = this.target.y + Math.sin(swarmAngle) * swarmRadius;

            // Direction to ideal position
            const idealDx = idealX - this.x;
            const idealDy = idealY - this.y;
            const idealDist = Math.sqrt(idealDx * idealDx + idealDy * idealDy);

            // Normalize
            const normalizedIdealX = idealDx / (idealDist || 1);
            const normalizedIdealY = idealDy / (idealDist || 1);

            // Calculate direction away from average position (to avoid clumping)
            const awayX = this.x - avgX;
            const awayY = this.y - avgY;
            const awayMagnitude = Math.sqrt(awayX * awayX + awayY * awayY);
            const normalizedAwayX = awayX / (awayMagnitude || 1);
            const normalizedAwayY = awayY / (awayMagnitude || 1);

            // Combine ideal position direction with avoidance
            // Weight depends on how close we are to ideal position
            const idealWeight = Math.min(1, idealDist / 100); // More weight when far from ideal position
            const avoidWeight = Math.max(0, 1 - (awayMagnitude / 50)); // More weight when close to other enemies

            return {
                x: normalizedIdealX * idealWeight + normalizedAwayX * avoidWeight,
                y: normalizedIdealY * idealWeight + normalizedAwayY * avoidWeight
            };
        }

        // For other enemy types, use simpler swarming behavior
        // Calculate direction away from average position (to spread out)
        const awayX = this.x - avgX;
        const awayY = this.y - avgY;

        // Normalize
        const awayMagnitude = Math.sqrt(awayX * awayX + awayY * awayY);

        // If too close to other enemies, move away slightly
        if (awayMagnitude < 50) { // 50 pixel minimum distance
            const normalizedAwayX = awayX / (awayMagnitude || 1);
            const normalizedAwayY = awayY / (awayMagnitude || 1);

            // Combine base direction with avoidance
            return {
                x: baseDirection.x * 0.7 + normalizedAwayX * 0.3,
                y: baseDirection.y * 0.7 + normalizedAwayY * 0.3
            };
        }

        // Otherwise just use base direction
        return baseDirection;
    }

    // Get direction for ambush behavior
    getAmbushDirection() {
        if (!this.target) return { x: 0, y: 0 };

        const dx = this.target.x - this.x;
        const dy = this.target.y - this.y;
        const distToTarget = Math.sqrt(dx * dx + dy * dy);

        // If within attack range, rush at target
        if (distToTarget <= this.attackRange * 2) {
            return {
                x: dx / distToTarget,
                y: dy / distToTarget
            };
        }

        // Otherwise, move slowly or stay still
        return {
            x: dx * 0.1 / distToTarget,
            y: dy * 0.1 / distToTarget
        };
    }

    // Get direction for ranged behavior
    getRangedDirection() {
        if (!this.target) return { x: 0, y: 0 };

        const dx = this.target.x - this.x;
        const dy = this.target.y - this.y;
        const distToTarget = Math.sqrt(dx * dx + dy * dy);

        // Ideal range is between attackRange and aggressionRange
        const idealRange = (this.attackRange + this.aggressionRange) / 2;

        // If too close, move away
        if (distToTarget < idealRange * 0.8) {
            return {
                x: -dx / distToTarget,
                y: -dy / distToTarget
            };
        }

        // If too far, move closer
        if (distToTarget > idealRange * 1.2) {
            return {
                x: dx / distToTarget,
                y: dy / distToTarget
            };
        }

        // If at ideal range, stay still
        return { x: 0, y: 0 };
    }

    // Get direction for teleporting behavior
    getTeleportingDirection() {
        if (!this.target) return { x: 0, y: 0 };

        const dx = this.target.x - this.x;
        const dy = this.target.y - this.y;
        const distToTarget = Math.sqrt(dx * dx + dy * dy);

        // Check if it's time to teleport
        if (this.canTeleport && this.teleportCooldown <= 0) {
            // Higher chance to teleport when far from target or very close
            let teleportChance = 0.005; // Base chance per frame

            if (distToTarget > this.aggressionRange * 1.5) {
                teleportChance = 0.02; // Higher chance when far away
            } else if (distToTarget < this.attackRange * 1.5) {
                teleportChance = 0.01; // Higher chance when very close
            }

            // Random chance to teleport
            if (Math.random() < teleportChance) {
                this.teleport();
                this.teleportCooldown = this.teleportCooldownMax;
                return { x: 0, y: 0 }; // No movement needed after teleport
            }
        }

        // When not teleporting, use a mix of circling and direct chase
        if (distToTarget < this.aggressionRange) {
            // Close enough to circle
            const circleDirection = this.getCirclingDirection();
            return circleDirection;
        } else {
            // Too far, chase directly
            return {
                x: dx / distToTarget,
                y: dy / distToTarget
            };
        }
    }

    // Get direction for tactical behavior
    getTacticalDirection(nearbyEnemies) {
        // Base direction is flanking
        const baseDirection = this.getFlankingDirection();

        // If no nearby enemies, just use base direction
        if (!nearbyEnemies || nearbyEnemies.length === 0) {
            return baseDirection;
        }

        // Check if we should change flanking direction based on other enemies
        let leftCount = 0;
        let rightCount = 0;

        for (const enemy of nearbyEnemies) {
            if (enemy.flankingDirection > 0) {
                rightCount++;
            } else if (enemy.flankingDirection < 0) {
                leftCount++;
            }
        }

        // Change direction if too many enemies are flanking the same way
        if (this.flankingDirection > 0 && rightCount > leftCount + 2) {
            this.flankingDirection = -1;
        } else if (this.flankingDirection < 0 && leftCount > rightCount + 2) {
            this.flankingDirection = 1;
        }

        return baseDirection;
    }

    // Get direction for formation behavior
    getFormationDirection(nearbyEnemies) {
        if (!this.target) return { x: 0, y: 0 };

        switch (this.formationType) {
            case 'LINE':
                return this.getLineFormationDirection(nearbyEnemies);
            case 'CIRCLE':
                return this.getCircleFormationDirection(nearbyEnemies);
            case 'SCATTERED':
                return this.getScatteredFormationDirection();
            case 'FLANKING':
                return this.getFlankingFormationDirection(nearbyEnemies);
            case 'SURROUND':
                return this.getSurroundFormationDirection(nearbyEnemies);
            default:
                return { x: 0, y: 0 };
        }
    }

    // Get direction for line formation
    getLineFormationDirection(nearbyEnemies) {
        if (!this.target || !nearbyEnemies || nearbyEnemies.length === 0) return { x: 0, y: 0 };

        // Calculate perpendicular direction to target
        const dx = this.target.x - this.x;
        const dy = this.target.y - this.y;
        const distToTarget = Math.sqrt(dx * dx + dy * dy);

        // Normalize direction to target
        const normalizedDx = dx / distToTarget;
        const normalizedDy = dy / distToTarget;

        // Perpendicular direction
        const perpX = -normalizedDy;
        const perpY = normalizedDx;

        // Find position in line based on enemy index in group
        const index = nearbyEnemies.indexOf(this);
        const lineOffset = (index - nearbyEnemies.length / 2) * 30;

        // Calculate ideal position in line
        const idealX = this.target.x - normalizedDx * 100 + perpX * lineOffset;
        const idealY = this.target.y - normalizedDy * 100 + perpY * lineOffset;

        // Direction to ideal position
        return {
            x: idealX - this.x,
            y: idealY - this.y
        };
    }

    // Get direction for circle formation
    getCircleFormationDirection(nearbyEnemies) {
        if (!this.target || !nearbyEnemies || nearbyEnemies.length === 0) return { x: 0, y: 0 };

        // Calculate angle based on position in group
        const index = nearbyEnemies.indexOf(this);
        const angleStep = (Math.PI * 2) / nearbyEnemies.length;
        const angle = index * angleStep;

        // Calculate ideal position in circle
        const radius = 100 + nearbyEnemies.length * 5; // Larger radius for more enemies
        const idealX = this.target.x + Math.cos(angle) * radius;
        const idealY = this.target.y + Math.sin(angle) * radius;

        // Direction to ideal position
        return {
            x: idealX - this.x,
            y: idealY - this.y
        };
    }

    // Get direction for scattered formation
    getScatteredFormationDirection() {
        if (!this.target) return { x: 0, y: 0 };

        // If we don't have a formation position yet, create one
        if (!this.formationPosition) {
            const angle = Math.random() * Math.PI * 2;
            const distance = 80 + Math.random() * 100;

            this.formationPosition = {
                x: Math.cos(angle) * distance,
                y: Math.sin(angle) * distance
            };
        }

        // Calculate ideal position relative to target
        const idealX = this.target.x + this.formationPosition.x;
        const idealY = this.target.y + this.formationPosition.y;

        // Direction to ideal position
        return {
            x: idealX - this.x,
            y: idealY - this.y
        };
    }

    // Get direction for flanking formation
    getFlankingFormationDirection(nearbyEnemies) {
        if (!this.target || !nearbyEnemies || nearbyEnemies.length === 0) return { x: 0, y: 0 };

        // Calculate direction to target
        const dx = this.target.x - this.x;
        const dy = this.target.y - this.y;
        const distToTarget = Math.sqrt(dx * dx + dy * dy);

        // Normalize direction to target
        const normalizedDx = dx / distToTarget;
        const normalizedDy = dy / distToTarget;

        // Perpendicular direction
        const perpX = -normalizedDy;
        const perpY = normalizedDx;

        // Divide enemies into left and right flanking groups
        const index = nearbyEnemies.indexOf(this);
        const isLeftFlank = index < nearbyEnemies.length / 2;

        // Calculate ideal position for flanking
        let idealX, idealY;

        if (isLeftFlank) {
            idealX = this.target.x + perpX * 150 - normalizedDx * 50;
            idealY = this.target.y + perpY * 150 - normalizedDy * 50;
        } else {
            idealX = this.target.x - perpX * 150 - normalizedDx * 50;
            idealY = this.target.y - perpY * 150 - normalizedDy * 50;
        }

        // Direction to ideal position
        return {
            x: idealX - this.x,
            y: idealY - this.y
        };
    }

    // Get direction for surround formation
    getSurroundFormationDirection(nearbyEnemies) {
        if (!this.target || !nearbyEnemies || nearbyEnemies.length === 0) return { x: 0, y: 0 };

        // Calculate angle based on position in group
        const index = nearbyEnemies.indexOf(this);
        const angleStep = (Math.PI * 2) / nearbyEnemies.length;
        const angle = index * angleStep;

        // Calculate ideal position to surround target
        const radius = 80; // Fixed radius for surrounding
        const idealX = this.target.x + Math.cos(angle) * radius;
        const idealY = this.target.y + Math.sin(angle) * radius;

        // Direction to ideal position
        return {
            x: idealX - this.x,
            y: idealY - this.y
        };
    }

    // Shoot a projectile at the target
    shoot() {


        if (!this.target) {

            return;
        }

        if (!Game.core) {

            return;
        }

        if (!Game.core.enemyProjectiles) {

            // Initialize the array if it doesn't exist
            Game.core.enemyProjectiles = [];

        }

        // Always create a visual effect to show the shooting attempt
        if (Game.core.showDamageAt) {
            Game.core.showDamageAt(this.x, this.y, '!', 'rgba(255,0,0,0.7)');

        }

        try {
            // Check if EnemyProjectile class is available


            if (typeof window.EnemyProjectile !== 'function') {


                // Create a simple visual effect instead
                if (Game.core.showDamageAt) {
                    Game.core.showDamageAt(this.x, this.y, '!', 'rgba(255,0,0,0.7)');

                }

                // Create a simple projectile as a fallback
                const simpleProjectile = {
                    x: this.x,
                    y: this.y,
                    direction: {
                        x: (this.target.x - this.x) / Math.sqrt(Math.pow(this.target.x - this.x, 2) + Math.pow(this.target.y - this.y, 2)),
                        y: (this.target.y - this.y) / Math.sqrt(Math.pow(this.target.x - this.x, 2) + Math.pow(this.target.y - this.y, 2))
                    },
                    speed: this.enemyType.includes('BAT') ? 5 : 3,
                    radius: this.enemyType.includes('BAT') ? 10 : 8,
                    damage: this.enemyType.includes('BAT') ? 4 : 3,
                    color: this.enemyType.includes('BAT') ? 'rgba(255, 0, 0, 0.8)' : 'rgba(255, 0, 0, 0.7)',
                    lifetime: 0,
                    maxLifetime: 3000,
                    isDead: false,
                    update: function(deltaTime) {
                        this.lifetime += deltaTime;
                        if (this.lifetime >= this.maxLifetime) {
                            this.isDead = true;
                            return;
                        }

                        this.x += this.direction.x * this.speed * deltaTime / 16;
                        this.y += this.direction.y * this.speed * deltaTime / 16;

                        // Check for collisions with player
                        if (Game.core && Game.core.player && !Game.core.player.isDead && !Game.core.player.isInvul) {
                            const dx = this.x - Game.core.player.x;
                            const dy = this.y - Game.core.player.y;
                            const dist = Math.sqrt(dx * dx + dy * dy);

                            if (dist < this.radius + Game.core.player.radius) {
                                // Player takes damage
                                Game.core.player.takeDamage(this.damage);

                                // Show damage number
                                Game.core.showDamageAt(Game.core.player.x, Game.core.player.y, this.damage);

                                // Mark as dead after hitting player
                                this.isDead = true;
                                // Player hit by projectile
                            }
                        }
                    },
                    draw: function(ctx, camera, _sprites) {
                        if (this.isDead) return;

                        // Calculate screen position
                        const screenX = this.x - camera.x + camera.width / 2;
                        const screenY = this.y - camera.y + camera.height / 2;

                        // Draw a projectile with custom styling
                        ctx.save();

                        // Draw the main circle
                        ctx.beginPath();
                        ctx.arc(screenX, screenY, this.radius, 0, Math.PI * 2);
                        ctx.fillStyle = this.color || 'rgba(255, 0, 0, 0.7)';
                        ctx.fill();

                        // Draw a trail
                        if (this.direction) {
                            ctx.beginPath();
                            ctx.moveTo(screenX, screenY);
                            ctx.lineTo(
                                screenX - this.direction.x * this.radius * 2,
                                screenY - this.direction.y * this.radius * 2
                            );

                            // Use a matching trail color
                            const trailColor = this.color ? this.color.replace('0.8', '0.5').replace('0.7', '0.5') : 'rgba(255, 0, 0, 0.5)';
                            ctx.strokeStyle = trailColor;
                            ctx.lineWidth = 2;
                            ctx.stroke();
                        }

                        ctx.restore();
                    }
                };

                // Add to game
                Game.core.enemyProjectiles.push(simpleProjectile);

                return;
            }

            // Create a new projectile


            const projectile = new window.EnemyProjectile(
                this.x,
                this.y,
                this.target.x,
                this.target.y,
                this.enemyType
            );

            // Add to game
            Game.core.enemyProjectiles.push(projectile);


            // Add slight visual effect
            if (Game.core.showDamageAt) {
                Game.core.showDamageAt(this.x, this.y, '!', 'rgba(255,0,0,0.7)');
            }
        } catch (error) {


            // Create a simple visual effect as fallback
            if (Game.core && Game.core.showDamageAt) {
                Game.core.showDamageAt(this.x, this.y, 'Error!', 'rgba(255,0,0,0.7)');
            }
        }
    }

    // Get separation direction to avoid crowding
    getSeparationDirection(nearbyEnemies) {
        if (!nearbyEnemies || nearbyEnemies.length === 0) {
            return { x: 0, y: 0, weight: 0 };
        }

        let separationX = 0;
        let separationY = 0;
        let count = 0;
        let maxWeight = 0;

        // Use a larger separation distance for BAT enemies
        const minDistance = this.enemyType.includes('BAT') ? 100 : 40; // 100 pixels for BATs, 40 for others

        for (const other of nearbyEnemies) {
            // Skip self
            if (other === this) continue;

            // For BAT enemies, only consider other BATs of the same type to prevent grouping
            if (this.enemyType.includes('BAT') && !other.enemyType.includes('BAT')) {
                continue;
            }

            const dx = this.x - other.x;
            const dy = this.y - other.y;

            // Calculate distance
            const dist = distance(this.x, this.y, other.x, other.y);

            // Only separate if too close
            if (dist < minDistance) {
                // Weight is stronger when closer - stronger for BAT enemies
                const weight = this.enemyType.includes('BAT') ?
                    2.0 - (2.0 * dist / minDistance) : // Stronger weight for BATs
                    1.0 - (dist / minDistance);        // Normal weight for others

                maxWeight = Math.max(maxWeight, weight);

                // Normalized direction away from other enemy
                separationX += (dx / dist) * weight;
                separationY += (dy / dist) * weight;
                count++;
            }
        }

        // If no close enemies, return zero direction
        if (count === 0) {
            return { x: 0, y: 0, weight: 0 };
        }

        // Average the separation direction
        separationX /= count;
        separationY /= count;

        return {
            x: separationX,
            y: separationY,
            weight: maxWeight
        };
    }
}
// EnemyGroup class for managing groups of enemies
class EnemyGroup {
    constructor() {
        this.stored = [];
        this.spawned = [];
        this.enabled = true;
        this.enemyType = null;
        this.groupId = Math.random().toString(36).substring(2, 9); // Generate a unique group ID
    }
    // Initialize the group with an enemy type
    init(enemyType) {
        this.enemyType = enemyType;
    }
    // Spawn an enemy at a position
    spawnAt(x, y) {
        const enemy = this.spawn();
        enemy.x = x;
        enemy.y = y;

        // Set group ID for formation behavior
        enemy.setGroupId(this.groupId);

        // Set target to player
        if (Game.core && Game.core.player) {
            enemy.setTarget(Game.core.player);
        }

        // Determine formation type based on group size
        this.updateFormationTypes();

        return enemy;
    }

    // Update formation types based on group size
    updateFormationTypes() {
        // Only apply formations to groups with multiple enemies
        if (this.spawned.length < 2) return;

        try {
            // Determine formation type based on enemy type and group size
            let formationType = 'NONE';

            if (this.enemyType.includes('BOSS')) {
                // Boss groups use tactical formations
                formationType = 'CIRCLE';
            } else if (this.enemyType.includes('SWARM')) {
                // Swarm enemies use scattered formations
                formationType = 'SCATTERED';
            } else if (this.spawned.length >= 8) {
                // Large groups surround the player
                formationType = 'SURROUND';
            } else if (this.spawned.length >= 5) {
                // Medium groups use circle formations
                formationType = 'CIRCLE';
            } else if (this.spawned.length >= 3) {
                // Small groups use flanking formations
                formationType = 'FLANKING';
            } else {
                // Tiny groups use line formations
                formationType = 'LINE';
            }

            // Apply formation type to all enemies in the group
            for (const enemy of this.spawned) {
                enemy.setFormationType(formationType);
            }
        } catch (error) {

        }
    }
    // Spawn an enemy
    spawn() {
        let enemy = this.stored.pop();
        if (!enemy) {
            enemy = this.make();
        }
        this.spawned.push(enemy);
        if (Game.core) {
            Game.core.enemies.push(enemy);
        }
        return enemy;
    }
    // Return an enemy to the pool
    return(enemy) {
        const index = this.spawned.indexOf(enemy);
        if (index !== -1) {
            this.spawned.splice(index, 1);
        }

        // Reset enemy state before storing
        enemy.isDead = false;
        enemy.hp = enemy.maxHp;
        enemy.receivingDamage = false;
        enemy.isTimeStopped = false;
        enemy.isFrozen = false;
        enemy.currentDirection = new Vector2(0, 0);

        // Update enemy type count in stage
        if (Game.core && Game.core.stage && Game.core.stage.enemyTypeCounts) {
            const enemyType = enemy.enemyType;
            if (Game.core.stage.enemyTypeCounts[enemyType] > 0) {
                Game.core.stage.enemyTypeCounts[enemyType]--;
            }
        }

        this.stored.push(enemy);
    }
    // Create a new enemy
    make() {

        const enemy = new Enemy(0, 0, this.enemyType);
        return enemy;
    }
}

// Attach to window object for global access
window.Enemy = Enemy;
window.EnemyGroup = EnemyGroup;