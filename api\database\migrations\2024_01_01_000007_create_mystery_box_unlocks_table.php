<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('mystery_box_unlocks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained('telegram_users')->onDelete('cascade');
            $table->string('box_type', 50); // References mystery_box_types.box_type
            
            $table->enum('unlock_source', ['pet_purchase', 'prize_tree', 'collection_bonus', 'admin_grant']);
            $table->string('source_reference')->nullable(); // Pet ID, prize tree level, etc.
            $table->timestamp('unlocked_at');
            
            $table->timestamps();
            
            $table->unique(['telegram_user_id', 'box_type']);
            $table->index(['telegram_user_id', 'unlocked_at']);
            
            $table->foreign('box_type')
                  ->references('box_type')
                  ->on('mystery_box_types')
                  ->onDelete('cascade');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('mystery_box_unlocks');
    }
};
