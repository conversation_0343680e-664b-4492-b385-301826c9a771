import { motion } from 'framer-motion';
import { BattlxIcon } from '@/components/icons/BattlxIcon';
import { Prize } from '@/types/PrizeTypes';

interface PrizeNodeDetailsProps {
  node: Prize;
  isUnlocked: boolean;
  canUnlock: boolean;
  onUnlock: () => void;
  isLoading: boolean;
  onClose: () => void;
}

export default function PrizeNodeDetails({
  node,
  isUnlocked,
  canUnlock,
  onUnlock,
  isLoading,
  onClose
}: PrizeNodeDetailsProps) {
  // Render reward details based on reward type
  const renderRewardDetails = () => {
    if (!node.reward_details) return null;

    switch (node.reward_type) {
      case 'cosmetic':
        return (
          <div className="mt-2">
            <div className="text-sm text-[#9B8B6C]/80">
              Type: {node.reward_details.type}
            </div>
            {node.reward_details.preview_image && (
              <div className="mt-2">
                <img
                  src={node.reward_details.preview_image}
                  alt="Preview"
                  className="w-full h-auto rounded-lg border border-[#9B8B6C]/40"
                />
              </div>
            )}
          </div>
        );

      case 'card':
        return (
          <div className="mt-2">
            <div className="text-sm text-[#9B8B6C]/80">
              Card Rarity: {node.reward_details.rarity}
            </div>
          </div>
        );

      case 'balance':
        return (
          <div className="mt-2">
            <div className="flex items-center text-sm text-[#9B8B6C]/80">
              <BattlxIcon icon="coin" className="w-4 h-4 mr-1 text-[#9B8B6C]" />
              {node.reward_details.amount} {node.reward_details.currency}
            </div>
          </div>
        );

      case 'title':
        return (
          <div className="mt-2">
            <div className="text-sm text-[#9B8B6C]/80">
              Title: <span style={{ color: node.reward_details.color }}>{node.reward_details.title}</span>
            </div>
          </div>
        );

      case 'emote':
        return (
          <div className="mt-2">
            <div className="text-sm text-[#9B8B6C]/80">
              Emote: {node.reward_details.emote}
            </div>
          </div>
        );

      case 'special_item':
        return (
          <div className="mt-2">
            <div className="text-sm text-[#9B8B6C]/80">
              Special Item: {node.reward_details.item_type}
            </div>
          </div>
        );

      case 'booster':
        return (
          <div className="mt-2">
            <div className="text-sm text-[#9B8B6C]/80">
              Booster: {node.reward_details.type} (x{node.reward_details.multiplier})
            </div>
            <div className="text-sm text-[#9B8B6C]/80">
              Duration: {node.reward_details.duration} hours
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <motion.div
      className="fixed inset-4 top-1/2 left-[10%] transform -translate-y-1/2 w-[95%] max-w-sm bg-gradient-to-br from-[#120D0E] to-[#1A1617] border-2 border-[#9B8B6C]/60 rounded-xl shadow-[0_8px_32px_rgba(0,0,0,0.8)] p-4 z-50 max-h-[80vh] overflow-y-auto"
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      exit={{ scale: 0.8, opacity: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 25 }}
    >
      {/* Close Button */}
      <button
        onClick={onClose}
        className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center bg-[#9B8B6C]/20 hover:bg-[#9B8B6C]/40 rounded-lg transition-all duration-200 group"
      >
        <BattlxIcon icon="x" className="w-4 h-4 text-[#9B8B6C] group-hover:text-[#B3B3B3]" />
      </button>

      <div className="flex items-start">
        <div className="flex items-center justify-center w-12 h-12 mr-4 rounded-full bg-[#120D0E] border-2 border-[#9B8B6C]/60">
          <BattlxIcon icon={node.icon as any || 'skill'} className="w-6 h-6 text-[#9B8B6C]" />
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-bold text-[#9B8B6C]">{node.name}</h3>
          <p className="text-sm text-[#B3B3B3]">{node.description}</p>

          {/* Reward type */}
          <div className="flex items-center mt-2">
            <div className="px-2 py-1 text-xs rounded-full bg-[#9B8B6C]/20 text-[#9B8B6C]">
              {node.reward_type.charAt(0).toUpperCase() + node.reward_type.slice(1)}
            </div>
            <div className="ml-2 text-xs text-[#9B8B6C]/80">
              Tier {node.tier}
            </div>
          </div>

          {/* Reward details */}
          {renderRewardDetails()}
        </div>
      </div>

      {/* Cost and unlock button - Mobile optimized layout */}
      <div className="mt-4 pt-4 border-t border-[#9B8B6C]/20 space-y-3">
        {/* Cost display */}
        <div className="flex items-center justify-center">
          <BattlxIcon icon="coin" className="w-5 h-5 mr-2 text-[#9B8B6C]" />
          <span className="text-lg font-bold text-[#9B8B6C]">{node.cost} Points</span>
        </div>

        {/* Action button - Full width for mobile */}
        {isUnlocked ? (
          <div className="w-full py-3 text-center text-sm font-medium rounded-lg bg-[#9B8B6C]/20 text-[#9B8B6C] border border-[#9B8B6C]/40">
            ✓ Unlocked
          </div>
        ) : (
          <button
            className={`w-full py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
              canUnlock
                ? 'bg-[#9B8B6C] text-[#120D0E] hover:bg-[#9B8B6C]/90 shadow-[0_4px_15px_rgba(155,139,108,0.4)]'
                : 'bg-[#9B8B6C]/20 text-[#9B8B6C]/60 cursor-not-allowed border border-[#9B8B6C]/20'
            }`}
            onClick={onUnlock}
            disabled={!canUnlock || isLoading}
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="w-4 h-4 border-2 border-[#120D0E] border-t-transparent rounded-full animate-spin mr-2"></div>
                Unlocking...
              </div>
            ) : (
              'Unlock Skill'
            )}
          </button>
        )}
      </div>
    </motion.div>
  );
}
