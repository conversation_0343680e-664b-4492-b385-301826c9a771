/**
 * Menu State
 * Displays the main menu
 */
class MenuState extends GameState {
    /**
     * @param {Engine} engine - Game engine
     */
    constructor(engine) {
        super(engine);
        this.input = new InputHandler(engine);
        this.background = null;
        this.player = null;
        this.menuMusic = null;
    }

    /**
     * Called when entering the state
     */
    enter() {
        // Create background
        this.background = new Background(this.engine);

        // Create player
        this.player = new Player(this.engine, 100, 775);

        // Play menu music
        this.menuMusic = this.engine.playSound('menuMusic', { volume: 0.3, loop: true });
    }

    /**
     * Called when exiting the state
     */
    exit() {
        // Stop menu music
        if (this.menuMusic) {
            this.engine.stopSound(this.menuMusic);
        }
    }

    /**
     * Update the state
     * @param {number} deltaTime - Time since last update
     */
    update(deltaTime) {
        // Update input
        this.input.update();

        // Check for start input
        if (this.input.isStartPressed()) {
            this.engine.setState(CONSTANTS.STATES.PLAYING);
            return;
        }

        // Update background
        this.background.update(deltaTime, this.player.y);

        // Update player (just animation, no movement)
        this.player.currentSprite.update(deltaTime);
    }

    /**
     * Render the state
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     */
    render(ctx) {
        // Draw background
        this.background.render(ctx, this.player.y);

        // Draw player
        this.player.render(ctx);

        // Draw menu text
        Utils.drawText(ctx, 'JAZZ JACKRABBIT', this.engine.width / 2, 200, {
            fillStyle: '#FFFF00',
            strokeStyle: '#000000',
            font: 'bold 40px Arial',
            lineWidth: 5
        });

        Utils.drawText(ctx, 'ENDLESS RUNNER', this.engine.width / 2, 250, {
            fillStyle: '#FFFFFF',
            strokeStyle: '#000000',
            font: 'bold 30px Arial',
            lineWidth: 4
        });

        // Draw instruction text with pulsing effect
        const pulseAmount = Math.sin(performance.now() / 500) * 0.2 + 0.8;
        ctx.globalAlpha = pulseAmount;
        Utils.drawText(ctx, 'PRESS SPACE OR TAP TO START', this.engine.width / 2, 400, {
            fillStyle: '#FFFFFF',
            strokeStyle: '#000000',
            font: 'bold 20px Arial',
            lineWidth: 3
        });
        ctx.globalAlpha = 1;

        // Best score display removed as it's no longer needed
        // Scores are now handled by the backend
    }
}

// Explicitly add MenuState to the window object
window.MenuState = MenuState;
