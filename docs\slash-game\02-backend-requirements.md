# Slash Game Integration Guide: 02 - Backend Integration Requirements

This document outlines the necessary backend integration requirements for the Slash game, building upon the existing backend structure for game handling.

## 1. Utilizing Existing Backend Components

Based on the initial analysis of [`GameController.php`](api/app/Http/Controllers/GameController.php) and [`TelegramUser.php`](api/app/Models/TelegramUser.php), the existing backend infrastructure already provides significant support for integrating new games, including the Slash game.

*   **`TelegramUser.php`**: The `TelegramUser` model includes the necessary fields and methods to support the Slash game:
    *   `slash_game_unlocked`: A boolean field to track if the user has unlocked the Slash game. This field already exists based on the migration file listing.
    *   `game_score`: An integer field used to store the total accumulated score across all games. The final coin amount collected in each Slash game session will be added to this field.
    *   `canPlaySlashGame()`: A method that checks if the user can play the Slash game. As observed, this method currently only checks the `slash_game_unlocked` status, which aligns with the requirement for unlimited plays upon unlock.

*   **`GameController.php`**: The `GameController` provides the necessary API endpoints:
    *   `unlockGame`: This endpoint already includes logic to handle unlocking the 'slash' game based on the defined `SLASH_GAME_UNLOCK_PRICE`.
    *   `checkPlayAvailability`: This endpoint correctly calls `canPlaySlashGame()` for the 'slash' game ID to determine if the user is allowed to play.
    *   `updateScore`: This common endpoint is designed to receive a score and add it to the user's `game_score`. This endpoint will be used by the frontend to submit the final coin amount from a Slash game session.
    *   `usePlay`: This endpoint is used for games with limited plays (like Tower). The `GameWrapper` on the frontend is designed to bypass calling this endpoint for games like Rabbit and Slash, which have unlimited plays upon unlock.

## 2. Necessary Backend Modifications

Given the existing structure, the required backend modifications for the core Slash game integration are minimal.

*   **Confirm `SLASH_GAME_UNLOCK_PRICE`**: Ensure the `SLASH_GAME_UNLOCK_PRICE` constant in [`GameController.php`](api/app/Http/Controllers/GameController.php) is set to the desired price for unlocking the Slash game (currently set to 5000, same as Rabbit).
*   **Verify `updateScore` Handling**: Confirm that the `updateScore` method in [`GameController.php`](api/app/Http/Controllers/GameController.php) correctly handles the submitted score (coin amount) for the 'slash' game ID and adds it to the user's `game_score` without any game-specific logic that might interfere. Based on the code review, the current implementation of `updateScore` is generic and simply adds the provided score to `game_score`, which is suitable.
*   **Verify `usePlay` Bypass**: Confirm that the frontend's `GameWrapper.tsx` correctly identifies the 'slash' game as having unlimited plays and does not call the `usePlay` endpoint for it. This is handled on the frontend, but it's a critical part of the backend interaction pattern for unlimited play games.

## 3. Database Schema Modifications

Based on the existing migration files and the requirement for unlimited plays upon unlock with score accumulation in `game_score`, no new database tables are required for the core Slash game integration.

*   The `telegram_users` table should already have the `slash_game_unlocked` boolean column and the `game_score` integer column. These were confirmed to exist during the initial analysis of the migration files.

Any future features specific to the Slash game (e.g., game-specific stats, achievements within the game) might require additional database modifications, but for the initial integration as outlined, the existing schema is sufficient.

## 4. API Endpoints for Slash Game

The Slash game will utilize the following existing API endpoints via the `GameWrapper` frontend component:

*   `POST /api/game/unlock`: Used when a user attempts to unlock the Slash game.
*   `GET /api/game/check-play-availability?game_id=slash`: Used to check if the user has unlocked the Slash game and is allowed to play.
*   `POST /api/game/update-score`: Used to submit the final coin amount collected in a game session as the score.

No new API endpoints are required for the basic integration of the Slash game with unlimited plays and score accumulation.