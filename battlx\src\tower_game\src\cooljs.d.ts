declare module 'cooljs' {
  export interface Engine {
    width: number;
    height: number;
    debug?: boolean;
    paused?: boolean;
    ctx: CanvasRenderingContext2D;
    
    // Audio methods
    playAudio: (id: string, loop?: boolean) => void;
    pauseAudio: (id: string) => void;
    removeAudio: (id: string) => void;
    
    // Asset loading
    addImg: (id: string, path: string) => void;
    addAudio: (id: string, path: string) => void;
    addLayer: (name: string) => void;
    swapLayer: (a: number, b: number) => void;
    
    // Game state management
    setVariable: (name: string, value: any) => void;
    getVariable: (name: string) => any;
    setTimeMovement: (name: string, duration: number) => void;
    checkTimeMovement: (name: string) => boolean;
    
    // Instance management
    addInstance: (instance: Instance) => void;
    getInstance: (name: string) => Instance | null;
    removeInstance: (name: string) => void;
    clearInstances: () => void;
    
    // Animation control
    startAnimate: () => void;
    endAnimate: () => void;
    stopAnimate: () => void;
    
    // Event handling
    addKeyDownListener: (key: string, handler: Function) => void;
    touchStartListener: (() => void) | null;
    removeEventListeners: () => void;
    
    // Custom game methods
    playBgm: () => void;
    pauseBgm: () => void;
    start: () => void;
    destroy: () => void;
    togglePaused: () => void;
  }

  export interface Instance {
    name: string;
    action: (instance: Instance) => void;
    painter: (instance: Instance, engine: Engine) => void;
    index?: number;
    count?: number;
    status?: string;
  }

  export const Engine: new (options: {
    canvasId: string;
    highResolution?: boolean;
    width: number;
    height: number;
    soundOn?: boolean;
  }) => Engine;
}
