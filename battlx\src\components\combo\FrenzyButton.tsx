import React, { useEffect, useRef } from 'react';
import { useSpring, animated } from 'react-spring';
import { useComboStore } from '@/store/combo-store';

/**
 * FrenzyButton component that appears when frenzy mode is ready to be activated
 */
const FrenzyButton: React.FC = () => {
  const { frenzyReady, activateFrenzy } = useComboStore();
  const buttonRef = useRef<HTMLDivElement>(null);
  
  // Pulsing animation
  const springProps = useSpring({
    from: { scale: 1, opacity: 0.7 },
    to: async (next) => {
      // eslint-disable-next-line no-constant-condition
      while (true) {
        await next({ scale: 1.2, opacity: 1 });
        await next({ scale: 1, opacity: 0.7 });
      }
    },
    config: { tension: 300, friction: 10 }
  });
  
  // Add a subtle shake animation when the button appears
  useEffect(() => {
    if (frenzyReady && buttonRef.current) {
      buttonRef.current.classList.add('shake-animation');
      
      // Remove the animation class after it completes
      setTimeout(() => {
        if (buttonRef.current) {
          buttonRef.current.classList.remove('shake-animation');
        }
      }, 500);
    }
  }, [frenzyReady]);
  
  // Only render the component if frenzy is ready
  if (!frenzyReady) return null;
  
  return (
    <animated.div
      ref={buttonRef}
      className="frenzy-button"
      style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: springProps.scale.to(s => `translate(-50%, -50%) scale(${s})`),
        opacity: springProps.opacity,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        color: '#FFD700', // Gold
        padding: '15px 25px',
        borderRadius: '10px',
        fontWeight: 'bold',
        fontSize: '1.8rem',
        cursor: 'pointer',
        zIndex: 100,
        boxShadow: '0 0 20px #FFD700',
        fontFamily: 'Olnova-HeavyCond, sans-serif',
        textTransform: 'uppercase',
        letterSpacing: '2px',
        border: '2px solid #FFD700'
      }}
      onClick={activateFrenzy}
    >
      FRENZY!
    </animated.div>
  );
};

export default FrenzyButton;
