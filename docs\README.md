# Prize Tree System Implementation

This repository contains the implementation of a Prize Tree system for a Fruit Ninja-style React application. The system allows users to earn achievement points through various activities and spend them to unlock prizes organized in themed trees.

## Features

- **Achievement Points**: Earn points through gameplay, completing tasks, missions, and reaching milestones
- **Prize Trees**: Themed collections of prizes organized in a hierarchical structure
- **Prize Unlocking**: Spend achievement points to unlock prizes
- **Prize Gallery**: View and equip unlocked prizes
- **Visual Customization**: Equip cosmetic prizes to customize your gameplay experience

## Implementation Files

### Backend

- **Models**:
  - `TelegramUser.php`: Extended with prize and achievement point relationships
  - `Prize.php`: Represents a prize in the prize tree
  - `PrizeTree.php`: Represents a themed collection of prizes
  - `UserPrize.php`: Represents a prize unlocked by a user
  - `UserAchievementPoint.php`: Tracks a user's achievement point balance
  - `AchievementPointTransaction.php`: Records achievement point transactions
  - `TapStat.php`: Tracks a user's tapping statistics
  - `GameStat.php`: Tracks a user's game statistics

- **Controllers**:
  - `PrizeTreeController.php`: Handles prize tree-related requests
  - `UserPrizeController.php`: Handles user prize-related requests
  - `AchievementPointController.php`: Handles achievement point-related requests
  - `ClickerController.php`: Updated to award achievement points for tapping milestones
  - `UserTaskController.php`: Updated to award achievement points for completing tasks
  - `ReferralTaskController.php`: Updated to award achievement points for completing referral tasks
  - `UserMissionController.php`: Updated to award achievement points for completing missions
  - `GameController.php`: Updated to award achievement points for game milestones

- **Services**:
  - `AchievementPointService.php`: Handles achievement point transactions
  - `PrizeService.php`: Handles prize-related operations

- **Middleware**:
  - `AdminMiddleware.php`: Protects admin-only endpoints

- **Migrations**:
  - `create_prize_trees_table.php`: Creates the prize_trees table
  - `create_prizes_table.php`: Creates the prizes table
  - `create_user_prizes_table.php`: Creates the user_prizes table
  - `create_achievement_point_transactions_table.php`: Creates the achievement_point_transactions table
  - `create_user_achievement_points_table.php`: Creates the user_achievement_points table
  - `create_tap_stats_table.php`: Creates the tap_stats table
  - `create_game_stats_table.php`: Creates the game_stats table

- **Seeders**:
  - `PrizeTreeSeeder.php`: Seeds the database with initial prize trees and prizes

### Frontend

- **Pages**:
  - `PrizeTree.jsx`: Main page for viewing and interacting with prize trees
  - `PrizeGallery.jsx`: Page for viewing and equipping unlocked prizes

- **Components**:
  - `PrizeTreeCanvas.jsx`: Renders the prize tree visualization
  - `PrizeNodeDetails.jsx`: Displays details about a selected prize
  - `PrizeTreeSelector.jsx`: Allows switching between different prize trees
  - `UserPrizeGallery.jsx`: Displays a user's unlocked prizes

- **Store**:
  - `user-store.js`: Updated to include achievement points

- **Router**:
  - `router.jsx`: Updated with new routes for prize tree pages

- **Navigation**:
  - `AppBar.jsx`: Updated with a new tab for the prize tree

## Documentation

- `backendplan_1.md`: Initial backend implementation plan
- `backendplan_2.md`: Continued backend implementation plan
- `backendplan_3.md`: Achievement points integration plan
- `integrations_1.md`: Initial frontend integration plan
- `integrations_2.md`: Continued frontend integration plan
- `integrations_3.md`: Prize tree frontend integration plan
- `notes_1.md`: Initial implementation notes
- `notes_2.md`: Continued implementation notes
- `notes_3.md`: Prize tree implementation notes

## Slash Game Integration Guide

This section provides a step-by-step guide for integrating the Slash game into the web application. Refer to the detailed documents in the `docs/slash-game/` directory for in-depth information on each step.

1.  **Initial Analysis and Code Review:** Understand the existing game integration patterns and the current state of the Slash game code.
    *   Refer to: [`docs/slash-game/01-initial-analysis.md`](docs/slash-game/01-initial-analysis.md)

2.  **Backend Integration Requirements:** Define necessary backend modifications and API interactions. The Slash game uses the existing unlimited play upon unlock model and the common score accumulation endpoint (using the final coin amount as the score).
    *   Refer to: [`docs/slash-game/02-backend-requirements.md`](docs/slash-game/02-backend-requirements.md)

3.  **Frontend Component Structure:** Detail the React component hierarchy and state management. This includes implementing the `GameInstance` interface in `battlx/src/slash_game/src/main.ts` to bridge the game's core logic with `GameWrapper.tsx`.
    *   Refer to: [`docs/slash-game/03-frontend-structure.md`](docs/slash-game/03-frontend-structure.md)
    *   **Key Implementation:** Create the [`battlx/src/slash_game/src/main.ts`](battlx/src/slash_game/src/main.ts) file and implement the `load`, `start`, `destroy`, `playBgm`, `pauseBgm` methods, connecting them to the Slash game's internal `Game` and `GameCore` classes.

4.  **Game Assets Integration:** Document the strategy for organizing and loading Slash game assets. Assets are located in `battlx/src/slash_game/assets/`. The loading is handled internally by the game's core logic and orchestrated by the `main.ts` file.
    *   Refer to: [`docs/slash-game/04-asset-integration.md`](docs/slash-game/04-asset-integration.md)

5.  **Game Logic Implementation:** Outline the core game mechanics and state management within the Slash game module.
    *   Refer to: [`docs/slash-game/05-game-logic.md`](docs/slash-game/05-game-logic.md)
    *   **Key Implementation:** Ensure that the game over condition in the Slash game's core logic (`GameCore.js`) correctly retrieves the final collected coin amount (`playerOptions.coins`) and passes it as the score when calling the `onGameOver` callback provided by `GameWrapper`.

6.  **API Integration Points:** Document how the frontend game logic interacts with the backend API via `GameWrapper.tsx`. The final coin amount is sent as the score to the `/api/game/update-score` endpoint.
    *   Refer to: [`docs/slash-game/06-api-integration.md`](docs/slash-game/06-api-integration.md)

7.  **Enum and Constants Management:** List necessary enums and constants and define their management, referencing the existing files in `battlx/src/slash_game/js/consts/` and `battlx/src/slash_game/js/enums/`. Ensure the game ID 'slash' is used consistently.
    *   Refer to: [`docs/slash-game/07-enum-management.md`](docs/slash-game/07-enum-management.md)

8.  **Script Optimization and Reorganization:** Analyze the script structure and suggest improvements for performance and maintainability. Consider bundling the game's JavaScript files.
    *   Refer to: [`docs/slash-game/08-script-optimization.md`](docs/slash-game/08-script-optimization.md)

9.  **Testing and Validation Requirements:** Define the testing strategy and specific test cases, including verifying the correct handling of the final coin amount as the score throughout the integration.
    *   Refer to: [`docs/slash-game/09-testing-requirements.md`](docs/slash-game/09-testing-requirements.md)

10. **Deployment and Integration Checklist:** Provide a checklist for deploying and verifying the successful integration, including confirming that the final coin amount is correctly saved as the score on the backend.
    *   Refer to: [`docs/slash-game/10-deployment-checklist.md`](docs/slash-game/10-deployment-checklist.md)

## API Endpoints

- `GET /prizes/trees`: Get all prize trees
- `GET /prizes/trees/{id}`: Get a specific prize tree with its prizes
- `GET /prizes/user`: Get user's prizes and achievement points
- `POST /prizes/unlock`: Unlock a prize
- `POST /prizes/equip`: Equip a prize
- `POST /prizes/unequip`: Unequip a prize
- `GET /prizes/transactions`: Get achievement point transactions
- `POST /achievement-points/award`: Award achievement points (admin only)

## Achievement Point Sources

- **Tapping Milestones**: 1-5 points for reaching certain numbers of total taps
- **Daily Tasks**: 1 point for each daily task completion
- **Regular Tasks**: 1 point for each task completion
- **Referral Tasks**: 2 points for each referral task completion
- **Mission Completion**: 2 points for each mission completion
- **Game Milestones**: 1-3 points for reaching score milestones and unlocking games

## Prize Types

- **Cosmetic**: Visual enhancements like slash effects, backgrounds, etc.
- **Title**: Special titles displayed next to the user's name
- **Card**: Collectible cards
- **Balance**: In-game currency rewards
- **Booster**: Temporary boosts to gameplay
- **Special Item**: Unique items with special effects
- **Emote**: Animated expressions for social interactions

## Installation

1. Run database migrations:
   ```
   php artisan migrate
   ```

2. Seed the database with initial prize trees:
   ```
   php artisan db:seed --class=PrizeTreeSeeder
   ```

3. Build the frontend assets:
   ```
   npm run build
   ```

## Usage

1. Navigate to the "Prizes" tab in the application
2. Explore the available prize trees
3. Select a prize to view its details
4. Unlock prizes using achievement points
5. View and equip unlocked prizes in the Prize Gallery

## Future Enhancements

- Seasonal prize trees with exclusive prizes
- Achievement badges for milestone achievements
- Prize sharing functionality
- Prize crafting system
- More interactive prize effects
