# Slash Game Integration Guide: 07 - Enum and Constants Management

This document lists the required enums and constants for the Slash game integration and defines guidelines for their management, referencing the existing constant and enum files within the Slash game's source code.

## 1. Required Enums and Constants

The Slash game's core logic already utilizes several JavaScript files for defining constants and enums within the `battlx/src/slash_game/js/` directory:

*   **Constants:**
    *   [`battlx/src/slash_game/js/consts/characters.js`](battlx/src/slash_game/js/consts/characters.js): Defines constants related to player characters.
    *   [`battlx/src/slash_game/js/consts/destructibles.js`](battlx/src/slash_game/js/consts/destructibles.js): Defines constants for destructible objects.
    *   [`battlx/src/slash_game/js/consts/enemies.js`](battlx/src/slash_game/js/consts/enemies.js): Defines constants for enemy types and properties.
    *   [`battlx/src/slash_game/js/consts/enemySpawnConfig.js`](battlx/src/slash_game/js/consts/enemySpawnConfig.js): Defines configuration for enemy spawning.
    *   [`battlx/src/slash_game/js/consts/pickups.js`](battlx/src/slash_game/js/consts/pickups.js): Defines constants for pickup types and properties.
    *   [`battlx/src/slash_game/js/consts/stages.js`](battlx/src/slash_game/js/consts/stages.js): Defines constants related to game stages or waves.
    *   [`battlx/src/slash_game/js/consts/weapons.js`](battlx/src/slash_game/js/consts/weapons.js): Defines constants for weapon types and properties.

*   **Enums:**
    *   [`battlx/src/slash_game/js/enums/characterType.js`](battlx/src/slash_game/js/enums/characterType.js): Defines an enum for character types.
    *   [`battlx/src/slash_game/js/enums/destructibleType.js`](battlx/src/slash_game/js/enums/destructibleType.js): Defines an enum for destructible types.
    *   [`battlx/src/slash_game/js/enums/enemyType.js`](battlx/src/slash_game/js/enums/enemyType.js): Defines an enum for enemy types.
    *   [`battlx/src/slash_game/js/enums/fixedTreasures.js`](battlx/src/slash_game/js/enums/fixedTreasures.js): Defines an enum for fixed treasure types.
    *   [`battlx/src/slash_game/js/enums/npcType.js`](battlx/src/slash_game/js/enums/npcType.js): Defines an enum for NPC types.
    *   [`battlx/src/slash_game/js/enums/pickupType.js`](battlx/src/slash_game/js/enums/pickupType.js): Defines an enum for pickup types (including `GEM` and `COIN`).
    *   [`battlx/src/slash_game/js/enums/sceneType.js`](battlx/src/slash_game/js/enums/sceneType.js): Defines an enum for scene types.
    *   [`battlx/src/slash_game/js/enums/treasureType.js`](battlx/src/slash_game/js/enums/treasureType.js): Defines an enum for treasure types.
    *   [`battlx/src/slash_game/js/enums/weaponType.js`](battlx/src/slash_game/js/enums/weaponType.js): Defines an enum for weapon types.

In addition to these existing definitions, the integration will require:

*   Defining the unique string identifier for the Slash game, which is `'slash'`. This constant should be used consistently in the frontend (`GameWrapper.tsx`, `../../games/registry.ts`) and backend (`GameController.php`) when referring to the Slash game.

## 2. Naming Conventions

Maintain consistency with the existing naming conventions used in the `js/consts/` and `js/enums/` directories. Typically, constants are defined using uppercase letters with underscores (`LIKE_THIS`), and enum members follow a similar convention.

## 3. Implementation Approach

*   Continue to use the existing constant and enum files within `battlx/src/slash_game/js/` for game-specific definitions.
*   If new constants or enums are needed for the integration (e.g., related to specific UI states in `SlashGameDrawer.tsx`), create new files within the appropriate directories (`battlx/src/slash_game/js/consts/` or `battlx/src/slash_game/js/enums/`) or define them within the relevant component files if their scope is limited.
*   Ensure the game ID string `'slash'` is defined as a constant in a shared location accessible by both the game module and the frontend components that interact with it (e.g., in `../../games/registry.ts` or a dedicated constants file for game IDs).
*   Import and use these constants and enums consistently throughout the Slash game module and frontend integration code to improve readability and maintainability.