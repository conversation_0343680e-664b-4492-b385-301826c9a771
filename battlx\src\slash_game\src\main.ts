import { GameInstance } from '../../games/registry';
import { resourceManager } from '../../games/resource-manager';
import { loadGameScripts, getLoadingProgress } from './loader';

/**
 * Initialize and return a Slash Game instance
 * @param options Game options from GameWrapper
 * @returns GameInstance compatible with the registry
 */
export const slashGame = (options: any): GameInstance => {
  // Game instance reference
  let gameInstance: any = null;
  let canvasElement: HTMLCanvasElement | null = null;
  let gameInitialized = false;

  // Create GameInstance wrapper
  const gameInstanceWrapper: GameInstance = {
    // Load assets and initialize game
    load: (onReady, onProgress) => {
      // First load all required scripts
      loadGameScripts()
        .then(() => {
          try {
            // Get the canvas element
            canvasElement = document.getElementById(options.canvasId) as HTMLCanvasElement;
            if (!canvasElement) {
              console.error(`Canvas element with ID ${options.canvasId} not found`);
              onProgress({
                success: 0,
                total: 1,
                failed: 1
              });
              return;
            }

            // Check if required classes are available
            if (!(window as any).Game) {
              console.error('Game class not found in window object');
              onProgress({
                success: 0,
                total: 1,
                failed: 1
              });
              return;
            }

            // Create game instance
            gameInstance = new (window as any).Game(canvasElement);

            // Store integration callbacks in the game for state access
            if (options.setGameScore && gameInstance.core) {
              gameInstance.core.setVariable = gameInstance.core.setVariable || function(this: any, name: string, value: any) {
                this[name] = value;
              };
              gameInstance.core.setVariable('setGameScore', options.setGameScore);
            }
            if (options.onGameOver && gameInstance.core) {
              gameInstance.core.setVariable = gameInstance.core.setVariable || function(this: any, name: string, value: any) {
                this[name] = value;
              };
              gameInstance.core.setVariable('onGameOver', options.onGameOver);
            }

            // Override game over method to integrate with React
            if (gameInstance.core) {
              const originalGameOver = gameInstance.core.gameOver.bind(gameInstance.core);
              gameInstance.core.gameOver = function() {
                // Get final score (coins collected)
                const finalScore = this.playerOptions?.lifetimeCoins || 0;

                // Call the integration callback
                if (options.setGameScore) {
                  options.setGameScore(finalScore);
                }
                if (options.onGameOver) {
                  options.onGameOver();
                }

                // Call original game over
                originalGameOver();
              };
            }

            gameInitialized = true;
            onReady();
          } catch (error) {
            console.error('Error initializing Slash game:', error);
            onProgress({
              success: 0,
              total: 1,
              failed: 1
            });
          }
        })
        .catch((error) => {
          console.error('Failed to load Slash Game scripts:', error);
          onProgress({
            success: 0,
            total: 1,
            failed: 1
          });
        });

      // Report loading progress
      const updateProgress = () => {
        const progress = getLoadingProgress();
        onProgress({
          success: progress.loaded,
          total: progress.total,
          failed: 0
        });

        if (progress.loaded < progress.total) {
          requestAnimationFrame(updateProgress);
        }
      };

      updateProgress();
    },

    // Start game
    start: () => {
      if (gameInstance && gameInitialized) {
        try {
          gameInstance.start();
        } catch (error) {
          console.error('Error starting Slash game:', error);
        }
      }
    },

    // Destroy game
    destroy: () => {
      if (!gameInstance) return;

      try {
        // Stop the game
        gameInstance.stop();

        // Clean up game core
        if (gameInstance.core) {
          gameInstance.core.cleanUp();

          // Clear any timers
          if (gameInstance.core.gameTimer) {
            clearInterval(gameInstance.core.gameTimer);
          }
        }

        // Clear canvas
        if (canvasElement) {
          const ctx = canvasElement.getContext('2d');
          if (ctx) {
            ctx.clearRect(0, 0, canvasElement.width, canvasElement.height);
          }
        }

        // Release resources through the resource manager
        resourceManager.releaseResources('slash');
      } catch (error) {
        console.error('Error destroying Slash game:', error);
      }

      // Clear references
      gameInstance = null;
      gameInitialized = false;
    },

    // Reset game state
    resetGameState: () => {
      if (!gameInstance || !gameInstance.core) return;

      try {
        gameInstance.core.restart();
      } catch (error) {
        console.error('Error resetting Slash game state:', error);
      }
    },

    // Clear event listeners
    clearEventListeners: () => {
      if (!gameInstance || !gameInstance.core) return;

      try {
        // Remove event listeners
        if (gameInstance.core.input) {
          // Clear input handlers
          window.removeEventListener('keydown', gameInstance.core.input.keyDownHandler);
          window.removeEventListener('keyup', gameInstance.core.input.keyUpHandler);
        }

        if (canvasElement && gameInstance.core.joystick) {
          canvasElement.removeEventListener('touchstart', gameInstance.core.joystick.touchStartHandler);
          canvasElement.removeEventListener('touchend', gameInstance.core.joystick.touchEndHandler);
          canvasElement.removeEventListener('mousedown', gameInstance.core.joystick.mouseDownHandler);
          canvasElement.removeEventListener('mouseup', gameInstance.core.joystick.mouseUpHandler);
        }
      } catch (error) {
        console.error('Error clearing Slash game event listeners:', error);
      }
    }
  };



  return gameInstanceWrapper;
};