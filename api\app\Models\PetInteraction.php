<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PetInteraction extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id', 'pet_id', 'interaction_type', 'energy_cost',
        'happiness_gained', 'experience_gained', 'coins_rewarded',
        'materials_rewarded', 'collectible_rewarded', 'bonus_applied',
        'interaction_time'
    ];

    protected $casts = [
        'interaction_time' => 'datetime',
        'bonus_applied' => 'boolean',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }

    public function pet(): BelongsTo
    {
        return $this->belongsTo(Pet::class);
    }

    // Scopes
    public function scopeToday($query)
    {
        return $query->whereDate('interaction_time', today());
    }

    public function scopeByType($query, $type)
    {
        return $query->where('interaction_type', $type);
    }

    public function scopeWithBonus($query)
    {
        return $query->where('bonus_applied', true);
    }

    // Accessors
    public function getInteractionTypeDisplayAttribute(): string
    {
        return match($this->interaction_type) {
            'feed' => 'Fed',
            'play' => 'Played',
            'pet' => 'Petted',
            'train' => 'Trained',
            default => ucfirst($this->interaction_type)
        };
    }

    public function getTotalValueAttribute(): int
    {
        return $this->coins_rewarded + ($this->materials_rewarded * 10);
    }
}
