import { Button } from "./ui/button";
import Drawer from "./ui/drawer";
import { useMutation } from "@tanstack/react-query";
import { $http } from "@/lib/http";
import { toast } from "react-toastify";
import { BattlxIcon } from "@/components/icons/BattlxIcon";
import { UserType } from "@/types/UserType";
import { useUserStore } from "@/store/user-store";
import { useMemo } from "react";
import { GameType } from "@/types/game";
import { GAMES } from "@/games/registry";

const GAME_PRICE = 5000;

type GameDrawerProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: GameType;
};

export default function GameUnlockDrawer({ type, open, onOpenChange }: GameDrawerProps) {
  const { balance } = useUserStore();

  const insufficientBalance = useMemo(() => {
    return balance < GAME_PRICE;
  }, [balance]);

  const unlockMutation = useMutation({
    mutationFn: () =>
      $http.post<{ message: string; user: UserType }>(
        `/game/unlock`,
        { type }
      ),
    onSuccess: ({ data }) => {
      toast.success(data.message || "Game unlocked successfully");
      useUserStore.setState(data.user);
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Failed to unlock game");
    },
  });

  const gameConfig = GAMES[type];
  const gameTitle = type === 'tower' ? 'Tower Game' : 'Jazz Game';

  return (
    <Drawer
      open={open}
      onOpenChange={onOpenChange}
    >
      <img
        src={gameConfig.thumbnail}
        alt={gameTitle}
        className="object-contain h-32 mx-auto opacity-80 rounded-none [filter:sepia(50%)_saturate(50%)_brightness(80%)_contrast(120%)]"
      />
      <h2 className="mt-6 text-2xl font-medium text-center text-[#9B8B6C]">{gameTitle}</h2>
      <div className="flex flex-col mx-auto mt-4 w-fit">
        <p className="text-xs text-center text-[#B3B3B3]/80">Unlock Price</p>
      </div>

      <div className="flex items-center justify-center mx-auto mt-6 space-x-1 text-[#9B8B6C]">
        <BattlxIcon
          icon="coins"
          className="w-8 h-8 text-[#9B8B6C]"
        />
        <span className="font-bold">
          {GAME_PRICE.toLocaleString()}
        </span>
      </div>
      <Button
        className="w-full mt-6 bg-[#1A1617] border border-[#B3B3B3]/20 text-[#9B8B6C] hover:bg-[#4A0E0E]/30 transition-all duration-300 shadow-[0_4px_15px_rgba(74,14,14,0.3)] disabled:opacity-50 disabled:cursor-not-allowed rounded-none"
        disabled={unlockMutation.isPending || insufficientBalance}
        onClick={() => unlockMutation.mutate()}
      >
        {unlockMutation.isPending && (
          <BattlxIcon icon="loading" className="w-6 h-6 mr-2 animate-spin" />
        )}
        {insufficientBalance ? "Insufficient Balance" : "Unlock Game"}
      </Button>
    </Drawer>
  );
}