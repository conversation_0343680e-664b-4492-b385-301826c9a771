<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use App\Models\TelegramUser;

class WalletController extends Controller
{
    /**
     * Get the authenticated user's wallet address.
     *
     * @return JsonResponse
     */
   public function index(): JsonResponse
   {
       try {
           /** @var TelegramUser|null $user */
           $user = Auth::user();
           
           if (!$user) {
               return response()->json([
                   'success' => false,
                   'message' => 'Unauthenticated'
               ], 401);
           }

           return response()->json([
               'success' => true,
               'ton_wallet' => $user->ton_wallet
           ]);
       } catch (\Exception $e) {
           return response()->json([
               'success' => false,
               'message' => 'Failed to fetch wallet address'
           ], 500);
       }
   }

   /**
    * Store a TON wallet address for the authenticated user.
    *
    * @param Request $request
    * @return JsonResponse
    */
   public function store(Request $request): JsonResponse
   {
       $request->validate([
           'ton_wallet' => ['required', 'string', 'regex:/^UQ[a-zA-Z0-9_-]{46}$/'],
       ]);

       /** @var TelegramUser $user */
       $user = Auth::user();
       
       try {
           $user->ton_wallet = $request->ton_wallet;
           $user->save();

           return response()->json([
               'success' => true,
               'message' => 'Wallet address stored successfully',
               'ton_wallet' => $user->ton_wallet
           ]);
       } catch (\Exception $e) {
           return response()->json([
               'success' => false,
               'message' => 'Failed to store wallet address'
           ], 500);
       }
   }
}