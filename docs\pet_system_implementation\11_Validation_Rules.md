# Validation Rules Implementation

## Overview
This document covers the implementation of comprehensive validation rules for the Pet System API endpoints, including custom validators and form requests.

## Implementation Time: 1-2 days
## Complexity: Medium
## Dependencies: API controllers and routes

## Pet Management Validation

### PetPurchaseRequest
```php
<?php
// File: api/app/Http/Requests/PetPurchaseRequest.php

namespace App\Http\Requests;

use App\Models\PetTemplate;
use App\Rules\SufficientBalance;
use App\Rules\PetNotOwned;
use App\Rules\PetUnlockRequirementsMet;
use Illuminate\Foundation\Http\FormRequest;

class PetPurchaseRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    public function rules(): array
    {
        return [
            'pet_template_id' => [
                'required',
                'integer',
                'exists:pet_templates,id',
                new PetNotOwned($this->user()),
                new PetUnlockRequirementsMet($this->user())
            ],
            'purchase_method' => [
                'required',
                'string',
                'in:coins,gems'
            ],
            'nickname' => [
                'nullable',
                'string',
                'min:1',
                'max:50',
                'regex:/^[a-zA-Z0-9\s\-_]+$/' // Alphanumeric, spaces, hyphens, underscores only
            ]
        ];
    }

    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if (!$validator->errors()->has('pet_template_id') && !$validator->errors()->has('purchase_method')) {
                $petTemplate = PetTemplate::find($this->pet_template_id);
                $purchaseMethod = $this->purchase_method;
                
                if ($petTemplate) {
                    $cost = $petTemplate->getPurchaseCost($purchaseMethod);
                    
                    if ($cost <= 0) {
                        $validator->errors()->add('purchase_method', 'This pet cannot be purchased with the selected method.');
                    } else {
                        // Check if user has sufficient balance
                        $sufficientBalance = new SufficientBalance($purchaseMethod, $cost);
                        if (!$sufficientBalance->passes('', $this->user())) {
                            $validator->errors()->add('purchase_method', $sufficientBalance->message());
                        }
                    }
                }
            }
        });
    }

    public function messages(): array
    {
        return [
            'pet_template_id.required' => 'Please select a pet to purchase.',
            'pet_template_id.exists' => 'The selected pet is not available.',
            'purchase_method.required' => 'Please select a payment method.',
            'purchase_method.in' => 'Invalid payment method selected.',
            'nickname.regex' => 'Pet nickname can only contain letters, numbers, spaces, hyphens, and underscores.',
            'nickname.max' => 'Pet nickname cannot be longer than 50 characters.'
        ];
    }
}
```

### PetInteractionRequest
```php
<?php
// File: api/app/Http/Requests/PetInteractionRequest.php

namespace App\Http\Requests;

use App\Rules\SufficientEnergy;
use App\Rules\InteractionCooldownExpired;
use App\Rules\DailyInteractionLimitNotExceeded;
use Illuminate\Foundation\Http\FormRequest;

class PetInteractionRequest extends FormRequest
{
    public function authorize(): bool
    {
        // Check if user owns the pet
        $pet = $this->route('pet');
        return $pet && $pet->telegram_user_id === $this->user()->id;
    }

    public function rules(): array
    {
        $pet = $this->route('pet');
        
        return [
            'interaction_type' => [
                'required',
                'string',
                'in:feed,play,pet',
                new InteractionCooldownExpired($pet),
                new DailyInteractionLimitNotExceeded($pet)
            ]
        ];
    }

    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if (!$validator->errors()->has('interaction_type')) {
                $pet = $this->route('pet');
                $interactionType = $this->interaction_type;
                
                // Check energy requirements
                $energyRequirements = [
                    'feed' => 5,
                    'play' => 10,
                    'pet' => 2
                ];
                
                $requiredEnergy = $energyRequirements[$interactionType] ?? 0;
                $sufficientEnergy = new SufficientEnergy($requiredEnergy);
                
                if (!$sufficientEnergy->passes('', $this->user())) {
                    $validator->errors()->add('interaction_type', $sufficientEnergy->message());
                }
            }
        });
    }

    public function messages(): array
    {
        return [
            'interaction_type.required' => 'Please select an interaction type.',
            'interaction_type.in' => 'Invalid interaction type selected.'
        ];
    }
}
```

### PetNicknameRequest
```php
<?php
// File: api/app/Http/Requests/PetNicknameRequest.php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PetNicknameRequest extends FormRequest
{
    public function authorize(): bool
    {
        $pet = $this->route('pet');
        return $pet && $pet->telegram_user_id === $this->user()->id;
    }

    public function rules(): array
    {
        return [
            'nickname' => [
                'nullable',
                'string',
                'min:1',
                'max:50',
                'regex:/^[a-zA-Z0-9\s\-_]+$/',
                function ($attribute, $value, $fail) {
                    // Check for inappropriate content
                    $bannedWords = ['admin', 'bot', 'system', 'null', 'undefined'];
                    $lowercaseValue = strtolower($value);
                    
                    foreach ($bannedWords as $word) {
                        if (strpos($lowercaseValue, $word) !== false) {
                            $fail('This nickname is not allowed.');
                            break;
                        }
                    }
                }
            ]
        ];
    }

    public function messages(): array
    {
        return [
            'nickname.regex' => 'Pet nickname can only contain letters, numbers, spaces, hyphens, and underscores.',
            'nickname.max' => 'Pet nickname cannot be longer than 50 characters.',
            'nickname.min' => 'Pet nickname must be at least 1 character long.'
        ];
    }
}
```

## Mystery Box Validation

### MysteryBoxOpenRequest
```php
<?php
// File: api/app/Http/Requests/MysteryBoxOpenRequest.php

namespace App\Http\Requests;

use App\Models\MysteryBoxType;
use App\Rules\MysteryBoxUnlocked;
use App\Rules\SufficientBalance;
use Illuminate\Foundation\Http\FormRequest;

class MysteryBoxOpenRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'box_type' => [
                'required',
                'string',
                'exists:mystery_box_types,box_type',
                new MysteryBoxUnlocked($this->user())
            ],
            'purchase_method' => [
                'required',
                'string',
                'in:coins,gems,achievement_points'
            ],
            'quantity' => [
                'integer',
                'min:1',
                'max:10'
            ]
        ];
    }

    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            if (!$validator->errors()->has('box_type') && !$validator->errors()->has('purchase_method')) {
                $mysteryBoxType = MysteryBoxType::where('box_type', $this->box_type)->first();
                $purchaseMethod = $this->purchase_method;
                $quantity = $this->quantity ?? 1;
                
                if ($mysteryBoxType) {
                    $cost = $mysteryBoxType->getCost($purchaseMethod);
                    $totalCost = $cost * $quantity;
                    
                    if ($cost <= 0) {
                        $validator->errors()->add('purchase_method', 'This mystery box cannot be purchased with the selected method.');
                    } else {
                        $sufficientBalance = new SufficientBalance($purchaseMethod, $totalCost);
                        if (!$sufficientBalance->passes('', $this->user())) {
                            $validator->errors()->add('purchase_method', $sufficientBalance->message());
                        }
                    }
                }
            }
        });
    }

    public function messages(): array
    {
        return [
            'box_type.required' => 'Please select a mystery box type.',
            'box_type.exists' => 'The selected mystery box type is not available.',
            'purchase_method.required' => 'Please select a payment method.',
            'purchase_method.in' => 'Invalid payment method selected.',
            'quantity.min' => 'You must open at least 1 mystery box.',
            'quantity.max' => 'You can open at most 10 mystery boxes at once.'
        ];
    }
}
```

## Collection Validation

### CollectionSearchRequest
```php
<?php
// File: api/app/Http/Requests/CollectionSearchRequest.php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CollectionSearchRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'q' => [
                'required',
                'string',
                'min:2',
                'max:100',
                'regex:/^[a-zA-Z0-9\s\-_]+$/'
            ],
            'category' => [
                'nullable',
                'string',
                'in:shadow,undead,demon,spirit,beast'
            ],
            'rarity' => [
                'nullable',
                'string',
                'in:common,rare,epic,legendary,mythic'
            ]
        ];
    }

    public function messages(): array
    {
        return [
            'q.required' => 'Search query is required.',
            'q.min' => 'Search query must be at least 2 characters long.',
            'q.max' => 'Search query cannot be longer than 100 characters.',
            'q.regex' => 'Search query can only contain letters, numbers, spaces, hyphens, and underscores.',
            'category.in' => 'Invalid category selected.',
            'rarity.in' => 'Invalid rarity selected.'
        ];
    }
}
```

## Custom Validation Rules

### SufficientBalance Rule
```php
<?php
// File: api/app/Rules/SufficientBalance.php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class SufficientBalance implements ValidationRule
{
    protected string $currency;
    protected int $amount;

    public function __construct(string $currency, int $amount)
    {
        $this->currency = $currency;
        $this->amount = $amount;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $user = $value; // Expecting user object to be passed
        
        if (!$user) {
            $fail('User not found.');
            return;
        }

        $hasEnough = match($this->currency) {
            'coins' => $user->balance >= $this->amount,
            'gems' => ($user->gems ?? 0) >= $this->amount,
            'achievement_points' => ($user->achievementPoints->total_earned ?? 0) >= $this->amount,
            default => false
        };

        if (!$hasEnough) {
            $currencyName = match($this->currency) {
                'coins' => 'coins',
                'gems' => 'gems',
                'achievement_points' => 'achievement points',
                default => $this->currency
            };
            
            $fail("Insufficient {$currencyName}. Required: {$this->amount}");
        }
    }

    public function message(): string
    {
        $currencyName = match($this->currency) {
            'coins' => 'coins',
            'gems' => 'gems',
            'achievement_points' => 'achievement points',
            default => $this->currency
        };
        
        return "Insufficient {$currencyName}. Required: {$this->amount}";
    }

    public function passes($attribute, $value): bool
    {
        $user = $value;
        
        if (!$user) {
            return false;
        }

        return match($this->currency) {
            'coins' => $user->balance >= $this->amount,
            'gems' => ($user->gems ?? 0) >= $this->amount,
            'achievement_points' => ($user->achievementPoints->total_earned ?? 0) >= $this->amount,
            default => false
        };
    }
}
```

### PetNotOwned Rule
```php
<?php
// File: api/app/Rules/PetNotOwned.php

namespace App\Rules;

use Closure;
use App\Models\TelegramUser;
use Illuminate\Contracts\Validation\ValidationRule;

class PetNotOwned implements ValidationRule
{
    protected TelegramUser $user;

    public function __construct(TelegramUser $user)
    {
        $this->user = $user;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $petTemplateId = $value;
        
        $alreadyOwned = $this->user->pets()
                                  ->where('pet_template_id', $petTemplateId)
                                  ->exists();

        if ($alreadyOwned) {
            $fail('You already own this pet.');
        }
    }
}
```

### InteractionCooldownExpired Rule
```php
<?php
// File: api/app/Rules/InteractionCooldownExpired.php

namespace App\Rules;

use Closure;
use App\Models\Pet;
use Illuminate\Contracts\Validation\ValidationRule;

class InteractionCooldownExpired implements ValidationRule
{
    protected Pet $pet;

    public function __construct(Pet $pet)
    {
        $this->pet = $pet;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $interactionType = $value;
        
        if (!$this->pet->canInteract($interactionType)) {
            $cooldowns = [
                'feed' => '1 hour',
                'play' => '2 hours',
                'pet' => '30 minutes'
            ];
            
            $cooldownTime = $cooldowns[$interactionType] ?? 'some time';
            $fail("You must wait {$cooldownTime} before performing this interaction again.");
        }
    }
}
```

### DailyInteractionLimitNotExceeded Rule
```php
<?php
// File: api/app/Rules/DailyInteractionLimitNotExceeded.php

namespace App\Rules;

use Closure;
use App\Models\Pet;
use Illuminate\Contracts\Validation\ValidationRule;

class DailyInteractionLimitNotExceeded implements ValidationRule
{
    protected Pet $pet;

    public function __construct(Pet $pet)
    {
        $this->pet = $pet;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $interactionType = $value;
        
        $limits = [
            'feed' => 5,
            'play' => 3,
            'pet' => 10
        ];
        
        $limit = $limits[$interactionType] ?? 0;
        
        $todayInteractions = $this->pet->interactions()
                                     ->where('interaction_type', $interactionType)
                                     ->whereDate('interaction_time', today())
                                     ->count();

        if ($todayInteractions >= $limit) {
            $fail("Daily limit of {$limit} {$interactionType} interactions reached.");
        }
    }
}
```

## Validation Helper Service

### ValidationHelperService
```php
<?php
// File: api/app/Services/ValidationHelperService.php

namespace App\Services;

use App\Models\TelegramUser;
use App\Models\Pet;
use App\Models\PetTemplate;
use App\Models\MysteryBoxType;

class ValidationHelperService
{
    public static function validatePetOwnership(TelegramUser $user, Pet $pet): bool
    {
        return $pet->telegram_user_id === $user->id;
    }

    public static function validatePetPurchaseEligibility(TelegramUser $user, PetTemplate $petTemplate): array
    {
        $errors = [];

        // Check if already owned
        if ($user->pets()->where('pet_template_id', $petTemplate->id)->exists()) {
            $errors[] = 'You already own this pet.';
        }

        // Check if pet is active
        if (!$petTemplate->is_active) {
            $errors[] = 'This pet is not available for purchase.';
        }

        // Check unlock requirements
        if (!$petTemplate->canBePurchasedBy($user)) {
            $errors[] = 'You do not meet the requirements to purchase this pet.';
        }

        return $errors;
    }

    public static function validateMysteryBoxAccess(TelegramUser $user, MysteryBoxType $mysteryBoxType): array
    {
        $errors = [];

        // Check if box type is active
        if (!$mysteryBoxType->is_active) {
            $errors[] = 'This mystery box type is not available.';
        }

        // Check if user has unlocked this box type
        if (!$mysteryBoxType->isUnlockedBy($user)) {
            $errors[] = 'You have not unlocked this mystery box type.';
        }

        // Check if box is purchasable
        if (!$mysteryBoxType->is_purchasable) {
            $errors[] = 'This mystery box type cannot be purchased.';
        }

        return $errors;
    }

    public static function validateEnergyRequirement(TelegramUser $user, int $requiredEnergy): bool
    {
        return $user->available_energy >= $requiredEnergy;
    }

    public static function validateCurrencyBalance(TelegramUser $user, string $currency, int $amount): bool
    {
        return match($currency) {
            'coins' => $user->balance >= $amount,
            'gems' => ($user->gems ?? 0) >= $amount,
            'achievement_points' => ($user->achievementPoints->total_earned ?? 0) >= $amount,
            default => false
        };
    }
}
```

## Acceptance Criteria
- [ ] All form requests created with proper validation
- [ ] Custom validation rules implemented
- [ ] Error messages are user-friendly
- [ ] Validation covers all edge cases
- [ ] Performance optimized for validation checks
- [ ] Security considerations addressed
- [ ] Validation helper service functional

## Next Steps
1. Create event system for pet actions
2. Implement admin interface controllers
3. Create comprehensive error handling
4. Set up API documentation with validation examples

## Troubleshooting
- Test validation rules thoroughly with edge cases
- Ensure custom rules are properly registered
- Check validation performance with large datasets
- Verify error messages are localized if needed
- Monitor validation failures for potential improvements
