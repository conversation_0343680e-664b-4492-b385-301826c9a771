# Slash Game Integration Guide: 06 - API Integration Points

This document details how the Slash game integration interacts with the backend API, focusing on the specific endpoints used and the data flow between the frontend (`GameWrapper.tsx`) and the backend (`GameController.php`).

## 1. Required API Endpoints

The Slash game integration will utilize the following existing API endpoints provided by the backend:

*   **`POST /api/game/unlock`**: This endpoint is used when a user initiates the process to unlock the Slash game. The frontend sends the `game_id` ('slash') to this endpoint. The backend verifies the user's balance and updates the `slash_game_unlocked` status in the `telegram_users` table if successful.
*   **`GET /api/game/check-play-availability?game_id=slash`**: This endpoint is called by `GameWrapper.tsx` when the Slash game screen is loaded. The frontend sends the `game_id` ('slash') as a query parameter. The backend's `checkPlayAvailability` method in [`GameController.php`](api/app/Http/Controllers/GameController.php) calls `canPlaySlashGame()` on the user model, which checks the `slash_game_unlocked` status. The response indicates if the user is allowed to play.
*   **`POST /api/game/update-score`**: This is the crucial endpoint for submitting the game result. It is called by `GameWrapper.tsx` when the `onGameOver` callback is triggered by the Slash game module. The frontend sends the final collected **coin amount** from the game session as the `score` parameter, along with the `game_id` ('slash'). The backend's `updateScore` method in [`GameController.php`](api/app/Http/Controllers/GameController.php) adds this submitted score to the user's total `game_score` in the `telegram_users` table.

The `POST /api/game/use-play` endpoint, used for games with limited play attempts, will **not** be used for the Slash game, as it follows the unlimited play upon unlock model.

## 2. Data Flow Patterns

The interaction with the API follows these data flow patterns:

```mermaid
sequenceDiagram
    participant User
    participant Frontend as React Frontend (GameWrapper)
    participant GameModule as Slash Game Module (main.ts)
    participant GameCore as Slash Game Core (GameCore.js)
    participant Backend as Laravel Backend (GameController)
    participant Database as TelegramUser Table

    User->>Frontend: Navigates to Slash Game
    Frontend->>Backend: GET /api/game/check-play-availability?game_id=slash
    Backend->>Database: Check slash_game_unlocked status
    Database-->>Backend: Status
    Backend-->>Frontend: Play Availability Response
    alt If Allowed to Play
        Frontend->>GameModule: Initialize Game (canvasId, onGameOver callback)
        GameModule->>GameCore: Initialize Game Core (canvas)
        GameModule->>GameCore: Load Assets
        GameCore-->>GameModule: Asset Loading Progress
        GameModule-->>Frontend: Report Loading Progress
        GameModule-->>Frontend: Loading Complete
        Frontend->>GameModule: Start Game
        GameModule->>GameCore: Start Game Loop
        Note over GameCore: Gameplay Simulation (Coin Collection)
        GameCore->>GameCore: Track playerOptions.coins
        Note over GameCore: Game Over Condition Met (e.g., Player Health = 0)
        GameCore->>GameCore: Get final playerOptions.coins
        GameCore->>GameModule: Call onGameOver(final_coin_amount)
        GameModule-->>Frontend: onGameOver(final_coin_amount)
        Frontend->>Backend: POST /api/game/update-score (score=final_coin_amount, game_id=slash)
        Backend->>Database: Add score to game_score
        Database-->>Backend: Update Result
        Backend-->>Frontend: Score Update Response
        Frontend->>User: Display Game Over Screen (with final coin amount)
    else If Not Allowed to Play (Game Locked)
        Frontend->>User: Display Game Locked Message
        User->>Frontend: Initiate Unlock Process
        Frontend->>Backend: POST /api/game/unlock (game_id=slash)
        Backend->>Database: Check Balance, Deduct Cost, Update slash_game_unlocked
        Database-->>Backend: Transaction Result
        Backend-->>Frontend: Unlock Response
        alt If Unlock Successful
            Frontend->>User: Display Unlock Success, Prompt to Play
        else If Unlock Failed
            Frontend->>User: Display Unlock Failed Message
        end
    end
```

## 3. Error Handling

Error handling for API interactions should be implemented in `GameWrapper.tsx`:

*   Handle potential errors from the `check-play-availability` endpoint (e.g., game locked, unauthorized, backend errors) by displaying appropriate messages to the user or redirecting them.
*   Handle potential errors from the `update-score` endpoint (e.g., network issues, backend errors) by logging the error and potentially informing the user that the score could not be saved.
*   Implement retries or graceful degradation where appropriate for API calls.