<?php

namespace Database\Seeders;

use App\Models\TaskType;
use Illuminate\Database\Seeder;

class TaskTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if the task_types table is empty
        if (TaskType::count() == 0) {
            $types = [
                ['name' => 'video'],
                ['name' => 'other']
            ];

            foreach ($types as $type) {
                TaskType::updateOrCreate(
                    ['name' => $type['name']],
                    $type
                );
            }
        }
    }
}