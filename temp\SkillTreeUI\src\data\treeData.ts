import { Section } from '../types';

export const sections: Section[] = [
  {
    id: 'nft-collection',
    title: 'NFT Collection',
    description: 'Collect and upgrade NFTs to enhance your power in the game',
    nodes: [
      {
        id: 'nft-1',
        title: 'Common NFT',
        description: 'Unlock your first NFT',
        type: 'nft',
        level: 1,
        position: { x: 50, y: 20 },
        unlocked: true,
        completed: false,
        reward: 'Basic Character NFT',
        connections: ['nft-2', 'nft-3']
      },
      {
        id: 'nft-2',
        title: 'Uncommon NFT',
        description: 'Upgrade to uncommon tier',
        type: 'nft',
        level: 2,
        position: { x: 30, y: 40 },
        unlocked: false,
        completed: false,
        reward: 'Uncommon Weapon NFT',
        connections: ['nft-4']
      },
      {
        id: 'nft-3',
        title: 'Rare NFT',
        description: 'Collect a rare item',
        type: 'nft',
        level: 2,
        position: { x: 70, y: 40 },
        unlocked: false,
        completed: false,
        reward: 'Rare Armor NFT',
        connections: ['nft-5']
      },
      {
        id: 'nft-4',
        title: 'Epic NFT Set',
        description: 'Complete a collection of epic NFTs',
        type: 'nft',
        level: 3,
        position: { x: 30, y: 60 },
        unlocked: false,
        completed: false,
        reward: 'Epic Equipment Set',
        connections: ['nft-6']
      },
      {
        id: 'nft-5',
        title: 'Legendary NFT',
        description: 'Obtain a legendary item',
        type: 'nft',
        level: 3,
        position: { x: 70, y: 60 },
        unlocked: false,
        completed: false,
        reward: 'Legendary Weapon NFT',
        connections: ['nft-6']
      },
      {
        id: 'nft-6',
        title: 'Mythic NFT',
        description: 'The rarest collection in the game',
        type: 'nft',
        level: 4,
        position: { x: 50, y: 80 },
        unlocked: false,
        completed: false,
        reward: 'Mythic Character Transformation',
        connections: []
      }
    ]
  },
  {
    id: 'community-engagement',
    title: 'Community Engagement',
    description: 'Participate in community events and competitions',
    nodes: [
      {
        id: 'comm-1',
        title: 'Tournament Entry',
        description: 'Join your first tournament',
        type: 'reward',
        level: 1,
        position: { x: 50, y: 20 },
        unlocked: true,
        completed: false,
        reward: 'Tournament Badge',
        connections: ['comm-2', 'comm-3']
      },
      {
        id: 'comm-2',
        title: 'Guild Formation',
        description: 'Create or join a guild',
        type: 'reward',
        level: 2,
        position: { x: 30, y: 40 },
        unlocked: false,
        completed: false,
        reward: 'Guild Banner NFT',
        connections: ['comm-4']
      },
      {
        id: 'comm-3',
        title: 'Event Champion',
        description: 'Win a community event',
        type: 'reward',
        level: 2,
        position: { x: 70, y: 40 },
        unlocked: false,
        completed: false,
        reward: 'Champions Crown NFT',
        connections: ['comm-5']
      },
      {
        id: 'comm-4',
        title: 'Territory Control',
        description: 'Help your guild control territory',
        type: 'reward',
        level: 3,
        position: { x: 30, y: 60 },
        unlocked: false,
        completed: false,
        reward: 'Territory Control Bonus',
        connections: ['comm-6']
      },
      {
        id: 'comm-5',
        title: 'Tournament Winner',
        description: 'Win a major tournament',
        type: 'reward',
        level: 3,
        position: { x: 70, y: 60 },
        unlocked: false,
        completed: false,
        reward: 'Tournament Trophy NFT',
        connections: ['comm-6']
      },
      {
        id: 'comm-6',
        title: 'Legendary Status',
        description: 'Achieve legendary status in the community',
        type: 'reward',
        level: 4,
        position: { x: 50, y: 80 },
        unlocked: false,
        completed: false,
        reward: 'Legendary Player Title & Badge',
        connections: []
      }
    ]
  },
  {
    id: 'token-economy',
    title: 'Token Economy',
    description: 'Build your token portfolio and unlock financial benefits',
    nodes: [
      {
        id: 'token-1',
        title: 'Token Holder',
        description: 'Acquire your first tokens',
        type: 'token',
        level: 1,
        position: { x: 50, y: 20 },
        unlocked: true,
        completed: false,
        reward: '100 Game Tokens',
        connections: ['token-2', 'token-3']
      },
      {
        id: 'token-2',
        title: 'Token Staker',
        description: 'Stake tokens for rewards',
        type: 'token',
        level: 2,
        position: { x: 30, y: 40 },
        unlocked: false,
        completed: false,
        reward: 'Staking Yield Boost',
        connections: ['token-4']
      },
      {
        id: 'token-3',
        title: 'Market Trader',
        description: 'Trade tokens on the marketplace',
        type: 'token',
        level: 2,
        position: { x: 70, y: 40 },
        unlocked: false,
        completed: false,
        reward: 'Reduced Trading Fees',
        connections: ['token-5']
      },
      {
        id: 'token-4',
        title: 'Liquidity Provider',
        description: 'Provide liquidity to token pools',
        type: 'token',
        level: 3,
        position: { x: 30, y: 60 },
        unlocked: false,
        completed: false,
        reward: 'LP Reward Multiplier',
        connections: ['token-6']
      },
      {
        id: 'token-5',
        title: 'Token Whale',
        description: 'Hold a significant amount of tokens',
        type: 'token',
        level: 3,
        position: { x: 70, y: 60 },
        unlocked: false,
        completed: false,
        reward: 'Whale Status Benefits',
        connections: ['token-6']
      },
      {
        id: 'token-6',
        title: 'Economic Elite',
        description: 'Become an economic powerhouse',
        type: 'token',
        level: 4,
        position: { x: 50, y: 80 },
        unlocked: false,
        completed: false,
        reward: 'Governance Voting Rights',
        connections: []
      }
    ]
  }
];