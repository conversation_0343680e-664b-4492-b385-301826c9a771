# Prize Tree System - Backend Implementation (Part 3)

## Achievement Points Integration

This document outlines how achievement points are integrated throughout the application to reward users for various actions.

### 1. Achievement Point Sources

Achievement points are awarded for the following actions:

#### Tapping Milestones
- 100 taps: 1 point
- 500 taps: 1 point
- 1,000 taps: 2 points
- 5,000 taps: 2 points
- 10,000 taps: 3 points
- 50,000 taps: 3 points
- 100,000 taps: 5 points

#### Daily Tasks
- Each daily task completion: 1 point

#### Regular Tasks
- Each task completion: 1 point

#### Referral Tasks
- Each referral task completion: 2 points (higher value due to difficulty)

#### Mission Completion
- Each mission completion: 2 points

#### Game Milestones
- Game unlocking: 2 points
- Score milestones (varies by game):
  - Total score milestones: 1-3 points
  - High score milestones: 1-3 points

### 2. Implementation Details

#### ClickerController
The `ClickerController` has been updated to award achievement points for:
- Tap milestones through the `checkTapMilestones` method
- Daily task completions in the `claimDailyTaskReward` method

```php
private function checkTapMilestones($user, $tapCount)
{
    // Get or create tap stats
    $tapStats = \App\Models\TapStat::firstOrCreate(
        ['telegram_user_id' => $user->id],
        ['total_taps' => 0]
    );
    
    // Update total taps
    $oldTotal = $tapStats->total_taps;
    $tapStats->total_taps += $tapCount;
    $tapStats->save();
    
    // Define milestones
    $milestones = [100, 500, 1000, 5000, 10000, 50000, 100000];
    
    // Check if any milestones were crossed
    foreach ($milestones as $milestone) {
        if ($oldTotal < $milestone && $tapStats->total_taps >= $milestone) {
            // Award achievement points
            $points = 1;
            if ($milestone >= 1000) $points = 2;
            if ($milestone >= 10000) $points = 3;
            if ($milestone >= 100000) $points = 5;
            
            $this->achievementPointService->awardPoints(
                $user->id,
                $points,
                'tap_milestone',
                $milestone,
                "Reached {$milestone} total taps"
            );
        }
    }
}
```

#### UserTaskController
The `UserTaskController` has been updated to award achievement points when a user claims a task reward:

```php
public function claim(Request $request, Task $task)
{
    // ... existing code ...
    
    DB::transaction(function () use ($task, &$claimed, $user, $userTask) {
        $userTask->pivot->is_rewarded = true;
        $userTask->pivot->save();

        $user->increment('balance', $task->reward);
        
        // Award achievement points for completing a task
        $this->achievementPointService->awardPoints(
            $user->id,
            1, // Award 1 point for each task
            'task_complete',
            $task->id,
            "Completed task: {$task->title}"
        );

        $claimed = true;
    });
    
    // ... existing code ...
}
```

#### ReferralTaskController
The `ReferralTaskController` has been updated to award achievement points when a user completes a referral task:

```php
public function complete(ReferralTask $referralTask)
{
    // ... existing code ...
    
    // Award achievement points for completing a referral task
    $this->achievementPointService->awardPoints(
        $user->id,
        2, // Award 2 points for each referral task (higher value due to difficulty)
        'referral_task_complete',
        $referralTask->id,
        "Completed referral task: {$referralTask->number_of_referrals} referrals"
    );
    
    // ... existing code ...
}
```

#### UserMissionController
The `UserMissionController` has been updated to award achievement points when a user completes a mission:

```php
// For boss missions (type 2), mark as completed when purchased
if ($missionLevel->mission->mission_type_id === 2) {
    MissionCompletion::create([
        'telegram_user_id' => $user->id,
        'mission_id' => $missionLevel->mission_id,
        'completed_at' => now(),
    ]);
    
    $missionCompleted = true;
    
    // Award achievement points for completing a mission
    $this->achievementPointService->awardPoints(
        $user->id,
        2, // Award 2 points for completing a mission
        'mission_complete',
        $missionLevel->mission_id,
        "Completed mission: {$missionLevel->mission->name}"
    );
    
    // ... existing code ...
}
```

#### GameController
The `GameController` has been updated to award achievement points for game-related achievements:

```php
// Check for score milestones and award achievement points
private function checkScoreMilestones($user, $score, $gameId)
{
    // ... existing code ...
    
    // Check if any total score milestones were crossed
    foreach ($scoreMilestones as $milestone) {
        if ($oldTotalScore < $milestone && $gameStats->total_score >= $milestone) {
            // Award achievement points
            $points = 1;
            if ($milestone >= 10000) $points = 2;
            if ($milestone >= 100000) $points = 3;
            
            $this->achievementPointService->awardPoints(
                $user->id,
                $points,
                'game_score_milestone',
                $milestone,
                "Reached {$milestone} total score in {$gameId} game"
            );
        }
    }
    
    // ... existing code for high score milestones ...
}

// Unlock a game
public function unlockGame(Request $request)
{
    // ... existing code ...
    
    // Award achievement points for unlocking a game
    $this->achievementPointService->awardPoints(
        $user->id,
        2, // Award 2 points for unlocking a game
        'game_unlock',
        $gameId,
        "Unlocked {$gameId} game"
    );
    
    // ... existing code ...
}
```

### 3. Frontend Integration

The frontend has been updated to display achievement points in the user interface and to show when achievement points are earned. The user store has been updated to include achievement points:

```javascript
// user-store.js
export const useUserStore = create((set, get) => ({
  // ... existing state ...
  
  // Achievement points
  achievement_points: 0,
  
  // ... existing methods ...
  
  /**
   * Update achievement points
   */
  updateAchievementPoints: (points) => {
    set({
      achievement_points: points
    });
  },
}));
```

The sync endpoint in the `ClickerController` has been updated to include achievement points in the response:

```php
// Get user's achievement points
$achievementPoints = \App\Models\UserAchievementPoint::firstOrCreate(
    ['telegram_user_id' => $user->id],
    ['total_earned' => 0, 'total_spent' => 0]
);

return response()->json([
    'user' => array_merge($user->toArray(), [
        'level' => $user->level,
        'achievement_points' => $achievementPoints->available_points
    ]),
    // ... existing response data ...
]);
```

### 4. Prize Tree Integration

The Prize Tree system uses achievement points as the currency for unlocking prizes. When a user unlocks a prize, achievement points are deducted from their total:

```php
// Deduct achievement points
$deductResult = $this->achievementPointService->deductPoints(
    $userId,
    $prize->cost,
    'prize_unlock',
    $prizeId,
    "Unlocked prize: {$prize->name}"
);

if (!$deductResult['success']) {
    throw new \Exception($deductResult['message']);
}
```

### 5. Achievement Point Transactions

All achievement point transactions are recorded in the `achievement_point_transactions` table, which allows for tracking and auditing of point sources and usage:

```php
// Record transaction
AchievementPointTransaction::create([
    'telegram_user_id' => $userId,
    'amount' => $amount,
    'type' => 'earn',
    'source' => $source,
    'source_id' => $sourceId,
    'description' => $description
]);
```

The `AchievementPointController` provides an endpoint for viewing transaction history:

```php
public function transactions(Request $request)
{
    $user = $request->user();
    
    $transactions = AchievementPointTransaction::where('telegram_user_id', $user->id)
        ->orderBy('created_at', 'desc')
        ->paginate(20);
        
    return response()->json($transactions);
}
```

### 6. Admin Controls

Administrators can award achievement points to users through the admin-protected endpoint:

```php
public function award(Request $request)
{
    $request->validate([
        'user_id' => 'required|exists:telegram_users,id',
        'amount' => 'required|integer|min:1',
        'source' => 'required|string',
        'source_id' => 'nullable|integer',
        'description' => 'nullable|string'
    ]);
    
    $userId = $request->user_id;
    $amount = $request->amount;
    
    // Use the service to award points
    $result = $this->achievementPointService->awardPoints(
        $userId,
        $amount,
        $request->source,
        $request->source_id,
        $request->description
    );
    
    // ... existing code ...
}
```

This endpoint is protected by the `admin` middleware to ensure only authorized users can award points.
