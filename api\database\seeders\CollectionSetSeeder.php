<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CollectionSetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $collectionSets = [
            [
                'set_id' => 'shadow_collection',
                'name' => 'Shadow Realm',
                'category' => 'shadow',
                'description' => 'Collectibles from the mysterious shadow realm, harboring dark secrets and ancient power.',
                'icon_url' => '/images/collections/shadow_icon.png',
                'completion_rewards' => json_encode([
                    ['type' => 'coins', 'amount' => 10000],
                    ['type' => 'gems', 'amount' => 50],
                    ['type' => 'achievement_points', 'amount' => 500]
                ]),
                'bonus_mystery_box_type' => 'shadow_master_box',
                'total_collectibles' => 4,
                'required_for_completion' => 4,
                'sort_order' => 1
            ],
            [
                'set_id' => 'undead_collection',
                'name' => 'Undead Legion',
                'category' => 'undead',
                'description' => 'Artifacts and relics from the undead realm, touched by necromantic magic.',
                'icon_url' => '/images/collections/undead_icon.png',
                'completion_rewards' => json_encode([
                    ['type' => 'coins', 'amount' => 10000],
                    ['type' => 'gems', 'amount' => 50],
                    ['type' => 'achievement_points', 'amount' => 500]
                ]),
                'bonus_mystery_box_type' => 'undead_master_box',
                'total_collectibles' => 3,
                'required_for_completion' => 3,
                'sort_order' => 2
            ],
            [
                'set_id' => 'demon_collection',
                'name' => 'Infernal Depths',
                'category' => 'demon',
                'description' => 'Treasures from the burning depths of hell, forged in demonic flames.',
                'icon_url' => '/images/collections/demon_icon.png',
                'completion_rewards' => json_encode([
                    ['type' => 'coins', 'amount' => 15000],
                    ['type' => 'gems', 'amount' => 75],
                    ['type' => 'achievement_points', 'amount' => 750]
                ]),
                'bonus_mystery_box_type' => 'demon_master_box',
                'total_collectibles' => 5,
                'required_for_completion' => 5,
                'sort_order' => 3
            ],
            [
                'set_id' => 'spirit_collection',
                'name' => 'Ethereal Plane',
                'category' => 'spirit',
                'description' => 'Mystical items from the spirit realm, imbued with ethereal energy.',
                'icon_url' => '/images/collections/spirit_icon.png',
                'completion_rewards' => json_encode([
                    ['type' => 'coins', 'amount' => 12000],
                    ['type' => 'gems', 'amount' => 60],
                    ['type' => 'achievement_points', 'amount' => 600]
                ]),
                'bonus_mystery_box_type' => 'spirit_master_box',
                'total_collectibles' => 4,
                'required_for_completion' => 4,
                'sort_order' => 4
            ],
            [
                'set_id' => 'beast_collection',
                'name' => 'Primal Wilderness',
                'category' => 'beast',
                'description' => 'Trophies from the wild beasts of the gothic realm, symbols of primal power.',
                'icon_url' => '/images/collections/beast_icon.png',
                'completion_rewards' => json_encode([
                    ['type' => 'coins', 'amount' => 8000],
                    ['type' => 'gems', 'amount' => 40],
                    ['type' => 'achievement_points', 'amount' => 400]
                ]),
                'bonus_mystery_box_type' => 'beast_master_box',
                'total_collectibles' => 3,
                'required_for_completion' => 3,
                'sort_order' => 5
            ]
        ];

        foreach ($collectionSets as $set) {
            DB::table('collection_sets')->updateOrInsert(
                ['set_id' => $set['set_id']],
                array_merge($set, [
                    'created_at' => now(),
                    'updated_at' => now()
                ])
            );
        }
    }
}
