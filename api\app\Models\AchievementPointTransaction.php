<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AchievementPointTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'telegram_user_id',
        'amount',
        'type',
        'source',
        'source_id',
        'description'
    ];

    /**
     * Get the user that owns this transaction.
     */
    public function user()
    {
        return $this->belongsTo(TelegramUser::class, 'telegram_user_id');
    }
}
