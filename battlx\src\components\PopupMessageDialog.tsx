import { PopupMessageType } from "@/types/PopupMessageType";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "./ui/dialog";
import { Button } from "./ui/button";
import { useEffect, useState } from "react";
import { useLocalStorage } from "@uidotdev/usehooks";

type Props = {
  message?: PopupMessageType;
};
export default function PopupMessageDialog({ message }: Props) {
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [popupCount, setPopupCount] = useLocalStorage<Record<number, number>>(
    `popup-count`,
    {}
  );
  useEffect(() => {
    if (
      message?.id &&
      (popupCount[message.id] === undefined || popupCount[message.id] < 2)
    ) {
      setIsOpen(true);
      setPopupCount({
        ...popupCount,
        [message.id]: (popupCount[message.id] || 0) + 1,
      });
    }
  }, [message]);

  if (!message?.id) return null;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="popup-body">
        <div className="flex flex-col items-center justify-center text-center">
          <img
            src={message.image}
            alt={message.title}
            className="mx-auto h-20"
          />
          <DialogTitle className="mt-6 text-2xl font-bold text-[#9B8B6C] [text-shadow:2px_2px_4px_rgba(0,0,0,0.6)]">
            {message.title}
          </DialogTitle>
          <DialogDescription className="mt-4 font-medium text-[#9B8B6C]">
            {message.text}
          </DialogDescription>
          <Button className="w-full mt-6 bg-[#4A0E0E] hover:bg-[#732626] text-[#9B8B6C] border-2 border-[#9B8B6C]/30" asChild>
            <a href={message.button_link} target="_blank">
              {message.button_text}
            </a>
          </Button>
          <p
            className="mt-4 text-[#9B8B6C]/70 hover:text-[#9B8B6C] transition-colors cursor-pointer"
            onClick={() => setIsOpen(false)}
          >
            I don't want this reward
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
}
