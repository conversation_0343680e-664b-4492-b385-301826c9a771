<?php

namespace App\Services;

use App\Models\UserAchievementPoint;
use App\Models\AchievementPointTransaction;
use Illuminate\Support\Facades\DB;

class AchievementPointService
{
    /**
     * Award achievement points to a user.
     *
     * @param int $userId
     * @param int $amount
     * @param string $source
     * @param int|null $sourceId
     * @param string|null $description
     * @return array
     */
    public function awardPoints($userId, $amount, $source, $sourceId = null, $description = null)
    {
        DB::beginTransaction();
        
        try {
            // Update or create user achievement points
            $achievementPoints = UserAchievementPoint::firstOrCreate(
                ['telegram_user_id' => $userId],
                ['total_earned' => 0, 'total_spent' => 0]
            );
            
            $achievementPoints->total_earned += $amount;
            $achievementPoints->save();
            
            // Record transaction
            AchievementPointTransaction::create([
                'telegram_user_id' => $userId,
                'amount' => $amount,
                'type' => 'earn',
                'source' => $source,
                'source_id' => $sourceId,
                'description' => $description
            ]);
            
            DB::commit();
            
            return [
                'success' => true,
                'message' => 'Achievement points awarded successfully',
                'total_points' => $achievementPoints->available_points
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            return [
                'success' => false,
                'message' => 'Failed to award achievement points: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Deduct achievement points from a user.
     *
     * @param int $userId
     * @param int $amount
     * @param string $source
     * @param int|null $sourceId
     * @param string|null $description
     * @return array
     */
    public function deductPoints($userId, $amount, $source, $sourceId = null, $description = null)
    {
        DB::beginTransaction();
        
        try {
            // Get user achievement points
            $achievementPoints = UserAchievementPoint::where('telegram_user_id', $userId)->first();
            
            if (!$achievementPoints || $achievementPoints->available_points < $amount) {
                return [
                    'success' => false,
                    'message' => 'Not enough achievement points',
                    'required' => $amount,
                    'available' => $achievementPoints ? $achievementPoints->available_points : 0
                ];
            }
            
            // Update achievement points
            $achievementPoints->total_spent += $amount;
            $achievementPoints->save();
            
            // Record transaction
            AchievementPointTransaction::create([
                'telegram_user_id' => $userId,
                'amount' => -$amount,
                'type' => 'spend',
                'source' => $source,
                'source_id' => $sourceId,
                'description' => $description
            ]);
            
            DB::commit();
            
            return [
                'success' => true,
                'message' => 'Achievement points deducted successfully',
                'remaining_points' => $achievementPoints->available_points
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            return [
                'success' => false,
                'message' => 'Failed to deduct achievement points: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Refund achievement points to a user.
     *
     * @param int $userId
     * @param int $amount
     * @param string $source
     * @param int|null $sourceId
     * @param string|null $description
     * @return array
     */
    public function refundPoints($userId, $amount, $source, $sourceId = null, $description = null)
    {
        DB::beginTransaction();
        
        try {
            // Update or create user achievement points
            $achievementPoints = UserAchievementPoint::firstOrCreate(
                ['telegram_user_id' => $userId],
                ['total_earned' => 0, 'total_spent' => 0]
            );
            
            $achievementPoints->total_spent = max(0, $achievementPoints->total_spent - $amount);
            $achievementPoints->save();
            
            // Record transaction
            AchievementPointTransaction::create([
                'telegram_user_id' => $userId,
                'amount' => $amount,
                'type' => 'refund',
                'source' => $source,
                'source_id' => $sourceId,
                'description' => $description
            ]);
            
            DB::commit();
            
            return [
                'success' => true,
                'message' => 'Achievement points refunded successfully',
                'total_points' => $achievementPoints->available_points
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            
            return [
                'success' => false,
                'message' => 'Failed to refund achievement points: ' . $e->getMessage()
            ];
        }
    }
}
