<?php

use App\Models\Mission;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mission_levels', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Mission::class)->constrained()->onDelete('cascade');
            $table->integer('level');
            $table->integer('cost');
            $table->integer('production_per_hour');
            $table->timestamps();
        });

        // Add CHECK constraints using raw SQL
        DB::statement('ALTER TABLE mission_levels ADD CONSTRAINT chk_level_non_negative CHECK (level >= 0)');
        DB::statement('ALTER TABLE mission_levels ADD CONSTRAINT chk_cost_non_negative CHECK (cost >= 0)');
        DB::statement('ALTER TABLE mission_levels ADD CONSTRAINT chk_production_per_hour_non_negative CHECK (production_per_hour >= 0)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the CHECK constraints
        DB::statement('ALTER TABLE mission_levels DROP CONSTRAINT IF EXISTS chk_level_non_negative');
        DB::statement('ALTER TABLE mission_levels DROP CONSTRAINT IF EXISTS chk_cost_non_negative');
        DB::statement('ALTER TABLE mission_levels DROP CONSTRAINT IF EXISTS chk_production_per_hour_non_negative');

        Schema::dropIfExists('mission_levels');
    }
};