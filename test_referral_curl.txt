# Manual curl commands for testing referral system
# Copy and paste these commands one by one in PowerShell or Command Prompt

# 1. Create referrer user
curl -X POST "http://localhost:8000/api/auth/telegram-user" -H "Content-Type: application/json" -d "{\"telegram_id\": 12345, \"first_name\": \"Alice\", \"last_name\": \"Referrer\", \"username\": \"alice_referrer\"}"

# 2. Create referred user with referral
curl -X POST "http://localhost:8000/api/auth/telegram-user" -H "Content-Type: application/json" -d "{\"telegram_id\": 67890, \"first_name\": \"Bob\", \"last_name\": \"Referred\", \"username\": \"bob_referred\", \"referred_by\": \"12345\"}"

# 3. Try to create the same user again (should not give referral bonus)
curl -X POST "http://localhost:8000/api/auth/telegram-user" -H "Content-Type: application/json" -d "{\"telegram_id\": 67890, \"first_name\": \"Bob\", \"last_name\": \"Referred\", \"username\": \"bob_referred\", \"referred_by\": \"12345\"}"

# 4. Test self-referral (should not work)
curl -X POST "http://localhost:8000/api/auth/telegram-user" -H "Content-Type: application/json" -d "{\"telegram_id\": 99999, \"first_name\": \"Charlie\", \"last_name\": \"SelfRef\", \"username\": \"charlie_self\", \"referred_by\": \"99999\"}"

# 5. Test referral with non-existent referrer
curl -X POST "http://localhost:8000/api/auth/telegram-user" -H "Content-Type: application/json" -d "{\"telegram_id\": 11111, \"first_name\": \"David\", \"last_name\": \"InvalidRef\", \"username\": \"david_invalid\", \"referred_by\": \"999999\"}"
